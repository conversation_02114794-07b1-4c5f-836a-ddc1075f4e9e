# Unity 国际化与本地化指南

## 目录
1. [概述](#概述)
2. [Unity 本地化包](#unity-本地化包)
3. [文本本地化](#文本本地化)
4. [资源本地化](#资源本地化)
5. [音频本地化](#音频本地化)
6. [脚本国际化](#脚本国际化)
7. [最佳实践](#最佳实践)
8. [工具与工作流](#工具与工作流)

## 概述

Unity 提供了强大的本地化系统，通过 Localization Package 支持多语言游戏和应用的开发。Unity 的国际化解决方案涵盖了文本、音频、纹理等各种资源的本地化，并提供了完整的工具链来管理多语言内容。

### 主要特性
- **统一本地化系统**：集成的本地化包管理所有类型的资源
- **实时语言切换**：运行时动态切换语言
- **智能回退**：自动回退到默认语言
- **CSV 导入导出**：支持与翻译团队协作
- **伪本地化**：测试和调试本地化功能
- **平台特定本地化**：针对不同平台的特殊处理

## Unity 本地化包

### 安装本地化包
```csharp
// 通过 Package Manager 安装
// Window > Package Manager
// 搜索 "Localization"
// 安装 "Localization" 包

// 或通过 manifest.json 添加
{
  "dependencies": {
    "com.unity.localization": "1.4.4"
  }
}
```

### 基础设置
```csharp
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.Settings;

public class LocalizationManager : MonoBehaviour
{
    void Start()
    {
        // 初始化本地化系统
        LocalizationSettings.InitializationOperation.Completed += OnLocalizationInitialized;
    }
    
    void OnLocalizationInitialized(UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationHandle obj)
    {
        Debug.Log("Localization system initialized");
        
        // 设置默认语言
        var availableLocales = LocalizationSettings.AvailableLocales.Locales;
        if (availableLocales.Count > 0)
        {
            LocalizationSettings.SelectedLocale = availableLocales[0];
        }
    }
    
    public void ChangeLanguage(string localeCode)
    {
        var locale = LocalizationSettings.AvailableLocales.GetLocale(localeCode);
        if (locale != null)
        {
            LocalizationSettings.SelectedLocale = locale;
        }
    }
}
```

### 语言环境配置
```csharp
// 创建语言环境资源
[CreateAssetMenu(fileName = "LocaleSettings", menuName = "Localization/Locale Settings")]
public class LocaleSettings : ScriptableObject
{
    [System.Serializable]
    public class LocaleInfo
    {
        public string localeCode;
        public string displayName;
        public Sprite flag;
        public bool isRightToLeft;
    }
    
    public LocaleInfo[] supportedLocales = new LocaleInfo[]
    {
        new LocaleInfo { localeCode = "en", displayName = "English", isRightToLeft = false },
        new LocaleInfo { localeCode = "zh-CN", displayName = "简体中文", isRightToLeft = false },
        new LocaleInfo { localeCode = "ja", displayName = "日本語", isRightToLeft = false },
        new LocaleInfo { localeCode = "ar", displayName = "العربية", isRightToLeft = true }
    };
    
    public LocaleInfo GetLocaleInfo(string localeCode)
    {
        return System.Array.Find(supportedLocales, locale => locale.localeCode == localeCode);
    }
}
```

## 文本本地化

### 本地化字符串表
```csharp
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.Components;

public class LocalizedTextExample : MonoBehaviour
{
    [SerializeField] private LocalizedString welcomeMessage = new LocalizedString("UI", "welcome_message");
    [SerializeField] private LocalizedString playerName = new LocalizedString("Game", "player_name");
    
    void Start()
    {
        // 获取本地化文本
        string localizedWelcome = welcomeMessage.GetLocalizedString();
        Debug.Log(localizedWelcome);
        
        // 带参数的本地化文本
        string localizedPlayerName = playerName.GetLocalizedString("John");
        Debug.Log(localizedPlayerName);
    }
    
    // 动态创建本地化字符串
    public void ShowDynamicMessage(string tableReference, string entryReference)
    {
        var localizedString = new LocalizedString(tableReference, entryReference);
        string message = localizedString.GetLocalizedString();
        
        // 显示消息
        ShowMessage(message);
    }
    
    private void ShowMessage(string message)
    {
        // UI 显示逻辑
        Debug.Log($"Showing message: {message}");
    }
}
```

### UI 文本组件
```csharp
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Localization.Components;

public class LocalizedUIText : MonoBehaviour
{
    [SerializeField] private Text uiText;
    [SerializeField] private LocalizeStringEvent localizeStringEvent;
    
    void Start()
    {
        // 设置本地化事件
        if (localizeStringEvent == null)
        {
            localizeStringEvent = GetComponent<LocalizeStringEvent>();
        }
        
        if (localizeStringEvent != null)
        {
            localizeStringEvent.OnUpdateString.AddListener(UpdateText);
        }
    }
    
    void UpdateText(string localizedText)
    {
        if (uiText != null)
        {
            uiText.text = localizedText;
            
            // 处理从右到左的文本
            HandleRTLText();
        }
    }
    
    void HandleRTLText()
    {
        var currentLocale = UnityEngine.Localization.Settings.LocalizationSettings.SelectedLocale;
        if (currentLocale != null)
        {
            // 检查是否为从右到左的语言
            bool isRTL = IsRightToLeftLanguage(currentLocale.Identifier.Code);
            
            if (isRTL)
            {
                // 调整文本对齐
                uiText.alignment = TextAnchor.MiddleRight;
                
                // 可能需要反转文本或使用 RTL 插件
                ProcessRTLText();
            }
            else
            {
                uiText.alignment = TextAnchor.MiddleLeft;
            }
        }
    }
    
    bool IsRightToLeftLanguage(string localeCode)
    {
        string[] rtlLanguages = { "ar", "he", "fa", "ur" };
        return System.Array.Exists(rtlLanguages, lang => localeCode.StartsWith(lang));
    }
    
    void ProcessRTLText()
    {
        // 使用 RTL 文本处理插件或自定义逻辑
        // 例如：Arabic Support for Unity 插件
    }
}
```

### 复数形式处理
```csharp
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.SmartFormat.PersistentVariables;

public class PluralFormsExample : MonoBehaviour
{
    [SerializeField] private LocalizedString itemCountMessage;
    
    public void ShowItemCount(int count)
    {
        // 设置变量
        var variable = new IntVariable { Value = count };
        itemCountMessage.Arguments = new object[] { variable };
        
        // 获取本地化文本（支持复数形式）
        string message = itemCountMessage.GetLocalizedString();
        Debug.Log(message);
    }
    
    // 在字符串表中的条目示例：
    // 英文: "You have {0} {0:item|items}"
    // 中文: "你有 {0} 个物品"
    // 俄文: "У вас есть {0} {0:предмет|предмета|предметов}"
}
```

## 资源本地化

### 纹理本地化
```csharp
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.UI;

public class LocalizedImageExample : MonoBehaviour
{
    [SerializeField] private Image targetImage;
    [SerializeField] private LocalizedAsset<Sprite> localizedSprite;
    
    void Start()
    {
        // 加载本地化精灵
        LoadLocalizedSprite();
        
        // 监听语言变化
        UnityEngine.Localization.Settings.LocalizationSettings.SelectedLocaleChanged += OnLocaleChanged;
    }
    
    void LoadLocalizedSprite()
    {
        if (localizedSprite.IsEmpty) return;
        
        var loadOperation = localizedSprite.LoadAssetAsync();
        loadOperation.Completed += (operation) =>
        {
            if (operation.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded)
            {
                targetImage.sprite = operation.Result;
            }
        };
    }
    
    void OnLocaleChanged(UnityEngine.Localization.Locale locale)
    {
        LoadLocalizedSprite();
    }
    
    void OnDestroy()
    {
        UnityEngine.Localization.Settings.LocalizationSettings.SelectedLocaleChanged -= OnLocaleChanged;
    }
}
```

### 预制体本地化
```csharp
using UnityEngine;
using UnityEngine.Localization;

public class LocalizedPrefabManager : MonoBehaviour
{
    [SerializeField] private LocalizedAsset<GameObject> localizedUIPrefab;
    [SerializeField] private Transform parentTransform;
    
    private GameObject currentInstance;
    
    void Start()
    {
        LoadLocalizedPrefab();
        UnityEngine.Localization.Settings.LocalizationSettings.SelectedLocaleChanged += OnLocaleChanged;
    }
    
    void LoadLocalizedPrefab()
    {
        // 销毁当前实例
        if (currentInstance != null)
        {
            DestroyImmediate(currentInstance);
        }
        
        // 加载本地化预制体
        var loadOperation = localizedUIPrefab.LoadAssetAsync();
        loadOperation.Completed += (operation) =>
        {
            if (operation.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded)
            {
                currentInstance = Instantiate(operation.Result, parentTransform);
            }
        };
    }
    
    void OnLocaleChanged(UnityEngine.Localization.Locale locale)
    {
        LoadLocalizedPrefab();
    }
}
```

## 音频本地化

### 本地化音频剪辑
```csharp
using UnityEngine;
using UnityEngine.Localization;

public class LocalizedAudioManager : MonoBehaviour
{
    [SerializeField] private AudioSource audioSource;
    [SerializeField] private LocalizedAsset<AudioClip> localizedBackgroundMusic;
    [SerializeField] private LocalizedAsset<AudioClip> localizedVoiceover;
    
    void Start()
    {
        LoadLocalizedAudio();
        UnityEngine.Localization.Settings.LocalizationSettings.SelectedLocaleChanged += OnLocaleChanged;
    }
    
    void LoadLocalizedAudio()
    {
        // 加载背景音乐
        LoadBackgroundMusic();
        
        // 加载语音
        LoadVoiceover();
    }
    
    void LoadBackgroundMusic()
    {
        var loadOperation = localizedBackgroundMusic.LoadAssetAsync();
        loadOperation.Completed += (operation) =>
        {
            if (operation.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded)
            {
                audioSource.clip = operation.Result;
                audioSource.Play();
            }
        };
    }
    
    void LoadVoiceover()
    {
        var loadOperation = localizedVoiceover.LoadAssetAsync();
        loadOperation.Completed += (operation) =>
        {
            if (operation.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded)
            {
                // 播放语音
                PlayVoiceover(operation.Result);
            }
        };
    }
    
    void PlayVoiceover(AudioClip clip)
    {
        // 创建临时音频源播放语音
        GameObject tempAudioObject = new GameObject("Voiceover");
        AudioSource voiceSource = tempAudioObject.AddComponent<AudioSource>();
        voiceSource.clip = clip;
        voiceSource.Play();
        
        // 播放完成后销毁
        Destroy(tempAudioObject, clip.length);
    }
    
    void OnLocaleChanged(UnityEngine.Localization.Locale locale)
    {
        LoadLocalizedAudio();
    }
}
```

## 脚本国际化

### 数字和日期格式化
```csharp
using System;
using System.Globalization;
using UnityEngine;

public class NumberFormatting : MonoBehaviour
{
    public void FormatNumbers()
    {
        float price = 1234.56f;
        DateTime date = DateTime.Now;
        
        // 获取当前语言环境
        var currentLocale = UnityEngine.Localization.Settings.LocalizationSettings.SelectedLocale;
        CultureInfo culture = new CultureInfo(currentLocale.Identifier.Code);
        
        // 格式化货币
        string formattedPrice = price.ToString("C", culture);
        Debug.Log($"Price: {formattedPrice}");
        
        // 格式化日期
        string formattedDate = date.ToString("d", culture);
        Debug.Log($"Date: {formattedDate}");
        
        // 格式化数字
        int largeNumber = 1234567;
        string formattedNumber = largeNumber.ToString("N0", culture);
        Debug.Log($"Number: {formattedNumber}");
    }
}
```

### 字符串比较和排序
```csharp
using System;
using System.Collections.Generic;
using System.Globalization;
using UnityEngine;

public class StringComparison : MonoBehaviour
{
    public void SortLocalizedStrings(List<string> strings)
    {
        var currentLocale = UnityEngine.Localization.Settings.LocalizationSettings.SelectedLocale;
        CultureInfo culture = new CultureInfo(currentLocale.Identifier.Code);
        
        // 使用当前文化进行排序
        strings.Sort((x, y) => string.Compare(x, y, culture, CompareOptions.None));
        
        foreach (string str in strings)
        {
            Debug.Log(str);
        }
    }
    
    public bool CompareStringsIgnoreCase(string str1, string str2)
    {
        var currentLocale = UnityEngine.Localization.Settings.LocalizationSettings.SelectedLocale;
        CultureInfo culture = new CultureInfo(currentLocale.Identifier.Code);
        
        return string.Compare(str1, str2, culture, CompareOptions.IgnoreCase) == 0;
    }
}
```

## 最佳实践

### 1. 字符串键管理
```csharp
// 使用常量管理字符串键
public static class LocalizationKeys
{
    public const string TABLE_UI = "UI";
    public const string TABLE_GAME = "Game";
    public const string TABLE_TUTORIAL = "Tutorial";
    
    // UI 相关
    public const string UI_WELCOME = "welcome_message";
    public const string UI_START_GAME = "start_game";
    public const string UI_SETTINGS = "settings";
    public const string UI_EXIT = "exit";
    
    // 游戏相关
    public const string GAME_SCORE = "score";
    public const string GAME_LEVEL = "level";
    public const string GAME_HEALTH = "health";
    
    // 创建本地化字符串的辅助方法
    public static LocalizedString CreateUIString(string key)
    {
        return new LocalizedString(TABLE_UI, key);
    }
    
    public static LocalizedString CreateGameString(string key)
    {
        return new LocalizedString(TABLE_GAME, key);
    }
}

// 使用示例
public class UIManager : MonoBehaviour
{
    private LocalizedString welcomeMessage;
    
    void Start()
    {
        welcomeMessage = LocalizationKeys.CreateUIString(LocalizationKeys.UI_WELCOME);
    }
}
```

### 2. 伪本地化测试
```csharp
using UnityEngine;
using UnityEngine.Localization;

public class PseudoLocalizationTester : MonoBehaviour
{
    [SerializeField] private bool enablePseudoLocalization = false;
    
    void Start()
    {
        if (enablePseudoLocalization)
        {
            EnablePseudoLocalization();
        }
    }
    
    void EnablePseudoLocalization()
    {
        // 创建伪本地化处理器
        var pseudoLocale = ScriptableObject.CreateInstance<UnityEngine.Localization.Locale>();
        pseudoLocale.Identifier = new UnityEngine.Localization.LocaleIdentifier("pseudo");
        
        // 添加到可用语言列表
        var availableLocales = UnityEngine.Localization.Settings.LocalizationSettings.AvailableLocales;
        availableLocales.AddLocale(pseudoLocale);
        
        // 设置为当前语言
        UnityEngine.Localization.Settings.LocalizationSettings.SelectedLocale = pseudoLocale;
    }
    
    // 伪本地化文本处理
    public string PseudoLocalizeText(string originalText)
    {
        if (!enablePseudoLocalization) return originalText;
        
        // 添加前缀和后缀
        string pseudoText = "[" + originalText + "]";
        
        // 增加长度（模拟翻译后的长度变化）
        pseudoText += new string('x', originalText.Length / 3);
        
        // 替换某些字符（模拟特殊字符）
        pseudoText = pseudoText.Replace('a', 'á').Replace('e', 'é').Replace('o', 'ó');
        
        return pseudoText;
    }
}
```

## 工具与工作流

### CSV 导入导出
```csharp
using UnityEngine;
using UnityEditor;
using UnityEngine.Localization;
using System.IO;

#if UNITY_EDITOR
public class LocalizationCSVExporter : EditorWindow
{
    [MenuItem("Tools/Localization/Export to CSV")]
    static void ExportToCSV()
    {
        var stringTables = UnityEngine.Localization.Settings.LocalizationSettings.StringDatabase.GetAllTables();
        
        foreach (var table in stringTables)
        {
            string csvPath = Path.Combine(Application.dataPath, $"Localization_{table.TableCollectionName}.csv");
            
            using (StreamWriter writer = new StreamWriter(csvPath))
            {
                // 写入标题行
                writer.WriteLine("Key,English,Chinese,Japanese,Arabic");
                
                // 写入数据行
                foreach (var entry in table.Values)
                {
                    string key = entry.Key;
                    string english = GetLocalizedValue(table.TableCollectionName, key, "en");
                    string chinese = GetLocalizedValue(table.TableCollectionName, key, "zh-CN");
                    string japanese = GetLocalizedValue(table.TableCollectionName, key, "ja");
                    string arabic = GetLocalizedValue(table.TableCollectionName, key, "ar");
                    
                    writer.WriteLine($"{key},{english},{chinese},{japanese},{arabic}");
                }
            }
        }
        
        Debug.Log("CSV export completed");
    }
    
    static string GetLocalizedValue(string tableName, string key, string localeCode)
    {
        // 获取指定语言的本地化值
        var locale = UnityEngine.Localization.Settings.LocalizationSettings.AvailableLocales.GetLocale(localeCode);
        if (locale == null) return "";
        
        var table = UnityEngine.Localization.Settings.LocalizationSettings.StringDatabase.GetTable(tableName, locale);
        if (table == null) return "";
        
        var entry = table.GetEntry(key);
        return entry?.Value ?? "";
    }
}
#endif
```

### 自动化本地化验证
```csharp
using UnityEngine;
using System.Collections.Generic;

public class LocalizationValidator : MonoBehaviour
{
    [System.Serializable]
    public class ValidationResult
    {
        public string tableName;
        public string key;
        public string localeCode;
        public string issue;
    }
    
    public List<ValidationResult> ValidateLocalization()
    {
        List<ValidationResult> results = new List<ValidationResult>();
        
        var stringTables = UnityEngine.Localization.Settings.LocalizationSettings.StringDatabase.GetAllTables();
        var availableLocales = UnityEngine.Localization.Settings.LocalizationSettings.AvailableLocales.Locales;
        
        foreach (var table in stringTables)
        {
            foreach (var entry in table.Values)
            {
                string key = entry.Key;
                
                foreach (var locale in availableLocales)
                {
                    string value = GetLocalizedValue(table.TableCollectionName, key, locale.Identifier.Code);
                    
                    // 检查缺失的翻译
                    if (string.IsNullOrEmpty(value))
                    {
                        results.Add(new ValidationResult
                        {
                            tableName = table.TableCollectionName,
                            key = key,
                            localeCode = locale.Identifier.Code,
                            issue = "Missing translation"
                        });
                    }
                    
                    // 检查过长的文本
                    if (value.Length > 100)
                    {
                        results.Add(new ValidationResult
                        {
                            tableName = table.TableCollectionName,
                            key = key,
                            localeCode = locale.Identifier.Code,
                            issue = "Text too long"
                        });
                    }
                    
                    // 检查特殊字符
                    if (ContainsInvalidCharacters(value))
                    {
                        results.Add(new ValidationResult
                        {
                            tableName = table.TableCollectionName,
                            key = key,
                            localeCode = locale.Identifier.Code,
                            issue = "Contains invalid characters"
                        });
                    }
                }
            }
        }
        
        return results;
    }
    
    private string GetLocalizedValue(string tableName, string key, string localeCode)
    {
        // 实现获取本地化值的逻辑
        return "";
    }
    
    private bool ContainsInvalidCharacters(string text)
    {
        // 检查是否包含无效字符
        return false;
    }
}
```

---

*Unity 的本地化系统提供了完整的多语言支持，适合各种规模的项目。更多详细信息请参考 [Unity 本地化包文档](https://docs.unity3d.com/Packages/com.unity.localization@latest/)*
