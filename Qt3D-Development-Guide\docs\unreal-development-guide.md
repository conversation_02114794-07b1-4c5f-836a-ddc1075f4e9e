# Unreal Engine 开发指南

## 目录
1. [概述](#概述)
2. [技术特性](#技术特性)
3. [开发环境搭建](#开发环境搭建)
4. [核心概念](#核心概念)
5. [Blueprint 可视化编程](#blueprint-可视化编程)
6. [C++ 编程](#c-编程)
7. [2D/3D 开发](#2d3d-开发)
8. [最佳实践](#最佳实践)

## 概述

Unreal Engine 是 Epic Games 开发的世界领先的实时 3D 创作平台，以其顶级的渲染质量、强大的工具链和 AAA 级游戏开发能力而闻名。从独立开发者到大型工作室，Unreal Engine 为各种规模的项目提供了专业级的开发工具。

### 主要优势
- **顶级画质**：业界领先的渲染质量和视觉效果
- **Blueprint 系统**：强大的可视化编程系统
- **免费使用**：5% 营收分成模式，降低开发门槛
- **完整工具链**：从概念到发布的完整开发工具
- **源码开放**：完整的引擎源码可供定制
- **跨平台支持**：支持主流平台和新兴平台

## 技术特性

### 渲染系统
- **Lumen**：动态全局光照系统
- **Nanite**：虚拟化几何体技术
- **Chaos Physics**：高性能物理模拟系统
- **Niagara**：下一代粒子系统
- **World Partition**：大世界流式加载系统
- **MetaHuman Creator**：高质量数字人类创建工具

### 材质系统
- **Material Editor**：节点式材质编辑器
- **Material Functions**：可重用的材质函数
- **Material Instances**：高效的材质变体系统
- **Shader Model 6**：支持最新的着色器技术
- **Virtual Texturing**：虚拟纹理技术

### 动画系统
- **Control Rig**：程序化动画控制系统
- **Sequencer**：电影级动画序列编辑器
- **Animation Blueprint**：复杂动画逻辑系统
- **Motion Matching**：基于数据驱动的动画系统
- **Live Link**：实时动画数据传输

## 开发环境搭建

### 系统要求
- **操作系统**：Windows 10 64-bit, macOS 10.14.6+, Ubuntu 18.04+
- **内存**：最少 32GB RAM，推荐 64GB+
- **存储**：至少 100GB 可用空间（SSD 推荐）
- **显卡**：DirectX 11/12 兼容显卡，推荐 RTX 系列

### 安装步骤

#### 1. 安装 Epic Games Launcher
```bash
# 下载并安装 Epic Games Launcher
https://www.unrealengine.com/download

# 创建 Epic Games 账户
# 登录 Launcher
```

#### 2. 安装 Unreal Engine
```bash
# 在 Launcher 中：
# 1. 点击 "Unreal Engine" 标签
# 2. 选择 "Install Engine"
# 3. 选择版本（推荐最新稳定版）
# 4. 选择安装位置
# 5. 等待下载和安装完成
```

#### 3. 安装开发工具
```bash
# Windows: 安装 Visual Studio 2019/2022
# - 包含 C++ 开发工具
# - 包含 Windows 10/11 SDK

# macOS: 安装 Xcode
# - 最新版本的 Xcode
# - Command Line Tools

# Linux: 安装构建工具
sudo apt-get install build-essential clang
```

## 核心概念

### Actor 和 Component
```cpp
// Actor 是游戏世界中的基本对象
UCLASS()
class MYGAME_API AMyActor : public AActor
{
    GENERATED_BODY()
    
public:
    AMyActor();
    
protected:
    virtual void BeginPlay() override;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class UStaticMeshComponent* MeshComponent;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class UBoxComponent* CollisionComponent;
    
public:
    virtual void Tick(float DeltaTime) override;
};

// 构造函数中设置组件
AMyActor::AMyActor()
{
    PrimaryActorTick.bCanEverTick = true;
    
    // 创建根组件
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
    
    // 创建网格组件
    MeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("MeshComponent"));
    MeshComponent->SetupAttachment(RootComponent);
    
    // 创建碰撞组件
    CollisionComponent = CreateDefaultSubobject<UBoxComponent>(TEXT("CollisionComponent"));
    CollisionComponent->SetupAttachment(RootComponent);
}
```

### Pawn 和 Controller
```cpp
// Pawn 是可以被控制的 Actor
UCLASS()
class MYGAME_API AMyPawn : public APawn
{
    GENERATED_BODY()
    
public:
    AMyPawn();
    
protected:
    virtual void BeginPlay() override;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class UStaticMeshComponent* MeshComponent;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class UCameraComponent* CameraComponent;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class UFloatingPawnMovement* MovementComponent;
    
public:
    virtual void Tick(float DeltaTime) override;
    virtual void SetupPlayerInputComponent(UInputComponent* PlayerInputComponent) override;
    
private:
    void MoveForward(float Value);
    void MoveRight(float Value);
    void Turn(float Value);
    void LookUp(float Value);
};

// 设置输入绑定
void AMyPawn::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
    Super::SetupPlayerInputComponent(PlayerInputComponent);
    
    PlayerInputComponent->BindAxis("MoveForward", this, &AMyPawn::MoveForward);
    PlayerInputComponent->BindAxis("MoveRight", this, &AMyPawn::MoveRight);
    PlayerInputComponent->BindAxis("Turn", this, &AMyPawn::Turn);
    PlayerInputComponent->BindAxis("LookUp", this, &AMyPawn::LookUp);
}
```

### GameMode 和 GameState
```cpp
// GameMode 定义游戏规则
UCLASS()
class MYGAME_API AMyGameMode : public AGameModeBase
{
    GENERATED_BODY()
    
public:
    AMyGameMode();
    
protected:
    virtual void BeginPlay() override;
    virtual void PostLogin(APlayerController* NewPlayer) override;
    virtual void Logout(AController* Exiting) override;
    
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Game Rules")
    int32 MaxPlayers = 4;
    
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Game Rules")
    float GameDuration = 300.0f; // 5 minutes
    
public:
    UFUNCTION(BlueprintCallable, Category = "Game")
    void StartGame();
    
    UFUNCTION(BlueprintCallable, Category = "Game")
    void EndGame();
};
```

## Blueprint 可视化编程

### 基础 Blueprint 节点
```cpp
// Blueprint 可调用函数
UCLASS()
class MYGAME_API UMyBlueprintFunctionLibrary : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()
    
public:
    UFUNCTION(BlueprintCallable, Category = "My Functions")
    static float CalculateDistance(FVector PointA, FVector PointB);
    
    UFUNCTION(BlueprintCallable, Category = "My Functions")
    static bool IsActorInRange(AActor* Actor, FVector Center, float Range);
    
    UFUNCTION(BlueprintPure, Category = "My Functions")
    static FString FormatTime(float TimeInSeconds);
};

// 实现
float UMyBlueprintFunctionLibrary::CalculateDistance(FVector PointA, FVector PointB)
{
    return FVector::Dist(PointA, PointB);
}

bool UMyBlueprintFunctionLibrary::IsActorInRange(AActor* Actor, FVector Center, float Range)
{
    if (!Actor) return false;
    
    float Distance = FVector::Dist(Actor->GetActorLocation(), Center);
    return Distance <= Range;
}
```

### Blueprint 接口
```cpp
// Blueprint 接口定义
UINTERFACE(MinimalAPI, Blueprintable)
class UInteractable : public UInterface
{
    GENERATED_BODY()
};

class MYGAME_API IInteractable
{
    GENERATED_BODY()
    
public:
    UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
    void Interact(AActor* InteractingActor);
    virtual void Interact_Implementation(AActor* InteractingActor) {}
    
    UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
    bool CanInteract(AActor* InteractingActor);
    virtual bool CanInteract_Implementation(AActor* InteractingActor) { return true; }
};

// 实现接口的类
UCLASS()
class MYGAME_API AInteractableActor : public AActor, public IInteractable
{
    GENERATED_BODY()
    
public:
    virtual void Interact_Implementation(AActor* InteractingActor) override;
    virtual bool CanInteract_Implementation(AActor* InteractingActor) override;
};
```

## C++ 编程

### 属性系统 (UPROPERTY)
```cpp
UCLASS()
class MYGAME_API AMyCharacter : public ACharacter
{
    GENERATED_BODY()
    
public:
    // 在编辑器中可见和可编辑
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float Health = 100.0f;
    
    // 只在编辑器中可见
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class UHealthComponent* HealthComponent;
    
    // 只在实例中可编辑
    UPROPERTY(EditInstanceOnly, Category = "Configuration")
    bool bIsImportantActor = false;
    
    // 序列化但不在编辑器中显示
    UPROPERTY(SaveGame)
    int32 ExperiencePoints = 0;
    
    // 复制到客户端
    UPROPERTY(Replicated)
    float ServerHealth;
    
protected:
    // 只在默认值中可编辑
    UPROPERTY(EditDefaultsOnly, Category = "Audio")
    class USoundBase* DeathSound;
};
```

### 函数系统 (UFUNCTION)
```cpp
public:
    // Blueprint 可调用
    UFUNCTION(BlueprintCallable, Category = "Health")
    void TakeDamage(float DamageAmount);
    
    // Blueprint 可实现
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnHealthChanged(float NewHealth);
    
    // Blueprint 原生事件
    UFUNCTION(BlueprintNativeEvent, Category = "Events")
    void OnDeath();
    virtual void OnDeath_Implementation();
    
    // 服务器 RPC
    UFUNCTION(Server, Reliable, WithValidation)
    void ServerTakeDamage(float DamageAmount);
    void ServerTakeDamage_Implementation(float DamageAmount);
    bool ServerTakeDamage_Validate(float DamageAmount);
    
    // 客户端 RPC
    UFUNCTION(Client, Reliable)
    void ClientPlayDeathEffect();
    void ClientPlayDeathEffect_Implementation();
```

### 委托和事件
```cpp
// 声明委托
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnHealthChanged, float, NewHealth);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnDeath);

UCLASS()
class MYGAME_API UHealthComponent : public UActorComponent
{
    GENERATED_BODY()
    
public:
    // Blueprint 可绑定的事件
    UPROPERTY(BlueprintAssignable)
    FOnHealthChanged OnHealthChanged;
    
    UPROPERTY(BlueprintAssignable)
    FOnDeath OnDeath;
    
    UFUNCTION(BlueprintCallable, Category = "Health")
    void TakeDamage(float DamageAmount);
    
private:
    UPROPERTY(EditAnywhere, Category = "Health")
    float MaxHealth = 100.0f;
    
    UPROPERTY(VisibleAnywhere, Category = "Health")
    float CurrentHealth;
};

// 使用委托
void UHealthComponent::TakeDamage(float DamageAmount)
{
    CurrentHealth = FMath::Max(0.0f, CurrentHealth - DamageAmount);
    
    // 广播事件
    OnHealthChanged.Broadcast(CurrentHealth);
    
    if (CurrentHealth <= 0.0f)
    {
        OnDeath.Broadcast();
    }
}
```

## 2D/3D 开发

### UMG (Unreal Motion Graphics)
```cpp
// UI Widget 基类
UCLASS()
class MYGAME_API UMyUserWidget : public UUserWidget
{
    GENERATED_BODY()
    
protected:
    virtual void NativeConstruct() override;
    virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
    
    // 绑定 UI 元素
    UPROPERTY(meta = (BindWidget))
    class UTextBlock* HealthText;
    
    UPROPERTY(meta = (BindWidget))
    class UProgressBar* HealthBar;
    
    UPROPERTY(meta = (BindWidget))
    class UButton* StartButton;
    
public:
    UFUNCTION(BlueprintCallable, Category = "UI")
    void UpdateHealth(float CurrentHealth, float MaxHealth);
    
private:
    UFUNCTION()
    void OnStartButtonClicked();
};

// 实现
void UMyUserWidget::NativeConstruct()
{
    Super::NativeConstruct();
    
    if (StartButton)
    {
        StartButton->OnClicked.AddDynamic(this, &UMyUserWidget::OnStartButtonClicked);
    }
}

void UMyUserWidget::UpdateHealth(float CurrentHealth, float MaxHealth)
{
    if (HealthText)
    {
        FText HealthString = FText::FromString(FString::Printf(TEXT("%.0f / %.0f"), CurrentHealth, MaxHealth));
        HealthText->SetText(HealthString);
    }
    
    if (HealthBar)
    {
        float HealthPercent = MaxHealth > 0 ? CurrentHealth / MaxHealth : 0.0f;
        HealthBar->SetPercent(HealthPercent);
    }
}
```

### Paper2D (2D 游戏开发)
```cpp
// 2D 角色类
UCLASS()
class MYGAME_API AMyPaper2DCharacter : public APaperCharacter
{
    GENERATED_BODY()
    
public:
    AMyPaper2DCharacter();
    
protected:
    virtual void BeginPlay() override;
    virtual void SetupPlayerInputComponent(UInputComponent* PlayerInputComponent) override;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Animation")
    class UPaperFlipbook* IdleAnimation;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Animation")
    class UPaperFlipbook* RunAnimation;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float MoveSpeed = 300.0f;
    
public:
    virtual void Tick(float DeltaTime) override;
    
private:
    void MoveRight(float Value);
    void Jump();
    
    void UpdateAnimation();
    
    bool bIsMoving = false;
    float MovementDirection = 0.0f;
};

// 2D 动画更新
void AMyPaper2DCharacter::UpdateAnimation()
{
    UPaperFlipbookComponent* FlipbookComponent = GetSprite();
    if (!FlipbookComponent) return;
    
    if (bIsMoving)
    {
        FlipbookComponent->SetFlipbook(RunAnimation);
    }
    else
    {
        FlipbookComponent->SetFlipbook(IdleAnimation);
    }
    
    // 根据移动方向翻转精灵
    if (MovementDirection != 0.0f)
    {
        bool bFaceRight = MovementDirection > 0.0f;
        FlipbookComponent->SetWorldScale3D(FVector(bFaceRight ? 1.0f : -1.0f, 1.0f, 1.0f));
    }
}
```

## 最佳实践

### 1. 项目组织
```
Content/
├── Blueprints/
│   ├── Characters/
│   ├── Weapons/
│   ├── UI/
│   └── GameModes/
├── Materials/
├── Meshes/
├── Textures/
├── Audio/
├── Maps/
└── Data/
```

### 2. 性能优化
```cpp
// LOD 系统使用
UCLASS()
class MYGAME_API AOptimizedActor : public AActor
{
    GENERATED_BODY()
    
public:
    AOptimizedActor();
    
protected:
    UPROPERTY(VisibleAnywhere, Category = "LOD")
    class UStaticMeshComponent* HighDetailMesh;
    
    UPROPERTY(VisibleAnywhere, Category = "LOD")
    class UStaticMeshComponent* LowDetailMesh;
    
    UPROPERTY(EditAnywhere, Category = "LOD")
    float LODDistance = 1000.0f;
    
public:
    virtual void Tick(float DeltaTime) override;
    
private:
    void UpdateLOD();
    APawn* GetClosestPlayer();
};

void AOptimizedActor::UpdateLOD()
{
    APawn* ClosestPlayer = GetClosestPlayer();
    if (!ClosestPlayer) return;
    
    float Distance = FVector::Dist(GetActorLocation(), ClosestPlayer->GetActorLocation());
    
    bool bUseHighDetail = Distance < LODDistance;
    HighDetailMesh->SetVisibility(bUseHighDetail);
    LowDetailMesh->SetVisibility(!bUseHighDetail);
}
```

### 3. 内存管理
```cpp
// 智能指针使用
UCLASS()
class MYGAME_API UMyGameInstance : public UGameInstance
{
    GENERATED_BODY()
    
private:
    // 使用 TSharedPtr 管理非 UObject 数据
    TSharedPtr<class FGameData> GameData;
    
    // 使用 TWeakPtr 避免循环引用
    TWeakPtr<class FPlayerData> CurrentPlayerData;
    
public:
    void InitializeGameData();
    void CleanupGameData();
};
```

---

*Unreal Engine 提供了业界领先的 3D 开发能力，适合从独立游戏到 AAA 级商业项目。更多详细信息请参考 [Unreal Engine 官方文档](https://docs.unrealengine.com/)*
