<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="qtGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#41cd52;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2c5aa0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Qt Logo Background Circle -->
  <circle cx="16" cy="16" r="15" fill="url(#qtGradient)" stroke="#ffffff" stroke-width="1"/>
  
  <!-- Qt Text -->
  <text x="16" y="20" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="white">Qt</text>
  
  <!-- 3D Effect -->
  <circle cx="16" cy="16" r="15" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  <circle cx="16" cy="16" r="12" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="0.5"/>
</svg>
