# Kanzi Engine Development Guide

## Table of Contents
1. [Overview](#overview)
2. [Technical Features](#technical-features)
3. [Development Environment Setup](#development-environment-setup)
4. [Core Concepts](#core-concepts)
5. [HMI Development](#hmi-development)
6. [2D/3D Interface Design](#2d3d-interface-design)
7. [Data Binding and Interaction](#data-binding-and-interaction)
8. [Best Practices](#best-practices)

## Overview

Kanzi is a professional HMI (Human-Machine Interface) development platform developed by Rightware, specifically designed for embedded systems and the automotive industry. Kanzi has become the preferred HMI solution for automotive instrument clusters, industrial controls, and high-end consumer electronics due to its extremely low resource usage, real-time performance, and compliance with automotive safety standards.

### Main Advantages
- **Embedded Optimization**: Designed specifically for resource-constrained embedded systems
- **Ultra-low Resource Usage**: Minimized memory and CPU usage
- **Real-time Performance**: Guaranteed real-time response and smooth animations
- **Automotive Standards**: Compliant with automotive safety standards like ISO 26262
- **Professional Toolchain**: Complete HMI design and development tools
- **Cross-platform Support**: Support for multiple embedded platforms and operating systems

## Technical Features

### Rendering Engine
- **GPU Accelerated Rendering**: Full utilization of hardware GPU acceleration
- **Vector Graphics**: High-quality 2D vector graphics rendering
- **3D Rendering**: Lightweight 3D rendering capabilities
- **Multi-layer Compositing**: Efficient layer compositing system
- **Anti-aliasing**: High-quality anti-aliasing technology
- **HDR Support**: High dynamic range display support

### HMI Framework
- **Component Architecture**: Modular UI component system
- **State Management**: Powerful state machines and state management
- **Animation System**: Smooth 2D/3D animation engine
- **Touch Interaction**: Multi-touch and gesture recognition
- **Data Binding**: Real-time data binding and updates
- **Theme System**: Flexible theme and style management

### Platform Support
- **Automotive Platforms**: QNX, Linux, Android Automotive
- **Industrial Platforms**: VxWorks, Linux RT, Windows IoT
- **Mobile Platforms**: Android, iOS (limited support)
- **Hardware Platforms**: ARM, x86, PowerPC, etc.

## Development Environment Setup

### System Requirements
- **Development Host**: Windows 10+, Ubuntu 18.04+, macOS 10.14+
- **Memory**: Minimum 8GB RAM, recommended 16GB+
- **Storage**: At least 10GB available space
- **Graphics**: Support for OpenGL ES 2.0+ or DirectX 11+

### Installation Steps

#### 1. Obtain Kanzi License
```bash
# Contact Rightware for evaluation license
# https://www.rightware.com/kanzi/

# Commercial licenses require contact with sales team
# Educational licenses available through academic partners
```

#### 2. Install Kanzi Studio
```bash
# Download Kanzi Studio installation package
# Run installer
# Enter license key
# Select installation components:
# - Kanzi Studio (design tools)
# - Kanzi Engine (runtime)
# - Platform SDKs (target platform SDKs)
```

#### 3. Configure Development Environment
```bash
# Set environment variables
export KANZI_HOME=/opt/kanzi
export PATH=$KANZI_HOME/bin:$PATH

# Configure target platform toolchain
# Example: ARM cross-compilation toolchain
export CROSS_COMPILE=arm-linux-gnueabihf-
export CC=${CROSS_COMPILE}gcc
export CXX=${CROSS_COMPILE}g++
```

## Core Concepts

### Node Hierarchy
```cpp
// Basic node types in Kanzi
#include <kanzi/kanzi.hpp>

using namespace kanzi;

// Create scene graph
class MyApplication : public Application
{
public:
    void onConfigure(ApplicationProperties& configuration) override
    {
        configuration.binaryName = "MyKanziApp";
        configuration.defaultWindowProperties.width = 1280;
        configuration.defaultWindowProperties.height = 720;
    }
    
    void onProjectLoaded() override
    {
        // Get root node
        Node2DSharedPtr rootNode = getScreen();
        
        // Create child nodes
        Node2DSharedPtr panel = Node2D::create(getDomain(), "MainPanel");
        rootNode->addChild(panel);
        
        // Set node properties
        panel->setWidth(400);
        panel->setHeight(300);
        panel->setTranslation(Vector2(100, 100));
    }
};

// Application entry point
Application* createApplication()
{
    return new MyApplication;
}
```

### Resource Management
```cpp
// Resource loading and management
class ResourceManager
{
public:
    void loadResources()
    {
        // Load texture
        TextureSharedPtr texture = Texture::createFromFile(getDomain(), "assets/button.png");
        
        // Load font
        FontSharedPtr font = Font::createFromFile(getDomain(), "assets/arial.ttf");
        
        // Load 3D model
        MeshSharedPtr mesh = Mesh::createFromFile(getDomain(), "assets/car.fbx");
        
        // Cache resources
        m_textureCache["button"] = texture;
        m_fontCache["default"] = font;
        m_meshCache["car"] = mesh;
    }
    
private:
    map<string, TextureSharedPtr> m_textureCache;
    map<string, FontSharedPtr> m_fontCache;
    map<string, MeshSharedPtr> m_meshCache;
};
```

### Component System
```cpp
// Custom component
class SpeedometerComponent : public NodeComponent
{
    KZ_COMPONENT(SpeedometerComponent)
    
public:
    static PropertyType<float> SpeedProperty;
    static PropertyType<float> MaxSpeedProperty;
    
    explicit SpeedometerComponent(Domain* domain, string_view name)
        : NodeComponent(domain, name)
    {
    }
    
    void initialize() override
    {
        NodeComponent::initialize();
        
        // Listen for property changes
        addPropertyNotificationHandler(SpeedProperty, 
            bind(&SpeedometerComponent::onSpeedChanged, this, placeholders::_1));
    }
    
private:
    void onSpeedChanged(PropertyObject& object)
    {
        float speed = getProperty(SpeedProperty);
        float maxSpeed = getProperty(MaxSpeedProperty);
        float angle = (speed / maxSpeed) * 270.0f - 135.0f; // -135° to +135°
        
        // Update needle rotation
        Node2DSharedPtr needle = findChild<Node2D>("Needle");
        if (needle)
        {
            needle->setRotation(angle);
        }
    }
};

// Register properties
PropertyType<float> SpeedometerComponent::SpeedProperty(
    kzMakeFixedString("SpeedometerComponent.Speed"), 0.0f);
PropertyType<float> SpeedometerComponent::MaxSpeedProperty(
    kzMakeFixedString("SpeedometerComponent.MaxSpeed"), 200.0f);
```

## HMI Development

### Dashboard Interface
```cpp
// Automotive dashboard implementation
class CarDashboard : public Node2D
{
public:
    static Node2DSharedPtr create(Domain* domain, string_view name)
    {
        return Node2DSharedPtr(new CarDashboard(domain, name));
    }
    
protected:
    explicit CarDashboard(Domain* domain, string_view name)
        : Node2D(domain, name)
    {
        initialize();
    }
    
    void initialize()
    {
        // Create speedometer
        m_speedometer = createSpeedometer();
        addChild(m_speedometer);
        
        // Create tachometer
        m_tachometer = createTachometer();
        addChild(m_tachometer);
        
        // Create fuel gauge
        m_fuelGauge = createFuelGauge();
        addChild(m_fuelGauge);
        
        // Create warning lights
        m_warningLights = createWarningLights();
        addChild(m_warningLights);
        
        // Create information display area
        m_infoDisplay = createInfoDisplay();
        addChild(m_infoDisplay);
    }
    
private:
    Node2DSharedPtr createSpeedometer()
    {
        Node2DSharedPtr speedometer = Node2D::create(getDomain(), "Speedometer");
        
        // Background disc
        Node2DSharedPtr background = Image2D::create(getDomain(), "SpeedBackground");
        background->setTexture(getResourceManager()->acquireResource<Texture>("speedometer_bg"));
        speedometer->addChild(background);
        
        // Scale marks
        for (int i = 0; i <= 200; i += 20)
        {
            Node2DSharedPtr tick = createSpeedTick(i);
            speedometer->addChild(tick);
        }
        
        // Needle
        Node2DSharedPtr needle = Image2D::create(getDomain(), "SpeedNeedle");
        needle->setTexture(getResourceManager()->acquireResource<Texture>("needle"));
        needle->setOrigin(Vector2(0.5f, 0.9f)); // Set rotation center
        speedometer->addChild(needle);
        
        // Digital display
        Text2DSharedPtr speedText = Text2D::create(getDomain(), "SpeedText");
        speedText->setFont(getResourceManager()->acquireResource<Font>("digital_font"));
        speedText->setText("0");
        speedometer->addChild(speedText);
        
        return speedometer;
    }
    
    Node2DSharedPtr createSpeedTick(int speed)
    {
        Node2DSharedPtr tick = Node2D::create(getDomain(), "Tick_" + to_string(speed));
        
        // Calculate tick position
        float angle = (speed / 200.0f) * 270.0f - 135.0f;
        float radius = 150.0f;
        float x = cos(degreesToRadians(angle)) * radius;
        float y = sin(degreesToRadians(angle)) * radius;
        
        tick->setTranslation(Vector2(x, y));
        tick->setRotation(angle + 90.0f);
        
        // Major and minor ticks
        if (speed % 40 == 0)
        {
            // Major tick - longer and thicker
            Rectangle2DSharedPtr line = Rectangle2D::create(getDomain(), "MajorTick");
            line->setWidth(3);
            line->setHeight(20);
            line->setBrush(ColorBrush::create(getDomain(), Color::createRGB(1.0f, 1.0f, 1.0f)));
            tick->addChild(line);
            
            // Number label
            Text2DSharedPtr label = Text2D::create(getDomain(), "TickLabel");
            label->setText(to_string(speed));
            label->setTranslation(Vector2(0, -30));
            tick->addChild(label);
        }
        else
        {
            // Minor tick - shorter and thinner
            Rectangle2DSharedPtr line = Rectangle2D::create(getDomain(), "MinorTick");
            line->setWidth(1);
            line->setHeight(10);
            line->setBrush(ColorBrush::create(getDomain(), Color::createRGB(0.8f, 0.8f, 0.8f)));
            tick->addChild(line);
        }
        
        return tick;
    }
    
    void updateSpeed(float speed)
    {
        // Update speedometer needle
        Node2DSharedPtr needle = m_speedometer->findChild<Node2D>("SpeedNeedle");
        if (needle)
        {
            float angle = (speed / 200.0f) * 270.0f - 135.0f;
            
            // Smooth animation
            PropertyAnimationSharedPtr animation = PropertyAnimation::create(getDomain(), "SpeedAnimation");
            animation->setTargetObject(needle);
            animation->setTargetProperty(Node2D::RotationProperty);
            animation->setDuration(chrono::milliseconds(500));
            animation->setStartValue(needle->getRotation());
            animation->setTargetValue(angle);
            animation->setEasingFunction(EasingFunction::createEaseInOut());
            animation->start();
        }
        
        // Update digital display
        Text2DSharedPtr speedText = m_speedometer->findChild<Text2D>("SpeedText");
        if (speedText)
        {
            speedText->setText(to_string(static_cast<int>(speed)));
        }
    }
    
private:
    Node2DSharedPtr m_speedometer;
    Node2DSharedPtr m_tachometer;
    Node2DSharedPtr m_fuelGauge;
    Node2DSharedPtr m_warningLights;
    Node2DSharedPtr m_infoDisplay;
};
```

## 2D/3D Interface Design

### Mixed 2D/3D Interface
```cpp
// 2D UI overlay in 3D scene
class Mixed2D3DInterface : public Node3D
{
public:
    static Node3DSharedPtr create(Domain* domain, string_view name)
    {
        return Node3DSharedPtr(new Mixed2D3DInterface(domain, name));
    }
    
protected:
    explicit Mixed2D3DInterface(Domain* domain, string_view name)
        : Node3D(domain, name)
    {
        initialize();
    }
    
    void initialize()
    {
        // Create 3D scene
        create3DScene();
        
        // Create 2D UI overlay
        create2DOverlay();
        
        // Setup camera
        setupCamera();
    }
    
private:
    void create3DScene()
    {
        // Load 3D car model
        Node3DSharedPtr carModel = Model3D::create(getDomain(), "CarModel");
        carModel->setMesh(getResourceManager()->acquireResource<Mesh>("car_model.fbx"));
        carModel->setMaterial(getResourceManager()->acquireResource<Material>("car_material"));
        addChild(carModel);
        
        // Ambient lighting
        LightSharedPtr ambientLight = AmbientLight::create(getDomain(), "AmbientLight");
        ambientLight->setColor(Color::createRGB(0.3f, 0.3f, 0.3f));
        addChild(ambientLight);
        
        // Directional light
        LightSharedPtr directionalLight = DirectionalLight::create(getDomain(), "DirectionalLight");
        directionalLight->setDirection(Vector3(-1, -1, -1));
        directionalLight->setColor(Color::createRGB(0.8f, 0.8f, 0.8f));
        addChild(directionalLight);
    }
    
    void create2DOverlay()
    {
        // Create 2D overlay
        Node2DSharedPtr overlay = Node2D::create(getDomain(), "UIOverlay");
        
        // HUD elements
        createSpeedDisplay(overlay);
        createNavigationInfo(overlay);
        createControlButtons(overlay);
        
        // Add 2D overlay to 3D scene
        addChild(overlay);
    }
    
    void createSpeedDisplay(Node2DSharedPtr parent)
    {
        Node2DSharedPtr speedPanel = Rectangle2D::create(getDomain(), "SpeedPanel");
        speedPanel->setWidth(200);
        speedPanel->setHeight(100);
        speedPanel->setTranslation(Vector2(50, 50));
        speedPanel->setBrush(ColorBrush::create(getDomain(), Color::createRGBA(0, 0, 0, 0.7f)));
        
        Text2DSharedPtr speedLabel = Text2D::create(getDomain(), "SpeedLabel");
        speedLabel->setText("Speed");
        speedLabel->setTranslation(Vector2(10, 10));
        speedPanel->addChild(speedLabel);
        
        Text2DSharedPtr speedValue = Text2D::create(getDomain(), "SpeedValue");
        speedValue->setText("0 km/h");
        speedValue->setTranslation(Vector2(10, 40));
        speedValue->setFontSize(24);
        speedPanel->addChild(speedValue);
        
        parent->addChild(speedPanel);
    }
    
    void setupCamera()
    {
        CameraSharedPtr camera = Camera::create(getDomain(), "MainCamera");
        camera->setTranslation(Vector3(0, 2, 5));
        camera->setRotation(Vector3(-15, 0, 0));
        camera->setFieldOfView(45.0f);
        camera->setNearPlane(0.1f);
        camera->setFarPlane(1000.0f);
        addChild(camera);
    }
};
```

## Data Binding and Interaction

### Data Binding System
```cpp
// Data source definition
class VehicleDataSource : public DataObject
{
    KZ_METACLASS_BEGIN(VehicleDataSource, DataObject, "VehicleDataSource")
        KZ_METACLASS_PROPERTY_TYPE(SpeedProperty)
        KZ_METACLASS_PROPERTY_TYPE(RPMProperty)
        KZ_METACLASS_PROPERTY_TYPE(FuelLevelProperty)
    KZ_METACLASS_END()
    
public:
    static PropertyType<float> SpeedProperty;
    static PropertyType<float> RPMProperty;
    static PropertyType<float> FuelLevelProperty;
    
    explicit VehicleDataSource(Domain* domain, string_view name)
        : DataObject(domain, name)
    {
        // Simulate data updates
        startDataSimulation();
    }
    
    void updateSpeed(float speed)
    {
        setProperty(SpeedProperty, speed);
    }
    
    void updateRPM(float rpm)
    {
        setProperty(RPMProperty, rpm);
    }
    
    void updateFuelLevel(float level)
    {
        setProperty(FuelLevelProperty, level);
    }
    
private:
    void startDataSimulation()
    {
        // Create timer to simulate data changes
        TimerSharedPtr timer = Timer::create(getDomain());
        timer->setInterval(chrono::milliseconds(100));
        timer->setTimeout([this]() {
            simulateDataUpdate();
        });
        timer->start();
    }
    
    void simulateDataUpdate()
    {
        static float time = 0.0f;
        time += 0.1f;
        
        // Simulate speed changes
        float speed = 60.0f + 30.0f * sin(time * 0.5f);
        updateSpeed(speed);
        
        // Simulate RPM changes
        float rpm = 2000.0f + 1000.0f * sin(time * 0.7f);
        updateRPM(rpm);
        
        // Simulate fuel consumption
        float fuel = 100.0f - time * 0.1f;
        updateFuelLevel(max(0.0f, fuel));
    }
};

// Property definitions
PropertyType<float> VehicleDataSource::SpeedProperty(
    kzMakeFixedString("VehicleDataSource.Speed"), 0.0f);
PropertyType<float> VehicleDataSource::RPMProperty(
    kzMakeFixedString("VehicleDataSource.RPM"), 0.0f);
PropertyType<float> VehicleDataSource::FuelLevelProperty(
    kzMakeFixedString("VehicleDataSource.FuelLevel"), 100.0f);
```

### Touch Interaction
```cpp
// Touch handling
class TouchInteractionHandler : public InputManipulator
{
public:
    explicit TouchInteractionHandler(Domain* domain)
        : InputManipulator(domain)
    {
    }
    
    void attachTo(Node& node) override
    {
        InputManipulator::attachTo(node);
        
        // Register touch events
        node.addMessageHandler(InputTouchBeginMessage::getStaticMessageType(),
            bind(&TouchInteractionHandler::onTouchBegin, this, placeholders::_1));
        node.addMessageHandler(InputTouchMoveMessage::getStaticMessageType(),
            bind(&TouchInteractionHandler::onTouchMove, this, placeholders::_1));
        node.addMessageHandler(InputTouchEndMessage::getStaticMessageType(),
            bind(&TouchInteractionHandler::onTouchEnd, this, placeholders::_1));
    }
    
private:
    void onTouchBegin(InputTouchBeginMessageArguments& messageArguments)
    {
        Vector2 touchPosition = messageArguments.getTouchPoint().position;
        m_lastTouchPosition = touchPosition;
        m_isTouching = true;
        
        // Handle touch start
        handleTouchStart(touchPosition);
    }
    
    void onTouchMove(InputTouchMoveMessageArguments& messageArguments)
    {
        if (!m_isTouching) return;
        
        Vector2 touchPosition = messageArguments.getTouchPoint().position;
        Vector2 delta = touchPosition - m_lastTouchPosition;
        
        // Handle drag
        handleTouchDrag(delta);
        
        m_lastTouchPosition = touchPosition;
    }
    
    void onTouchEnd(InputTouchEndMessageArguments& messageArguments)
    {
        m_isTouching = false;
        
        // Handle touch end
        handleTouchEnd();
    }
    
    void handleTouchStart(const Vector2& position)
    {
        // Implement touch start logic
        Node* attachedNode = getAttachedNode();
        if (attachedNode)
        {
            // Example: highlight
            attachedNode->setProperty(Node::OpacityProperty, 0.8f);
        }
    }
    
    void handleTouchDrag(const Vector2& delta)
    {
        // Implement drag logic
        Node* attachedNode = getAttachedNode();
        if (attachedNode)
        {
            Vector2 currentPos = attachedNode->getTranslation();
            attachedNode->setTranslation(currentPos + delta);
        }
    }
    
    void handleTouchEnd()
    {
        // Implement touch end logic
        Node* attachedNode = getAttachedNode();
        if (attachedNode)
        {
            // Restore normal state
            attachedNode->setProperty(Node::OpacityProperty, 1.0f);
        }
    }
    
private:
    Vector2 m_lastTouchPosition;
    bool m_isTouching = false;
};
```

## Best Practices

### 1. Performance Optimization
```cpp
// Memory pool management
class MemoryPoolManager
{
public:
    template<typename T>
    T* allocate()
    {
        return static_cast<T*>(m_pool.allocate(sizeof(T)));
    }
    
    template<typename T>
    void deallocate(T* ptr)
    {
        m_pool.deallocate(ptr, sizeof(T));
    }
    
private:
    FixedSizePool m_pool;
};

// Object reuse
class UIElementPool
{
public:
    Node2DSharedPtr getButton()
    {
        if (!m_buttonPool.empty())
        {
            Node2DSharedPtr button = m_buttonPool.back();
            m_buttonPool.pop_back();
            return button;
        }
        
        return createNewButton();
    }
    
    void returnButton(Node2DSharedPtr button)
    {
        // Reset state
        button->setVisible(false);
        button->removeFromParent();
        
        m_buttonPool.push_back(button);
    }
    
private:
    vector<Node2DSharedPtr> m_buttonPool;
    
    Node2DSharedPtr createNewButton()
    {
        // Implementation to create new button
        return Button2D::create(getDomain(), "PooledButton");
    }
};
```

### 2. Resource Management
```cpp
// Asynchronous resource loading
class AsyncResourceLoader
{
public:
    void loadResourceAsync(const string& path, function<void(ResourceSharedPtr)> callback)
    {
        // Load resource in background thread
        thread loadThread([this, path, callback]() {
            ResourceSharedPtr resource = loadResourceFromFile(path);
            
            // Call callback in main thread
            m_mainThreadQueue.push([callback, resource]() {
                callback(resource);
            });
        });
        
        loadThread.detach();
    }
    
    void processMainThreadQueue()
    {
        while (!m_mainThreadQueue.empty())
        {
            auto task = m_mainThreadQueue.front();
            m_mainThreadQueue.pop();
            task();
        }
    }
    
private:
    queue<function<void()>> m_mainThreadQueue;
    
    ResourceSharedPtr loadResourceFromFile(const string& path)
    {
        // Actual file loading logic
        return nullptr;
    }
};
```

### 3. State Management
```cpp
// State machine implementation
class UIStateMachine
{
public:
    enum class State
    {
        Idle,
        Navigation,
        MediaPlayer,
        Settings,
        Emergency
    };
    
    void setState(State newState)
    {
        if (m_currentState == newState) return;
        
        // Exit current state
        exitState(m_currentState);
        
        State previousState = m_currentState;
        m_currentState = newState;
        
        // Enter new state
        enterState(m_currentState, previousState);
    }
    
private:
    void enterState(State state, State previousState)
    {
        switch (state)
        {
            case State::Navigation:
                showNavigationUI();
                break;
            case State::MediaPlayer:
                showMediaPlayerUI();
                break;
            case State::Settings:
                showSettingsUI();
                break;
            case State::Emergency:
                showEmergencyUI();
                break;
        }
    }
    
    void exitState(State state)
    {
        switch (state)
        {
            case State::Navigation:
                hideNavigationUI();
                break;
            case State::MediaPlayer:
                hideMediaPlayerUI();
                break;
            case State::Settings:
                hideSettingsUI();
                break;
        }
    }
    
    State m_currentState = State::Idle;
};
```

---

*Kanzi is specifically designed for embedded HMI development and has unique advantages in automotive, industrial, and consumer electronics fields. For more detailed information, please refer to the [Kanzi Official Documentation](https://www.rightware.com/kanzi/documentation/)* 