<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unreal Engine 国际化与本地化指南</title> <!-- The title will be replaced for each page -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/docs.css">
    <link rel="stylesheet" href="../../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html" class="logo-text" data-i18n="nav-logo-text">3D 引擎指南</a>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="../index.html#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="../index.html#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="../index.html#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 文档内容区域 -->
    <div class="doc-layout container">
        <!-- 左侧目录 -->
        <aside class="doc-sidebar card">
            <div class="toc-container">
                <!-- 目录内容将通过JavaScript动态生成 -->
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <section class="doc-section card">
            <div class="doc-container">
                <main class="doc-content">
                    <div lang="zh"><h1 id="unreal-engine-国际化与本地化指南">Unreal Engine
国际化与本地化指南</h1>
<h2 id="目录">目录</h2>
<ol type="1">
<li><a href="#概述">概述</a></li>
<li><a href="#文本本地化系统">文本本地化系统</a></li>
<li><a href="#资源本地化">资源本地化</a></li>
<li><a href="#音频本地化">音频本地化</a></li>
<li><a href="#blueprint-国际化">Blueprint 国际化</a></li>
<li><a href="#c-国际化">C++ 国际化</a></li>
<li><a href="#本地化工具">本地化工具</a></li>
<li><a href="#最佳实践">最佳实践</a></li>
</ol>
<h2 id="概述">概述</h2>
<p>Unreal Engine
提供了完整的国际化（i18n）和本地化（l10n）系统，支持文本、音频、纹理等各种资源的多语言管理。UE
的本地化系统设计用于支持 AAA
级游戏的复杂本地化需求，包括实时语言切换、复杂的文本格式化和大规模翻译管理。</p>
<h3 id="主要特性">主要特性</h3>
<ul>
<li><strong>统一文本系统</strong>：FText 系统支持完整的国际化功能</li>
<li><strong>实时语言切换</strong>：运行时动态切换语言</li>
<li><strong>复杂文本格式化</strong>：支持复数、性别、数字格式化等</li>
<li><strong>翻译编辑器</strong>：内置的翻译管理工具</li>
<li><strong>本地化仪表板</strong>：可视化的本地化进度管理</li>
<li><strong>自动文本收集</strong>：自动收集需要翻译的文本</li>
</ul>
<h2 id="文本本地化系统">文本本地化系统</h2>
<h3 id="ftext-基础使用">FText 基础使用</h3>
<div class="sourceCode" id="cb1"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 基本的 FText 使用</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="pp">#include </span><span class="im">&quot;Internationalization/Text.h&quot;</span></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API AMyActor <span class="op">:</span> <span class="kw">public</span> AActor</span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb1-7"><a href="#cb1-7" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb1-8"><a href="#cb1-8" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb1-9"><a href="#cb1-9" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb1-10"><a href="#cb1-10" aria-hidden="true" tabindex="-1"></a>    AMyActor<span class="op">();</span></span>
<span id="cb1-11"><a href="#cb1-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb1-12"><a href="#cb1-12" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb1-13"><a href="#cb1-13" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 本地化文本属性</span></span>
<span id="cb1-14"><a href="#cb1-14" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditAnywhere<span class="op">,</span> BlueprintReadWrite<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb1-15"><a href="#cb1-15" aria-hidden="true" tabindex="-1"></a>    FText WelcomeMessage<span class="op">;</span></span>
<span id="cb1-16"><a href="#cb1-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb1-17"><a href="#cb1-17" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditAnywhere<span class="op">,</span> BlueprintReadWrite<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb1-18"><a href="#cb1-18" aria-hidden="true" tabindex="-1"></a>    FText PlayerName<span class="op">;</span></span>
<span id="cb1-19"><a href="#cb1-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb1-20"><a href="#cb1-20" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb1-21"><a href="#cb1-21" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取本地化文本</span></span>
<span id="cb1-22"><a href="#cb1-22" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb1-23"><a href="#cb1-23" aria-hidden="true" tabindex="-1"></a>    FText GetLocalizedWelcome<span class="op">();</span></span>
<span id="cb1-24"><a href="#cb1-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb1-25"><a href="#cb1-25" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 格式化文本</span></span>
<span id="cb1-26"><a href="#cb1-26" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb1-27"><a href="#cb1-27" aria-hidden="true" tabindex="-1"></a>    FText GetFormattedMessage<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> PlayerName<span class="op">,</span> int32 Level<span class="op">);</span></span>
<span id="cb1-28"><a href="#cb1-28" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb1-29"><a href="#cb1-29" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-30"><a href="#cb1-30" aria-hidden="true" tabindex="-1"></a><span class="co">// 实现</span></span>
<span id="cb1-31"><a href="#cb1-31" aria-hidden="true" tabindex="-1"></a>AMyActor<span class="op">::</span>AMyActor<span class="op">()</span></span>
<span id="cb1-32"><a href="#cb1-32" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb1-33"><a href="#cb1-33" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 使用 LOCTEXT 宏定义本地化文本</span></span>
<span id="cb1-34"><a href="#cb1-34" aria-hidden="true" tabindex="-1"></a>    WelcomeMessage <span class="op">=</span> LOCTEXT<span class="op">(</span><span class="st">&quot;WelcomeMessage&quot;</span><span class="op">,</span> <span class="st">&quot;Welcome to the game!&quot;</span><span class="op">);</span></span>
<span id="cb1-35"><a href="#cb1-35" aria-hidden="true" tabindex="-1"></a>    PlayerName <span class="op">=</span> LOCTEXT<span class="op">(</span><span class="st">&quot;PlayerName&quot;</span><span class="op">,</span> <span class="st">&quot;Player&quot;</span><span class="op">);</span></span>
<span id="cb1-36"><a href="#cb1-36" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb1-37"><a href="#cb1-37" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-38"><a href="#cb1-38" aria-hidden="true" tabindex="-1"></a>FText AMyActor<span class="op">::</span>GetLocalizedWelcome<span class="op">()</span></span>
<span id="cb1-39"><a href="#cb1-39" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb1-40"><a href="#cb1-40" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> WelcomeMessage<span class="op">;</span></span>
<span id="cb1-41"><a href="#cb1-41" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb1-42"><a href="#cb1-42" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-43"><a href="#cb1-43" aria-hidden="true" tabindex="-1"></a>FText AMyActor<span class="op">::</span>GetFormattedMessage<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> PlayerName<span class="op">,</span> int32 Level<span class="op">)</span></span>
<span id="cb1-44"><a href="#cb1-44" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb1-45"><a href="#cb1-45" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 使用 FText::Format 进行文本格式化</span></span>
<span id="cb1-46"><a href="#cb1-46" aria-hidden="true" tabindex="-1"></a>    FFormatNamedArguments Args<span class="op">;</span></span>
<span id="cb1-47"><a href="#cb1-47" aria-hidden="true" tabindex="-1"></a>    Args<span class="op">.</span>Add<span class="op">(</span>TEXT<span class="op">(</span><span class="st">&quot;PlayerName&quot;</span><span class="op">),</span> FText<span class="op">::</span>FromString<span class="op">(</span>PlayerName<span class="op">));</span></span>
<span id="cb1-48"><a href="#cb1-48" aria-hidden="true" tabindex="-1"></a>    Args<span class="op">.</span>Add<span class="op">(</span>TEXT<span class="op">(</span><span class="st">&quot;Level&quot;</span><span class="op">),</span> FText<span class="op">::</span>AsNumber<span class="op">(</span>Level<span class="op">));</span></span>
<span id="cb1-49"><a href="#cb1-49" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb1-50"><a href="#cb1-50" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> FText<span class="op">::</span>Format<span class="op">(</span></span>
<span id="cb1-51"><a href="#cb1-51" aria-hidden="true" tabindex="-1"></a>        LOCTEXT<span class="op">(</span><span class="st">&quot;FormattedMessage&quot;</span><span class="op">,</span> <span class="st">&quot;Hello {PlayerName}, you are level {Level}!&quot;</span><span class="op">),</span></span>
<span id="cb1-52"><a href="#cb1-52" aria-hidden="true" tabindex="-1"></a>        Args</span>
<span id="cb1-53"><a href="#cb1-53" aria-hidden="true" tabindex="-1"></a>    <span class="op">);</span></span>
<span id="cb1-54"><a href="#cb1-54" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb1-55"><a href="#cb1-55" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-56"><a href="#cb1-56" aria-hidden="true" tabindex="-1"></a><span class="co">// 在 .cpp 文件末尾定义本地化命名空间</span></span>
<span id="cb1-57"><a href="#cb1-57" aria-hidden="true" tabindex="-1"></a><span class="pp">#define LOCTEXT_NAMESPACE </span><span class="st">&quot;MyActor&quot;</span></span>
<span id="cb1-58"><a href="#cb1-58" aria-hidden="true" tabindex="-1"></a><span class="co">// ... 类实现 ...</span></span>
<span id="cb1-59"><a href="#cb1-59" aria-hidden="true" tabindex="-1"></a><span class="pp">#undef LOCTEXT_NAMESPACE</span></span></code></pre></div>
<h3 id="复数形式处理">复数形式处理</h3>
<div class="sourceCode" id="cb2"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 复数形式的文本处理</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API UInventoryWidget <span class="op">:</span> <span class="kw">public</span> UUserWidget</span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb2-7"><a href="#cb2-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb2-8"><a href="#cb2-8" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;UI&quot;</span><span class="op">)</span></span>
<span id="cb2-9"><a href="#cb2-9" aria-hidden="true" tabindex="-1"></a>    FText GetItemCountText<span class="op">(</span>int32 ItemCount<span class="op">);</span></span>
<span id="cb2-10"><a href="#cb2-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb2-11"><a href="#cb2-11" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;UI&quot;</span><span class="op">)</span></span>
<span id="cb2-12"><a href="#cb2-12" aria-hidden="true" tabindex="-1"></a>    FText GetTimeRemainingText<span class="op">(</span><span class="dt">float</span> TimeInSeconds<span class="op">);</span></span>
<span id="cb2-13"><a href="#cb2-13" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb2-14"><a href="#cb2-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-15"><a href="#cb2-15" aria-hidden="true" tabindex="-1"></a>FText UInventoryWidget<span class="op">::</span>GetItemCountText<span class="op">(</span>int32 ItemCount<span class="op">)</span></span>
<span id="cb2-16"><a href="#cb2-16" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb2-17"><a href="#cb2-17" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 使用复数形式</span></span>
<span id="cb2-18"><a href="#cb2-18" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> FText<span class="op">::</span>Format<span class="op">(</span></span>
<span id="cb2-19"><a href="#cb2-19" aria-hidden="true" tabindex="-1"></a>        LOCTEXT<span class="op">(</span><span class="st">&quot;ItemCount&quot;</span><span class="op">,</span> <span class="st">&quot;You have </span><span class="sc">{0}</span><span class="st"> </span><span class="sc">{0}</span><span class="st">|plural(one=item,other=items)&quot;</span><span class="op">),</span></span>
<span id="cb2-20"><a href="#cb2-20" aria-hidden="true" tabindex="-1"></a>        FText<span class="op">::</span>AsNumber<span class="op">(</span>ItemCount<span class="op">)</span></span>
<span id="cb2-21"><a href="#cb2-21" aria-hidden="true" tabindex="-1"></a>    <span class="op">);</span></span>
<span id="cb2-22"><a href="#cb2-22" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb2-23"><a href="#cb2-23" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-24"><a href="#cb2-24" aria-hidden="true" tabindex="-1"></a>FText UInventoryWidget<span class="op">::</span>GetTimeRemainingText<span class="op">(</span><span class="dt">float</span> TimeInSeconds<span class="op">)</span></span>
<span id="cb2-25"><a href="#cb2-25" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb2-26"><a href="#cb2-26" aria-hidden="true" tabindex="-1"></a>    int32 Minutes <span class="op">=</span> FMath<span class="op">::</span>FloorToInt<span class="op">(</span>TimeInSeconds <span class="op">/</span> <span class="fl">60.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb2-27"><a href="#cb2-27" aria-hidden="true" tabindex="-1"></a>    int32 Seconds <span class="op">=</span> FMath<span class="op">::</span>FloorToInt<span class="op">(</span>TimeInSeconds<span class="op">)</span> <span class="op">%</span> <span class="dv">60</span><span class="op">;</span></span>
<span id="cb2-28"><a href="#cb2-28" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb2-29"><a href="#cb2-29" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>Minutes <span class="op">&gt;</span> <span class="dv">0</span><span class="op">)</span></span>
<span id="cb2-30"><a href="#cb2-30" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb2-31"><a href="#cb2-31" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> FText<span class="op">::</span>Format<span class="op">(</span></span>
<span id="cb2-32"><a href="#cb2-32" aria-hidden="true" tabindex="-1"></a>            LOCTEXT<span class="op">(</span><span class="st">&quot;TimeWithMinutes&quot;</span><span class="op">,</span> <span class="st">&quot;</span><span class="sc">{0}</span><span class="st"> </span><span class="sc">{0}</span><span class="st">|plural(one=minute,other=minutes) and </span><span class="sc">{1}</span><span class="st"> </span><span class="sc">{1}</span><span class="st">|plural(one=second,other=seconds) remaining&quot;</span><span class="op">),</span></span>
<span id="cb2-33"><a href="#cb2-33" aria-hidden="true" tabindex="-1"></a>            FText<span class="op">::</span>AsNumber<span class="op">(</span>Minutes<span class="op">),</span></span>
<span id="cb2-34"><a href="#cb2-34" aria-hidden="true" tabindex="-1"></a>            FText<span class="op">::</span>AsNumber<span class="op">(</span>Seconds<span class="op">)</span></span>
<span id="cb2-35"><a href="#cb2-35" aria-hidden="true" tabindex="-1"></a>        <span class="op">);</span></span>
<span id="cb2-36"><a href="#cb2-36" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-37"><a href="#cb2-37" aria-hidden="true" tabindex="-1"></a>    <span class="cf">else</span></span>
<span id="cb2-38"><a href="#cb2-38" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb2-39"><a href="#cb2-39" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> FText<span class="op">::</span>Format<span class="op">(</span></span>
<span id="cb2-40"><a href="#cb2-40" aria-hidden="true" tabindex="-1"></a>            LOCTEXT<span class="op">(</span><span class="st">&quot;TimeSecondsOnly&quot;</span><span class="op">,</span> <span class="st">&quot;</span><span class="sc">{0}</span><span class="st"> </span><span class="sc">{0}</span><span class="st">|plural(one=second,other=seconds) remaining&quot;</span><span class="op">),</span></span>
<span id="cb2-41"><a href="#cb2-41" aria-hidden="true" tabindex="-1"></a>            FText<span class="op">::</span>AsNumber<span class="op">(</span>Seconds<span class="op">)</span></span>
<span id="cb2-42"><a href="#cb2-42" aria-hidden="true" tabindex="-1"></a>        <span class="op">);</span></span>
<span id="cb2-43"><a href="#cb2-43" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-44"><a href="#cb2-44" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="数字和日期格式化">数字和日期格式化</h3>
<div class="sourceCode" id="cb3"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 数字和日期的本地化格式化</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API ULocalizationHelper <span class="op">:</span> <span class="kw">public</span> UBlueprintFunctionLibrary</span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 格式化货币</span></span>
<span id="cb3-9"><a href="#cb3-9" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb3-10"><a href="#cb3-10" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> FText FormatCurrency<span class="op">(</span><span class="dt">float</span> Amount<span class="op">,</span> <span class="at">const</span> FString<span class="op">&amp;</span> CurrencyCode <span class="op">=</span> TEXT<span class="op">(</span><span class="st">&quot;USD&quot;</span><span class="op">));</span></span>
<span id="cb3-11"><a href="#cb3-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb3-12"><a href="#cb3-12" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 格式化百分比</span></span>
<span id="cb3-13"><a href="#cb3-13" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb3-14"><a href="#cb3-14" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> FText FormatPercentage<span class="op">(</span><span class="dt">float</span> Value<span class="op">);</span></span>
<span id="cb3-15"><a href="#cb3-15" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb3-16"><a href="#cb3-16" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 格式化日期</span></span>
<span id="cb3-17"><a href="#cb3-17" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb3-18"><a href="#cb3-18" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> FText FormatDate<span class="op">(</span><span class="at">const</span> FDateTime<span class="op">&amp;</span> DateTime<span class="op">);</span></span>
<span id="cb3-19"><a href="#cb3-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb3-20"><a href="#cb3-20" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 格式化时间</span></span>
<span id="cb3-21"><a href="#cb3-21" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb3-22"><a href="#cb3-22" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> FText FormatTime<span class="op">(</span><span class="at">const</span> FDateTime<span class="op">&amp;</span> DateTime<span class="op">);</span></span>
<span id="cb3-23"><a href="#cb3-23" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb3-24"><a href="#cb3-24" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-25"><a href="#cb3-25" aria-hidden="true" tabindex="-1"></a>FText ULocalizationHelper<span class="op">::</span>FormatCurrency<span class="op">(</span><span class="dt">float</span> Amount<span class="op">,</span> <span class="at">const</span> FString<span class="op">&amp;</span> CurrencyCode<span class="op">)</span></span>
<span id="cb3-26"><a href="#cb3-26" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb3-27"><a href="#cb3-27" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> FText<span class="op">::</span>AsCurrencyBase<span class="op">(</span></span>
<span id="cb3-28"><a href="#cb3-28" aria-hidden="true" tabindex="-1"></a>        FMath<span class="op">::</span>RoundToInt<span class="op">(</span>Amount <span class="op">*</span> <span class="dv">100</span><span class="op">),</span> <span class="co">// 转换为分</span></span>
<span id="cb3-29"><a href="#cb3-29" aria-hidden="true" tabindex="-1"></a>        CurrencyCode</span>
<span id="cb3-30"><a href="#cb3-30" aria-hidden="true" tabindex="-1"></a>    <span class="op">);</span></span>
<span id="cb3-31"><a href="#cb3-31" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb3-32"><a href="#cb3-32" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-33"><a href="#cb3-33" aria-hidden="true" tabindex="-1"></a>FText ULocalizationHelper<span class="op">::</span>FormatPercentage<span class="op">(</span><span class="dt">float</span> Value<span class="op">)</span></span>
<span id="cb3-34"><a href="#cb3-34" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb3-35"><a href="#cb3-35" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> FText<span class="op">::</span>AsPercent<span class="op">(</span>Value<span class="op">);</span></span>
<span id="cb3-36"><a href="#cb3-36" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb3-37"><a href="#cb3-37" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-38"><a href="#cb3-38" aria-hidden="true" tabindex="-1"></a>FText ULocalizationHelper<span class="op">::</span>FormatDate<span class="op">(</span><span class="at">const</span> FDateTime<span class="op">&amp;</span> DateTime<span class="op">)</span></span>
<span id="cb3-39"><a href="#cb3-39" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb3-40"><a href="#cb3-40" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> FText<span class="op">::</span>AsDate<span class="op">(</span>DateTime<span class="op">,</span> EDateTimeStyle<span class="op">::</span>Short<span class="op">);</span></span>
<span id="cb3-41"><a href="#cb3-41" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb3-42"><a href="#cb3-42" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-43"><a href="#cb3-43" aria-hidden="true" tabindex="-1"></a>FText ULocalizationHelper<span class="op">::</span>FormatTime<span class="op">(</span><span class="at">const</span> FDateTime<span class="op">&amp;</span> DateTime<span class="op">)</span></span>
<span id="cb3-44"><a href="#cb3-44" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb3-45"><a href="#cb3-45" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> FText<span class="op">::</span>AsTime<span class="op">(</span>DateTime<span class="op">,</span> EDateTimeStyle<span class="op">::</span>Short<span class="op">);</span></span>
<span id="cb3-46"><a href="#cb3-46" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="资源本地化">资源本地化</h2>
<h3 id="纹理本地化">纹理本地化</h3>
<div class="sourceCode" id="cb4"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 本地化纹理管理</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API ULocalizedTextureManager <span class="op">:</span> <span class="kw">public</span> UObject</span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 本地化纹理映射</span></span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditAnywhere<span class="op">,</span> BlueprintReadWrite<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a>    TMap<span class="op">&lt;</span>FString<span class="op">,</span> TSoftObjectPtr<span class="op">&lt;</span>UTexture2D<span class="op">&gt;&gt;</span> LocalizedTextures<span class="op">;</span></span>
<span id="cb4-11"><a href="#cb4-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-12"><a href="#cb4-12" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取当前语言的纹理</span></span>
<span id="cb4-13"><a href="#cb4-13" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb4-14"><a href="#cb4-14" aria-hidden="true" tabindex="-1"></a>    UTexture2D<span class="op">*</span> GetLocalizedTexture<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> TextureKey<span class="op">);</span></span>
<span id="cb4-15"><a href="#cb4-15" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-16"><a href="#cb4-16" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 预加载本地化纹理</span></span>
<span id="cb4-17"><a href="#cb4-17" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb4-18"><a href="#cb4-18" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> PreloadLocalizedTextures<span class="op">();</span></span>
<span id="cb4-19"><a href="#cb4-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-20"><a href="#cb4-20" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb4-21"><a href="#cb4-21" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 缓存已加载的纹理</span></span>
<span id="cb4-22"><a href="#cb4-22" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">()</span></span>
<span id="cb4-23"><a href="#cb4-23" aria-hidden="true" tabindex="-1"></a>    TMap<span class="op">&lt;</span>FString<span class="op">,</span> UTexture2D<span class="op">*&gt;</span> LoadedTextures<span class="op">;</span></span>
<span id="cb4-24"><a href="#cb4-24" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb4-25"><a href="#cb4-25" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-26"><a href="#cb4-26" aria-hidden="true" tabindex="-1"></a>UTexture2D<span class="op">*</span> ULocalizedTextureManager<span class="op">::</span>GetLocalizedTexture<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> TextureKey<span class="op">)</span></span>
<span id="cb4-27"><a href="#cb4-27" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb4-28"><a href="#cb4-28" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取当前语言代码</span></span>
<span id="cb4-29"><a href="#cb4-29" aria-hidden="true" tabindex="-1"></a>    FString CurrentCulture <span class="op">=</span> FInternationalization<span class="op">::</span>Get<span class="op">().</span>GetCurrentCulture<span class="op">()-&gt;</span>GetName<span class="op">();</span></span>
<span id="cb4-30"><a href="#cb4-30" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-31"><a href="#cb4-31" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 构建本地化键</span></span>
<span id="cb4-32"><a href="#cb4-32" aria-hidden="true" tabindex="-1"></a>    FString LocalizedKey <span class="op">=</span> FString<span class="op">::</span>Printf<span class="op">(</span>TEXT<span class="op">(</span><span class="st">&quot;</span><span class="sc">%s</span><span class="st">_</span><span class="sc">%s</span><span class="st">&quot;</span><span class="op">),</span> <span class="op">*</span>TextureKey<span class="op">,</span> <span class="op">*</span>CurrentCulture<span class="op">);</span></span>
<span id="cb4-33"><a href="#cb4-33" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-34"><a href="#cb4-34" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 检查缓存</span></span>
<span id="cb4-35"><a href="#cb4-35" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>LoadedTextures<span class="op">.</span>Contains<span class="op">(</span>LocalizedKey<span class="op">))</span></span>
<span id="cb4-36"><a href="#cb4-36" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-37"><a href="#cb4-37" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> LoadedTextures<span class="op">[</span>LocalizedKey<span class="op">];</span></span>
<span id="cb4-38"><a href="#cb4-38" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-39"><a href="#cb4-39" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-40"><a href="#cb4-40" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 检查是否有本地化版本</span></span>
<span id="cb4-41"><a href="#cb4-41" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>LocalizedTextures<span class="op">.</span>Contains<span class="op">(</span>LocalizedKey<span class="op">))</span></span>
<span id="cb4-42"><a href="#cb4-42" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-43"><a href="#cb4-43" aria-hidden="true" tabindex="-1"></a>        TSoftObjectPtr<span class="op">&lt;</span>UTexture2D<span class="op">&gt;</span> SoftTexture <span class="op">=</span> LocalizedTextures<span class="op">[</span>LocalizedKey<span class="op">];</span></span>
<span id="cb4-44"><a href="#cb4-44" aria-hidden="true" tabindex="-1"></a>        UTexture2D<span class="op">*</span> Texture <span class="op">=</span> SoftTexture<span class="op">.</span>LoadSynchronous<span class="op">();</span></span>
<span id="cb4-45"><a href="#cb4-45" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb4-46"><a href="#cb4-46" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>Texture<span class="op">)</span></span>
<span id="cb4-47"><a href="#cb4-47" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb4-48"><a href="#cb4-48" aria-hidden="true" tabindex="-1"></a>            LoadedTextures<span class="op">.</span>Add<span class="op">(</span>LocalizedKey<span class="op">,</span> Texture<span class="op">);</span></span>
<span id="cb4-49"><a href="#cb4-49" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> Texture<span class="op">;</span></span>
<span id="cb4-50"><a href="#cb4-50" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb4-51"><a href="#cb4-51" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-52"><a href="#cb4-52" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-53"><a href="#cb4-53" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 回退到默认版本</span></span>
<span id="cb4-54"><a href="#cb4-54" aria-hidden="true" tabindex="-1"></a>    FString DefaultKey <span class="op">=</span> FString<span class="op">::</span>Printf<span class="op">(</span>TEXT<span class="op">(</span><span class="st">&quot;</span><span class="sc">%s</span><span class="st">_en&quot;</span><span class="op">),</span> <span class="op">*</span>TextureKey<span class="op">);</span></span>
<span id="cb4-55"><a href="#cb4-55" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>LocalizedTextures<span class="op">.</span>Contains<span class="op">(</span>DefaultKey<span class="op">))</span></span>
<span id="cb4-56"><a href="#cb4-56" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-57"><a href="#cb4-57" aria-hidden="true" tabindex="-1"></a>        TSoftObjectPtr<span class="op">&lt;</span>UTexture2D<span class="op">&gt;</span> SoftTexture <span class="op">=</span> LocalizedTextures<span class="op">[</span>DefaultKey<span class="op">];</span></span>
<span id="cb4-58"><a href="#cb4-58" aria-hidden="true" tabindex="-1"></a>        UTexture2D<span class="op">*</span> Texture <span class="op">=</span> SoftTexture<span class="op">.</span>LoadSynchronous<span class="op">();</span></span>
<span id="cb4-59"><a href="#cb4-59" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb4-60"><a href="#cb4-60" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>Texture<span class="op">)</span></span>
<span id="cb4-61"><a href="#cb4-61" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb4-62"><a href="#cb4-62" aria-hidden="true" tabindex="-1"></a>            LoadedTextures<span class="op">.</span>Add<span class="op">(</span>LocalizedKey<span class="op">,</span> Texture<span class="op">);</span></span>
<span id="cb4-63"><a href="#cb4-63" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> Texture<span class="op">;</span></span>
<span id="cb4-64"><a href="#cb4-64" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb4-65"><a href="#cb4-65" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-66"><a href="#cb4-66" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-67"><a href="#cb4-67" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> <span class="kw">nullptr</span><span class="op">;</span></span>
<span id="cb4-68"><a href="#cb4-68" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="本地化资源组件">本地化资源组件</h3>
<div class="sourceCode" id="cb5"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 自动本地化的图像组件</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">(</span>ClassGroup<span class="op">=(</span>UI<span class="op">),</span> meta<span class="op">=(</span>BlueprintSpawnableComponent<span class="op">))</span></span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API ULocalizedImageComponent <span class="op">:</span> <span class="kw">public</span> UImage</span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a>    ULocalizedImageComponent<span class="op">();</span></span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-10"><a href="#cb5-10" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb5-11"><a href="#cb5-11" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 本地化纹理键</span></span>
<span id="cb5-12"><a href="#cb5-12" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditAnywhere<span class="op">,</span> BlueprintReadWrite<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb5-13"><a href="#cb5-13" aria-hidden="true" tabindex="-1"></a>    FString TextureKey<span class="op">;</span></span>
<span id="cb5-14"><a href="#cb5-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-15"><a href="#cb5-15" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 纹理管理器引用</span></span>
<span id="cb5-16"><a href="#cb5-16" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditAnywhere<span class="op">,</span> BlueprintReadWrite<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb5-17"><a href="#cb5-17" aria-hidden="true" tabindex="-1"></a>    ULocalizedTextureManager<span class="op">*</span> TextureManager<span class="op">;</span></span>
<span id="cb5-18"><a href="#cb5-18" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-19"><a href="#cb5-19" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> BeginPlay<span class="op">()</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb5-20"><a href="#cb5-20" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-21"><a href="#cb5-21" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 语言变化回调</span></span>
<span id="cb5-22"><a href="#cb5-22" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">()</span></span>
<span id="cb5-23"><a href="#cb5-23" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> OnCultureChanged<span class="op">();</span></span>
<span id="cb5-24"><a href="#cb5-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-25"><a href="#cb5-25" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 更新本地化纹理</span></span>
<span id="cb5-26"><a href="#cb5-26" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb5-27"><a href="#cb5-27" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> UpdateLocalizedTexture<span class="op">();</span></span>
<span id="cb5-28"><a href="#cb5-28" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb5-29"><a href="#cb5-29" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-30"><a href="#cb5-30" aria-hidden="true" tabindex="-1"></a>ULocalizedImageComponent<span class="op">::</span>ULocalizedImageComponent<span class="op">()</span></span>
<span id="cb5-31"><a href="#cb5-31" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb5-32"><a href="#cb5-32" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 绑定语言变化事件</span></span>
<span id="cb5-33"><a href="#cb5-33" aria-hidden="true" tabindex="-1"></a>    FInternationalization<span class="op">::</span>Get<span class="op">().</span>OnCultureChanged<span class="op">().</span>AddUObject<span class="op">(</span><span class="kw">this</span><span class="op">,</span> <span class="op">&amp;</span>ULocalizedImageComponent<span class="op">::</span>OnCultureChanged<span class="op">);</span></span>
<span id="cb5-34"><a href="#cb5-34" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb5-35"><a href="#cb5-35" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-36"><a href="#cb5-36" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> ULocalizedImageComponent<span class="op">::</span>BeginPlay<span class="op">()</span></span>
<span id="cb5-37"><a href="#cb5-37" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb5-38"><a href="#cb5-38" aria-hidden="true" tabindex="-1"></a>    Super<span class="op">::</span>BeginPlay<span class="op">();</span></span>
<span id="cb5-39"><a href="#cb5-39" aria-hidden="true" tabindex="-1"></a>    UpdateLocalizedTexture<span class="op">();</span></span>
<span id="cb5-40"><a href="#cb5-40" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb5-41"><a href="#cb5-41" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-42"><a href="#cb5-42" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> ULocalizedImageComponent<span class="op">::</span>OnCultureChanged<span class="op">()</span></span>
<span id="cb5-43"><a href="#cb5-43" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb5-44"><a href="#cb5-44" aria-hidden="true" tabindex="-1"></a>    UpdateLocalizedTexture<span class="op">();</span></span>
<span id="cb5-45"><a href="#cb5-45" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb5-46"><a href="#cb5-46" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-47"><a href="#cb5-47" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> ULocalizedImageComponent<span class="op">::</span>UpdateLocalizedTexture<span class="op">()</span></span>
<span id="cb5-48"><a href="#cb5-48" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb5-49"><a href="#cb5-49" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>TextureManager <span class="op">&amp;&amp;</span> <span class="op">!</span>TextureKey<span class="op">.</span>IsEmpty<span class="op">())</span></span>
<span id="cb5-50"><a href="#cb5-50" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb5-51"><a href="#cb5-51" aria-hidden="true" tabindex="-1"></a>        UTexture2D<span class="op">*</span> LocalizedTexture <span class="op">=</span> TextureManager<span class="op">-&gt;</span>GetLocalizedTexture<span class="op">(</span>TextureKey<span class="op">);</span></span>
<span id="cb5-52"><a href="#cb5-52" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>LocalizedTexture<span class="op">)</span></span>
<span id="cb5-53"><a href="#cb5-53" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb5-54"><a href="#cb5-54" aria-hidden="true" tabindex="-1"></a>            SetBrushFromTexture<span class="op">(</span>LocalizedTexture<span class="op">);</span></span>
<span id="cb5-55"><a href="#cb5-55" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb5-56"><a href="#cb5-56" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-57"><a href="#cb5-57" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="音频本地化">音频本地化</h2>
<h3 id="本地化音频系统">本地化音频系统</h3>
<div class="sourceCode" id="cb6"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 本地化音频管理器</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API ULocalizedAudioManager <span class="op">:</span> <span class="kw">public</span> UGameInstanceSubsystem</span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb6-5"><a href="#cb6-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb6-6"><a href="#cb6-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-7"><a href="#cb6-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb6-8"><a href="#cb6-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 本地化音频映射</span></span>
<span id="cb6-9"><a href="#cb6-9" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditAnywhere<span class="op">,</span> BlueprintReadWrite<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb6-10"><a href="#cb6-10" aria-hidden="true" tabindex="-1"></a>    TMap<span class="op">&lt;</span>FString<span class="op">,</span> TSoftObjectPtr<span class="op">&lt;</span>USoundBase<span class="op">&gt;&gt;</span> LocalizedAudioClips<span class="op">;</span></span>
<span id="cb6-11"><a href="#cb6-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-12"><a href="#cb6-12" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 播放本地化音频</span></span>
<span id="cb6-13"><a href="#cb6-13" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Audio&quot;</span><span class="op">)</span></span>
<span id="cb6-14"><a href="#cb6-14" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> PlayLocalizedAudio<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> AudioKey<span class="op">,</span> AActor<span class="op">*</span> Owner <span class="op">=</span> <span class="kw">nullptr</span><span class="op">);</span></span>
<span id="cb6-15"><a href="#cb6-15" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-16"><a href="#cb6-16" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取本地化音频剪辑</span></span>
<span id="cb6-17"><a href="#cb6-17" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Audio&quot;</span><span class="op">)</span></span>
<span id="cb6-18"><a href="#cb6-18" aria-hidden="true" tabindex="-1"></a>    USoundBase<span class="op">*</span> GetLocalizedAudioClip<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> AudioKey<span class="op">);</span></span>
<span id="cb6-19"><a href="#cb6-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-20"><a href="#cb6-20" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 预加载语音包</span></span>
<span id="cb6-21"><a href="#cb6-21" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Audio&quot;</span><span class="op">)</span></span>
<span id="cb6-22"><a href="#cb6-22" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> PreloadVoicePackage<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> LanguageCode<span class="op">);</span></span>
<span id="cb6-23"><a href="#cb6-23" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-24"><a href="#cb6-24" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb6-25"><a href="#cb6-25" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> Initialize<span class="op">(</span>FSubsystemCollectionBase<span class="op">&amp;</span> Collection<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb6-26"><a href="#cb6-26" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-27"><a href="#cb6-27" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb6-28"><a href="#cb6-28" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 音频缓存</span></span>
<span id="cb6-29"><a href="#cb6-29" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">()</span></span>
<span id="cb6-30"><a href="#cb6-30" aria-hidden="true" tabindex="-1"></a>    TMap<span class="op">&lt;</span>FString<span class="op">,</span> USoundBase<span class="op">*&gt;</span> AudioCache<span class="op">;</span></span>
<span id="cb6-31"><a href="#cb6-31" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-32"><a href="#cb6-32" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 当前加载的语音包</span></span>
<span id="cb6-33"><a href="#cb6-33" aria-hidden="true" tabindex="-1"></a>    FString CurrentVoicePackage<span class="op">;</span></span>
<span id="cb6-34"><a href="#cb6-34" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb6-35"><a href="#cb6-35" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-36"><a href="#cb6-36" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> ULocalizedAudioManager<span class="op">::</span>Initialize<span class="op">(</span>FSubsystemCollectionBase<span class="op">&amp;</span> Collection<span class="op">)</span></span>
<span id="cb6-37"><a href="#cb6-37" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb6-38"><a href="#cb6-38" aria-hidden="true" tabindex="-1"></a>    Super<span class="op">::</span>Initialize<span class="op">(</span>Collection<span class="op">);</span></span>
<span id="cb6-39"><a href="#cb6-39" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-40"><a href="#cb6-40" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 监听语言变化</span></span>
<span id="cb6-41"><a href="#cb6-41" aria-hidden="true" tabindex="-1"></a>    FInternationalization<span class="op">::</span>Get<span class="op">().</span>OnCultureChanged<span class="op">().</span>AddUObject<span class="op">(</span><span class="kw">this</span><span class="op">,</span> <span class="op">&amp;</span>ULocalizedAudioManager<span class="op">::</span>OnCultureChanged<span class="op">);</span></span>
<span id="cb6-42"><a href="#cb6-42" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb6-43"><a href="#cb6-43" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-44"><a href="#cb6-44" aria-hidden="true" tabindex="-1"></a>USoundBase<span class="op">*</span> ULocalizedAudioManager<span class="op">::</span>GetLocalizedAudioClip<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> AudioKey<span class="op">)</span></span>
<span id="cb6-45"><a href="#cb6-45" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb6-46"><a href="#cb6-46" aria-hidden="true" tabindex="-1"></a>    FString CurrentCulture <span class="op">=</span> FInternationalization<span class="op">::</span>Get<span class="op">().</span>GetCurrentCulture<span class="op">()-&gt;</span>GetName<span class="op">();</span></span>
<span id="cb6-47"><a href="#cb6-47" aria-hidden="true" tabindex="-1"></a>    FString LocalizedKey <span class="op">=</span> FString<span class="op">::</span>Printf<span class="op">(</span>TEXT<span class="op">(</span><span class="st">&quot;</span><span class="sc">%s</span><span class="st">_</span><span class="sc">%s</span><span class="st">&quot;</span><span class="op">),</span> <span class="op">*</span>AudioKey<span class="op">,</span> <span class="op">*</span>CurrentCulture<span class="op">);</span></span>
<span id="cb6-48"><a href="#cb6-48" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-49"><a href="#cb6-49" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 检查缓存</span></span>
<span id="cb6-50"><a href="#cb6-50" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>AudioCache<span class="op">.</span>Contains<span class="op">(</span>LocalizedKey<span class="op">))</span></span>
<span id="cb6-51"><a href="#cb6-51" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-52"><a href="#cb6-52" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> AudioCache<span class="op">[</span>LocalizedKey<span class="op">];</span></span>
<span id="cb6-53"><a href="#cb6-53" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-54"><a href="#cb6-54" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-55"><a href="#cb6-55" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 加载本地化音频</span></span>
<span id="cb6-56"><a href="#cb6-56" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>LocalizedAudioClips<span class="op">.</span>Contains<span class="op">(</span>LocalizedKey<span class="op">))</span></span>
<span id="cb6-57"><a href="#cb6-57" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-58"><a href="#cb6-58" aria-hidden="true" tabindex="-1"></a>        TSoftObjectPtr<span class="op">&lt;</span>USoundBase<span class="op">&gt;</span> SoftAudio <span class="op">=</span> LocalizedAudioClips<span class="op">[</span>LocalizedKey<span class="op">];</span></span>
<span id="cb6-59"><a href="#cb6-59" aria-hidden="true" tabindex="-1"></a>        USoundBase<span class="op">*</span> AudioClip <span class="op">=</span> SoftAudio<span class="op">.</span>LoadSynchronous<span class="op">();</span></span>
<span id="cb6-60"><a href="#cb6-60" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb6-61"><a href="#cb6-61" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>AudioClip<span class="op">)</span></span>
<span id="cb6-62"><a href="#cb6-62" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb6-63"><a href="#cb6-63" aria-hidden="true" tabindex="-1"></a>            AudioCache<span class="op">.</span>Add<span class="op">(</span>LocalizedKey<span class="op">,</span> AudioClip<span class="op">);</span></span>
<span id="cb6-64"><a href="#cb6-64" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> AudioClip<span class="op">;</span></span>
<span id="cb6-65"><a href="#cb6-65" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb6-66"><a href="#cb6-66" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-67"><a href="#cb6-67" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-68"><a href="#cb6-68" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 回退到英文版本</span></span>
<span id="cb6-69"><a href="#cb6-69" aria-hidden="true" tabindex="-1"></a>    FString DefaultKey <span class="op">=</span> FString<span class="op">::</span>Printf<span class="op">(</span>TEXT<span class="op">(</span><span class="st">&quot;</span><span class="sc">%s</span><span class="st">_en&quot;</span><span class="op">),</span> <span class="op">*</span>AudioKey<span class="op">);</span></span>
<span id="cb6-70"><a href="#cb6-70" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>LocalizedAudioClips<span class="op">.</span>Contains<span class="op">(</span>DefaultKey<span class="op">))</span></span>
<span id="cb6-71"><a href="#cb6-71" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-72"><a href="#cb6-72" aria-hidden="true" tabindex="-1"></a>        TSoftObjectPtr<span class="op">&lt;</span>USoundBase<span class="op">&gt;</span> SoftAudio <span class="op">=</span> LocalizedAudioClips<span class="op">[</span>DefaultKey<span class="op">];</span></span>
<span id="cb6-73"><a href="#cb6-73" aria-hidden="true" tabindex="-1"></a>        USoundBase<span class="op">*</span> AudioClip <span class="op">=</span> SoftAudio<span class="op">.</span>LoadSynchronous<span class="op">();</span></span>
<span id="cb6-74"><a href="#cb6-74" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb6-75"><a href="#cb6-75" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>AudioClip<span class="op">)</span></span>
<span id="cb6-76"><a href="#cb6-76" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb6-77"><a href="#cb6-77" aria-hidden="true" tabindex="-1"></a>            AudioCache<span class="op">.</span>Add<span class="op">(</span>LocalizedKey<span class="op">,</span> AudioClip<span class="op">);</span></span>
<span id="cb6-78"><a href="#cb6-78" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> AudioClip<span class="op">;</span></span>
<span id="cb6-79"><a href="#cb6-79" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb6-80"><a href="#cb6-80" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-81"><a href="#cb6-81" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-82"><a href="#cb6-82" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> <span class="kw">nullptr</span><span class="op">;</span></span>
<span id="cb6-83"><a href="#cb6-83" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb6-84"><a href="#cb6-84" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-85"><a href="#cb6-85" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> ULocalizedAudioManager<span class="op">::</span>PlayLocalizedAudio<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> AudioKey<span class="op">,</span> AActor<span class="op">*</span> Owner<span class="op">)</span></span>
<span id="cb6-86"><a href="#cb6-86" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb6-87"><a href="#cb6-87" aria-hidden="true" tabindex="-1"></a>    USoundBase<span class="op">*</span> AudioClip <span class="op">=</span> GetLocalizedAudioClip<span class="op">(</span>AudioKey<span class="op">);</span></span>
<span id="cb6-88"><a href="#cb6-88" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>AudioClip<span class="op">)</span></span>
<span id="cb6-89"><a href="#cb6-89" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-90"><a href="#cb6-90" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>Owner<span class="op">)</span></span>
<span id="cb6-91"><a href="#cb6-91" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb6-92"><a href="#cb6-92" aria-hidden="true" tabindex="-1"></a>            UGameplayStatics<span class="op">::</span>PlaySoundAtLocation<span class="op">(</span><span class="kw">this</span><span class="op">,</span> AudioClip<span class="op">,</span> Owner<span class="op">-&gt;</span>GetActorLocation<span class="op">());</span></span>
<span id="cb6-93"><a href="#cb6-93" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb6-94"><a href="#cb6-94" aria-hidden="true" tabindex="-1"></a>        <span class="cf">else</span></span>
<span id="cb6-95"><a href="#cb6-95" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb6-96"><a href="#cb6-96" aria-hidden="true" tabindex="-1"></a>            UGameplayStatics<span class="op">::</span>PlaySound2D<span class="op">(</span><span class="kw">this</span><span class="op">,</span> AudioClip<span class="op">);</span></span>
<span id="cb6-97"><a href="#cb6-97" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb6-98"><a href="#cb6-98" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-99"><a href="#cb6-99" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="blueprint-国际化">Blueprint 国际化</h2>
<h3 id="blueprint-文本节点">Blueprint 文本节点</h3>
<div class="sourceCode" id="cb7"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 自定义 Blueprint 节点用于本地化</span></span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb7-3"><a href="#cb7-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API ULocalizationBlueprintLibrary <span class="op">:</span> <span class="kw">public</span> UBlueprintFunctionLibrary</span>
<span id="cb7-4"><a href="#cb7-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb7-5"><a href="#cb7-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb7-6"><a href="#cb7-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-7"><a href="#cb7-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb7-8"><a href="#cb7-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取本地化文本</span></span>
<span id="cb7-9"><a href="#cb7-9" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">,</span> </span>
<span id="cb7-10"><a href="#cb7-10" aria-hidden="true" tabindex="-1"></a>              meta <span class="op">=</span> <span class="op">(</span>CallInEditor <span class="op">=</span> <span class="st">&quot;true&quot;</span><span class="op">))</span></span>
<span id="cb7-11"><a href="#cb7-11" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> FText GetLocalizedText<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> Namespace<span class="op">,</span> <span class="at">const</span> FString<span class="op">&amp;</span> Key<span class="op">,</span> <span class="at">const</span> FString<span class="op">&amp;</span> SourceString<span class="op">);</span></span>
<span id="cb7-12"><a href="#cb7-12" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-13"><a href="#cb7-13" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 格式化本地化文本</span></span>
<span id="cb7-14"><a href="#cb7-14" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb7-15"><a href="#cb7-15" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> FText FormatLocalizedText<span class="op">(</span><span class="at">const</span> FText<span class="op">&amp;</span> Pattern<span class="op">,</span> <span class="at">const</span> TArray<span class="op">&lt;</span>FText<span class="op">&gt;&amp;</span> Arguments<span class="op">);</span></span>
<span id="cb7-16"><a href="#cb7-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-17"><a href="#cb7-17" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取当前语言</span></span>
<span id="cb7-18"><a href="#cb7-18" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb7-19"><a href="#cb7-19" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> FString GetCurrentLanguage<span class="op">();</span></span>
<span id="cb7-20"><a href="#cb7-20" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-21"><a href="#cb7-21" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 设置语言</span></span>
<span id="cb7-22"><a href="#cb7-22" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb7-23"><a href="#cb7-23" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> <span class="dt">bool</span> SetLanguage<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> LanguageCode<span class="op">);</span></span>
<span id="cb7-24"><a href="#cb7-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-25"><a href="#cb7-25" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取可用语言列表</span></span>
<span id="cb7-26"><a href="#cb7-26" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb7-27"><a href="#cb7-27" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> TArray<span class="op">&lt;</span>FString<span class="op">&gt;</span> GetAvailableLanguages<span class="op">();</span></span>
<span id="cb7-28"><a href="#cb7-28" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb7-29"><a href="#cb7-29" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-30"><a href="#cb7-30" aria-hidden="true" tabindex="-1"></a>FText ULocalizationBlueprintLibrary<span class="op">::</span>GetLocalizedText<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> Namespace<span class="op">,</span> <span class="at">const</span> FString<span class="op">&amp;</span> Key<span class="op">,</span> <span class="at">const</span> FString<span class="op">&amp;</span> SourceString<span class="op">)</span></span>
<span id="cb7-31"><a href="#cb7-31" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb7-32"><a href="#cb7-32" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> FText<span class="op">::</span>FromStringTable<span class="op">(</span>FName<span class="op">(*</span>Namespace<span class="op">),</span> FName<span class="op">(*</span>Key<span class="op">),</span> SourceString<span class="op">);</span></span>
<span id="cb7-33"><a href="#cb7-33" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb7-34"><a href="#cb7-34" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-35"><a href="#cb7-35" aria-hidden="true" tabindex="-1"></a>FText ULocalizationBlueprintLibrary<span class="op">::</span>FormatLocalizedText<span class="op">(</span><span class="at">const</span> FText<span class="op">&amp;</span> Pattern<span class="op">,</span> <span class="at">const</span> TArray<span class="op">&lt;</span>FText<span class="op">&gt;&amp;</span> Arguments<span class="op">)</span></span>
<span id="cb7-36"><a href="#cb7-36" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb7-37"><a href="#cb7-37" aria-hidden="true" tabindex="-1"></a>    FFormatOrderedArguments Args<span class="op">;</span></span>
<span id="cb7-38"><a href="#cb7-38" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> FText<span class="op">&amp;</span> Arg <span class="op">:</span> Arguments<span class="op">)</span></span>
<span id="cb7-39"><a href="#cb7-39" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-40"><a href="#cb7-40" aria-hidden="true" tabindex="-1"></a>        Args<span class="op">.</span>Add<span class="op">(</span>Arg<span class="op">);</span></span>
<span id="cb7-41"><a href="#cb7-41" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-42"><a href="#cb7-42" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-43"><a href="#cb7-43" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> FText<span class="op">::</span>Format<span class="op">(</span>Pattern<span class="op">,</span> Args<span class="op">);</span></span>
<span id="cb7-44"><a href="#cb7-44" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb7-45"><a href="#cb7-45" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-46"><a href="#cb7-46" aria-hidden="true" tabindex="-1"></a>FString ULocalizationBlueprintLibrary<span class="op">::</span>GetCurrentLanguage<span class="op">()</span></span>
<span id="cb7-47"><a href="#cb7-47" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb7-48"><a href="#cb7-48" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> FInternationalization<span class="op">::</span>Get<span class="op">().</span>GetCurrentCulture<span class="op">()-&gt;</span>GetName<span class="op">();</span></span>
<span id="cb7-49"><a href="#cb7-49" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb7-50"><a href="#cb7-50" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-51"><a href="#cb7-51" aria-hidden="true" tabindex="-1"></a><span class="dt">bool</span> ULocalizationBlueprintLibrary<span class="op">::</span>SetLanguage<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> LanguageCode<span class="op">)</span></span>
<span id="cb7-52"><a href="#cb7-52" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb7-53"><a href="#cb7-53" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> FInternationalization<span class="op">::</span>Get<span class="op">().</span>SetCurrentCulture<span class="op">(</span>LanguageCode<span class="op">);</span></span>
<span id="cb7-54"><a href="#cb7-54" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb7-55"><a href="#cb7-55" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-56"><a href="#cb7-56" aria-hidden="true" tabindex="-1"></a>TArray<span class="op">&lt;</span>FString<span class="op">&gt;</span> ULocalizationBlueprintLibrary<span class="op">::</span>GetAvailableLanguages<span class="op">()</span></span>
<span id="cb7-57"><a href="#cb7-57" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb7-58"><a href="#cb7-58" aria-hidden="true" tabindex="-1"></a>    TArray<span class="op">&lt;</span>FString<span class="op">&gt;</span> Languages<span class="op">;</span></span>
<span id="cb7-59"><a href="#cb7-59" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-60"><a href="#cb7-60" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取可用的文化列表</span></span>
<span id="cb7-61"><a href="#cb7-61" aria-hidden="true" tabindex="-1"></a>    TArray<span class="op">&lt;</span>FString<span class="op">&gt;</span> CultureNames<span class="op">;</span></span>
<span id="cb7-62"><a href="#cb7-62" aria-hidden="true" tabindex="-1"></a>    FInternationalization<span class="op">::</span>Get<span class="op">().</span>GetCultureNames<span class="op">(</span>CultureNames<span class="op">);</span></span>
<span id="cb7-63"><a href="#cb7-63" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-64"><a href="#cb7-64" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> CultureName <span class="op">:</span> CultureNames<span class="op">)</span></span>
<span id="cb7-65"><a href="#cb7-65" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-66"><a href="#cb7-66" aria-hidden="true" tabindex="-1"></a>        Languages<span class="op">.</span>Add<span class="op">(</span>CultureName<span class="op">);</span></span>
<span id="cb7-67"><a href="#cb7-67" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-68"><a href="#cb7-68" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-69"><a href="#cb7-69" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> Languages<span class="op">;</span></span>
<span id="cb7-70"><a href="#cb7-70" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="本地化工具">本地化工具</h2>
<h3 id="自动文本收集">自动文本收集</h3>
<div class="sourceCode" id="cb8"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 自定义文本收集器</span></span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API UCustomTextCollector <span class="op">:</span> <span class="kw">public</span> UObject</span>
<span id="cb8-4"><a href="#cb8-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb8-5"><a href="#cb8-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb8-6"><a href="#cb8-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-7"><a href="#cb8-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb8-8"><a href="#cb8-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 收集项目中的所有文本</span></span>
<span id="cb8-9"><a href="#cb8-9" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>CallInEditor <span class="op">=</span> <span class="kw">true</span><span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb8-10"><a href="#cb8-10" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> CollectProjectTexts<span class="op">();</span></span>
<span id="cb8-11"><a href="#cb8-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-12"><a href="#cb8-12" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 导出文本到 CSV</span></span>
<span id="cb8-13"><a href="#cb8-13" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>CallInEditor <span class="op">=</span> <span class="kw">true</span><span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb8-14"><a href="#cb8-14" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ExportTextsToCSV<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> FilePath<span class="op">);</span></span>
<span id="cb8-15"><a href="#cb8-15" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-16"><a href="#cb8-16" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 从 CSV 导入翻译</span></span>
<span id="cb8-17"><a href="#cb8-17" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>CallInEditor <span class="op">=</span> <span class="kw">true</span><span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb8-18"><a href="#cb8-18" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ImportTranslationsFromCSV<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> FilePath<span class="op">);</span></span>
<span id="cb8-19"><a href="#cb8-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-20"><a href="#cb8-20" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb8-21"><a href="#cb8-21" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 收集的文本条目</span></span>
<span id="cb8-22"><a href="#cb8-22" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">()</span></span>
<span id="cb8-23"><a href="#cb8-23" aria-hidden="true" tabindex="-1"></a>    TArray<span class="op">&lt;</span>FTextCollectionEntry<span class="op">&gt;</span> CollectedTexts<span class="op">;</span></span>
<span id="cb8-24"><a href="#cb8-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-25"><a href="#cb8-25" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 扫描 Blueprint 资源</span></span>
<span id="cb8-26"><a href="#cb8-26" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ScanBlueprintAssets<span class="op">();</span></span>
<span id="cb8-27"><a href="#cb8-27" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-28"><a href="#cb8-28" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 扫描 C++ 源文件</span></span>
<span id="cb8-29"><a href="#cb8-29" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ScanCppSourceFiles<span class="op">();</span></span>
<span id="cb8-30"><a href="#cb8-30" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-31"><a href="#cb8-31" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 扫描 UMG 资源</span></span>
<span id="cb8-32"><a href="#cb8-32" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ScanUMGAssets<span class="op">();</span></span>
<span id="cb8-33"><a href="#cb8-33" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb8-34"><a href="#cb8-34" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-35"><a href="#cb8-35" aria-hidden="true" tabindex="-1"></a>USTRUCT<span class="op">()</span></span>
<span id="cb8-36"><a href="#cb8-36" aria-hidden="true" tabindex="-1"></a><span class="kw">struct</span> FTextCollectionEntry</span>
<span id="cb8-37"><a href="#cb8-37" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb8-38"><a href="#cb8-38" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb8-39"><a href="#cb8-39" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-40"><a href="#cb8-40" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">()</span></span>
<span id="cb8-41"><a href="#cb8-41" aria-hidden="true" tabindex="-1"></a>    FString Namespace<span class="op">;</span></span>
<span id="cb8-42"><a href="#cb8-42" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-43"><a href="#cb8-43" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">()</span></span>
<span id="cb8-44"><a href="#cb8-44" aria-hidden="true" tabindex="-1"></a>    FString Key<span class="op">;</span></span>
<span id="cb8-45"><a href="#cb8-45" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-46"><a href="#cb8-46" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">()</span></span>
<span id="cb8-47"><a href="#cb8-47" aria-hidden="true" tabindex="-1"></a>    FString SourceString<span class="op">;</span></span>
<span id="cb8-48"><a href="#cb8-48" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-49"><a href="#cb8-49" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">()</span></span>
<span id="cb8-50"><a href="#cb8-50" aria-hidden="true" tabindex="-1"></a>    FString AssetPath<span class="op">;</span></span>
<span id="cb8-51"><a href="#cb8-51" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-52"><a href="#cb8-52" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">()</span></span>
<span id="cb8-53"><a href="#cb8-53" aria-hidden="true" tabindex="-1"></a>    TMap<span class="op">&lt;</span>FString<span class="op">,</span> FString<span class="op">&gt;</span> Translations<span class="op">;</span></span>
<span id="cb8-54"><a href="#cb8-54" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="翻译验证工具">翻译验证工具</h3>
<div class="sourceCode" id="cb9"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 翻译质量验证</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API UTranslationValidator <span class="op">:</span> <span class="kw">public</span> UObject</span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb9-8"><a href="#cb9-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 验证翻译完整性</span></span>
<span id="cb9-9"><a href="#cb9-9" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>CallInEditor <span class="op">=</span> <span class="kw">true</span><span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb9-10"><a href="#cb9-10" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ValidateTranslationCompleteness<span class="op">();</span></span>
<span id="cb9-11"><a href="#cb9-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-12"><a href="#cb9-12" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 检查文本长度</span></span>
<span id="cb9-13"><a href="#cb9-13" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>CallInEditor <span class="op">=</span> <span class="kw">true</span><span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb9-14"><a href="#cb9-14" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> CheckTextLength<span class="op">();</span></span>
<span id="cb9-15"><a href="#cb9-15" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-16"><a href="#cb9-16" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 验证格式化参数</span></span>
<span id="cb9-17"><a href="#cb9-17" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>CallInEditor <span class="op">=</span> <span class="kw">true</span><span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb9-18"><a href="#cb9-18" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ValidateFormatArguments<span class="op">();</span></span>
<span id="cb9-19"><a href="#cb9-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-20"><a href="#cb9-20" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 生成验证报告</span></span>
<span id="cb9-21"><a href="#cb9-21" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>CallInEditor <span class="op">=</span> <span class="kw">true</span><span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization&quot;</span><span class="op">)</span></span>
<span id="cb9-22"><a href="#cb9-22" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> GenerateValidationReport<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> OutputPath<span class="op">);</span></span>
<span id="cb9-23"><a href="#cb9-23" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-24"><a href="#cb9-24" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb9-25"><a href="#cb9-25" aria-hidden="true" tabindex="-1"></a>    USTRUCT<span class="op">()</span></span>
<span id="cb9-26"><a href="#cb9-26" aria-hidden="true" tabindex="-1"></a>    <span class="kw">struct</span> FValidationIssue</span>
<span id="cb9-27"><a href="#cb9-27" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-28"><a href="#cb9-28" aria-hidden="true" tabindex="-1"></a>        GENERATED_BODY<span class="op">()</span></span>
<span id="cb9-29"><a href="#cb9-29" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb9-30"><a href="#cb9-30" aria-hidden="true" tabindex="-1"></a>        FString IssueType<span class="op">;</span></span>
<span id="cb9-31"><a href="#cb9-31" aria-hidden="true" tabindex="-1"></a>        FString Namespace<span class="op">;</span></span>
<span id="cb9-32"><a href="#cb9-32" aria-hidden="true" tabindex="-1"></a>        FString Key<span class="op">;</span></span>
<span id="cb9-33"><a href="#cb9-33" aria-hidden="true" tabindex="-1"></a>        FString Language<span class="op">;</span></span>
<span id="cb9-34"><a href="#cb9-34" aria-hidden="true" tabindex="-1"></a>        FString Description<span class="op">;</span></span>
<span id="cb9-35"><a href="#cb9-35" aria-hidden="true" tabindex="-1"></a>    <span class="op">};</span></span>
<span id="cb9-36"><a href="#cb9-36" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-37"><a href="#cb9-37" aria-hidden="true" tabindex="-1"></a>    TArray<span class="op">&lt;</span>FValidationIssue<span class="op">&gt;</span> ValidationIssues<span class="op">;</span></span>
<span id="cb9-38"><a href="#cb9-38" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-39"><a href="#cb9-39" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 检查单个翻译条目</span></span>
<span id="cb9-40"><a href="#cb9-40" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ValidateTranslationEntry<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> Namespace<span class="op">,</span> <span class="at">const</span> FString<span class="op">&amp;</span> Key<span class="op">,</span> </span>
<span id="cb9-41"><a href="#cb9-41" aria-hidden="true" tabindex="-1"></a>                                 <span class="at">const</span> FString<span class="op">&amp;</span> SourceText<span class="op">,</span> <span class="at">const</span> FString<span class="op">&amp;</span> TranslatedText<span class="op">,</span> </span>
<span id="cb9-42"><a href="#cb9-42" aria-hidden="true" tabindex="-1"></a>                                 <span class="at">const</span> FString<span class="op">&amp;</span> Language<span class="op">);</span></span>
<span id="cb9-43"><a href="#cb9-43" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="最佳实践">最佳实践</h2>
<h3 id="文本组织">1. 文本组织</h3>
<div class="sourceCode" id="cb10"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 使用命名空间组织文本</span></span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a><span class="pp">#define LOCTEXT_NAMESPACE </span><span class="st">&quot;GameUI&quot;</span></span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> UGameUIWidget <span class="op">:</span> <span class="kw">public</span> UUserWidget</span>
<span id="cb10-5"><a href="#cb10-5" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb10-6"><a href="#cb10-6" aria-hidden="true" tabindex="-1"></a>    <span class="co">// UI 相关文本</span></span>
<span id="cb10-7"><a href="#cb10-7" aria-hidden="true" tabindex="-1"></a>    FText MenuTitle <span class="op">=</span> LOCTEXT<span class="op">(</span><span class="st">&quot;MenuTitle&quot;</span><span class="op">,</span> <span class="st">&quot;Main Menu&quot;</span><span class="op">);</span></span>
<span id="cb10-8"><a href="#cb10-8" aria-hidden="true" tabindex="-1"></a>    FText StartButton <span class="op">=</span> LOCTEXT<span class="op">(</span><span class="st">&quot;StartButton&quot;</span><span class="op">,</span> <span class="st">&quot;Start Game&quot;</span><span class="op">);</span></span>
<span id="cb10-9"><a href="#cb10-9" aria-hidden="true" tabindex="-1"></a>    FText SettingsButton <span class="op">=</span> LOCTEXT<span class="op">(</span><span class="st">&quot;SettingsButton&quot;</span><span class="op">,</span> <span class="st">&quot;Settings&quot;</span><span class="op">);</span></span>
<span id="cb10-10"><a href="#cb10-10" aria-hidden="true" tabindex="-1"></a>    FText ExitButton <span class="op">=</span> LOCTEXT<span class="op">(</span><span class="st">&quot;ExitButton&quot;</span><span class="op">,</span> <span class="st">&quot;Exit&quot;</span><span class="op">);</span></span>
<span id="cb10-11"><a href="#cb10-11" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb10-12"><a href="#cb10-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-13"><a href="#cb10-13" aria-hidden="true" tabindex="-1"></a><span class="pp">#undef LOCTEXT_NAMESPACE</span></span>
<span id="cb10-14"><a href="#cb10-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-15"><a href="#cb10-15" aria-hidden="true" tabindex="-1"></a><span class="pp">#define LOCTEXT_NAMESPACE </span><span class="st">&quot;GameplayMessages&quot;</span></span>
<span id="cb10-16"><a href="#cb10-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-17"><a href="#cb10-17" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> UGameplayMessageWidget <span class="op">:</span> <span class="kw">public</span> UUserWidget</span>
<span id="cb10-18"><a href="#cb10-18" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb10-19"><a href="#cb10-19" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 游戏内消息</span></span>
<span id="cb10-20"><a href="#cb10-20" aria-hidden="true" tabindex="-1"></a>    FText LevelComplete <span class="op">=</span> LOCTEXT<span class="op">(</span><span class="st">&quot;LevelComplete&quot;</span><span class="op">,</span> <span class="st">&quot;Level Complete!&quot;</span><span class="op">);</span></span>
<span id="cb10-21"><a href="#cb10-21" aria-hidden="true" tabindex="-1"></a>    FText GameOver <span class="op">=</span> LOCTEXT<span class="op">(</span><span class="st">&quot;GameOver&quot;</span><span class="op">,</span> <span class="st">&quot;Game Over&quot;</span><span class="op">);</span></span>
<span id="cb10-22"><a href="#cb10-22" aria-hidden="true" tabindex="-1"></a>    FText NewHighScore <span class="op">=</span> LOCTEXT<span class="op">(</span><span class="st">&quot;NewHighScore&quot;</span><span class="op">,</span> <span class="st">&quot;New High Score!&quot;</span><span class="op">);</span></span>
<span id="cb10-23"><a href="#cb10-23" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb10-24"><a href="#cb10-24" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-25"><a href="#cb10-25" aria-hidden="true" tabindex="-1"></a><span class="pp">#undef LOCTEXT_NAMESPACE</span></span></code></pre></div>
<h3 id="性能优化">2. 性能优化</h3>
<div class="sourceCode" id="cb11"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 文本缓存管理</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API ULocalizedTextCache <span class="op">:</span> <span class="kw">public</span> UGameInstanceSubsystem</span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-7"><a href="#cb11-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb11-8"><a href="#cb11-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取缓存的文本</span></span>
<span id="cb11-9"><a href="#cb11-9" aria-hidden="true" tabindex="-1"></a>    FText GetCachedText<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> Namespace<span class="op">,</span> <span class="at">const</span> FString<span class="op">&amp;</span> Key<span class="op">);</span></span>
<span id="cb11-10"><a href="#cb11-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-11"><a href="#cb11-11" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 预加载常用文本</span></span>
<span id="cb11-12"><a href="#cb11-12" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> PreloadCommonTexts<span class="op">();</span></span>
<span id="cb11-13"><a href="#cb11-13" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-14"><a href="#cb11-14" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 清理缓存</span></span>
<span id="cb11-15"><a href="#cb11-15" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ClearCache<span class="op">();</span></span>
<span id="cb11-16"><a href="#cb11-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-17"><a href="#cb11-17" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb11-18"><a href="#cb11-18" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> Initialize<span class="op">(</span>FSubsystemCollectionBase<span class="op">&amp;</span> Collection<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb11-19"><a href="#cb11-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-20"><a href="#cb11-20" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb11-21"><a href="#cb11-21" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 文本缓存</span></span>
<span id="cb11-22"><a href="#cb11-22" aria-hidden="true" tabindex="-1"></a>    TMap<span class="op">&lt;</span>FString<span class="op">,</span> FText<span class="op">&gt;</span> TextCache<span class="op">;</span></span>
<span id="cb11-23"><a href="#cb11-23" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-24"><a href="#cb11-24" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 生成缓存键</span></span>
<span id="cb11-25"><a href="#cb11-25" aria-hidden="true" tabindex="-1"></a>    FString GenerateCacheKey<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> Namespace<span class="op">,</span> <span class="at">const</span> FString<span class="op">&amp;</span> Key<span class="op">);</span></span>
<span id="cb11-26"><a href="#cb11-26" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb11-27"><a href="#cb11-27" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-28"><a href="#cb11-28" aria-hidden="true" tabindex="-1"></a>FText ULocalizedTextCache<span class="op">::</span>GetCachedText<span class="op">(</span><span class="at">const</span> FString<span class="op">&amp;</span> Namespace<span class="op">,</span> <span class="at">const</span> FString<span class="op">&amp;</span> Key<span class="op">)</span></span>
<span id="cb11-29"><a href="#cb11-29" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb11-30"><a href="#cb11-30" aria-hidden="true" tabindex="-1"></a>    FString CacheKey <span class="op">=</span> GenerateCacheKey<span class="op">(</span>Namespace<span class="op">,</span> Key<span class="op">);</span></span>
<span id="cb11-31"><a href="#cb11-31" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-32"><a href="#cb11-32" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>TextCache<span class="op">.</span>Contains<span class="op">(</span>CacheKey<span class="op">))</span></span>
<span id="cb11-33"><a href="#cb11-33" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-34"><a href="#cb11-34" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> TextCache<span class="op">[</span>CacheKey<span class="op">];</span></span>
<span id="cb11-35"><a href="#cb11-35" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-36"><a href="#cb11-36" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-37"><a href="#cb11-37" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 创建并缓存文本</span></span>
<span id="cb11-38"><a href="#cb11-38" aria-hidden="true" tabindex="-1"></a>    FText NewText <span class="op">=</span> FText<span class="op">::</span>FromStringTable<span class="op">(</span>FName<span class="op">(*</span>Namespace<span class="op">),</span> FName<span class="op">(*</span>Key<span class="op">));</span></span>
<span id="cb11-39"><a href="#cb11-39" aria-hidden="true" tabindex="-1"></a>    TextCache<span class="op">.</span>Add<span class="op">(</span>CacheKey<span class="op">,</span> NewText<span class="op">);</span></span>
<span id="cb11-40"><a href="#cb11-40" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-41"><a href="#cb11-41" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> NewText<span class="op">;</span></span>
<span id="cb11-42"><a href="#cb11-42" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="测试和调试">3. 测试和调试</h3>
<div class="sourceCode" id="cb12"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 本地化测试工具</span></span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb12-3"><a href="#cb12-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API ULocalizationTestHelper <span class="op">:</span> <span class="kw">public</span> UBlueprintFunctionLibrary</span>
<span id="cb12-4"><a href="#cb12-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb12-5"><a href="#cb12-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb12-6"><a href="#cb12-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-7"><a href="#cb12-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb12-8"><a href="#cb12-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 启用伪本地化</span></span>
<span id="cb12-9"><a href="#cb12-9" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization Debug&quot;</span><span class="op">)</span></span>
<span id="cb12-10"><a href="#cb12-10" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> <span class="dt">void</span> EnablePseudoLocalization<span class="op">(</span><span class="dt">bool</span> bEnable<span class="op">);</span></span>
<span id="cb12-11"><a href="#cb12-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-12"><a href="#cb12-12" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 显示文本键</span></span>
<span id="cb12-13"><a href="#cb12-13" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization Debug&quot;</span><span class="op">)</span></span>
<span id="cb12-14"><a href="#cb12-14" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> <span class="dt">void</span> ShowTextKeys<span class="op">(</span><span class="dt">bool</span> bShow<span class="op">);</span></span>
<span id="cb12-15"><a href="#cb12-15" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-16"><a href="#cb12-16" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 测试所有语言</span></span>
<span id="cb12-17"><a href="#cb12-17" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization Debug&quot;</span><span class="op">)</span></span>
<span id="cb12-18"><a href="#cb12-18" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> <span class="dt">void</span> TestAllLanguages<span class="op">();</span></span>
<span id="cb12-19"><a href="#cb12-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-20"><a href="#cb12-20" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 生成缺失翻译报告</span></span>
<span id="cb12-21"><a href="#cb12-21" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Localization Debug&quot;</span><span class="op">)</span></span>
<span id="cb12-22"><a href="#cb12-22" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> <span class="dt">void</span> GenerateMissingTranslationReport<span class="op">();</span></span>
<span id="cb12-23"><a href="#cb12-23" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb12-24"><a href="#cb12-24" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-25"><a href="#cb12-25" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> ULocalizationTestHelper<span class="op">::</span>EnablePseudoLocalization<span class="op">(</span><span class="dt">bool</span> bEnable<span class="op">)</span></span>
<span id="cb12-26"><a href="#cb12-26" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb12-27"><a href="#cb12-27" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>bEnable<span class="op">)</span></span>
<span id="cb12-28"><a href="#cb12-28" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-29"><a href="#cb12-29" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 设置伪本地化文化</span></span>
<span id="cb12-30"><a href="#cb12-30" aria-hidden="true" tabindex="-1"></a>        FInternationalization<span class="op">::</span>Get<span class="op">().</span>SetCurrentCulture<span class="op">(</span>TEXT<span class="op">(</span><span class="st">&quot;qps-ploc&quot;</span><span class="op">));</span></span>
<span id="cb12-31"><a href="#cb12-31" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-32"><a href="#cb12-32" aria-hidden="true" tabindex="-1"></a>    <span class="cf">else</span></span>
<span id="cb12-33"><a href="#cb12-33" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-34"><a href="#cb12-34" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 恢复默认文化</span></span>
<span id="cb12-35"><a href="#cb12-35" aria-hidden="true" tabindex="-1"></a>        FInternationalization<span class="op">::</span>Get<span class="op">().</span>SetCurrentCulture<span class="op">(</span>TEXT<span class="op">(</span><span class="st">&quot;en&quot;</span><span class="op">));</span></span>
<span id="cb12-36"><a href="#cb12-36" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-37"><a href="#cb12-37" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<hr />
<p><em>Unreal Engine 的国际化系统为 AAA
级游戏提供了企业级的本地化解决方案。更多详细信息请参考 <a
href="https://docs.unrealengine.com/5.3/en-US/localization-and-internationalization-in-unreal-engine/">Unreal
Engine 国际化文档</a></em></p>
</div><div lang="en" style="display: none;"><h1>[English translation pending]</h1><p>The content for this page has not been translated yet. Please check back later.</p></div>
                </main>
            </div>
        </section>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">Qt 3D 开发指南</h4>
                    <p data-i18n="footer-subtitle">专业的跨平台 3D 应用开发解决方案</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.</p>
            </div>
        </div>
    </footer>

    <script src="../../assets/js/prism.js"></script>
    <script src="../../assets/js/docs.js"></script>
</body>
</html> 