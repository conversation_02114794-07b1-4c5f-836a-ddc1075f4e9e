// 主要JavaScript功能
document.addEventListener('DOMContentLoaded', function() {
    // 导航栏功能
    initNavigation();
    
    // 平滑滚动
    initSmoothScroll();
    
    // 语言切换
    initLanguageSwitch();
    
    // 国际化演示
    initI18nDemo();
    
    // 动画效果
    initAnimations();
    
    // 响应式导航
    initMobileNav();

    // 特性对比标签页
    initFeatureTabs();

    // 生成文档目录
    generateTableOfContents();
});

// 导航栏功能
function initNavigation() {
    const navbar = document.querySelector('.navbar');
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // 滚动时改变导航栏样式
        if (scrollTop > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = 'none';
        }
        
        // 高亮当前部分
        highlightCurrentSection();
        
        lastScrollTop = scrollTop;
    });
}

// 高亮当前部分
function highlightCurrentSection() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let currentSection = '';
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;
        
        if (window.pageYOffset >= sectionTop && 
            window.pageYOffset < sectionTop + sectionHeight) {
            currentSection = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${currentSection}`) {
            link.classList.add('active');
        }
    });
}

// 平滑滚动
function initSmoothScroll() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 80;
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// 语言切换功能
function initLanguageSwitch() {
    const languageSelect = document.getElementById('languageSelect');
    
    // 从 localStorage 获取语言或默认 'zh'
    const savedLang = localStorage.getItem('language') || 'zh';
    
    if (languageSelect) {
        languageSelect.value = savedLang;
        languageSelect.addEventListener('change', function() {
            const selectedLang = this.value;
            // 保存选择到 localStorage
            localStorage.setItem('language', selectedLang);
            switchLanguage(selectedLang);
        });
    }
    
    // 初始加载时切换到保存的语言
    switchLanguage(savedLang);
}

// 切换语言
function switchLanguage(lang) {
    const translations = {
        'zh': {
            'pageTitle': '3D 引擎开发指南 - Qt 3D | Unity | Unreal Engine | Kanzi',
            'nav-logo-text': '3D 引擎指南',
            'nav-engines': '引擎对比',
            'nav-features': '特性分析',
            'nav-guides': '国际化',
            'nav-resources': '文档',
            'lang-zh': '中文',
            'lang-en': 'English',
            'hero-title': '3D 引擎技术对比参考',
            'hero-subtitle': 'Qt 3D、Unity、Unreal Engine、Kanzi、Cocos、Godot 技术特性对比',
            'hero-description': '客观分析六大主流 3D 引擎的技术特性、开发特点和适用场景，为技术选型提供参考信息。',
            'hero-btn-primary': '开始对比',
            'hero-btn-secondary': '查看指南',
            'showcase-title': '主流 3D 引擎技术特性',
            'qt3d-title': 'Qt 3D',
            'qt3d-tagline': '企业级跨平台 3D 框架',
            'qt3d-highlight-1': '深度集成 Qt 生态系统',
            'qt3d-highlight-2': '2D/3D 无缝融合',
            'qt3d-highlight-3': '企业级支持',
            'qt3d-highlight-4': '轻量级运行时',
            'qt3d-demo-title': '典型应用',
            'qt3d-demo-text': '汽车仪表盘、工业控制界面、医疗设备显示',
            'btn-official-website': '官方网站',
            'btn-dev-guide': '开发指南',
            'unity-title': 'Unity',
            'unity-tagline': '跨平台游戏开发引擎',
            'unity-highlight-1': '易于学习的编辑器',
            'unity-highlight-2': '丰富的资源商店',
            'unity-highlight-3': '强大的社区支持',
            'unity-highlight-4': '多平台一键发布',
            'unity-demo-title': '热门Demo',
            'unity-demo-text': 'URP 3D Sample、Fantasy Kingdom、Behavior Package Demo',
            'unreal-title': 'Unreal Engine',
            'unreal-tagline': 'AAA 级游戏开发引擎',
            'unreal-highlight-1': '顶级渲染质量',
            'unreal-highlight-2': 'Blueprint 可视化编程',
            'unreal-highlight-3': '完整的开发工具链',
            'unreal-highlight-4': '电影级实时渲染',
            'unreal-demo-title': '技术Demo',
            'unreal-demo-text': 'Lumen in the Land of Nanite、Lyra Starter Game、MetaHuman Creator',
            'kanzi-title': 'Kanzi',
            'kanzi-tagline': '嵌入式 HMI 专业平台',
            'kanzi-highlight-1': '嵌入式系统优化',
            'kanzi-highlight-2': '极低资源占用',
            'kanzi-highlight-3': '汽车行业标准',
            'kanzi-highlight-4': '专业 HMI 工具',
            'kanzi-demo-title': '行业案例',
            'kanzi-demo-text': '奔驰MBUX、宝马iDrive、奥迪MMI、沃尔沃Sensus',
            'cocos-title': 'Cocos Creator',
            'cocos-tagline': '跨平台2D/3D游戏引擎',
            'cocos-highlight-1': '轻量级高性能',
            'cocos-highlight-2': '强大的2D能力',
            'cocos-highlight-3': '亚洲市场应用广泛',
            'cocos-highlight-4': '完整开发工具链',
            'cocos-demo-title': '应用案例',
            'cocos-demo-text': '多款知名移动游戏采用，在亚洲市场应用广泛',
            'godot-title': 'Godot Engine',
            'godot-tagline': '开源免费游戏引擎',
            'godot-highlight-1': '完全免费开源',
            'godot-highlight-2': '节点系统设计',
            'godot-highlight-3': '多语言支持',
            'godot-highlight-4': '轻量级架构',
            'godot-demo-title': '社区项目',
            'godot-demo-text': 'Brotato、Dome Keeper、Cassette Beasts等独立游戏',
            'features-comparison-title': '技术特性深度对比',
            'tab-rendering': '渲染技术',
            'tab-development': '开发体验',
            'tab-efficiency': '开发效率',
            'tab-platform': '平台支持',
            'tab-performance': '性能表现',
            'rendering-qt3d-api': '<span class="feature-name">渲染API:</span> OpenGL, Vulkan, Metal, D3D11',
            'rendering-qt3d-lighting': '<span class="feature-name">光照:</span> 前向渲染，延迟渲染',
            'rendering-qt3d-material': '<span class="feature-name">材质:</span> 基于节点的材质编辑器',
            'rendering-qt3d-posteffect': '<span class="feature-name">后处理:</span> 基础后处理效果',
            'rendering-unity-api': '<span class="feature-name">渲染API:</span> URP, HDRP, Built-in RP',
            'rendering-unity-lighting': '<span class="feature-name">光照:</span> 实时GI，烘焙光照，混合光照',
            'rendering-unity-material': '<span class="feature-name">材质:</span> Shader Graph 可视化编辑',
            'rendering-unity-posteffect': '<span class="feature-name">后处理:</span> 丰富的后处理栈',
            'rendering-unreal-api': '<span class="feature-name">渲染API:</span> Lumen, Nanite, TSR',
            'rendering-unreal-lighting': '<span class="feature-name">光照:</span> 动态全局光照 Lumen',
            'rendering-unreal-material': '<span class="feature-name">材质:</span> 强大的材质编辑器',
            'rendering-unreal-posteffect': '<span class="feature-name">后处理:</span> 电影级后处理',
            'rendering-kanzi-api': '<span class="feature-name">渲染API:</span> OpenGL ES, Vulkan',
            'rendering-kanzi-lighting': '<span class="feature-name">光照:</span> 优化的实时光照',
            'rendering-kanzi-material': '<span class="feature-name">材质:</span> 嵌入式优化材质',
            'rendering-kanzi-posteffect': '<span class="feature-name">后处理:</span> 轻量级效果',
            'rendering-cocos-api': '<span class="feature-name">渲染API:</span> OpenGL ES, Metal, WebGL',
            'rendering-cocos-lighting': '<span class="feature-name">光照:</span> 2D优化，3D基础光照',
            'rendering-cocos-material': '<span class="feature-name">材质:</span> 移动端优化材质',
            'rendering-cocos-posteffect': '<span class="feature-name">后处理:</span> 轻量级2D/3D效果',
            'dev-qt3d-lang': '<span class="feature-name">编程语言:</span> C++, QML',
            'dev-qt3d-ide': '<span class="feature-name">IDE:</span> Qt Creator',
            'dev-qt3d-debug': '<span class="feature-name">调试:</span> Qt 调试工具',
            'dev-qt3d-curve': '<span class="feature-name">学习曲线:</span> 中等（需要Qt基础）',
            'dev-unity-lang': '<span class="feature-name">编程语言:</span> C#, Visual Scripting',
            'dev-unity-ide': '<span class="feature-name">IDE:</span> Unity Editor',
            'dev-unity-debug': '<span class="feature-name">调试:</span> 内置调试器，Profiler',
            'dev-unity-curve': '<span class="feature-name">学习曲线:</span> 简单（可视化编辑器）',
            'dev-unreal-lang': '<span class="feature-name">编程语言:</span> C++, Blueprint',
            'dev-unreal-ide': '<span class="feature-name">IDE:</span> Unreal Editor',
            'dev-unreal-debug': '<span class="feature-name">调试:</span> 强大的调试和分析工具',
            'dev-unreal-curve': '<span class="feature-name">学习曲线:</span> 陡峭（功能复杂）',
            'dev-kanzi-lang': '<span class="feature-name">编程语言:</span> C++',
            'dev-kanzi-ide': '<span class="feature-name">IDE:</span> Kanzi Studio',
            'dev-kanzi-debug': '<span class="feature-name">调试:</span> 专业HMI调试工具',
            'dev-kanzi-curve': '<span class="feature-name">学习曲线:</span> 陡峭（专业工具）',
            'dev-cocos-lang': '<span class="feature-name">编程语言:</span> TypeScript, JavaScript',
            'dev-cocos-ide': '<span class="feature-name">IDE:</span> Cocos Creator Editor',
            'dev-cocos-debug': '<span class="feature-name">调试:</span> 内置调试器，Chrome DevTools',
            'dev-cocos-curve': '<span class="feature-name">学习曲线:</span> 中等（Web技术栈）',
            'eff-qt3d-curve': '<span class="feature-name">学习曲线:</span> 中等（需要Qt基础）',
            'eff-qt3d-speed': '<span class="feature-name">开发速度:</span> 中等（企业应用快）',
            'eff-qt3d-debug': '<span class="feature-name">调试效率:</span> 高（专业工具）',
            'eff-qt3d-team': '<span class="feature-name">团队协作:</span> 良好（版本控制友好）',
            'eff-unity-curve': '<span class="feature-name">学习曲线:</span> 简单（可视化编辑器）',
            'eff-unity-speed': '<span class="feature-name">开发速度:</span> 快（丰富资源商店）',
            'eff-unity-debug': '<span class="feature-name">调试效率:</span> 高（内置工具）',
            'eff-unity-team': '<span class="feature-name">团队协作:</span> 良好（协作工具）',
            'eff-unreal-curve': '<span class="feature-name">学习曲线:</span> 陡峭（功能复杂）',
            'eff-unreal-speed': '<span class="feature-name">开发速度:</span> 中等（Blueprint加速）',
            'eff-unreal-debug': '<span class="feature-name">调试效率:</span> 高（强大工具链）',
            'eff-unreal-team': '<span class="feature-name">团队协作:</span> 优秀（企业级工具）',
            'eff-kanzi-curve': '<span class="feature-name">学习曲线:</span> 陡峭（专业工具）',
            'eff-kanzi-speed': '<span class="feature-name">开发速度:</span> 中等（HMI专用）',
            'eff-kanzi-debug': '<span class="feature-name">调试效率:</span> 高（专业调试）',
            'eff-kanzi-team': '<span class="feature-name">团队协作:</span> 良好（工业标准）',
            'eff-cocos-curve': '<span class="feature-name">学习曲线:</span> 中等（Web技术栈）',
            'eff-cocos-speed': '<span class="feature-name">开发速度:</span> 快（2D游戏）',
            'eff-cocos-debug': '<span class="feature-name">调试效率:</span> 中等（Chrome DevTools）',
            'eff-cocos-team': '<span class="feature-name">团队协作:</span> 良好（版本控制）',
            'eff-godot-curve': '<span class="feature-name">学习曲线:</span> 简单（节点系统）',
            'eff-godot-speed': '<span class="feature-name">开发速度:</span> 快（轻量级）',
            'eff-godot-debug': '<span class="feature-name">调试效率:</span> 中等（内置调试器）',
            'eff-godot-team': '<span class="feature-name">团队协作:</span> 良好（开源友好）',
            'eff-details-title': '开发效率详细分析',
            'eff-startup-time': '项目启动时间',
            'eff-fastest': '最快',
            'eff-fast': '快',
            'eff-medium': '中等',
            'eff-slow': '慢',
            'eff-learning-cost': '学习成本',
            'eff-lowest': '最低',
            'eff-low': '低',
            'eff-high': '高',
            'platform-qt3d-desktop': '<span class="feature-name">桌面:</span> Windows, macOS, Linux',
            'platform-qt3d-mobile': '<span class="feature-name">移动:</span> Android, iOS',
            'platform-qt3d-embedded': '<span class="feature-name">嵌入式:</span> 广泛支持',
            'platform-qt3d-web': '<span class="feature-name">Web:</span> WebAssembly',
            'platform-unity-desktop': '<span class="feature-name">桌面:</span> Windows, macOS, Linux',
            'platform-unity-mobile': '<span class="feature-name">移动:</span> Android, iOS',
            'platform-unity-console': '<span class="feature-name">游戏机:</span> PlayStation, Xbox, Switch',
            'platform-unity-web': '<span class="feature-name">Web:</span> WebGL',
            'platform-unreal-desktop': '<span class="feature-name">桌面:</span> Windows, macOS, Linux',
            'platform-unreal-mobile': '<span class="feature-name">移动:</span> Android, iOS',
            'platform-unreal-console': '<span class="feature-name">游戏机:</span> PlayStation, Xbox',
            'platform-unreal-vr': '<span class="feature-name">VR/AR:</span> 全面支持',
            'platform-kanzi-auto': '<span class="feature-name">汽车:</span> QNX, Linux, Android Auto',
            'platform-kanzi-industrial': '<span class="feature-name">工业:</span> VxWorks, Linux RT',
            'platform-kanzi-embedded': '<span class="feature-name">嵌入式:</span> ARM, x86',
            'platform-kanzi-realtime': '<span class="feature-name">实时:</span> 硬实时系统',
            'platform-cocos-mobile': '<span class="feature-name">移动:</span> Android, iOS (优化)',
            'platform-cocos-web': '<span class="feature-name">Web:</span> H5, 微信小游戏',
            'platform-cocos-desktop': '<span class="feature-name">桌面:</span> Windows, macOS',
            'platform-cocos-new': '<span class="feature-name">新平台:</span> 华为快游戏, OPPO小游戏',
            'perf-qt3d-memory': '<span class="feature-name">内存占用:</span> 低（20-50MB）*',
            'perf-qt3d-startup': '<span class="feature-name">启动时间:</span> 快（1-3秒）*',
            'perf-qt3d-render': '<span class="feature-name">渲染性能:</span> 中等',
            'perf-qt3d-scene': '<span class="feature-name">适用场景:</span> 企业应用，嵌入式',
            'perf-unity-memory': '<span class="feature-name">内存占用:</span> 中等（50-200MB）*',
            'perf-unity-startup': '<span class="feature-name">启动时间:</span> 中等（3-8秒）*',
            'perf-unity-render': '<span class="feature-name">渲染性能:</span> 良好**',
            'perf-unity-scene': '<span class="feature-name">适用场景:</span> 跨平台游戏开发',
            'perf-unreal-memory': '<span class="feature-name">内存占用:</span> 高（500MB+）**',
            'perf-unreal-startup': '<span class="feature-name">启动时间:</span> 慢（10-30秒）**',
            'perf-unreal-render': '<span class="feature-name">渲染性能:</span> 优秀**',
            'perf-unreal-scene': '<span class="feature-name">适用场景:</span> 高端3D游戏开发',
            'perf-kanzi-memory': '<span class="feature-name">内存占用:</span> 极低（5-20MB）*',
            'perf-kanzi-startup': '<span class="feature-name">启动时间:</span> 极快（<1秒）*',
            'perf-kanzi-render': '<span class="feature-name">渲染性能:</span> 嵌入式优化',
            'perf-kanzi-scene': '<span class="feature-name">适用场景:</span> 汽车HMI，工业界面',
            'perf-cocos-memory': '<span class="feature-name">内存占用:</span> 低（30-80MB）***',
            'perf-cocos-startup': '<span class="feature-name">启动时间:</span> 快（1-3秒）***',
            'perf-cocos-render': '<span class="feature-name">渲染性能:</span> 移动端优化',
            'perf-cocos-scene': '<span class="feature-name">适用场景:</span> 移动游戏开发',
            'perf-godot-memory': '<span class="feature-name">内存占用:</span> 低（20-60MB）****',
            'perf-godot-startup': '<span class="feature-name">启动时间:</span> 快（1-2秒）****',
            'perf-godot-render': '<span class="feature-name">渲染性能:</span> 中等',
            'perf-godot-scene': '<span class="feature-name">适用场景:</span> 独立游戏开发',
            'perf-source-title': '数据来源说明',
            'perf-source-qt': '* 基于Qt官方文档和技术规格',
            'perf-source-unity-unreal': '** 参考<a href="https://pinglestudio.com/blog/unity-vs-unreal-a-detailed-performance-showdown" target="_blank">Pingle Studio技术对比报告</a>等行业分析',
            'perf-source-cocos': '*** 基于Cocos官方技术文档',
            'perf-source-godot': '**** 基于Godot官方文档和社区反馈',
            'perf-source-disclaimer': '注：性能数据仅供参考，实际表现因项目复杂度和硬件配置而异',
            'i18n-showcase-title': '国际化与本地化技术对比',
            'i18n-overview': '以下对比基于各引擎的官方文档和技术特性，从文本管理、资源本地化、工具支持、技术实现四个维度进行客观分析。',
            'i18n-qt-text': '文本管理',
            'i18n-qt-text-desc': 'QTranslator系统，.ts/.qm文件格式',
            'i18n-qt-resource': '资源本地化',
            'i18n-qt-resource-desc': '资源系统支持多语言资源变体',
            'i18n-qt-tools': '工具支持',
            'i18n-qt-tools-desc': 'Qt Linguist专业翻译工具',
            'i18n-qt-impl': '技术实现',
            'i18n-qt-impl-desc': '完整RTL支持，自动布局镜像',
            'i18n-unity-text': '文本管理',
            'i18n-unity-text-desc': 'Localization Package，字符串表系统',
            'i18n-unity-resource': '资源本地化',
            'i18n-unity-resource-desc': 'Addressables系统支持资源变体',
            'i18n-unity-tools': '工具支持',
            'i18n-unity-tools-desc': 'CSV工作流，翻译团队协作',
            'i18n-unity-impl': '技术实现',
            'i18n-unity-impl-desc': '实时语言切换，伪本地化测试',
            'i18n-unreal-text': '文本管理',
            'i18n-unreal-text-desc': 'FText系统，支持复数和格式化',
            'i18n-unreal-resource': '资源本地化',
            'i18n-unreal-resource-desc': '资产本地化系统，多语言资源管理',
            'i18n-unreal-tools': '工具支持',
            'i18n-unreal-tools-desc': '本地化仪表板，翻译编辑器',
            'i18n-unreal-impl': '技术实现',
            'i18n-unreal-impl-desc': '自动文本收集，XLIFF标准支持',
            'i18n-kanzi-text': '文本管理',
            'i18n-kanzi-text-desc': '字符串管理系统，多语言文本支持',
            'i18n-kanzi-resource': '资源本地化',
            'i18n-kanzi-resource-desc': '嵌入式优化的资源管理系统',
            'i18n-kanzi-tools': '工具支持',
            'i18n-kanzi-tools-desc': 'Kanzi Studio集成本地化工具',
            'i18n-kanzi-impl': '技术实现',
            'i18n-kanzi-impl-desc': '实时语言切换，汽车行业标准',
            'i18n-cocos-text': '文本管理',
            'i18n-cocos-text-desc': '多语言管理器，JSON/CSV格式',
            'i18n-cocos-resource': '资源本地化',
            'i18n-cocos-resource-desc': '资源管理系统，多语言资源变体',
            'i18n-cocos-tools': '工具支持',
            'i18n-cocos-tools-desc': 'JSON/CSV工作流，编辑器集成',
            'i18n-cocos-impl': '技术实现',
            'i18n-cocos-impl-desc': '热更新支持，移动端优化',
            'i18n-godot-text': '文本管理',
            'i18n-godot-text-desc': 'CSV翻译系统，tr()函数支持',
            'i18n-godot-resource': '资源本地化',
            'i18n-godot-resource-desc': '资源重映射，多语言资源管理',
            'i18n-godot-tools': '工具支持',
            'i18n-godot-tools-desc': '内置CSV编辑器，社区工具',
            'i18n-godot-impl': '技术实现',
            'i18n-godot-impl-desc': '运行时语言切换，开源扩展',
            'i18n-table-title': '国际化技术特性对比',
            'i18n-table-feature': '技术特性',
            'i18n-table-text-management': '文本管理系统',
            'i18n-table-text-management-qt': 'QTranslator (.ts/.qm)',
            'i18n-table-text-management-unity': 'Localization Package',
            'i18n-table-text-management-unreal': 'FText System',
            'i18n-table-text-management-kanzi': '字符串管理',
            'i18n-table-text-management-cocos': '多语言管理器',
            'i18n-table-text-management-godot': 'CSV翻译系统',
            'i18n-table-resource': '资源本地化',
            'i18n-table-resource-qt': '资源系统变体',
            'i18n-table-resource-unity': 'Addressables',
            'i18n-table-resource-unreal': 'Asset Localization',
            'i18n-table-resource-kanzi': '嵌入式资源管理',
            'i18n-table-resource-cocos': '资源管理系统',
            'i18n-table-resource-godot': '资源重映射',
            'i18n-table-rtl': 'RTL语言支持',
            'i18n-table-rtl-qt': '完整支持',
            'i18n-table-rtl-unity': '需要插件',
            'i18n-table-rtl-unreal': '内置支持',
            'i18n-table-rtl-kanzi': '基础支持',
            'i18n-table-rtl-cocos': '基础支持',
            'i18n-table-rtl-godot': '社区扩展',
            'i18n-table-plural': '复数和格式化',
            'i18n-table-plural-qt': 'QLocale支持',
            'i18n-table-plural-unity': 'Smart Format',
            'i18n-table-plural-unreal': '高级格式化',
            'i18n-table-plural-kanzi': '基础支持',
            'i18n-table-plural-cocos': 'Intl API',
            'i18n-table-plural-godot': '基础支持',
            'i18n-table-realtime': '实时语言切换',
            'i18n-table-realtime-qt': '支持',
            'i18n-table-realtime-unity': '支持',
            'i18n-table-realtime-unreal': '支持',
            'i18n-table-realtime-kanzi': '优化性能',
            'i18n-table-realtime-cocos': '热更新',
            'i18n-table-realtime-godot': '运行时切换',
            'i18n-table-tools': '开发工具',
            'i18n-table-tools-qt': 'Qt Linguist',
            'i18n-table-tools-unity': 'CSV工作流',
            'i18n-table-tools-unreal': 'Translation Editor',
            'i18n-table-tools-kanzi': 'Kanzi Studio',
            'i18n-table-tools-cocos': 'JSON/CSV',
            'i18n-table-tools-godot': '内置CSV编辑器',
            'i18n-table-difficulty': '学习难度',
            'i18n-table-difficulty-qt': '中等',
            'i18n-table-difficulty-unity': '简单',
            'i18n-table-difficulty-unreal': '复杂',
            'i18n-table-difficulty-kanzi': '复杂',
            'i18n-table-difficulty-cocos': '简单',
            'i18n-table-difficulty-godot': '简单',
            'i18n-table-note': '<strong>说明：</strong>以上对比基于各引擎官方文档，实际功能可能因版本更新而变化。建议根据具体项目需求选择合适的技术方案。',
            'comparison-title': '引擎对比分析',
            'comparison-intro': '详细对比 Qt 3D 与其他主流 3D 引擎的特性、优缺点和适用场景',
            'comparison-table-feature': '特性',
            'comparison-table-dev': '开发商',
            'comparison-table-lang': '主要语言',
            'comparison-table-license': '许可证',
            'license-qt': '商业/开源',
            'license-unity': '免费/商业',
            'license-unreal': '免费/商业',
            'license-kanzi': '商业',
            'license-cocos': '免费/开源',
            'license-godot': 'MIT开源',
            'comparison-table-platform': '跨平台支持',
            'comparison-table-platform-qt3d': '桌面、移动、嵌入式多平台',
            'comparison-table-platform-unity': '桌面、移动、主流游戏机、Web',
            'comparison-table-platform-unreal': '桌面、移动、主流游戏机、VR/AR',
            'comparison-table-platform-kanzi': '嵌入式、汽车、工业平台',
            'comparison-table-platform-cocos': '移动、Web、桌面',
            'comparison-table-platform-godot': '桌面、移动、Web、部分主机（社区支持）',
            'comparison-table-difficulty': '学习难度',
            'comparison-table-difficulty-qt3d': '适合有Qt基础的开发者',
            'comparison-table-difficulty-unity': '适合初学者和专业开发者',
            'comparison-table-difficulty-unreal': '适合有一定开发经验的用户',
            'comparison-table-difficulty-kanzi': '适合嵌入式和HMI专业开发者',
            'comparison-table-difficulty-cocos': '适合Web和移动开发者',
            'comparison-table-difficulty-godot': '适合独立开发者和教育用途',
            'comparison-table-render-qt3d': '支持OpenGL/Vulkan/Metal等多种API，2D/3D融合',
            'comparison-table-render-unity': '支持URP/HDRP，丰富后处理和实时渲染',
            'comparison-table-render-unreal': '支持Lumen、Nanite等高端渲染技术',
            'comparison-table-render-kanzi': '针对嵌入式优化的实时渲染',
            'comparison-table-render-cocos': '高效2D/3D渲染，移动端优化',
            'comparison-table-render-godot': '支持OpenGL ES/Vulkan/WebGL，轻量级架构',
            'pros-title': '亮点',
            'use-cases-title': '适合项目类型',
            'qt3d-pros-1': '深度集成 Qt 生态系统',
            'qt3d-pros-2': '跨平台一致性',
            'qt3d-pros-3': '企业级支持',
            'qt3d-pros-4': '轻量级运行时',
            'qt3d-use-1': '企业应用',
            'qt3d-use-2': '工业软件',
            'qt3d-use-3': '嵌入式系统',
            'qt3d-use-4': '跨平台应用',
            'unity-pros-1': '易于学习和上手',
            'unity-pros-2': '丰富的资源和插件',
            'unity-pros-3': '活跃的开发者社区',
            'unity-pros-4': '多平台发布能力',
            'unity-use-1': '独立游戏',
            'unity-use-2': '移动游戏',
            'unity-use-3': 'AR/VR游戏',
            'unity-use-4': '教育培训',
            'unreal-pros-1': '高质量实时渲染',
            'unreal-pros-2': 'Blueprint 可视化编程',
            'unreal-pros-3': '完整开发工具链',
            'unreal-pros-4': '丰富的行业应用',
            'unreal-use-1': '大型3D游戏',
            'unreal-use-2': '影视制作',
            'unreal-use-3': '建筑可视化',
            'unreal-use-4': 'VR/AR体验',
            'kanzi-pros-1': '嵌入式系统优化',
            'kanzi-pros-2': '低资源消耗',
            'kanzi-pros-3': '实时性能',
            'kanzi-pros-4': '汽车行业标准',
            'kanzi-use-1': '汽车仪表盘',
            'kanzi-use-2': '工业控制',
            'kanzi-use-3': '医疗设备',
            'kanzi-use-4': '消费电子',
            'cocos-pros-1': '轻量高效',
            'cocos-pros-2': '强大2D能力',
            'cocos-pros-3': '亚洲市场广泛应用',
            'cocos-pros-4': '完整工具链',
            'cocos-use-1': '移动游戏',
            'cocos-use-2': '轻量3D项目',
            'cocos-use-3': 'H5小游戏',
            'cocos-use-4': '教育应用',
            'godot-pros-1': '完全免费开源',
            'godot-pros-2': '节点系统易用',
            'godot-pros-3': '多语言支持',
            'godot-pros-4': '轻量级架构',
            'godot-use-1': '独立游戏',
            'godot-use-2': '教育项目',
            'godot-use-3': '快速原型',
            'godot-use-4': '多平台发布',
            'i18n-support-title': '国际化支持',
            'i18n-global-dev': '全球化应用开发',
            'i18n-global-desc': '3D 引擎提供完整的国际化和本地化支持，让您的 3D 应用能够轻松适应全球市场。',
            'i18n-feature-multi-lang': '多语言文本支持',
            'i18n-feature-unicode': 'Unicode 字符渲染',
            'i18n-feature-l10n-res': '本地化资源管理',
            'i18n-feature-rtl': '文本方向适配',
            'i18n-feature-dynamic-switch': '动态语言切换',
            'btn-learn-more': '了解更多',
            'lang-sample-zh-title': '中文',
            'lang-sample-zh-text': '欢迎使用 3D 引擎',
            'lang-sample-en-title': 'English',
            'lang-sample-en-text': 'Welcome to 3D Engine',
            'lang-sample-ja-title': '日本語',
            'lang-sample-ja-text': '3D エンジンへようこそ',
            'lang-sample-ar-title': 'العربية',
            'lang-sample-ar-text': 'مرحباً بك في محرك Qt 3D',
            'docs-title': '开发文档',
            'docs-guide-title': '开发指南',
            'docs-guide-desc': '完整的 Qt 3D 开发指南，包括环境搭建、核心概念和最佳实践。',
            'docs-guide-link': '查看文档',
            'docs-comparison-title': '引擎对比',
            'docs-comparison-desc': '详细对比 Qt 3D 与其他主流 3D 引擎的特性和适用场景。',
            'docs-comparison-link': '查看对比',
            'docs-i18n-title': '国际化指南',
            'docs-i18n-desc': 'Qt 3D 应用的国际化和本地化开发指南。',
            'docs-i18n-link': '查看指南',
            'footer-title': 'Qt 3D 开发指南',
            'footer-subtitle': '专业的跨平台 3D 应用开发解决方案',
            'footer-docs': '文档',
            'footer-link-guide': '开发指南',
            'footer-link-comparison': '引擎对比',
            'footer-link-i18n': '国际化',
            'footer-resources': '资源',
            'footer-link-official-docs': '官方文档',
            'footer-link-github': 'GitHub',
            'footer-link-forum': '社区论坛',
            'footer-copyright': '&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.',
            'rendering-godot-api': '<span class="feature-name">渲染API:</span> OpenGL ES, Vulkan, WebGL',
            'rendering-godot-lighting': '<span class="feature-name">光照:</span> 实时光照，烘焙光照',
            'rendering-godot-material': '<span class="feature-name">材质:</span> Shader 编辑器，Visual Shader',
            'rendering-godot-posteffect': '<span class="feature-name">后处理:</span> 基础后处理效果',
            'dev-godot-lang': '<span class="feature-name">编程语言:</span> GDScript, C#, VisualScript',
            'dev-godot-ide': '<span class="feature-name">IDE:</span> Godot Editor',
            'dev-godot-debug': '<span class="feature-name">调试:</span> 内置调试器，Profiler',
            'dev-godot-curve': '<span class="feature-name">学习曲线:</span> 简单（节点系统）',
            'platform-godot-desktop': '<span class="feature-name">桌面:</span> Windows, macOS, Linux',
            'platform-godot-mobile': '<span class="feature-name">移动:</span> Android, iOS',
            'platform-godot-web': '<span class="feature-name">Web:</span> HTML5',
            'platform-godot-console': '<span class="feature-name">游戏机:</span> 社区支持',
            'license-kanzi': '商业',
            'license-cocos': '免费/开源',
            'license-godot': 'MIT开源',
            // 中文
            'dev-qt3d': 'Qt Company',
            'dev-unity': 'Unity Technologies',
            'dev-unreal': 'Epic Games',
            'dev-kanzi': 'Rightware',
            'dev-cocos': '厦门雅基软件',
            'dev-godot': 'Godot 社区',
            'lang-qt3d': 'C++/QML',
            'lang-unity': 'C#/JavaScript',
            'lang-unreal': 'C++/Blueprint',
            'lang-kanzi': 'C++',
            'lang-cocos': 'TypeScript/JavaScript',
            'lang-godot': 'GDScript/C#/C++',
        },
        'en': {
            'pageTitle': '3D Engine Development Guide - Qt 3D | Unity | Unreal Engine | Kanzi',
            'nav-logo-text': '3D Engine Guide',
            'nav-engines': 'Engine Comparison',
            'nav-features': 'Feature Analysis',
            'nav-guides': 'Internationalization',
            'nav-resources': 'Documentation',
            'lang-zh': '中文',
            'lang-en': 'English',
            'hero-title': '3D Engine Technology Comparison Reference',
            'hero-subtitle': 'Technical feature comparison of Qt 3D, Unity, Unreal Engine, Kanzi, Cocos, Godot',
            'hero-description': 'Objectively analyze the technical characteristics, development features, and application scenarios of the six major 3D engines to provide reference information for technology selection.',
            'hero-btn-primary': 'Start Comparison',
            'hero-btn-secondary': 'View Guides',
            'showcase-title': 'Mainstream 3D Engine Technical Features',
            'qt3d-title': 'Qt 3D',
            'qt3d-tagline': 'Enterprise-level Cross-platform 3D Framework',
            'qt3d-highlight-1': 'Deep integration with Qt ecosystem',
            'qt3d-highlight-2': 'Seamless 2D/3D fusion',
            'qt3d-highlight-3': 'Enterprise-grade support',
            'qt3d-highlight-4': 'Lightweight runtime',
            'qt3d-demo-title': 'Typical Applications',
            'qt3d-demo-text': 'Automotive dashboards, industrial control interfaces, medical equipment displays',
            'btn-official-website': 'Official Website',
            'btn-dev-guide': 'Dev Guide',
            'unity-title': 'Unity',
            'unity-tagline': 'Cross-platform Game Development Engine',
            'unity-highlight-1': 'Easy-to-learn editor',
            'unity-highlight-2': 'Rich asset store',
            'unity-highlight-3': 'Strong community support',
            'unity-highlight-4': 'One-click multi-platform publishing',
            'unity-demo-title': 'Popular Demos',
            'unity-demo-text': 'URP 3D Sample, Fantasy Kingdom, Behavior Package Demo',
            'unreal-title': 'Unreal Engine',
            'unreal-tagline': 'AAA Game Development Engine',
            'unreal-highlight-1': 'Top-tier rendering quality',
            'unreal-highlight-2': 'Blueprint visual scripting',
            'unreal-highlight-3': 'Complete development toolchain',
            'unreal-highlight-4': 'Cinematic real-time rendering',
            'unreal-demo-title': 'Tech Demos',
            'unreal-demo-text': 'Lumen in the Land of Nanite, Lyra Starter Game, MetaHuman Creator',
            'kanzi-title': 'Kanzi',
            'kanzi-tagline': 'Professional Platform for Embedded HMIs',
            'kanzi-highlight-1': 'Optimized for embedded systems',
            'kanzi-highlight-2': 'Extremely low resource consumption',
            'kanzi-highlight-3': 'Automotive industry standard',
            'kanzi-highlight-4': 'Professional HMI tools',
            'kanzi-demo-title': 'Industry Cases',
            'kanzi-demo-text': 'Mercedes-Benz MBUX, BMW iDrive, Audi MMI, Volvo Sensus',
            'cocos-title': 'Cocos Creator',
            'cocos-tagline': 'Cross-platform 2D/3D Game Engine',
            'cocos-highlight-1': 'Lightweight and high-performance',
            'cocos-highlight-2': 'Powerful 2D capabilities',
            'cocos-highlight-3': 'Widely used in the Asian market',
            'cocos-highlight-4': 'Complete development toolchain',
            'cocos-demo-title': 'Application Cases',
            'cocos-demo-text': 'Used in many well-known mobile games, widely applied in the Asian market',
            'godot-title': 'Godot Engine',
            'godot-tagline': 'Free and Open-Source Game Engine',
            'godot-highlight-1': 'Completely free and open-source',
            'godot-highlight-2': 'Node-based system design',
            'godot-highlight-3': 'Multi-language support',
            'godot-highlight-4': 'Lightweight architecture',
            'godot-demo-title': 'Community Projects',
            'godot-demo-text': 'Indie games like Brotato, Dome Keeper, Cassette Beasts',
            'features-comparison-title': 'In-depth Comparison of Technical Features',
            'tab-rendering': 'Rendering',
            'tab-development': 'Development',
            'tab-efficiency': 'Efficiency',
            'tab-platform': 'Platform',
            'tab-performance': 'Performance',
            'rendering-qt3d-api': '<span class="feature-name">Rendering API:</span> OpenGL, Vulkan, Metal, D3D11',
            'rendering-qt3d-lighting': '<span class="feature-name">Lighting:</span> Forward Rendering, Deferred Rendering',
            'rendering-qt3d-material': '<span class="feature-name">Material:</span> Node-based Material Editor',
            'rendering-qt3d-posteffect': '<span class="feature-name">Post-processing:</span> Basic post-processing effects',
            'rendering-unity-api': '<span class="feature-name">Rendering API:</span> URP, HDRP, Built-in RP',
            'rendering-unity-lighting': '<span class="feature-name">Lighting:</span> Real-time GI, Baked Lighting, Mixed Lighting',
            'rendering-unity-material': '<span class="feature-name">Material:</span> Shader Graph Visual Editing',
            'rendering-unity-posteffect': '<span class="feature-name">Post-processing:</span> Rich Post-processing Stack',
            'rendering-unreal-api': '<span class="feature-name">Rendering API:</span> Lumen, Nanite, TSR',
            'rendering-unreal-lighting': '<span class="feature-name">Lighting:</span> Dynamic Global Illumination Lumen',
            'rendering-unreal-material': '<span class="feature-name">Material:</span> Powerful Material Editor',
            'rendering-unreal-posteffect': '<span class="feature-name">Post-processing:</span> Cinematic Post-processing',
            'rendering-kanzi-api': '<span class="feature-name">Rendering API:</span> OpenGL ES, Vulkan',
            'rendering-kanzi-lighting': '<span class="feature-name">Lighting:</span> Optimized Real-time Lighting',
            'rendering-kanzi-material': '<span class="feature-name">Material:</span> Embedded Optimized Materials',
            'rendering-kanzi-posteffect': '<span class="feature-name">Post-processing:</span> Lightweight Effects',
            'rendering-cocos-api': '<span class="feature-name">Rendering API:</span> OpenGL ES, Metal, WebGL',
            'rendering-cocos-lighting': '<span class="feature-name">Lighting:</span> 2D Optimized, Basic 3D Lighting',
            'rendering-cocos-material': '<span class="feature-name">Material:</span> Mobile Optimized Materials',
            'rendering-cocos-posteffect': '<span class="feature-name">Post-processing:</span> Lightweight 2D/3D Effects',
            'dev-qt3d-lang': '<span class="feature-name">Language:</span> C++, QML',
            'dev-qt3d-ide': '<span class="feature-name">IDE:</span> Qt Creator',
            'dev-qt3d-debug': '<span class="feature-name">Debugging:</span> Qt Debugging Tools',
            'dev-qt3d-curve': '<span class="feature-name">Learning Curve:</span> Medium (Requires Qt knowledge)',
            'dev-unity-lang': '<span class="feature-name">Language:</span> C#, Visual Scripting',
            'dev-unity-ide': '<span class="feature-name">IDE:</span> Unity Editor',
            'dev-unity-debug': '<span class="feature-name">Debugging:</span> Built-in Debugger, Profiler',
            'dev-unity-curve': '<span class="feature-name">Learning Curve:</span> Easy (Visual editor)',
            'dev-unreal-lang': '<span class="feature-name">Language:</span> C++, Blueprint',
            'dev-unreal-ide': '<span class="feature-name">IDE:</span> Unreal Editor',
            'dev-unreal-debug': '<span class="feature-name">Debugging:</span> Powerful debugging and analysis tools',
            'dev-unreal-curve': '<span class="feature-name">Learning Curve:</span> Steep (Complex features)',
            'dev-kanzi-lang': '<span class="feature-name">Language:</span> C++',
            'dev-kanzi-ide': '<span class="feature-name">IDE:</span> Kanzi Studio',
            'dev-kanzi-debug': '<span class="feature-name">Debugging:</span> Professional HMI debugging tools',
            'dev-kanzi-curve': '<span class="feature-name">Learning Curve:</span> Steep (Professional tool)',
            'dev-cocos-lang': '<span class="feature-name">Language:</span> TypeScript, JavaScript',
            'dev-cocos-ide': '<span class="feature-name">IDE:</span> Cocos Creator Editor',
            'dev-cocos-debug': '<span class="feature-name">Debugging:</span> Built-in debugger, Chrome DevTools',
            'dev-cocos-curve': '<span class="feature-name">Learning Curve:</span> Medium (Web tech stack)',
            'eff-qt3d-curve': '<span class="feature-name">Learning Curve:</span> Medium (Requires Qt knowledge)',
            'eff-qt3d-speed': '<span class="feature-name">Dev Speed:</span> Medium (Fast for enterprise apps)',
            'eff-qt3d-debug': '<span class="feature-name">Debug Efficiency:</span> High (Professional tools)',
            'eff-qt3d-team': '<span class="feature-name">Team Collaboration:</span> Good (VCS friendly)',
            'eff-unity-curve': '<span class="feature-name">Learning Curve:</span> Easy (Visual editor)',
            'eff-unity-speed': '<span class="feature-name">Dev Speed:</span> Fast (Rich asset store)',
            'eff-unity-debug': '<span class="feature-name">Debug Efficiency:</span> High (Built-in tools)',
            'eff-unity-team': '<span class="feature-name">Team Collaboration:</span> Good (Collaboration tools)',
            'eff-unreal-curve': '<span class="feature-name">Learning Curve:</span> Steep (Complex features)',
            'eff-unreal-speed': '<span class="feature-name">Dev Speed:</span> Medium (Blueprint helps)',
            'eff-unreal-debug': '<span class="feature-name">Debug Efficiency:</span> High (Powerful toolchain)',
            'eff-unreal-team': '<span class="feature-name">Team Collaboration:</span> Excellent (Enterprise tools)',
            'eff-kanzi-curve': '<span class="feature-name">Learning Curve:</span> Steep (Professional tool)',
            'eff-kanzi-speed': '<span class="feature-name">Dev Speed:</span> Medium (HMI specific)',
            'eff-kanzi-debug': '<span class="feature-name">Debug Efficiency:</span> High (Professional debugging)',
            'eff-kanzi-team': '<span class="feature-name">Team Collaboration:</span> Good (Industry standard)',
            'eff-cocos-curve': '<span class="feature-name">Learning Curve:</span> Medium (Web tech stack)',
            'eff-cocos-speed': '<span class="feature-name">Dev Speed:</span> Fast (2D games)',
            'eff-cocos-debug': '<span class="feature-name">Debug Efficiency:</span> Medium (Chrome DevTools)',
            'eff-cocos-team': '<span class="feature-name">Team Collaboration:</span> Good (VCS)',
            'eff-godot-curve': '<span class="feature-name">Learning Curve:</span> Easy (Node system)',
            'eff-godot-speed': '<span class="feature-name">Dev Speed:</span> Fast (Lightweight)',
            'eff-godot-debug': '<span class="feature-name">Debug Efficiency:</span> Medium (Built-in debugger)',
            'eff-godot-team': '<span class="feature-name">Team Collaboration:</span> Good (Open-source friendly)',
            'eff-details-title': 'Development Efficiency Analysis',
            'eff-startup-time': 'Project Startup Time',
            'eff-fastest': 'Fastest',
            'eff-fast': 'Fast',
            'eff-medium': 'Medium',
            'eff-slow': 'Slow',
            'eff-learning-cost': 'Learning Cost',
            'eff-lowest': 'Lowest',
            'eff-low': 'Low',
            'eff-high': 'High',
            'platform-qt3d-desktop': '<span class="feature-name">Desktop:</span> Windows, macOS, Linux',
            'platform-qt3d-mobile': '<span class="feature-name">Mobile:</span> Android, iOS',
            'platform-qt3d-embedded': '<span class="feature-name">Embedded:</span> Broad support',
            'platform-qt3d-web': '<span class="feature-name">Web:</span> WebAssembly',
            'platform-unity-desktop': '<span class="feature-name">Desktop:</span> Windows, macOS, Linux',
            'platform-unity-mobile': '<span class="feature-name">Mobile:</span> Android, iOS',
            'platform-unity-console': '<span class="feature-name">Console:</span> PlayStation, Xbox, Switch',
            'platform-unity-web': '<span class="feature-name">Web:</span> WebGL',
            'platform-unreal-desktop': '<span class="feature-name">Desktop:</span> Windows, macOS, Linux',
            'platform-unreal-mobile': '<span class="feature-name">Mobile:</span> Android, iOS',
            'platform-unreal-console': '<span class="feature-name">Console:</span> PlayStation, Xbox',
            'platform-unreal-vr': '<span class="feature-name">VR/AR:</span> Full support',
            'platform-kanzi-auto': '<span class="feature-name">Automotive:</span> QNX, Linux, Android Auto',
            'platform-kanzi-industrial': '<span class="feature-name">Industrial:</span> VxWorks, Linux RT',
            'platform-kanzi-embedded': '<span class="feature-name">Embedded:</span> ARM, x86',
            'platform-kanzi-realtime': '<span class="feature-name">Real-time:</span> Hard real-time systems',
            'platform-cocos-mobile': '<span class="feature-name">Mobile:</span> Android, iOS (Optimized)',
            'platform-cocos-web': '<span class="feature-name">Web:</span> H5, WeChat Mini Games',
            'platform-cocos-desktop': '<span class="feature-name">Desktop:</span> Windows, macOS',
            'platform-cocos-new': '<span class="feature-name">New Platforms:</span> Huawei Quick App, OPPO Mini Games',
            'perf-qt3d-memory': '<span class="feature-name">Memory:</span> Low (20-50MB)*',
            'perf-qt3d-startup': '<span class="feature-name">Startup:</span> Fast (1-3s)*',
            'perf-qt3d-render': '<span class="feature-name">Rendering Perf:</span> Medium',
            'perf-qt3d-scene': '<span class="feature-name">Use Case:</span> Enterprise, Embedded',
            'perf-unity-memory': '<span class="feature-name">Memory:</span> Medium (50-200MB)*',
            'perf-unity-startup': '<span class="feature-name">Startup:</span> Medium (3-8s)*',
            'perf-unity-render': '<span class="feature-name">Rendering Perf:</span> Good**',
            'perf-unity-scene': '<span class="feature-name">Use Case:</span> Cross-platform Games',
            'perf-unreal-memory': '<span class="feature-name">Memory:</span> High (500MB+)**',
            'perf-unreal-startup': '<span class="feature-name">Startup:</span> Slow (10-30s)**',
            'perf-unreal-render': '<span class="feature-name">Rendering Perf:</span> Excellent**',
            'perf-unreal-scene': '<span class="feature-name">Use Case:</span> High-end 3D Games',
            'perf-kanzi-memory': '<span class="feature-name">Memory:</span> Very Low (5-20MB)*',
            'perf-kanzi-startup': '<span class="feature-name">Startup:</span> Very Fast (<1s)*',
            'perf-kanzi-render': '<span class="feature-name">Rendering Perf:</span> Embedded Optimized',
            'perf-kanzi-scene': '<span class="feature-name">Use Case:</span> Automotive HMI, Industrial UI',
            'perf-cocos-memory': '<span class="feature-name">Memory:</span> Low (30-80MB)***',
            'perf-cocos-startup': '<span class="feature-name">Startup:</span> Fast (1-3s)***',
            'perf-cocos-render': '<span class="feature-name">Rendering Perf:</span> Mobile Optimized',
            'perf-cocos-scene': '<span class="feature-name">Use Case:</span> Mobile Games',
            'perf-godot-memory': '<span class="feature-name">Memory:</span> Low (20-60MB)****',
            'perf-godot-startup': '<span class="feature-name">Startup:</span> Fast (1-2s)****',
            'perf-godot-render': '<span class="feature-name">Rendering Perf:</span> Medium',
            'perf-godot-scene': '<span class="feature-name">Use Case:</span> Indie Games',
            'perf-source-title': 'Data Source Disclaimer',
            'perf-source-qt': '* Based on Qt official documentation and technical specifications',
            'perf-source-unity-unreal': '** References from industry analysis such as <a href="https://pinglestudio.com/blog/unity-vs-unreal-a-detailed-performance-showdown" target="_blank">Pingle Studio\'s tech comparison</a>',
            'perf-source-cocos': '*** Based on Cocos official technical documentation',
            'perf-source-godot': '**** Based on Godot official documentation and community feedback',
            'perf-source-disclaimer': 'Note: Performance data is for reference only and may vary based on project complexity and hardware configuration.',
            'i18n-showcase-title': 'Internationalization & Localization Technology Comparison',
            'i18n-overview': 'The following comparison is based on the official documentation and technical features of each engine, objectively analyzing from four dimensions: text management, resource localization, tool support, and technical implementation.',
            'i18n-qt-text': 'Text Management',
            'i18n-qt-text-desc': 'QTranslator system, .ts/.qm file formats',
            'i18n-qt-resource': 'Resource Localization',
            'i18n-qt-resource-desc': 'Resource system supports multi-language resource variants',
            'i18n-qt-tools': 'Tool Support',
            'i18n-qt-tools-desc': 'Qt Linguist professional translation tool',
            'i18n-qt-impl': 'Technical Implementation',
            'i18n-qt-impl-desc': 'Full RTL support, automatic layout mirroring',
            'i18n-unity-text': 'Text Management',
            'i18n-unity-text-desc': 'Localization Package, string table system',
            'i18n-unity-resource': 'Resource Localization',
            'i18n-unity-resource-desc': 'Addressables system supports resource variants',
            'i18n-unity-tools': 'Tool Support',
            'i18n-unity-tools-desc': 'CSV workflow, collaboration for translation teams',
            'i18n-unity-impl': 'Technical Implementation',
            'i18n-unity-impl-desc': 'Real-time language switching, pseudo-localization testing',
            'i18n-unreal-text': 'Text Management',
            'i18n-unreal-text-desc': 'FText system, supports plurals and formatting',
            'i18n-unreal-resource': 'Resource Localization',
            'i18n-unreal-resource-desc': 'Asset localization system, multi-language resource management',
            'i18n-unreal-tools': 'Tool Support',
            'i18n-unreal-tools-desc': 'Localization Dashboard, Translation Editor',
            'i18n-unreal-impl': 'Technical Implementation',
            'i18n-unreal-impl-desc': 'Automatic text gathering, XLIFF standard support',
            'i18n-kanzi-text': 'Text Management',
            'i18n-kanzi-text-desc': 'String management system, multi-language text support',
            'i18n-kanzi-resource': 'Resource Localization',
            'i18n-kanzi-resource-desc': 'Embedded-optimized resource management system',
            'i18n-kanzi-tools': 'Tool Support',
            'i18n-kanzi-tools-desc': 'Kanzi Studio integrated localization tools',
            'i18n-kanzi-impl': 'Technical Implementation',
            'i18n-kanzi-impl-desc': 'Real-time language switching, automotive industry standards',
            'i18n-cocos-text': 'Text Management',
            'i18n-cocos-text-desc': 'Multi-language manager, JSON/CSV format',
            'i18n-cocos-resource': 'Resource Localization',
            'i18n-cocos-resource-desc': 'Resource management system, multi-language resource variants',
            'i18n-cocos-tools': 'Tool Support',
            'i18n-cocos-tools-desc': 'JSON/CSV workflow, editor integration',
            'i18n-cocos-impl': 'Technical Implementation',
            'i18n-cocos-impl-desc': 'Hot-update support, mobile optimization',
            'i18n-godot-text': 'Text Management',
            'i18n-godot-text-desc': 'CSV translation system, tr() function support',
            'i18n-godot-resource': 'Resource Localization',
            'i18n-godot-resource-desc': 'Resource remapping, multi-language resource management',
            'i18n-godot-tools': 'Tool Support',
            'i18n-godot-tools-desc': 'Built-in CSV editor, community tools',
            'i18n-godot-impl': 'Technical Implementation',
            'i18n-godot-impl-desc': 'Runtime language switching, open-source extensions',
            'i18n-table-title': 'Internationalization Feature Comparison',
            'i18n-table-feature': 'Technical Feature',
            'i18n-table-text-management': 'Text Management System',
            'i18n-table-text-management-qt': 'QTranslator (.ts/.qm)',
            'i18n-table-text-management-unity': 'Localization Package',
            'i18n-table-text-management-unreal': 'FText System',
            'i18n-table-text-management-kanzi': 'String Management',
            'i18n-table-text-management-cocos': 'Multi-language Manager',
            'i18n-table-text-management-godot': 'CSV Translation System',
            'i18n-table-resource': 'Resource Localization',
            'i18n-table-resource-qt': 'Resource System Variants',
            'i18n-table-resource-unity': 'Addressables',
            'i18n-table-resource-unreal': 'Asset Localization',
            'i18n-table-resource-kanzi': 'Embedded Resource Mgmt',
            'i18n-table-resource-cocos': 'Resource Mgmt System',
            'i18n-table-resource-godot': 'Resource Remapping',
            'i18n-table-rtl': 'RTL Language Support',
            'i18n-table-rtl-qt': 'Full Support',
            'i18n-table-rtl-unity': 'Plugin Required',
            'i18n-table-rtl-unreal': 'Built-in Support',
            'i18n-table-rtl-kanzi': 'Basic Support',
            'i18n-table-rtl-cocos': 'Basic Support',
            'i18n-table-rtl-godot': 'Community Extension',
            'i18n-table-plural': 'Plurals and Formatting',
            'i18n-table-plural-qt': 'QLocale Support',
            'i18n-table-plural-unity': 'Smart Format',
            'i18n-table-plural-unreal': 'Advanced Formatting',
            'i18n-table-plural-kanzi': 'Basic Support',
            'i18n-table-plural-cocos': 'Intl API',
            'i18n-table-plural-godot': 'Basic Support',
            'i18n-table-realtime': 'Real-time Language Switching',
            'i18n-table-realtime-qt': 'Supported',
            'i18n-table-realtime-unity': 'Supported',
            'i18n-table-realtime-unreal': 'Supported',
            'i18n-table-realtime-kanzi': 'Optimized Performance',
            'i18n-table-realtime-cocos': 'Hot Update',
            'i18n-table-realtime-godot': 'Runtime Switching',
            'i18n-table-tools': 'Development Tools',
            'i18n-table-tools-qt': 'Qt Linguist',
            'i18n-table-tools-unity': 'CSV Workflow',
            'i18n-table-tools-unreal': 'Translation Editor',
            'i18n-table-tools-kanzi': 'Kanzi Studio',
            'i18n-table-tools-cocos': 'JSON/CSV',
            'i18n-table-tools-godot': 'Built-in CSV Editor',
            'i18n-table-difficulty': 'Learning Curve',
            'i18n-table-difficulty-qt': 'Medium',
            'i18n-table-difficulty-unity': 'Easy',
            'i18n-table-difficulty-unreal': 'Complex',
            'i18n-table-difficulty-kanzi': 'Complex',
            'i18n-table-difficulty-cocos': 'Easy',
            'i18n-table-difficulty-godot': 'Easy',
            'i18n-table-note': '<strong>Note:</strong> The comparison above is based on official engine documentation, and actual features may vary with version updates. It is recommended to choose the appropriate technical solution based on specific project requirements.',
            'comparison-title': 'Engine Comparison Analysis',
            'comparison-intro': 'Detailed comparison of the features, pros, cons, and application scenarios of Qt 3D and other mainstream 3D engines.',
            'comparison-table-feature': 'Feature',
            'comparison-table-dev': 'Developer',
            'comparison-table-lang': 'Main Language',
            'comparison-table-license': 'License',
            'license-qt': 'Commercial/Open Source',
            'license-unity': 'Free/Commercial',
            'license-unreal': 'Free/Commercial',
            'license-kanzi': 'Commercial',
            'license-cocos': 'Free/Open Source',
            'license-godot': 'MIT Open Source',
            'comparison-table-platform': 'Cross-platform Support',
            'comparison-table-platform-qt3d': 'Desktop, Mobile, Embedded',
            'comparison-table-platform-unity': 'Desktop, Mobile, Major Consoles, Web',
            'comparison-table-platform-unreal': 'Desktop, Mobile, Major Consoles, VR/AR',
            'comparison-table-platform-kanzi': 'Embedded, Automotive, Industrial',
            'comparison-table-platform-cocos': 'Mobile, Web, Desktop',
            'comparison-table-platform-godot': 'Desktop, Mobile, Web, Some Consoles (Community)',
            'comparison-table-difficulty': 'Learning Curve',
            'comparison-table-difficulty-qt3d': 'For developers with Qt experience',
            'comparison-table-difficulty-unity': 'For beginners and professionals',
            'comparison-table-difficulty-unreal': 'For users with some development experience',
            'comparison-table-difficulty-kanzi': 'For embedded and HMI professionals',
            'comparison-table-difficulty-cocos': 'For web and mobile developers',
            'comparison-table-difficulty-godot': 'For indie developers and education',
            'comparison-table-render': 'Rendering Quality',
            'comparison-table-render-qt3d': 'Supports OpenGL/Vulkan/Metal, 2D/3D integration',
            'comparison-table-render-unity': 'Supports URP/HDRP, rich post-processing and real-time rendering',
            'comparison-table-render-unreal': 'Supports Lumen, Nanite and other advanced rendering',
            'comparison-table-render-kanzi': 'Real-time rendering optimized for embedded',
            'comparison-table-render-cocos': 'Efficient 2D/3D rendering, mobile optimized',
            'comparison-table-render-godot': 'Supports OpenGL ES/Vulkan/WebGL, lightweight architecture',
            'pros-title': 'Highlights',
            'use-cases-title': 'Project Types',
            'pros-title': 'Highlights',
            'use-cases-title': 'Project Types',
            'qt3d-pros-1': 'Deep integration with Qt ecosystem',
            'qt3d-pros-2': 'Cross-platform consistency',
            'qt3d-pros-3': 'Enterprise-level support',
            'qt3d-pros-4': 'Lightweight runtime',
            'qt3d-use-1': 'Enterprise applications',
            'qt3d-use-2': 'Industrial software',
            'qt3d-use-3': 'Embedded systems',
            'qt3d-use-4': 'Cross-platform applications',
            'unity-pros-1': 'Easy to learn and use',
            'unity-pros-2': 'Rich resources and plugins',
            'unity-pros-3': 'Active developer community',
            'unity-pros-4': 'Multi-platform publishing capability',
            'unity-use-1': 'Indie games',
            'unity-use-2': 'Mobile games',
            'unity-use-3': 'AR/VR games',
            'unity-use-4': 'Educational training',
            'unreal-pros-1': 'High-quality real-time rendering',
            'unreal-pros-2': 'Blueprint visual scripting',
            'unreal-pros-3': 'Complete development toolchain',
            'unreal-pros-4': 'Rich industry applications',
            'unreal-use-1': 'Large-scale 3D games',
            'unreal-use-2': 'Film production',
            'unreal-use-3': 'Architectural visualization',
            'unreal-use-4': 'VR/AR experiences',
            'kanzi-pros-1': 'Embedded system optimization',
            'kanzi-pros-2': 'Low resource consumption',
            'kanzi-pros-3': 'Real-time performance',
            'kanzi-pros-4': 'Automotive industry standards',
            'kanzi-use-1': 'Automotive dashboards',
            'kanzi-use-2': 'Industrial control',
            'kanzi-use-3': 'Medical devices',
            'kanzi-use-4': 'Consumer electronics',
            'cocos-pros-1': 'Lightweight and efficient',
            'cocos-pros-2': 'Powerful 2D capabilities',
            'cocos-pros-3': 'Widely used in Asia',
            'cocos-pros-4': 'Complete toolchain',
            'cocos-use-1': 'Mobile games',
            'cocos-use-2': 'Lightweight 3D projects',
            'cocos-use-3': 'H5 mini-games',
            'cocos-use-4': 'Educational applications',
            'godot-pros-1': 'Completely free and open source',
            'godot-pros-2': 'Easy-to-use node system',
            'godot-pros-3': 'Multi-language support',
            'godot-pros-4': 'Lightweight architecture',
            'godot-use-1': 'Indie games',
            'godot-use-2': 'Educational projects',
            'godot-use-3': 'Rapid prototyping',
            'godot-use-4': 'Multi-platform publishing',
            'i18n-support-title': 'Internationalization Support',
            'i18n-global-dev': 'Global Application Development',
            'i18n-global-desc': 'Qt 3D provides complete internationalization and localization support, enabling your 3D application to easily adapt to global markets.',
            'i18n-feature-multi-lang': 'Multi-language text support',
            'i18n-feature-unicode': 'Unicode character rendering',
            'i18n-feature-l10n-res': 'Localized resource management',
            'i18n-feature-rtl': 'Text direction adaptation',
            'i18n-feature-dynamic-switch': 'Dynamic language switching',
            'btn-learn-more': 'Learn More',
            'lang-sample-zh-title': '中文',
            'lang-sample-zh-text': '欢迎使用 Qt 3D 引擎',
            'lang-sample-en-title': 'English',
            'lang-sample-en-text': 'Welcome to Qt 3D Engine',
            'lang-sample-ja-title': '日本語',
            'lang-sample-ja-text': 'Qt 3D エンジンへようこそ',
            'lang-sample-ar-title': 'العربية',
            'lang-sample-ar-text': 'مرحباً بك في محرك Qt 3D',
            'docs-title': 'Documentation',
            'docs-guide-title': 'Development Guide',
            'docs-guide-desc': 'A complete development guide for Qt 3D, including environment setup, core concepts, and best practices.',
            'docs-guide-link': 'View Documentation',
            'docs-comparison-title': 'Engine Comparison',
            'docs-comparison-desc': 'A detailed comparison of the features and application scenarios of Qt 3D and other mainstream 3D engines.',
            'docs-comparison-link': 'View Comparison',
            'docs-i18n-title': 'Internationalization Guide',
            'docs-i18n-desc': 'A guide to internationalization and localization development for Qt 3D applications.',
            'docs-i18n-link': 'View Guide',
            'footer-title': 'Qt 3D Development Guide',
            'footer-subtitle': 'Professional Cross-Platform 3D Application Development Solution',
            'footer-docs': 'Docs',
            'footer-link-guide': 'Dev Guide',
            'footer-link-comparison': 'Comparison',
            'footer-link-i18n': 'I18n',
            'footer-resources': 'Resources',
            'footer-link-official-docs': 'Official Docs',
            'footer-link-github': 'GitHub',
            'footer-link-forum': 'Community Forum',
            'footer-copyright': '&copy; 2024 Qt 3D Development Guide. Built with the Qt Framework.',
            'rendering-godot-api': '<span class="feature-name">Rendering API:</span> OpenGL ES, Vulkan, WebGL',
            'rendering-godot-lighting': '<span class="feature-name">Lighting:</span> Real-time lighting, baked lighting',
            'rendering-godot-material': '<span class="feature-name">Material:</span> Shader Editor, Visual Shader',
            'rendering-godot-posteffect': '<span class="feature-name">Post-processing:</span> Basic post-processing effects',
            'dev-godot-lang': '<span class="feature-name">Language:</span> GDScript, C#, VisualScript',
            'dev-godot-ide': '<span class="feature-name">IDE:</span> Godot Editor',
            'dev-godot-debug': '<span class="feature-name">Debugging:</span> Built-in debugger, Profiler',
            'dev-godot-curve': '<span class="feature-name">Learning Curve:</span> Easy (Node system)',
            'platform-godot-desktop': '<span class="feature-name">Desktop:</span> Windows, macOS, Linux',
            'platform-godot-mobile': '<span class="feature-name">Mobile:</span> Android, iOS',
            'platform-godot-web': '<span class="feature-name">Web:</span> HTML5',
            'platform-godot-console': '<span class="feature-name">Console:</span> Community supported',
            // 英文
            'dev-qt3d': 'Qt Company',
            'dev-unity': 'Unity Technologies',
            'dev-unreal': 'Epic Games',
            'dev-kanzi': 'Rightware',
            'dev-cocos': 'Xiamen Yaji Software',
            'dev-godot': 'Godot Community',
            'lang-qt3d': 'C++/QML',
            'lang-unity': 'C#/JavaScript',
            'lang-unreal': 'C++/Blueprint',
            'lang-kanzi': 'C++',
            'lang-cocos': 'TypeScript/JavaScript',
            'lang-godot': 'GDScript/C#/C++',
        }
    };

    const i18nData = translations[lang] || translations['zh'];

    document.querySelectorAll('[data-i18n]').forEach(el => {
        const key = el.getAttribute('data-i18n');
        if (i18nData[key]) {
            // 如果是 input 或 select，更新 value
            if (el.tagName === 'INPUT' || el.tagName === 'SELECT' || el.tagName === 'TEXTAREA') {
                if (el.placeholder) {
                    el.placeholder = i18nData[key];
                } else {
                    el.value = i18nData[key];
                }
            } else {
                // 否则，更新 innerHTML
                el.innerHTML = i18nData[key];
            }
        }
    });

    // Handle language switching for document content
    const contentDivs = document.querySelectorAll('.doc-content > [lang]');
    if (contentDivs.length > 0) {
        contentDivs.forEach(div => {
            if (div.getAttribute('lang') === lang) {
                div.style.display = 'block';
            } else {
                div.style.display = 'none';
            }
        });
    }

    // 更新页面 lang 属性
    document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
}

// 国际化演示
function initI18nDemo() {
    const languageSamples = document.querySelectorAll('.language-sample');
    
    languageSamples.forEach(sample => {
        sample.addEventListener('click', function() {
            // 移除所有活动状态
            languageSamples.forEach(s => s.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            
            // 添加点击动画
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
}

// 动画效果
function initAnimations() {
    // 滚动动画
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    const animateElements = document.querySelectorAll('.feature-card, .engine-card, .doc-card');
    animateElements.forEach(el => {
        observer.observe(el);
    });
    
    // 添加CSS动画类
    const style = document.createElement('style');
    style.textContent = `
        .feature-card, .engine-card, .doc-card {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .animate-in {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }
    `;
    document.head.appendChild(style);
    
    // 立方体悬停效果
    const heroCube = document.querySelector('.hero-cube');
    if (heroCube) {
        heroCube.addEventListener('mouseenter', function() {
            this.style.animationPlayState = 'paused';
        });
        
        heroCube.addEventListener('mouseleave', function() {
            this.style.animationPlayState = 'running';
        });
    }
}

// 响应式导航
function initMobileNav() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            this.classList.toggle('active');
        });
        
        // 点击导航链接时关闭菜单
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            });
        });
    }
}

// 工具函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 性能优化的滚动处理
const optimizedScrollHandler = throttle(function() {
    highlightCurrentSection();
}, 100);

window.addEventListener('scroll', optimizedScrollHandler);

// 页面加载完成后的初始化
window.addEventListener('load', function() {
    // 预加载图片
    preloadImages();
    
    // 初始化工具提示
    initTooltips();
    
    // 添加键盘导航支持
    initKeyboardNavigation();
});

// 预加载图片
function preloadImages() {
    const imageUrls = [
        'assets/images/qt-logo.svg',
        'assets/images/favicon.ico'
    ];
    
    imageUrls.forEach(url => {
        const img = new Image();
        img.src = url;
    });
}

// 工具提示
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(e) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = e.target.getAttribute('data-tooltip');
    document.body.appendChild(tooltip);
    
    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
}

function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// 特性对比标签页
function initFeatureTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // 移除所有活动状态
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // 添加当前活动状态
            this.classList.add('active');
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // 添加切换动画
            animateTabSwitch(targetContent);
        });
    });
}

function animateTabSwitch(targetContent) {
    if (!targetContent) return;

    // 添加淡入动画
    targetContent.style.opacity = '0';
    targetContent.style.transform = 'translateY(20px)';

    setTimeout(() => {
        targetContent.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        targetContent.style.opacity = '1';
        targetContent.style.transform = 'translateY(0)';
    }, 50);
}

// 键盘导航支持
function initKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        // ESC键关闭移动菜单
        if (e.key === 'Escape') {
            const navMenu = document.querySelector('.nav-menu');
            const navToggle = document.querySelector('.nav-toggle');
            if (navMenu && navMenu.classList.contains('active')) {
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            }
        }

        // Tab键导航增强
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }

        // 方向键切换标签页
        if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
            const activeTab = document.querySelector('.tab-button.active');
            if (activeTab) {
                const tabButtons = Array.from(document.querySelectorAll('.tab-button'));
                const currentIndex = tabButtons.indexOf(activeTab);
                let nextIndex;

                if (e.key === 'ArrowLeft') {
                    nextIndex = currentIndex > 0 ? currentIndex - 1 : tabButtons.length - 1;
                } else {
                    nextIndex = currentIndex < tabButtons.length - 1 ? currentIndex + 1 : 0;
                }

                tabButtons[nextIndex].click();
                tabButtons[nextIndex].focus();
                e.preventDefault();
            }
        }
    });

    // 鼠标点击时移除键盘导航样式
    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });
}

// 错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
    // 可以在这里添加错误报告逻辑
});

// 性能监控
if ('performance' in window) {
    window.addEventListener('load', function() {
        setTimeout(function() {
            const perfData = performance.timing;
            const loadTime = perfData.loadEventEnd - perfData.navigationStart;
            console.log('页面加载时间:', loadTime + 'ms');
        }, 0);
    });
}

// 生成文档目录
function generateTableOfContents() {
    const content = document.querySelector('.doc-content');
    const toc = document.querySelector('.toc-container');
    
    if (!content || !toc) return;

    const headings = content.querySelectorAll('h1, h2, h3, h4');
    if (headings.length === 0) return;

    const tocList = document.createElement('ul');
    const tocStack = [{ element: tocList, level: 0 }];

    headings.forEach((heading, index) => {
        // 为每个标题添加ID
        if (!heading.id) {
            heading.id = `heading-${index}`;
        }

        const level = parseInt(heading.tagName.charAt(1));
        const listItem = document.createElement('li');
        const link = document.createElement('a');
        
        link.textContent = heading.textContent;
        link.href = `#${heading.id}`;
        
        // 点击目录项时滚动到对应位置
        link.addEventListener('click', (e) => {
            e.preventDefault();
            heading.scrollIntoView({ behavior: 'smooth' });
            window.history.pushState(null, '', link.href);
        });

        listItem.appendChild(link);

        // 处理目录层级
        while (level <= tocStack[tocStack.length - 1].level) {
            tocStack.pop();
        }

        if (level > tocStack[tocStack.length - 1].level) {
            const newList = document.createElement('ul');
            tocStack[tocStack.length - 1].element.lastChild?.appendChild(newList);
            tocStack.push({ element: newList, level });
        }

        tocStack[tocStack.length - 1].element.appendChild(listItem);
    });

    toc.appendChild(tocList);

    // 监听滚动，高亮当前阅读的部分
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const id = entry.target.id;
                document.querySelectorAll('.toc-container a').forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${id}`) {
                        link.classList.add('active');
                    }
                });
            }
        });
    }, { threshold: 0.5 });

    headings.forEach(heading => observer.observe(heading));
}

// 页面加载完成后生成目录
document.addEventListener('DOMContentLoaded', () => {
    generateTableOfContents();
});
