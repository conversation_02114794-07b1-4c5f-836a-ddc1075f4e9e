# Kanzi 国际化与本地化指南

## 目录
1. [概述](#概述)
2. [Kanzi 本地化架构](#kanzi-本地化架构)
3. [文本本地化](#文本本地化)
4. [资源本地化](#资源本地化)
5. [HMI 界面适配](#hmi-界面适配)
6. [汽车行业特殊需求](#汽车行业特殊需求)
7. [Kanzi Studio 工具](#kanzi-studio-工具)
8. [最佳实践](#最佳实践)

## 概述

Kanzi 作为专业的汽车 HMI 开发平台，提供了完整的国际化和本地化解决方案，特别针对汽车行业的多语言需求进行了优化。Kanzi 的本地化系统支持实时语言切换、复杂的文本布局、多文化界面适配等功能，确保汽车 HMI 系统能够满足全球不同市场的需求。

### 主要特性
- **实时语言切换**：运行时无缝切换语言，无需重启系统
- **多文化界面适配**：支持从右到左文本、不同字体系统
- **资源本地化**：图标、图片、音频的多语言版本管理
- **汽车标准兼容**：符合汽车行业的安全和质量标准
- **性能优化**：针对嵌入式系统的内存和性能优化
- **工具链集成**：Kanzi Studio 提供完整的本地化工作流

## Kanzi 本地化架构

### 本地化数据管理
```cpp
// Kanzi 本地化数据源
#include <kanzi/kanzi.hpp>
#include <kanzi/core/resource/resource_manager.hpp>

using namespace kanzi;

class LocalizationDataSource : public DataObject
{
    KZ_METACLASS_BEGIN(LocalizationDataSource, DataObject, "LocalizationDataSource")
        KZ_METACLASS_PROPERTY_TYPE(CurrentLanguageProperty)
        KZ_METACLASS_PROPERTY_TYPE(AvailableLanguagesProperty)
    KZ_METACLASS_END()

public:
    static PropertyType<string> CurrentLanguageProperty;
    static PropertyType<StringVector> AvailableLanguagesProperty;

    explicit LocalizationDataSource(Domain* domain, string_view name)
        : DataObject(domain, name)
    {
        initializeLanguages();
        loadCurrentLanguage();
    }

    void setLanguage(const string& languageCode)
    {
        if (isLanguageSupported(languageCode))
        {
            setProperty(CurrentLanguageProperty, languageCode);
            loadLanguageResources(languageCode);
            notifyLanguageChanged();
        }
    }

    string getCurrentLanguage() const
    {
        return getProperty(CurrentLanguageProperty);
    }

    StringVector getAvailableLanguages() const
    {
        return getProperty(AvailableLanguagesProperty);
    }

private:
    void initializeLanguages()
    {
        StringVector languages = {
            "en-US",    // 英语（美国）
            "zh-CN",    // 简体中文
            "ja-JP",    // 日语
            "de-DE",    // 德语
            "fr-FR",    // 法语
            "es-ES",    // 西班牙语
            "ar-SA",    // 阿拉伯语
            "ko-KR"     // 韩语
        };

        setProperty(AvailableLanguagesProperty, languages);
    }

    void loadCurrentLanguage()
    {
        // 从系统设置或用户偏好加载当前语言
        string systemLanguage = getSystemLanguage();
        if (isLanguageSupported(systemLanguage))
        {
            setProperty(CurrentLanguageProperty, systemLanguage);
        }
        else
        {
            setProperty(CurrentLanguageProperty, "en-US"); // 默认英语
        }
    }

    bool isLanguageSupported(const string& languageCode) const
    {
        StringVector languages = getAvailableLanguages();
        return find(languages.begin(), languages.end(), languageCode) != languages.end();
    }

    void loadLanguageResources(const string& languageCode)
    {
        // 加载语言特定的资源包
        string resourcePath = "localization/" + languageCode + "/";

        // 加载文本资源
        loadTextResources(resourcePath + "strings.json");

        // 加载图像资源
        loadImageResources(resourcePath + "images/");

        // 加载音频资源
        loadAudioResources(resourcePath + "audio/");
    }

    string getSystemLanguage() const
    {
        // 从系统获取语言设置
        // 这里是示例实现
        return "en-US";
    }

    void notifyLanguageChanged()
    {
        // 通知所有监听器语言已更改
        MessageArguments args;
        dispatchMessage(LanguageChangedMessage::create(args));
    }
};

// 属性定义
PropertyType<string> LocalizationDataSource::CurrentLanguageProperty(
    kzMakeFixedString("LocalizationDataSource.CurrentLanguage"), "en-US");
PropertyType<StringVector> LocalizationDataSource::AvailableLanguagesProperty(
    kzMakeFixedString("LocalizationDataSource.AvailableLanguages"), StringVector());
```

### 本地化字符串管理
```cpp
// 本地化字符串管理器
class LocalizedStringManager
{
public:
    static LocalizedStringManager& getInstance()
    {
        static LocalizedStringManager instance;
        return instance;
    }

    void loadStrings(const string& languageCode)
    {
        string filePath = "localization/" + languageCode + "/strings.json";

        // 解析 JSON 文件
        Json::Value root;
        ifstream file(filePath);
        if (file.is_open())
        {
            file >> root;
            parseStringData(root, languageCode);
        }
    }

    string getString(const string& key, const string& defaultValue = "") const
    {
        string currentLanguage = getCurrentLanguage();
        string fullKey = currentLanguage + "." + key;

        auto it = m_strings.find(fullKey);
        if (it != m_strings.end())
        {
            return it->second;
        }

        // 回退到英语
        string fallbackKey = "en-US." + key;
        auto fallbackIt = m_strings.find(fallbackKey);
        if (fallbackIt != m_strings.end())
        {
            return fallbackIt->second;
        }

        return defaultValue;
    }

    string getFormattedString(const string& key, const vector<string>& args) const
    {
        string pattern = getString(key);
        return formatString(pattern, args);
    }

private:
    map<string, string> m_strings;

    void parseStringData(const Json::Value& root, const string& languageCode)
    {
        for (const auto& key : root.getMemberNames())
        {
            string fullKey = languageCode + "." + key;
            m_strings[fullKey] = root[key].asString();
        }
    }

    string formatString(const string& pattern, const vector<string>& args) const
    {
        string result = pattern;
        for (size_t i = 0; i < args.size(); ++i)
        {
            string placeholder = "{" + to_string(i) + "}";
            size_t pos = result.find(placeholder);
            if (pos != string::npos)
            {
                result.replace(pos, placeholder.length(), args[i]);
            }
        }
        return result;
    }

    string getCurrentLanguage() const
    {
        // 获取当前语言设置
        return "en-US"; // 示例
    }
};
```

## 文本本地化

### 本地化文本组件
```cpp
// 本地化文本节点
class LocalizedTextNode : public Text2D
{
    KZ_COMPONENT(LocalizedTextNode)

public:
    static PropertyType<string> TextKeyProperty;
    static PropertyType<StringVector> FormatArgumentsProperty;

    explicit LocalizedTextNode(Domain* domain, string_view name)
        : Text2D(domain, name)
    {
        // 监听语言变化
        addPropertyNotificationHandler(TextKeyProperty,
            bind(&LocalizedTextNode::onTextKeyChanged, this, placeholders::_1));

        // 监听格式参数变化
        addPropertyNotificationHandler(FormatArgumentsProperty,
            bind(&LocalizedTextNode::onFormatArgumentsChanged, this, placeholders::_1));
    }

    void initialize() override
    {
        Text2D::initialize();
        updateLocalizedText();

        // 注册语言变化监听
        registerLanguageChangeListener();
    }

private:
    void onTextKeyChanged(PropertyObject& object)
    {
        updateLocalizedText();
    }

    void onFormatArgumentsChanged(PropertyObject& object)
    {
        updateLocalizedText();
    }

    void updateLocalizedText()
    {
        string textKey = getProperty(TextKeyProperty);
        if (textKey.empty()) return;

        StringVector formatArgs = getProperty(FormatArgumentsProperty);

        string localizedText;
        if (formatArgs.empty())
        {
            localizedText = LocalizedStringManager::getInstance().getString(textKey);
        }
        else
        {
            vector<string> args(formatArgs.begin(), formatArgs.end());
            localizedText = LocalizedStringManager::getInstance().getFormattedString(textKey, args);
        }

        setText(localizedText);

        // 根据语言调整字体和布局
        adjustForCurrentLanguage();
    }

    void adjustForCurrentLanguage()
    {
        string currentLanguage = getCurrentLanguage();

        // 设置合适的字体
        FontSharedPtr font = getLocalizedFont(currentLanguage);
        if (font)
        {
            setFont(font);
        }

        // 调整文本对齐方式
        if (isRightToLeftLanguage(currentLanguage))
        {
            setHorizontalAlignment(TextConcept::HorizontalAlignment::Right);
        }
        else
        {
            setHorizontalAlignment(TextConcept::HorizontalAlignment::Left);
        }

        // 调整字体大小
        float fontSize = getLocalizedFontSize(currentLanguage);
        setFontSize(fontSize);
    }

    FontSharedPtr getLocalizedFont(const string& languageCode)
    {
        // 根据语言选择合适的字体
        if (languageCode.find("zh") == 0) // 中文
        {
            return getResourceManager()->acquireResource<Font>("NotoSansCJK");
        }
        else if (languageCode.find("ja") == 0) // 日文
        {
            return getResourceManager()->acquireResource<Font>("NotoSansJP");
        }
        else if (languageCode.find("ar") == 0) // 阿拉伯文
        {
            return getResourceManager()->acquireResource<Font>("NotoSansArabic");
        }
        else if (languageCode.find("ko") == 0) // 韩文
        {
            return getResourceManager()->acquireResource<Font>("NotoSansKR");
        }
        else // 默认拉丁字体
        {
            return getResourceManager()->acquireResource<Font>("NotoSans");
        }
    }

    bool isRightToLeftLanguage(const string& languageCode)
    {
        return languageCode.find("ar") == 0 || languageCode.find("he") == 0 ||
               languageCode.find("fa") == 0 || languageCode.find("ur") == 0;
    }

    float getLocalizedFontSize(const string& languageCode)
    {
        // 某些语言可能需要调整字体大小
        if (languageCode.find("zh") == 0 || languageCode.find("ja") == 0 || languageCode.find("ko") == 0)
        {
            return getFontSize() * 1.1f; // CJK 字符稍大一些
        }
        return getFontSize();
    }

    void registerLanguageChangeListener()
    {
        // 注册语言变化监听器
        // 实际实现中需要连接到语言变化事件
    }

    string getCurrentLanguage()
    {
        // 获取当前语言设置
        return "en-US"; // 示例
    }
};

// 属性定义
PropertyType<string> LocalizedTextNode::TextKeyProperty(
    kzMakeFixedString("LocalizedTextNode.TextKey"), "");
PropertyType<StringVector> LocalizedTextNode::FormatArgumentsProperty(
    kzMakeFixedString("LocalizedTextNode.FormatArguments"), StringVector());
```

## 资源本地化

### 本地化图像管理
```cpp
// 本地化图像资源管理器
class LocalizedImageManager
{
public:
    static LocalizedImageManager& getInstance()
    {
        static LocalizedImageManager instance;
        return instance;
    }

    TextureSharedPtr getLocalizedTexture(const string& textureKey)
    {
        string currentLanguage = getCurrentLanguage();
        string localizedKey = textureKey + "_" + currentLanguage;

        // 检查缓存
        auto it = m_textureCache.find(localizedKey);
        if (it != m_textureCache.end())
        {
            return it->second;
        }

        // 尝试加载本地化版本
        string localizedPath = "localization/" + currentLanguage + "/images/" + textureKey + ".png";
        TextureSharedPtr texture = loadTexture(localizedPath);

        if (!texture)
        {
            // 回退到默认版本
            string defaultPath = "localization/en-US/images/" + textureKey + ".png";
            texture = loadTexture(defaultPath);
        }

        if (texture)
        {
            m_textureCache[localizedKey] = texture;
        }

        return texture;
    }

    void preloadLanguageTextures(const string& languageCode)
    {
        string imagePath = "localization/" + languageCode + "/images/";

        // 预加载常用图标
        vector<string> commonIcons = {
            "home_icon", "settings_icon", "navigation_icon",
            "music_icon", "phone_icon", "climate_icon",
            "fuel_icon", "speed_icon", "warning_icon"
        };

        for (const string& icon : commonIcons)
        {
            string localizedKey = icon + "_" + languageCode;
            if (m_textureCache.find(localizedKey) == m_textureCache.end())
            {
                TextureSharedPtr texture = loadTexture(imagePath + icon + ".png");
                if (texture)
                {
                    m_textureCache[localizedKey] = texture;
                }
            }
        }
    }

    void clearCache()
    {
        m_textureCache.clear();
    }

private:
    map<string, TextureSharedPtr> m_textureCache;

    TextureSharedPtr loadTexture(const string& path)
    {
        // 实际的纹理加载实现
        return Texture::createFromFile(getDomain(), path);
    }

    string getCurrentLanguage()
    {
        // 获取当前语言设置
        return "en-US"; // 示例
    }
};
```

### 本地化图像组件
```cpp
// 自动本地化的图像节点
class LocalizedImageNode : public Image2D
{
    KZ_COMPONENT(LocalizedImageNode)

public:
    static PropertyType<string> ImageKeyProperty;

    explicit LocalizedImageNode(Domain* domain, string_view name)
        : Image2D(domain, name)
    {
        addPropertyNotificationHandler(ImageKeyProperty,
            bind(&LocalizedImageNode::onImageKeyChanged, this, placeholders::_1));
    }

    void initialize() override
    {
        Image2D::initialize();
        updateLocalizedImage();
        registerLanguageChangeListener();
    }

private:
    void onImageKeyChanged(PropertyObject& object)
    {
        updateLocalizedImage();
    }

    void updateLocalizedImage()
    {
        string imageKey = getProperty(ImageKeyProperty);
        if (imageKey.empty()) return;

        TextureSharedPtr localizedTexture =
            LocalizedImageManager::getInstance().getLocalizedTexture(imageKey);

        if (localizedTexture)
        {
            setTexture(localizedTexture);
        }
    }

    void registerLanguageChangeListener()
    {
        // 注册语言变化监听器
        // 当语言变化时重新加载图像
    }
};

PropertyType<string> LocalizedImageNode::ImageKeyProperty(
    kzMakeFixedString("LocalizedImageNode.ImageKey"), "");
```

## HMI 界面适配

### 汽车仪表盘本地化
```cpp
// 本地化汽车仪表盘
class LocalizedCarDashboard : public Node2D
{
public:
    static Node2DSharedPtr create(Domain* domain, string_view name)
    {
        return Node2DSharedPtr(new LocalizedCarDashboard(domain, name));
    }

protected:
    explicit LocalizedCarDashboard(Domain* domain, string_view name)
        : Node2D(domain, name)
    {
        initialize();
    }

    void initialize()
    {
        createSpeedometer();
        createNavigationDisplay();
        createClimateControls();
        createMediaControls();

        // 监听语言变化
        registerLanguageChangeListener();
    }

private:
    void createSpeedometer()
    {
        // 速度表背景
        LocalizedImageNode* speedometerBg = new LocalizedImageNode(getDomain(), "SpeedometerBackground");
        speedometerBg->setProperty(LocalizedImageNode::ImageKeyProperty, "speedometer_bg");
        speedometerBg->setTranslation(Vector2(200, 200));
        addChild(speedometerBg);

        // 速度单位文本（km/h 或 mph）
        LocalizedTextNode* speedUnit = new LocalizedTextNode(getDomain(), "SpeedUnit");
        speedUnit->setProperty(LocalizedTextNode::TextKeyProperty, "speed_unit");
        speedUnit->setTranslation(Vector2(200, 350));
        speedUnit->setHorizontalAlignment(TextConcept::HorizontalAlignment::Center);
        addChild(speedUnit);

        // 当前速度显示
        LocalizedTextNode* currentSpeed = new LocalizedTextNode(getDomain(), "CurrentSpeed");
        currentSpeed->setProperty(LocalizedTextNode::TextKeyProperty, "current_speed");
        currentSpeed->setTranslation(Vector2(200, 250));
        currentSpeed->setFontSize(36);
        addChild(currentSpeed);
    }

    void createNavigationDisplay()
    {
        Node2D* navPanel = Node2D::create(getDomain(), "NavigationPanel");
        navPanel->setTranslation(Vector2(500, 100));
        navPanel->setWidth(300);
        navPanel->setHeight(200);

        // 导航指示图标
        LocalizedImageNode* navIcon = new LocalizedImageNode(getDomain(), "NavigationIcon");
        navIcon->setProperty(LocalizedImageNode::ImageKeyProperty, "nav_turn_right");
        navIcon->setTranslation(Vector2(20, 20));
        navPanel->addChild(navIcon);

        // 距离显示
        LocalizedTextNode* distanceText = new LocalizedTextNode(getDomain(), "DistanceText");
        distanceText->setProperty(LocalizedTextNode::TextKeyProperty, "distance_to_turn");
        StringVector args = {"500", "meters"};
        distanceText->setProperty(LocalizedTextNode::FormatArgumentsProperty, args);
        distanceText->setTranslation(Vector2(80, 30));
        navPanel->addChild(distanceText);

        // 街道名称
        LocalizedTextNode* streetName = new LocalizedTextNode(getDomain(), "StreetName");
        streetName->setProperty(LocalizedTextNode::TextKeyProperty, "turn_onto_street");
        StringVector streetArgs = {"Main Street"};
        streetName->setProperty(LocalizedTextNode::FormatArgumentsProperty, streetArgs);
        streetName->setTranslation(Vector2(20, 80));
        navPanel->addChild(streetName);

        addChild(navPanel);
    }

    void createClimateControls()
    {
        Node2D* climatePanel = Node2D::create(getDomain(), "ClimatePanel");
        climatePanel->setTranslation(Vector2(50, 400));
        climatePanel->setWidth(250);
        climatePanel->setHeight(150);

        // 温度显示
        LocalizedTextNode* tempDisplay = new LocalizedTextNode(getDomain(), "TemperatureDisplay");
        tempDisplay->setProperty(LocalizedTextNode::TextKeyProperty, "temperature_display");
        StringVector tempArgs = {"22", "°C"};
        tempDisplay->setProperty(LocalizedTextNode::FormatArgumentsProperty, tempArgs);
        tempDisplay->setTranslation(Vector2(20, 20));
        tempDisplay->setFontSize(24);
        climatePanel->addChild(tempDisplay);

        // 气候控制按钮
        createClimateButton(climatePanel, "auto_climate", Vector2(20, 60));
        createClimateButton(climatePanel, "ac_button", Vector2(80, 60));
        createClimateButton(climatePanel, "heat_button", Vector2(140, 60));

        addChild(climatePanel);
    }

    void createClimateButton(Node2D* parent, const string& buttonKey, const Vector2& position)
    {
        Node2D* button = Node2D::create(getDomain(), buttonKey + "_button");
        button->setTranslation(position);
        button->setWidth(50);
        button->setHeight(40);

        // 按钮图标
        LocalizedImageNode* icon = new LocalizedImageNode(getDomain(), buttonKey + "_icon");
        icon->setProperty(LocalizedImageNode::ImageKeyProperty, buttonKey);
        icon->setTranslation(Vector2(10, 5));
        button->addChild(icon);

        // 按钮文本
        LocalizedTextNode* label = new LocalizedTextNode(getDomain(), buttonKey + "_label");
        label->setProperty(LocalizedTextNode::TextKeyProperty, buttonKey);
        label->setTranslation(Vector2(25, 25));
        label->setFontSize(10);
        label->setHorizontalAlignment(TextConcept::HorizontalAlignment::Center);
        button->addChild(label);

        parent->addChild(button);
    }

    void createMediaControls()
    {
        Node2D* mediaPanel = Node2D::create(getDomain(), "MediaPanel");
        mediaPanel->setTranslation(Vector2(350, 400));
        mediaPanel->setWidth(300);
        mediaPanel->setHeight(150);

        // 当前播放信息
        LocalizedTextNode* nowPlaying = new LocalizedTextNode(getDomain(), "NowPlaying");
        nowPlaying->setProperty(LocalizedTextNode::TextKeyProperty, "now_playing");
        nowPlaying->setTranslation(Vector2(20, 20));
        nowPlaying->setFontSize(14);
        mediaPanel->addChild(nowPlaying);

        // 歌曲信息（这里可能不需要本地化，但格式需要适配）
        Text2D* songInfo = Text2D::create(getDomain(), "SongInfo");
        songInfo->setText("Artist - Song Title");
        songInfo->setTranslation(Vector2(20, 40));
        songInfo->setFontSize(16);
        mediaPanel->addChild(songInfo);

        // 媒体控制按钮
        createMediaButton(mediaPanel, "prev_track", Vector2(50, 80));
        createMediaButton(mediaPanel, "play_pause", Vector2(100, 80));
        createMediaButton(mediaPanel, "next_track", Vector2(150, 80));

        addChild(mediaPanel);
    }

    void createMediaButton(Node2D* parent, const string& buttonKey, const Vector2& position)
    {
        Node2D* button = Node2D::create(getDomain(), buttonKey + "_button");
        button->setTranslation(position);
        button->setWidth(40);
        button->setHeight(40);

        LocalizedImageNode* icon = new LocalizedImageNode(getDomain(), buttonKey + "_icon");
        icon->setProperty(LocalizedImageNode::ImageKeyProperty, buttonKey);
        icon->setTranslation(Vector2(5, 5));
        button->addChild(icon);

        parent->addChild(button);
    }

    void registerLanguageChangeListener()
    {
        // 注册语言变化监听器
        // 当语言变化时，所有本地化组件会自动更新
    }
};
```

## 汽车行业特殊需求

### 安全关键信息本地化
```cpp
// 安全关键信息管理器
class SafetyCriticalMessageManager
{
public:
    enum class MessagePriority
    {
        Low,
        Medium,
        High,
        Critical
    };

    struct SafetyMessage
    {
        string messageKey;
        MessagePriority priority;
        bool requiresAcknowledgment;
        chrono::milliseconds displayDuration;
        string iconKey;
    };

    void showSafetyMessage(const SafetyMessage& message)
    {
        // 安全消息始终使用驾驶员首选语言
        string driverLanguage = getDriverPreferredLanguage();
        string previousLanguage = getCurrentLanguage();

        // 临时切换到驾驶员语言
        if (driverLanguage != previousLanguage)
        {
            setLanguage(driverLanguage);
        }

        // 显示安全消息
        displayMessage(message);

        // 恢复之前的语言设置
        if (driverLanguage != previousLanguage)
        {
            setLanguage(previousLanguage);
        }
    }

    void registerSafetyMessages()
    {
        // 注册各种安全消息
        m_safetyMessages["engine_warning"] = {
            "engine_warning",
            MessagePriority::Critical,
            true,
            chrono::milliseconds(0), // 持续显示直到确认
            "warning_engine"
        };

        m_safetyMessages["low_fuel"] = {
            "low_fuel_warning",
            MessagePriority::High,
            false,
            chrono::milliseconds(5000),
            "warning_fuel"
        };

        m_safetyMessages["seatbelt_reminder"] = {
            "seatbelt_reminder",
            MessagePriority::Medium,
            false,
            chrono::milliseconds(3000),
            "warning_seatbelt"
        };
    }

private:
    map<string, SafetyMessage> m_safetyMessages;

    string getDriverPreferredLanguage()
    {
        // 从驾驶员配置文件获取首选语言
        return "en-US"; // 示例
    }

    void displayMessage(const SafetyMessage& message)
    {
        // 实现安全消息显示逻辑
        // 包括图标、文本、优先级处理等
    }
};
```

### 法规遵循和标准化
```cpp
// 汽车法规遵循管理器
class AutomotiveComplianceManager
{
public:
    struct RegionalRequirements
    {
        string region;
        vector<string> requiredLanguages;
        map<string, string> unitPreferences; // 速度、温度等单位
        bool requiresRightHandDrive;
        vector<string> mandatoryWarnings;
    };

    void configureForRegion(const string& regionCode)
    {
        RegionalRequirements requirements = getRegionalRequirements(regionCode);

        // 配置语言支持
        configureLanguageSupport(requirements.requiredLanguages);

        // 配置单位系统
        configureUnits(requirements.unitPreferences);

        // 配置驾驶方向
        configureDrivingSide(requirements.requiresRightHandDrive);

        // 配置强制警告
        configureMandatoryWarnings(requirements.mandatoryWarnings);
    }

private:
    RegionalRequirements getRegionalRequirements(const string& regionCode)
    {
        RegionalRequirements requirements;

        if (regionCode == "EU")
        {
            requirements.region = "European Union";
            requirements.requiredLanguages = {"en-GB", "de-DE", "fr-FR", "es-ES", "it-IT"};
            requirements.unitPreferences["speed"] = "km/h";
            requirements.unitPreferences["temperature"] = "celsius";
            requirements.requiresRightHandDrive = false;
            requirements.mandatoryWarnings = {"seatbelt_warning", "speed_limit_warning"};
        }
        else if (regionCode == "US")
        {
            requirements.region = "United States";
            requirements.requiredLanguages = {"en-US", "es-US"};
            requirements.unitPreferences["speed"] = "mph";
            requirements.unitPreferences["temperature"] = "fahrenheit";
            requirements.requiresRightHandDrive = false;
            requirements.mandatoryWarnings = {"seatbelt_warning"};
        }
        else if (regionCode == "JP")
        {
            requirements.region = "Japan";
            requirements.requiredLanguages = {"ja-JP", "en-US"};
            requirements.unitPreferences["speed"] = "km/h";
            requirements.unitPreferences["temperature"] = "celsius";
            requirements.requiresRightHandDrive = true;
            requirements.mandatoryWarnings = {"seatbelt_warning", "door_open_warning"};
        }

        return requirements;
    }

    void configureLanguageSupport(const vector<string>& languages)
    {
        // 配置支持的语言列表
        for (const string& lang : languages)
        {
            enableLanguage(lang);
        }
    }

    void configureUnits(const map<string, string>& unitPreferences)
    {
        // 配置单位系统
        for (const auto& pair : unitPreferences)
        {
            setUnitPreference(pair.first, pair.second);
        }
    }

    void configureDrivingSide(bool rightHandDrive)
    {
        // 配置驾驶方向相关的UI布局
        if (rightHandDrive)
        {
            // 调整仪表盘布局为右舵车
            adjustDashboardForRHD();
        }
    }

    void configureMandatoryWarnings(const vector<string>& warnings)
    {
        // 配置强制警告消息
        for (const string& warning : warnings)
        {
            enableMandatoryWarning(warning);
        }
    }

    void enableLanguage(const string& languageCode) { /* 实现 */ }
    void setUnitPreference(const string& type, const string& unit) { /* 实现 */ }
    void adjustDashboardForRHD() { /* 实现 */ }
    void enableMandatoryWarning(const string& warning) { /* 实现 */ }
};
```

## Kanzi Studio 工具

### 本地化工作流
```cpp
// Kanzi Studio 本地化工具集成
class KanziStudioLocalizationTools
{
public:
    // 导出本地化字符串
    void exportStringsForTranslation(const string& outputPath)
    {
        Json::Value exportData;

        // 收集所有需要翻译的字符串
        collectLocalizableStrings(exportData);

        // 导出为翻译友好的格式
        exportToTranslationFormat(exportData, outputPath);
    }

    // 导入翻译结果
    void importTranslations(const string& translationFile)
    {
        Json::Value translations;
        ifstream file(translationFile);
        if (file.is_open())
        {
            file >> translations;
            processTranslations(translations);
        }
    }

    // 验证本地化资源
    void validateLocalizationResources()
    {
        vector<string> issues;

        // 检查缺失的翻译
        checkMissingTranslations(issues);

        // 检查资源文件
        checkMissingResources(issues);

        // 检查文本长度
        checkTextLength(issues);

        // 生成验证报告
        generateValidationReport(issues);
    }

private:
    void collectLocalizableStrings(Json::Value& exportData)
    {
        // 扫描项目文件，收集所有本地化字符串
        // 包括 .kzb 文件、脚本文件等
    }

    void exportToTranslationFormat(const Json::Value& data, const string& outputPath)
    {
        // 导出为 XLIFF、CSV 或其他翻译工具支持的格式
    }

    void processTranslations(const Json::Value& translations)
    {
        // 处理翻译结果，更新项目文件
    }

    void checkMissingTranslations(vector<string>& issues)
    {
        // 检查缺失的翻译
    }

    void checkMissingResources(vector<string>& issues)
    {
        // 检查缺失的本地化资源文件
    }

    void checkTextLength(vector<string>& issues)
    {
        // 检查文本长度是否适合UI布局
    }

    void generateValidationReport(const vector<string>& issues)
    {
        // 生成验证报告
    }
};
```

## 最佳实践

### 1. 性能优化
```cpp
// 本地化资源预加载策略
class LocalizationPreloader
{
public:
    void preloadForLanguage(const string& languageCode)
    {
        // 预加载策略：只加载当前屏幕和下一个可能屏幕的资源
        preloadCurrentScreenResources(languageCode);
        preloadNextScreenResources(languageCode);

        // 异步预加载常用资源
        asyncPreloadCommonResources(languageCode);
    }

    void optimizeMemoryUsage()
    {
        // 清理未使用的本地化资源
        cleanupUnusedResources();

        // 压缩纹理资源
        compressTextureResources();

        // 使用资源池管理
        setupResourcePools();
    }

private:
    void preloadCurrentScreenResources(const string& languageCode)
    {
        // 预加载当前屏幕的所有本地化资源
        string currentScreen = getCurrentScreenName();
        loadScreenResources(currentScreen, languageCode);
    }

    void preloadNextScreenResources(const string& languageCode)
    {
        // 根据用户行为预测，预加载可能的下一个屏幕
        vector<string> likelyNextScreens = predictNextScreens();
        for (const string& screen : likelyNextScreens)
        {
            loadScreenResources(screen, languageCode);
        }
    }

    void asyncPreloadCommonResources(const string& languageCode)
    {
        // 在后台线程异步加载常用资源
        thread preloadThread([this, languageCode]() {
            loadCommonIcons(languageCode);
            loadCommonTexts(languageCode);
            loadCommonAudio(languageCode);
        });
        preloadThread.detach();
    }

    string getCurrentScreenName() { return "dashboard"; }
    vector<string> predictNextScreens() { return {"navigation", "media", "settings"}; }
    void loadScreenResources(const string& screen, const string& lang) { /* 实现 */ }
    void cleanupUnusedResources() { /* 实现 */ }
    void compressTextureResources() { /* 实现 */ }
    void setupResourcePools() { /* 实现 */ }
    void loadCommonIcons(const string& lang) { /* 实现 */ }
    void loadCommonTexts(const string& lang) { /* 实现 */ }
    void loadCommonAudio(const string& lang) { /* 实现 */ }
};
```

### 2. 测试和验证
```cpp
// 本地化测试框架
class LocalizationTestFramework
{
public:
    struct TestResult
    {
        string testName;
        bool passed;
        string errorMessage;
        vector<string> warnings;
    };

    vector<TestResult> runAllTests()
    {
        vector<TestResult> results;

        // 运行各种本地化测试
        results.push_back(testLanguageSwitching());
        results.push_back(testTextTruncation());
        results.push_back(testResourceLoading());
        results.push_back(testRTLSupport());
        results.push_back(testNumberFormatting());
        results.push_back(testDateTimeFormatting());

        return results;
    }

private:
    TestResult testLanguageSwitching()
    {
        TestResult result;
        result.testName = "Language Switching";
        result.passed = true;

        try
        {
            // 测试语言切换功能
            vector<string> languages = {"en-US", "zh-CN", "ja-JP", "de-DE"};
            for (const string& lang : languages)
            {
                setLanguage(lang);
                if (getCurrentLanguage() != lang)
                {
                    result.passed = false;
                    result.errorMessage = "Failed to switch to " + lang;
                    break;
                }

                // 验证UI更新
                if (!verifyUIUpdated(lang))
                {
                    result.passed = false;
                    result.errorMessage = "UI not updated for " + lang;
                    break;
                }
            }
        }
        catch (const exception& e)
        {
            result.passed = false;
            result.errorMessage = e.what();
        }

        return result;
    }

    TestResult testTextTruncation()
    {
        TestResult result;
        result.testName = "Text Truncation";
        result.passed = true;

        // 测试长文本是否正确处理
        vector<string> longTextKeys = {"very_long_warning_message", "detailed_navigation_instruction"};

        for (const string& key : longTextKeys)
        {
            string text = getLocalizedString(key);
            if (isTextTruncated(text))
            {
                result.warnings.push_back("Text truncated for key: " + key);
            }
        }

        return result;
    }

    TestResult testResourceLoading()
    {
        TestResult result;
        result.testName = "Resource Loading";
        result.passed = true;

        // 测试本地化资源是否正确加载
        vector<string> languages = getAvailableLanguages();
        vector<string> resourceKeys = {"home_icon", "settings_icon", "warning_sound"};

        for (const string& lang : languages)
        {
            for (const string& key : resourceKeys)
            {
                if (!isResourceAvailable(key, lang))
                {
                    result.warnings.push_back("Missing resource: " + key + " for " + lang);
                }
            }
        }

        return result;
    }

    TestResult testRTLSupport()
    {
        TestResult result;
        result.testName = "RTL Support";
        result.passed = true;

        // 测试从右到左语言支持
        vector<string> rtlLanguages = {"ar-SA", "he-IL"};

        for (const string& lang : rtlLanguages)
        {
            setLanguage(lang);
            if (!isRTLLayoutActive())
            {
                result.passed = false;
                result.errorMessage = "RTL layout not activated for " + lang;
                break;
            }
        }

        return result;
    }

    TestResult testNumberFormatting() { /* 实现数字格式化测试 */ return TestResult(); }
    TestResult testDateTimeFormatting() { /* 实现日期时间格式化测试 */ return TestResult(); }

    // 辅助方法
    void setLanguage(const string& lang) { /* 实现 */ }
    string getCurrentLanguage() { return "en-US"; }
    bool verifyUIUpdated(const string& lang) { return true; }
    string getLocalizedString(const string& key) { return ""; }
    bool isTextTruncated(const string& text) { return false; }
    vector<string> getAvailableLanguages() { return {"en-US", "zh-CN"}; }
    bool isResourceAvailable(const string& key, const string& lang) { return true; }
    bool isRTLLayoutActive() { return true; }
};
```

---

*Kanzi 的国际化系统专为汽车 HMI 设计，提供了符合汽车行业标准的完整本地化解决方案。更多详细信息请参考 [Kanzi 官方文档](https://docs.kanzi.com/)*
```
```