import os
import subprocess
import glob
import sys

# --- Main Script ---
def main():
    # 1. 解析命令行参数
    if len(sys.argv) < 2 or sys.argv[1] not in ('zh', 'en'):
        print('用法: python convert.py zh|en')
        return
    lang = sys.argv[1]

    # 2. 根据语言设置路径
    if lang == 'zh':
        DOCS_DIR = 'docs'
        OUTPUT_DIR = 'web/docs/zh/'
        content_lang = 'zh'
        placeholder_lang = 'en'
        placeholder_html = '<h1>[English translation pending]</h1><p>The content for this page has not been translated yet. Please check back later.</p>'
    else:
        DOCS_DIR = 'docs_en'
        OUTPUT_DIR = 'web/docs/en/'
        content_lang = 'en'
        placeholder_lang = 'zh'
        placeholder_html = '<h1>[尚未翻译]</h1><p>本页面内容尚未翻译为中文，请稍后再试。</p>'

    TEMPLATE_PATH = 'web/docs/_template.html'
    PANDOC_PATH = r'C:\Users\<USER>\AppData\Local\Pandoc\pandoc.exe'

    # 3. 检查 pandoc
    if not os.path.exists(PANDOC_PATH):
        print(f"\n---")
        print(f"FATAL ERROR: pandoc not found at {PANDOC_PATH}")
        print("Please ensure the path to pandoc is correct.")
        print("---\n")
        return

    # 4. 确保输出目录存在
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    # 5. 读取 HTML 模板
    try:
        with open(TEMPLATE_PATH, 'r', encoding='utf-8') as f:
            template_html = f.read()
    except FileNotFoundError:
        print(f"Error: Template file not found at {TEMPLATE_PATH}")
        return

    # 6. 查找所有 markdown 文件
    md_files = glob.glob(os.path.join(DOCS_DIR, '*.md'))
    if not md_files:
        print(f"No markdown files found in {DOCS_DIR}")
        return

    # 7. 处理每个 markdown 文件
    for md_path in md_files:
        print(f"Processing {md_path}...")
        # 1. 读取 Markdown 内容
        try:
            with open(md_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
            print(f"  Markdown内容前100字符: {md_content[:100]}")
        except Exception as e:
            print(f"  Error reading markdown file: {e}")
            continue
        # 2. 用 pandoc 转换为 HTML 片段
        try:
            html_fragment = subprocess.check_output(
                [PANDOC_PATH, '-f', 'markdown', '-t', 'html5', md_path],
                encoding='utf-8',
                stderr=subprocess.PIPE
            )
            print(f"  Pandoc转换结果前200字符: {html_fragment[:200]}")
        except subprocess.CalledProcessError as e:
            print(f"  Error running pandoc: {e}")
            print(f"  Pandoc stderr: {e.stderr.decode('utf-8')}")
            continue
        except Exception as e:
            print(f"  Error running pandoc: {e}")
            continue
        # 3. 构建国际化内容结构
        main_content = f'<div lang="{content_lang}">{html_fragment}</div>'
        placeholder_content = f'<div lang="{placeholder_lang}" style="display: none;">{placeholder_html}</div>'
        if lang == 'zh':
            i18n_content = main_content + placeholder_content
        else:
            i18n_content = placeholder_content + main_content
        # 4. 提取标题
        title = os.path.basename(md_path).replace('.md', '')
        first_line = md_content.splitlines()[0] if md_content else ''
        if first_line.startswith('# '):
            title = first_line[2:].strip()
        # 5. 注入内容和标题到模板
        final_html = template_html.replace('<!-- MARKDOWN CONTENT WILL BE INJECTED HERE -->', i18n_content)
        final_html = final_html.replace('<title>文档页面</title>', f'<title>{title}</title>')
        # 6. 保存 HTML 文件
        output_filename = os.path.basename(md_path).replace('.md', '.html')
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(final_html)
            print(f"  Successfully created {output_path}")
        except Exception as e:
            print(f"  Error writing HTML file: {e}")

if __name__ == "__main__":
    main()