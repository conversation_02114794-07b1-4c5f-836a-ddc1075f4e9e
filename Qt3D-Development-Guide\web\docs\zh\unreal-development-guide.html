<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unreal Engine 开发指南</title> <!-- The title will be replaced for each page -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/docs.css">
    <link rel="stylesheet" href="../../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html" class="logo-text" data-i18n="nav-logo-text">3D 引擎指南</a>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="../index.html#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="../index.html#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="../index.html#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 文档内容区域 -->
    <div class="doc-layout container">
        <!-- 左侧目录 -->
        <aside class="doc-sidebar card">
            <div class="toc-container">
                <!-- 目录内容将通过JavaScript动态生成 -->
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <section class="doc-section card">
            <div class="doc-container">
                <main class="doc-content">
                    <div lang="zh"><h1 id="unreal-engine-开发指南">Unreal Engine 开发指南</h1>
<h2 id="目录">目录</h2>
<ol type="1">
<li><a href="#概述">概述</a></li>
<li><a href="#技术特性">技术特性</a></li>
<li><a href="#开发环境搭建">开发环境搭建</a></li>
<li><a href="#核心概念">核心概念</a></li>
<li><a href="#blueprint-可视化编程">Blueprint 可视化编程</a></li>
<li><a href="#c-编程">C++ 编程</a></li>
<li><a href="#2d3d-开发">2D/3D 开发</a></li>
<li><a href="#最佳实践">最佳实践</a></li>
</ol>
<h2 id="概述">概述</h2>
<p>Unreal Engine 是 Epic Games 开发的世界领先的实时 3D
创作平台，以其顶级的渲染质量、强大的工具链和 AAA
级游戏开发能力而闻名。从独立开发者到大型工作室，Unreal Engine
为各种规模的项目提供了专业级的开发工具。</p>
<h3 id="主要优势">主要优势</h3>
<ul>
<li><strong>顶级画质</strong>：业界领先的渲染质量和视觉效果</li>
<li><strong>Blueprint 系统</strong>：强大的可视化编程系统</li>
<li><strong>免费使用</strong>：5% 营收分成模式，降低开发门槛</li>
<li><strong>完整工具链</strong>：从概念到发布的完整开发工具</li>
<li><strong>源码开放</strong>：完整的引擎源码可供定制</li>
<li><strong>跨平台支持</strong>：支持主流平台和新兴平台</li>
</ul>
<h2 id="技术特性">技术特性</h2>
<h3 id="渲染系统">渲染系统</h3>
<ul>
<li><strong>Lumen</strong>：动态全局光照系统</li>
<li><strong>Nanite</strong>：虚拟化几何体技术</li>
<li><strong>Chaos Physics</strong>：高性能物理模拟系统</li>
<li><strong>Niagara</strong>：下一代粒子系统</li>
<li><strong>World Partition</strong>：大世界流式加载系统</li>
<li><strong>MetaHuman Creator</strong>：高质量数字人类创建工具</li>
</ul>
<h3 id="材质系统">材质系统</h3>
<ul>
<li><strong>Material Editor</strong>：节点式材质编辑器</li>
<li><strong>Material Functions</strong>：可重用的材质函数</li>
<li><strong>Material Instances</strong>：高效的材质变体系统</li>
<li><strong>Shader Model 6</strong>：支持最新的着色器技术</li>
<li><strong>Virtual Texturing</strong>：虚拟纹理技术</li>
</ul>
<h3 id="动画系统">动画系统</h3>
<ul>
<li><strong>Control Rig</strong>：程序化动画控制系统</li>
<li><strong>Sequencer</strong>：电影级动画序列编辑器</li>
<li><strong>Animation Blueprint</strong>：复杂动画逻辑系统</li>
<li><strong>Motion Matching</strong>：基于数据驱动的动画系统</li>
<li><strong>Live Link</strong>：实时动画数据传输</li>
</ul>
<h2 id="开发环境搭建">开发环境搭建</h2>
<h3 id="系统要求">系统要求</h3>
<ul>
<li><strong>操作系统</strong>：Windows 10 64-bit, macOS 10.14.6+, Ubuntu
18.04+</li>
<li><strong>内存</strong>：最少 32GB RAM，推荐 64GB+</li>
<li><strong>存储</strong>：至少 100GB 可用空间（SSD 推荐）</li>
<li><strong>显卡</strong>：DirectX 11/12 兼容显卡，推荐 RTX 系列</li>
</ul>
<h3 id="安装步骤">安装步骤</h3>
<h4 id="安装-epic-games-launcher">1. 安装 Epic Games Launcher</h4>
<div class="sourceCode" id="cb1"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 下载并安装 Epic Games Launcher</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="ex">https://www.unrealengine.com/download</span></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a><span class="co"># 创建 Epic Games 账户</span></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a><span class="co"># 登录 Launcher</span></span></code></pre></div>
<h4 id="安装-unreal-engine">2. 安装 Unreal Engine</h4>
<div class="sourceCode" id="cb2"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 在 Launcher 中：</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a><span class="co"># 1. 点击 &quot;Unreal Engine&quot; 标签</span></span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a><span class="co"># 2. 选择 &quot;Install Engine&quot;</span></span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a><span class="co"># 3. 选择版本（推荐最新稳定版）</span></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a><span class="co"># 4. 选择安装位置</span></span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a><span class="co"># 5. 等待下载和安装完成</span></span></code></pre></div>
<h4 id="安装开发工具">3. 安装开发工具</h4>
<div class="sourceCode" id="cb3"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Windows: 安装 Visual Studio 2019/2022</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a><span class="co"># - 包含 C++ 开发工具</span></span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a><span class="co"># - 包含 Windows 10/11 SDK</span></span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a><span class="co"># macOS: 安装 Xcode</span></span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a><span class="co"># - 最新版本的 Xcode</span></span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a><span class="co"># - Command Line Tools</span></span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-9"><a href="#cb3-9" aria-hidden="true" tabindex="-1"></a><span class="co"># Linux: 安装构建工具</span></span>
<span id="cb3-10"><a href="#cb3-10" aria-hidden="true" tabindex="-1"></a><span class="fu">sudo</span> apt-get install build-essential clang</span></code></pre></div>
<h2 id="核心概念">核心概念</h2>
<h3 id="actor-和-component">Actor 和 Component</h3>
<div class="sourceCode" id="cb4"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Actor 是游戏世界中的基本对象</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API AMyActor <span class="op">:</span> <span class="kw">public</span> AActor</span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a>    AMyActor<span class="op">();</span></span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb4-11"><a href="#cb4-11" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> BeginPlay<span class="op">()</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb4-12"><a href="#cb4-12" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-13"><a href="#cb4-13" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>VisibleAnywhere<span class="op">,</span> BlueprintReadOnly<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Components&quot;</span><span class="op">)</span></span>
<span id="cb4-14"><a href="#cb4-14" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UStaticMeshComponent<span class="op">*</span> MeshComponent<span class="op">;</span></span>
<span id="cb4-15"><a href="#cb4-15" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-16"><a href="#cb4-16" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>VisibleAnywhere<span class="op">,</span> BlueprintReadOnly<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Components&quot;</span><span class="op">)</span></span>
<span id="cb4-17"><a href="#cb4-17" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UBoxComponent<span class="op">*</span> CollisionComponent<span class="op">;</span></span>
<span id="cb4-18"><a href="#cb4-18" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-19"><a href="#cb4-19" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb4-20"><a href="#cb4-20" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> Tick<span class="op">(</span><span class="dt">float</span> DeltaTime<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb4-21"><a href="#cb4-21" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb4-22"><a href="#cb4-22" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-23"><a href="#cb4-23" aria-hidden="true" tabindex="-1"></a><span class="co">// 构造函数中设置组件</span></span>
<span id="cb4-24"><a href="#cb4-24" aria-hidden="true" tabindex="-1"></a>AMyActor<span class="op">::</span>AMyActor<span class="op">()</span></span>
<span id="cb4-25"><a href="#cb4-25" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb4-26"><a href="#cb4-26" aria-hidden="true" tabindex="-1"></a>    PrimaryActorTick<span class="op">.</span>bCanEverTick <span class="op">=</span> <span class="kw">true</span><span class="op">;</span></span>
<span id="cb4-27"><a href="#cb4-27" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-28"><a href="#cb4-28" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 创建根组件</span></span>
<span id="cb4-29"><a href="#cb4-29" aria-hidden="true" tabindex="-1"></a>    RootComponent <span class="op">=</span> CreateDefaultSubobject<span class="op">&lt;</span>USceneComponent<span class="op">&gt;(</span>TEXT<span class="op">(</span><span class="st">&quot;RootComponent&quot;</span><span class="op">));</span></span>
<span id="cb4-30"><a href="#cb4-30" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-31"><a href="#cb4-31" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 创建网格组件</span></span>
<span id="cb4-32"><a href="#cb4-32" aria-hidden="true" tabindex="-1"></a>    MeshComponent <span class="op">=</span> CreateDefaultSubobject<span class="op">&lt;</span>UStaticMeshComponent<span class="op">&gt;(</span>TEXT<span class="op">(</span><span class="st">&quot;MeshComponent&quot;</span><span class="op">));</span></span>
<span id="cb4-33"><a href="#cb4-33" aria-hidden="true" tabindex="-1"></a>    MeshComponent<span class="op">-&gt;</span>SetupAttachment<span class="op">(</span>RootComponent<span class="op">);</span></span>
<span id="cb4-34"><a href="#cb4-34" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-35"><a href="#cb4-35" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 创建碰撞组件</span></span>
<span id="cb4-36"><a href="#cb4-36" aria-hidden="true" tabindex="-1"></a>    CollisionComponent <span class="op">=</span> CreateDefaultSubobject<span class="op">&lt;</span>UBoxComponent<span class="op">&gt;(</span>TEXT<span class="op">(</span><span class="st">&quot;CollisionComponent&quot;</span><span class="op">));</span></span>
<span id="cb4-37"><a href="#cb4-37" aria-hidden="true" tabindex="-1"></a>    CollisionComponent<span class="op">-&gt;</span>SetupAttachment<span class="op">(</span>RootComponent<span class="op">);</span></span>
<span id="cb4-38"><a href="#cb4-38" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="pawn-和-controller">Pawn 和 Controller</h3>
<div class="sourceCode" id="cb5"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Pawn 是可以被控制的 Actor</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API AMyPawn <span class="op">:</span> <span class="kw">public</span> APawn</span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a>    AMyPawn<span class="op">();</span></span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-10"><a href="#cb5-10" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb5-11"><a href="#cb5-11" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> BeginPlay<span class="op">()</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb5-12"><a href="#cb5-12" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-13"><a href="#cb5-13" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>VisibleAnywhere<span class="op">,</span> BlueprintReadOnly<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Components&quot;</span><span class="op">)</span></span>
<span id="cb5-14"><a href="#cb5-14" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UStaticMeshComponent<span class="op">*</span> MeshComponent<span class="op">;</span></span>
<span id="cb5-15"><a href="#cb5-15" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-16"><a href="#cb5-16" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>VisibleAnywhere<span class="op">,</span> BlueprintReadOnly<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Components&quot;</span><span class="op">)</span></span>
<span id="cb5-17"><a href="#cb5-17" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UCameraComponent<span class="op">*</span> CameraComponent<span class="op">;</span></span>
<span id="cb5-18"><a href="#cb5-18" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-19"><a href="#cb5-19" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>VisibleAnywhere<span class="op">,</span> BlueprintReadOnly<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Components&quot;</span><span class="op">)</span></span>
<span id="cb5-20"><a href="#cb5-20" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UFloatingPawnMovement<span class="op">*</span> MovementComponent<span class="op">;</span></span>
<span id="cb5-21"><a href="#cb5-21" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-22"><a href="#cb5-22" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb5-23"><a href="#cb5-23" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> Tick<span class="op">(</span><span class="dt">float</span> DeltaTime<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb5-24"><a href="#cb5-24" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> SetupPlayerInputComponent<span class="op">(</span>UInputComponent<span class="op">*</span> PlayerInputComponent<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb5-25"><a href="#cb5-25" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-26"><a href="#cb5-26" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb5-27"><a href="#cb5-27" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> MoveForward<span class="op">(</span><span class="dt">float</span> Value<span class="op">);</span></span>
<span id="cb5-28"><a href="#cb5-28" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> MoveRight<span class="op">(</span><span class="dt">float</span> Value<span class="op">);</span></span>
<span id="cb5-29"><a href="#cb5-29" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> Turn<span class="op">(</span><span class="dt">float</span> Value<span class="op">);</span></span>
<span id="cb5-30"><a href="#cb5-30" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> LookUp<span class="op">(</span><span class="dt">float</span> Value<span class="op">);</span></span>
<span id="cb5-31"><a href="#cb5-31" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb5-32"><a href="#cb5-32" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-33"><a href="#cb5-33" aria-hidden="true" tabindex="-1"></a><span class="co">// 设置输入绑定</span></span>
<span id="cb5-34"><a href="#cb5-34" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> AMyPawn<span class="op">::</span>SetupPlayerInputComponent<span class="op">(</span>UInputComponent<span class="op">*</span> PlayerInputComponent<span class="op">)</span></span>
<span id="cb5-35"><a href="#cb5-35" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb5-36"><a href="#cb5-36" aria-hidden="true" tabindex="-1"></a>    Super<span class="op">::</span>SetupPlayerInputComponent<span class="op">(</span>PlayerInputComponent<span class="op">);</span></span>
<span id="cb5-37"><a href="#cb5-37" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-38"><a href="#cb5-38" aria-hidden="true" tabindex="-1"></a>    PlayerInputComponent<span class="op">-&gt;</span>BindAxis<span class="op">(</span><span class="st">&quot;MoveForward&quot;</span><span class="op">,</span> <span class="kw">this</span><span class="op">,</span> <span class="op">&amp;</span>AMyPawn<span class="op">::</span>MoveForward<span class="op">);</span></span>
<span id="cb5-39"><a href="#cb5-39" aria-hidden="true" tabindex="-1"></a>    PlayerInputComponent<span class="op">-&gt;</span>BindAxis<span class="op">(</span><span class="st">&quot;MoveRight&quot;</span><span class="op">,</span> <span class="kw">this</span><span class="op">,</span> <span class="op">&amp;</span>AMyPawn<span class="op">::</span>MoveRight<span class="op">);</span></span>
<span id="cb5-40"><a href="#cb5-40" aria-hidden="true" tabindex="-1"></a>    PlayerInputComponent<span class="op">-&gt;</span>BindAxis<span class="op">(</span><span class="st">&quot;Turn&quot;</span><span class="op">,</span> <span class="kw">this</span><span class="op">,</span> <span class="op">&amp;</span>AMyPawn<span class="op">::</span>Turn<span class="op">);</span></span>
<span id="cb5-41"><a href="#cb5-41" aria-hidden="true" tabindex="-1"></a>    PlayerInputComponent<span class="op">-&gt;</span>BindAxis<span class="op">(</span><span class="st">&quot;LookUp&quot;</span><span class="op">,</span> <span class="kw">this</span><span class="op">,</span> <span class="op">&amp;</span>AMyPawn<span class="op">::</span>LookUp<span class="op">);</span></span>
<span id="cb5-42"><a href="#cb5-42" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="gamemode-和-gamestate">GameMode 和 GameState</h3>
<div class="sourceCode" id="cb6"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="co">// GameMode 定义游戏规则</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API AMyGameMode <span class="op">:</span> <span class="kw">public</span> AGameModeBase</span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb6-5"><a href="#cb6-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb6-6"><a href="#cb6-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-7"><a href="#cb6-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb6-8"><a href="#cb6-8" aria-hidden="true" tabindex="-1"></a>    AMyGameMode<span class="op">();</span></span>
<span id="cb6-9"><a href="#cb6-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-10"><a href="#cb6-10" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb6-11"><a href="#cb6-11" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> BeginPlay<span class="op">()</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb6-12"><a href="#cb6-12" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> PostLogin<span class="op">(</span>APlayerController<span class="op">*</span> NewPlayer<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb6-13"><a href="#cb6-13" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> Logout<span class="op">(</span>AController<span class="op">*</span> Exiting<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb6-14"><a href="#cb6-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-15"><a href="#cb6-15" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditDefaultsOnly<span class="op">,</span> BlueprintReadOnly<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Game Rules&quot;</span><span class="op">)</span></span>
<span id="cb6-16"><a href="#cb6-16" aria-hidden="true" tabindex="-1"></a>    int32 MaxPlayers <span class="op">=</span> <span class="dv">4</span><span class="op">;</span></span>
<span id="cb6-17"><a href="#cb6-17" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-18"><a href="#cb6-18" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditDefaultsOnly<span class="op">,</span> BlueprintReadOnly<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Game Rules&quot;</span><span class="op">)</span></span>
<span id="cb6-19"><a href="#cb6-19" aria-hidden="true" tabindex="-1"></a>    <span class="dt">float</span> GameDuration <span class="op">=</span> <span class="fl">300.0</span><span class="bu">f</span><span class="op">;</span> <span class="co">// 5 minutes</span></span>
<span id="cb6-20"><a href="#cb6-20" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-21"><a href="#cb6-21" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb6-22"><a href="#cb6-22" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Game&quot;</span><span class="op">)</span></span>
<span id="cb6-23"><a href="#cb6-23" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> StartGame<span class="op">();</span></span>
<span id="cb6-24"><a href="#cb6-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-25"><a href="#cb6-25" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Game&quot;</span><span class="op">)</span></span>
<span id="cb6-26"><a href="#cb6-26" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> EndGame<span class="op">();</span></span>
<span id="cb6-27"><a href="#cb6-27" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="blueprint-可视化编程">Blueprint 可视化编程</h2>
<h3 id="基础-blueprint-节点">基础 Blueprint 节点</h3>
<div class="sourceCode" id="cb7"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Blueprint 可调用函数</span></span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb7-3"><a href="#cb7-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API UMyBlueprintFunctionLibrary <span class="op">:</span> <span class="kw">public</span> UBlueprintFunctionLibrary</span>
<span id="cb7-4"><a href="#cb7-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb7-5"><a href="#cb7-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb7-6"><a href="#cb7-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-7"><a href="#cb7-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb7-8"><a href="#cb7-8" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;My Functions&quot;</span><span class="op">)</span></span>
<span id="cb7-9"><a href="#cb7-9" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> <span class="dt">float</span> CalculateDistance<span class="op">(</span>FVector PointA<span class="op">,</span> FVector PointB<span class="op">);</span></span>
<span id="cb7-10"><a href="#cb7-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-11"><a href="#cb7-11" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;My Functions&quot;</span><span class="op">)</span></span>
<span id="cb7-12"><a href="#cb7-12" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> <span class="dt">bool</span> IsActorInRange<span class="op">(</span>AActor<span class="op">*</span> Actor<span class="op">,</span> FVector Center<span class="op">,</span> <span class="dt">float</span> Range<span class="op">);</span></span>
<span id="cb7-13"><a href="#cb7-13" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-14"><a href="#cb7-14" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintPure<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;My Functions&quot;</span><span class="op">)</span></span>
<span id="cb7-15"><a href="#cb7-15" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> FString FormatTime<span class="op">(</span><span class="dt">float</span> TimeInSeconds<span class="op">);</span></span>
<span id="cb7-16"><a href="#cb7-16" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb7-17"><a href="#cb7-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-18"><a href="#cb7-18" aria-hidden="true" tabindex="-1"></a><span class="co">// 实现</span></span>
<span id="cb7-19"><a href="#cb7-19" aria-hidden="true" tabindex="-1"></a><span class="dt">float</span> UMyBlueprintFunctionLibrary<span class="op">::</span>CalculateDistance<span class="op">(</span>FVector PointA<span class="op">,</span> FVector PointB<span class="op">)</span></span>
<span id="cb7-20"><a href="#cb7-20" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb7-21"><a href="#cb7-21" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> FVector<span class="op">::</span>Dist<span class="op">(</span>PointA<span class="op">,</span> PointB<span class="op">);</span></span>
<span id="cb7-22"><a href="#cb7-22" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb7-23"><a href="#cb7-23" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-24"><a href="#cb7-24" aria-hidden="true" tabindex="-1"></a><span class="dt">bool</span> UMyBlueprintFunctionLibrary<span class="op">::</span>IsActorInRange<span class="op">(</span>AActor<span class="op">*</span> Actor<span class="op">,</span> FVector Center<span class="op">,</span> <span class="dt">float</span> Range<span class="op">)</span></span>
<span id="cb7-25"><a href="#cb7-25" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb7-26"><a href="#cb7-26" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(!</span>Actor<span class="op">)</span> <span class="cf">return</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb7-27"><a href="#cb7-27" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-28"><a href="#cb7-28" aria-hidden="true" tabindex="-1"></a>    <span class="dt">float</span> Distance <span class="op">=</span> FVector<span class="op">::</span>Dist<span class="op">(</span>Actor<span class="op">-&gt;</span>GetActorLocation<span class="op">(),</span> Center<span class="op">);</span></span>
<span id="cb7-29"><a href="#cb7-29" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> Distance <span class="op">&lt;=</span> Range<span class="op">;</span></span>
<span id="cb7-30"><a href="#cb7-30" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="blueprint-接口">Blueprint 接口</h3>
<div class="sourceCode" id="cb8"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Blueprint 接口定义</span></span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a>UINTERFACE<span class="op">(</span>MinimalAPI<span class="op">,</span> Blueprintable<span class="op">)</span></span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> UInteractable <span class="op">:</span> <span class="kw">public</span> UInterface</span>
<span id="cb8-4"><a href="#cb8-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb8-5"><a href="#cb8-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb8-6"><a href="#cb8-6" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb8-7"><a href="#cb8-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-8"><a href="#cb8-8" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API IInteractable</span>
<span id="cb8-9"><a href="#cb8-9" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb8-10"><a href="#cb8-10" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb8-11"><a href="#cb8-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-12"><a href="#cb8-12" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb8-13"><a href="#cb8-13" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintNativeEvent<span class="op">,</span> BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Interaction&quot;</span><span class="op">)</span></span>
<span id="cb8-14"><a href="#cb8-14" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> Interact<span class="op">(</span>AActor<span class="op">*</span> InteractingActor<span class="op">);</span></span>
<span id="cb8-15"><a href="#cb8-15" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> Interact_Implementation<span class="op">(</span>AActor<span class="op">*</span> InteractingActor<span class="op">)</span> <span class="op">{}</span></span>
<span id="cb8-16"><a href="#cb8-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-17"><a href="#cb8-17" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintNativeEvent<span class="op">,</span> BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Interaction&quot;</span><span class="op">)</span></span>
<span id="cb8-18"><a href="#cb8-18" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> CanInteract<span class="op">(</span>AActor<span class="op">*</span> InteractingActor<span class="op">);</span></span>
<span id="cb8-19"><a href="#cb8-19" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">bool</span> CanInteract_Implementation<span class="op">(</span>AActor<span class="op">*</span> InteractingActor<span class="op">)</span> <span class="op">{</span> <span class="cf">return</span> <span class="kw">true</span><span class="op">;</span> <span class="op">}</span></span>
<span id="cb8-20"><a href="#cb8-20" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb8-21"><a href="#cb8-21" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-22"><a href="#cb8-22" aria-hidden="true" tabindex="-1"></a><span class="co">// 实现接口的类</span></span>
<span id="cb8-23"><a href="#cb8-23" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb8-24"><a href="#cb8-24" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API AInteractableActor <span class="op">:</span> <span class="kw">public</span> AActor<span class="op">,</span> <span class="kw">public</span> IInteractable</span>
<span id="cb8-25"><a href="#cb8-25" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb8-26"><a href="#cb8-26" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb8-27"><a href="#cb8-27" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-28"><a href="#cb8-28" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb8-29"><a href="#cb8-29" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> Interact_Implementation<span class="op">(</span>AActor<span class="op">*</span> InteractingActor<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb8-30"><a href="#cb8-30" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">bool</span> CanInteract_Implementation<span class="op">(</span>AActor<span class="op">*</span> InteractingActor<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb8-31"><a href="#cb8-31" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="c-编程">C++ 编程</h2>
<h3 id="属性系统-uproperty">属性系统 (UPROPERTY)</h3>
<div class="sourceCode" id="cb9"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API AMyCharacter <span class="op">:</span> <span class="kw">public</span> ACharacter</span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 在编辑器中可见和可编辑</span></span>
<span id="cb9-8"><a href="#cb9-8" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditAnywhere<span class="op">,</span> BlueprintReadWrite<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Stats&quot;</span><span class="op">)</span></span>
<span id="cb9-9"><a href="#cb9-9" aria-hidden="true" tabindex="-1"></a>    <span class="dt">float</span> Health <span class="op">=</span> <span class="fl">100.0</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb9-10"><a href="#cb9-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-11"><a href="#cb9-11" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 只在编辑器中可见</span></span>
<span id="cb9-12"><a href="#cb9-12" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>VisibleAnywhere<span class="op">,</span> BlueprintReadOnly<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Components&quot;</span><span class="op">)</span></span>
<span id="cb9-13"><a href="#cb9-13" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UHealthComponent<span class="op">*</span> HealthComponent<span class="op">;</span></span>
<span id="cb9-14"><a href="#cb9-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-15"><a href="#cb9-15" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 只在实例中可编辑</span></span>
<span id="cb9-16"><a href="#cb9-16" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditInstanceOnly<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Configuration&quot;</span><span class="op">)</span></span>
<span id="cb9-17"><a href="#cb9-17" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> bIsImportantActor <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb9-18"><a href="#cb9-18" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-19"><a href="#cb9-19" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 序列化但不在编辑器中显示</span></span>
<span id="cb9-20"><a href="#cb9-20" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>SaveGame<span class="op">)</span></span>
<span id="cb9-21"><a href="#cb9-21" aria-hidden="true" tabindex="-1"></a>    int32 ExperiencePoints <span class="op">=</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb9-22"><a href="#cb9-22" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-23"><a href="#cb9-23" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 复制到客户端</span></span>
<span id="cb9-24"><a href="#cb9-24" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>Replicated<span class="op">)</span></span>
<span id="cb9-25"><a href="#cb9-25" aria-hidden="true" tabindex="-1"></a>    <span class="dt">float</span> ServerHealth<span class="op">;</span></span>
<span id="cb9-26"><a href="#cb9-26" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-27"><a href="#cb9-27" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb9-28"><a href="#cb9-28" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 只在默认值中可编辑</span></span>
<span id="cb9-29"><a href="#cb9-29" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditDefaultsOnly<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Audio&quot;</span><span class="op">)</span></span>
<span id="cb9-30"><a href="#cb9-30" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> USoundBase<span class="op">*</span> DeathSound<span class="op">;</span></span>
<span id="cb9-31"><a href="#cb9-31" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="函数系统-ufunction">函数系统 (UFUNCTION)</h3>
<div class="sourceCode" id="cb10"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Blueprint 可调用</span></span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Health&quot;</span><span class="op">)</span></span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> TakeDamage<span class="op">(</span><span class="dt">float</span> DamageAmount<span class="op">);</span></span>
<span id="cb10-5"><a href="#cb10-5" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-6"><a href="#cb10-6" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Blueprint 可实现</span></span>
<span id="cb10-7"><a href="#cb10-7" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintImplementableEvent<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Events&quot;</span><span class="op">)</span></span>
<span id="cb10-8"><a href="#cb10-8" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> OnHealthChanged<span class="op">(</span><span class="dt">float</span> NewHealth<span class="op">);</span></span>
<span id="cb10-9"><a href="#cb10-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-10"><a href="#cb10-10" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Blueprint 原生事件</span></span>
<span id="cb10-11"><a href="#cb10-11" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintNativeEvent<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Events&quot;</span><span class="op">)</span></span>
<span id="cb10-12"><a href="#cb10-12" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> OnDeath<span class="op">();</span></span>
<span id="cb10-13"><a href="#cb10-13" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> OnDeath_Implementation<span class="op">();</span></span>
<span id="cb10-14"><a href="#cb10-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-15"><a href="#cb10-15" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 服务器 RPC</span></span>
<span id="cb10-16"><a href="#cb10-16" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>Server<span class="op">,</span> Reliable<span class="op">,</span> WithValidation<span class="op">)</span></span>
<span id="cb10-17"><a href="#cb10-17" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ServerTakeDamage<span class="op">(</span><span class="dt">float</span> DamageAmount<span class="op">);</span></span>
<span id="cb10-18"><a href="#cb10-18" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ServerTakeDamage_Implementation<span class="op">(</span><span class="dt">float</span> DamageAmount<span class="op">);</span></span>
<span id="cb10-19"><a href="#cb10-19" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> ServerTakeDamage_Validate<span class="op">(</span><span class="dt">float</span> DamageAmount<span class="op">);</span></span>
<span id="cb10-20"><a href="#cb10-20" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-21"><a href="#cb10-21" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 客户端 RPC</span></span>
<span id="cb10-22"><a href="#cb10-22" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>Client<span class="op">,</span> Reliable<span class="op">)</span></span>
<span id="cb10-23"><a href="#cb10-23" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ClientPlayDeathEffect<span class="op">();</span></span>
<span id="cb10-24"><a href="#cb10-24" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> ClientPlayDeathEffect_Implementation<span class="op">();</span></span></code></pre></div>
<h3 id="委托和事件">委托和事件</h3>
<div class="sourceCode" id="cb11"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 声明委托</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a>DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam<span class="op">(</span>FOnHealthChanged<span class="op">,</span> <span class="dt">float</span><span class="op">,</span> NewHealth<span class="op">);</span></span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a>DECLARE_DYNAMIC_MULTICAST_DELEGATE<span class="op">(</span>FOnDeath<span class="op">);</span></span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API UHealthComponent <span class="op">:</span> <span class="kw">public</span> UActorComponent</span>
<span id="cb11-7"><a href="#cb11-7" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb11-8"><a href="#cb11-8" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb11-9"><a href="#cb11-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-10"><a href="#cb11-10" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb11-11"><a href="#cb11-11" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Blueprint 可绑定的事件</span></span>
<span id="cb11-12"><a href="#cb11-12" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>BlueprintAssignable<span class="op">)</span></span>
<span id="cb11-13"><a href="#cb11-13" aria-hidden="true" tabindex="-1"></a>    FOnHealthChanged OnHealthChanged<span class="op">;</span></span>
<span id="cb11-14"><a href="#cb11-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-15"><a href="#cb11-15" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>BlueprintAssignable<span class="op">)</span></span>
<span id="cb11-16"><a href="#cb11-16" aria-hidden="true" tabindex="-1"></a>    FOnDeath OnDeath<span class="op">;</span></span>
<span id="cb11-17"><a href="#cb11-17" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-18"><a href="#cb11-18" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Health&quot;</span><span class="op">)</span></span>
<span id="cb11-19"><a href="#cb11-19" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> TakeDamage<span class="op">(</span><span class="dt">float</span> DamageAmount<span class="op">);</span></span>
<span id="cb11-20"><a href="#cb11-20" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-21"><a href="#cb11-21" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb11-22"><a href="#cb11-22" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditAnywhere<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Health&quot;</span><span class="op">)</span></span>
<span id="cb11-23"><a href="#cb11-23" aria-hidden="true" tabindex="-1"></a>    <span class="dt">float</span> MaxHealth <span class="op">=</span> <span class="fl">100.0</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb11-24"><a href="#cb11-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-25"><a href="#cb11-25" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>VisibleAnywhere<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Health&quot;</span><span class="op">)</span></span>
<span id="cb11-26"><a href="#cb11-26" aria-hidden="true" tabindex="-1"></a>    <span class="dt">float</span> CurrentHealth<span class="op">;</span></span>
<span id="cb11-27"><a href="#cb11-27" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb11-28"><a href="#cb11-28" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-29"><a href="#cb11-29" aria-hidden="true" tabindex="-1"></a><span class="co">// 使用委托</span></span>
<span id="cb11-30"><a href="#cb11-30" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> UHealthComponent<span class="op">::</span>TakeDamage<span class="op">(</span><span class="dt">float</span> DamageAmount<span class="op">)</span></span>
<span id="cb11-31"><a href="#cb11-31" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb11-32"><a href="#cb11-32" aria-hidden="true" tabindex="-1"></a>    CurrentHealth <span class="op">=</span> FMath<span class="op">::</span>Max<span class="op">(</span><span class="fl">0.0</span><span class="bu">f</span><span class="op">,</span> CurrentHealth <span class="op">-</span> DamageAmount<span class="op">);</span></span>
<span id="cb11-33"><a href="#cb11-33" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-34"><a href="#cb11-34" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 广播事件</span></span>
<span id="cb11-35"><a href="#cb11-35" aria-hidden="true" tabindex="-1"></a>    OnHealthChanged<span class="op">.</span>Broadcast<span class="op">(</span>CurrentHealth<span class="op">);</span></span>
<span id="cb11-36"><a href="#cb11-36" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-37"><a href="#cb11-37" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>CurrentHealth <span class="op">&lt;=</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">)</span></span>
<span id="cb11-38"><a href="#cb11-38" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-39"><a href="#cb11-39" aria-hidden="true" tabindex="-1"></a>        OnDeath<span class="op">.</span>Broadcast<span class="op">();</span></span>
<span id="cb11-40"><a href="#cb11-40" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-41"><a href="#cb11-41" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="d3d-开发">2D/3D 开发</h2>
<h3 id="umg-unreal-motion-graphics">UMG (Unreal Motion Graphics)</h3>
<div class="sourceCode" id="cb12"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="co">// UI Widget 基类</span></span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb12-3"><a href="#cb12-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API UMyUserWidget <span class="op">:</span> <span class="kw">public</span> UUserWidget</span>
<span id="cb12-4"><a href="#cb12-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb12-5"><a href="#cb12-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb12-6"><a href="#cb12-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-7"><a href="#cb12-7" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb12-8"><a href="#cb12-8" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> NativeConstruct<span class="op">()</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb12-9"><a href="#cb12-9" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> NativeTick<span class="op">(</span><span class="at">const</span> FGeometry<span class="op">&amp;</span> MyGeometry<span class="op">,</span> <span class="dt">float</span> InDeltaTime<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb12-10"><a href="#cb12-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-11"><a href="#cb12-11" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 绑定 UI 元素</span></span>
<span id="cb12-12"><a href="#cb12-12" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>meta <span class="op">=</span> <span class="op">(</span>BindWidget<span class="op">))</span></span>
<span id="cb12-13"><a href="#cb12-13" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UTextBlock<span class="op">*</span> HealthText<span class="op">;</span></span>
<span id="cb12-14"><a href="#cb12-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-15"><a href="#cb12-15" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>meta <span class="op">=</span> <span class="op">(</span>BindWidget<span class="op">))</span></span>
<span id="cb12-16"><a href="#cb12-16" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UProgressBar<span class="op">*</span> HealthBar<span class="op">;</span></span>
<span id="cb12-17"><a href="#cb12-17" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-18"><a href="#cb12-18" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>meta <span class="op">=</span> <span class="op">(</span>BindWidget<span class="op">))</span></span>
<span id="cb12-19"><a href="#cb12-19" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UButton<span class="op">*</span> StartButton<span class="op">;</span></span>
<span id="cb12-20"><a href="#cb12-20" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-21"><a href="#cb12-21" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb12-22"><a href="#cb12-22" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">(</span>BlueprintCallable<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;UI&quot;</span><span class="op">)</span></span>
<span id="cb12-23"><a href="#cb12-23" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> UpdateHealth<span class="op">(</span><span class="dt">float</span> CurrentHealth<span class="op">,</span> <span class="dt">float</span> MaxHealth<span class="op">);</span></span>
<span id="cb12-24"><a href="#cb12-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-25"><a href="#cb12-25" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb12-26"><a href="#cb12-26" aria-hidden="true" tabindex="-1"></a>    UFUNCTION<span class="op">()</span></span>
<span id="cb12-27"><a href="#cb12-27" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> OnStartButtonClicked<span class="op">();</span></span>
<span id="cb12-28"><a href="#cb12-28" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb12-29"><a href="#cb12-29" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-30"><a href="#cb12-30" aria-hidden="true" tabindex="-1"></a><span class="co">// 实现</span></span>
<span id="cb12-31"><a href="#cb12-31" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> UMyUserWidget<span class="op">::</span>NativeConstruct<span class="op">()</span></span>
<span id="cb12-32"><a href="#cb12-32" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb12-33"><a href="#cb12-33" aria-hidden="true" tabindex="-1"></a>    Super<span class="op">::</span>NativeConstruct<span class="op">();</span></span>
<span id="cb12-34"><a href="#cb12-34" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-35"><a href="#cb12-35" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>StartButton<span class="op">)</span></span>
<span id="cb12-36"><a href="#cb12-36" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-37"><a href="#cb12-37" aria-hidden="true" tabindex="-1"></a>        StartButton<span class="op">-&gt;</span>OnClicked<span class="op">.</span>AddDynamic<span class="op">(</span><span class="kw">this</span><span class="op">,</span> <span class="op">&amp;</span>UMyUserWidget<span class="op">::</span>OnStartButtonClicked<span class="op">);</span></span>
<span id="cb12-38"><a href="#cb12-38" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-39"><a href="#cb12-39" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb12-40"><a href="#cb12-40" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-41"><a href="#cb12-41" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> UMyUserWidget<span class="op">::</span>UpdateHealth<span class="op">(</span><span class="dt">float</span> CurrentHealth<span class="op">,</span> <span class="dt">float</span> MaxHealth<span class="op">)</span></span>
<span id="cb12-42"><a href="#cb12-42" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb12-43"><a href="#cb12-43" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>HealthText<span class="op">)</span></span>
<span id="cb12-44"><a href="#cb12-44" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-45"><a href="#cb12-45" aria-hidden="true" tabindex="-1"></a>        FText HealthString <span class="op">=</span> FText<span class="op">::</span>FromString<span class="op">(</span>FString<span class="op">::</span>Printf<span class="op">(</span>TEXT<span class="op">(</span><span class="st">&quot;</span><span class="sc">%.0f</span><span class="st"> / </span><span class="sc">%.0f</span><span class="st">&quot;</span><span class="op">),</span> CurrentHealth<span class="op">,</span> MaxHealth<span class="op">));</span></span>
<span id="cb12-46"><a href="#cb12-46" aria-hidden="true" tabindex="-1"></a>        HealthText<span class="op">-&gt;</span>SetText<span class="op">(</span>HealthString<span class="op">);</span></span>
<span id="cb12-47"><a href="#cb12-47" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-48"><a href="#cb12-48" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-49"><a href="#cb12-49" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>HealthBar<span class="op">)</span></span>
<span id="cb12-50"><a href="#cb12-50" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-51"><a href="#cb12-51" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> HealthPercent <span class="op">=</span> MaxHealth <span class="op">&gt;</span> <span class="dv">0</span> <span class="op">?</span> CurrentHealth <span class="op">/</span> MaxHealth <span class="op">:</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb12-52"><a href="#cb12-52" aria-hidden="true" tabindex="-1"></a>        HealthBar<span class="op">-&gt;</span>SetPercent<span class="op">(</span>HealthPercent<span class="op">);</span></span>
<span id="cb12-53"><a href="#cb12-53" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-54"><a href="#cb12-54" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="paper2d-2d-游戏开发">Paper2D (2D 游戏开发)</h3>
<div class="sourceCode" id="cb13"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 2D 角色类</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API AMyPaper2DCharacter <span class="op">:</span> <span class="kw">public</span> APaperCharacter</span>
<span id="cb13-4"><a href="#cb13-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb13-5"><a href="#cb13-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb13-6"><a href="#cb13-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-7"><a href="#cb13-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb13-8"><a href="#cb13-8" aria-hidden="true" tabindex="-1"></a>    AMyPaper2DCharacter<span class="op">();</span></span>
<span id="cb13-9"><a href="#cb13-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-10"><a href="#cb13-10" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb13-11"><a href="#cb13-11" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> BeginPlay<span class="op">()</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb13-12"><a href="#cb13-12" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> SetupPlayerInputComponent<span class="op">(</span>UInputComponent<span class="op">*</span> PlayerInputComponent<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb13-13"><a href="#cb13-13" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-14"><a href="#cb13-14" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>VisibleAnywhere<span class="op">,</span> BlueprintReadOnly<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Animation&quot;</span><span class="op">)</span></span>
<span id="cb13-15"><a href="#cb13-15" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UPaperFlipbook<span class="op">*</span> IdleAnimation<span class="op">;</span></span>
<span id="cb13-16"><a href="#cb13-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-17"><a href="#cb13-17" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>VisibleAnywhere<span class="op">,</span> BlueprintReadOnly<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Animation&quot;</span><span class="op">)</span></span>
<span id="cb13-18"><a href="#cb13-18" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UPaperFlipbook<span class="op">*</span> RunAnimation<span class="op">;</span></span>
<span id="cb13-19"><a href="#cb13-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-20"><a href="#cb13-20" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditAnywhere<span class="op">,</span> BlueprintReadWrite<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;Movement&quot;</span><span class="op">)</span></span>
<span id="cb13-21"><a href="#cb13-21" aria-hidden="true" tabindex="-1"></a>    <span class="dt">float</span> MoveSpeed <span class="op">=</span> <span class="fl">300.0</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb13-22"><a href="#cb13-22" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-23"><a href="#cb13-23" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb13-24"><a href="#cb13-24" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> Tick<span class="op">(</span><span class="dt">float</span> DeltaTime<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb13-25"><a href="#cb13-25" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-26"><a href="#cb13-26" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb13-27"><a href="#cb13-27" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> MoveRight<span class="op">(</span><span class="dt">float</span> Value<span class="op">);</span></span>
<span id="cb13-28"><a href="#cb13-28" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> Jump<span class="op">();</span></span>
<span id="cb13-29"><a href="#cb13-29" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-30"><a href="#cb13-30" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> UpdateAnimation<span class="op">();</span></span>
<span id="cb13-31"><a href="#cb13-31" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-32"><a href="#cb13-32" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> bIsMoving <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb13-33"><a href="#cb13-33" aria-hidden="true" tabindex="-1"></a>    <span class="dt">float</span> MovementDirection <span class="op">=</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb13-34"><a href="#cb13-34" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb13-35"><a href="#cb13-35" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-36"><a href="#cb13-36" aria-hidden="true" tabindex="-1"></a><span class="co">// 2D 动画更新</span></span>
<span id="cb13-37"><a href="#cb13-37" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> AMyPaper2DCharacter<span class="op">::</span>UpdateAnimation<span class="op">()</span></span>
<span id="cb13-38"><a href="#cb13-38" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb13-39"><a href="#cb13-39" aria-hidden="true" tabindex="-1"></a>    UPaperFlipbookComponent<span class="op">*</span> FlipbookComponent <span class="op">=</span> GetSprite<span class="op">();</span></span>
<span id="cb13-40"><a href="#cb13-40" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(!</span>FlipbookComponent<span class="op">)</span> <span class="cf">return</span><span class="op">;</span></span>
<span id="cb13-41"><a href="#cb13-41" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-42"><a href="#cb13-42" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>bIsMoving<span class="op">)</span></span>
<span id="cb13-43"><a href="#cb13-43" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-44"><a href="#cb13-44" aria-hidden="true" tabindex="-1"></a>        FlipbookComponent<span class="op">-&gt;</span>SetFlipbook<span class="op">(</span>RunAnimation<span class="op">);</span></span>
<span id="cb13-45"><a href="#cb13-45" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-46"><a href="#cb13-46" aria-hidden="true" tabindex="-1"></a>    <span class="cf">else</span></span>
<span id="cb13-47"><a href="#cb13-47" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-48"><a href="#cb13-48" aria-hidden="true" tabindex="-1"></a>        FlipbookComponent<span class="op">-&gt;</span>SetFlipbook<span class="op">(</span>IdleAnimation<span class="op">);</span></span>
<span id="cb13-49"><a href="#cb13-49" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-50"><a href="#cb13-50" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-51"><a href="#cb13-51" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 根据移动方向翻转精灵</span></span>
<span id="cb13-52"><a href="#cb13-52" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>MovementDirection <span class="op">!=</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">)</span></span>
<span id="cb13-53"><a href="#cb13-53" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-54"><a href="#cb13-54" aria-hidden="true" tabindex="-1"></a>        <span class="dt">bool</span> bFaceRight <span class="op">=</span> MovementDirection <span class="op">&gt;</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb13-55"><a href="#cb13-55" aria-hidden="true" tabindex="-1"></a>        FlipbookComponent<span class="op">-&gt;</span>SetWorldScale3D<span class="op">(</span>FVector<span class="op">(</span>bFaceRight <span class="op">?</span> <span class="fl">1.0</span><span class="bu">f</span> <span class="op">:</span> <span class="op">-</span><span class="fl">1.0</span><span class="bu">f</span><span class="op">,</span> <span class="fl">1.0</span><span class="bu">f</span><span class="op">,</span> <span class="fl">1.0</span><span class="bu">f</span><span class="op">));</span></span>
<span id="cb13-56"><a href="#cb13-56" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-57"><a href="#cb13-57" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="最佳实践">最佳实践</h2>
<h3 id="项目组织">1. 项目组织</h3>
<pre><code>Content/
├── Blueprints/
│   ├── Characters/
│   ├── Weapons/
│   ├── UI/
│   └── GameModes/
├── Materials/
├── Meshes/
├── Textures/
├── Audio/
├── Maps/
└── Data/</code></pre>
<h3 id="性能优化">2. 性能优化</h3>
<div class="sourceCode" id="cb15"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb15-1"><a href="#cb15-1" aria-hidden="true" tabindex="-1"></a><span class="co">// LOD 系统使用</span></span>
<span id="cb15-2"><a href="#cb15-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb15-3"><a href="#cb15-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API AOptimizedActor <span class="op">:</span> <span class="kw">public</span> AActor</span>
<span id="cb15-4"><a href="#cb15-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb15-5"><a href="#cb15-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb15-6"><a href="#cb15-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-7"><a href="#cb15-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb15-8"><a href="#cb15-8" aria-hidden="true" tabindex="-1"></a>    AOptimizedActor<span class="op">();</span></span>
<span id="cb15-9"><a href="#cb15-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-10"><a href="#cb15-10" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb15-11"><a href="#cb15-11" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>VisibleAnywhere<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;LOD&quot;</span><span class="op">)</span></span>
<span id="cb15-12"><a href="#cb15-12" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UStaticMeshComponent<span class="op">*</span> HighDetailMesh<span class="op">;</span></span>
<span id="cb15-13"><a href="#cb15-13" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-14"><a href="#cb15-14" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>VisibleAnywhere<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;LOD&quot;</span><span class="op">)</span></span>
<span id="cb15-15"><a href="#cb15-15" aria-hidden="true" tabindex="-1"></a>    <span class="kw">class</span> UStaticMeshComponent<span class="op">*</span> LowDetailMesh<span class="op">;</span></span>
<span id="cb15-16"><a href="#cb15-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-17"><a href="#cb15-17" aria-hidden="true" tabindex="-1"></a>    UPROPERTY<span class="op">(</span>EditAnywhere<span class="op">,</span> Category <span class="op">=</span> <span class="st">&quot;LOD&quot;</span><span class="op">)</span></span>
<span id="cb15-18"><a href="#cb15-18" aria-hidden="true" tabindex="-1"></a>    <span class="dt">float</span> LODDistance <span class="op">=</span> <span class="fl">1000.0</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb15-19"><a href="#cb15-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-20"><a href="#cb15-20" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb15-21"><a href="#cb15-21" aria-hidden="true" tabindex="-1"></a>    <span class="kw">virtual</span> <span class="dt">void</span> Tick<span class="op">(</span><span class="dt">float</span> DeltaTime<span class="op">)</span> <span class="kw">override</span><span class="op">;</span></span>
<span id="cb15-22"><a href="#cb15-22" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-23"><a href="#cb15-23" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb15-24"><a href="#cb15-24" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> UpdateLOD<span class="op">();</span></span>
<span id="cb15-25"><a href="#cb15-25" aria-hidden="true" tabindex="-1"></a>    APawn<span class="op">*</span> GetClosestPlayer<span class="op">();</span></span>
<span id="cb15-26"><a href="#cb15-26" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb15-27"><a href="#cb15-27" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-28"><a href="#cb15-28" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> AOptimizedActor<span class="op">::</span>UpdateLOD<span class="op">()</span></span>
<span id="cb15-29"><a href="#cb15-29" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb15-30"><a href="#cb15-30" aria-hidden="true" tabindex="-1"></a>    APawn<span class="op">*</span> ClosestPlayer <span class="op">=</span> GetClosestPlayer<span class="op">();</span></span>
<span id="cb15-31"><a href="#cb15-31" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(!</span>ClosestPlayer<span class="op">)</span> <span class="cf">return</span><span class="op">;</span></span>
<span id="cb15-32"><a href="#cb15-32" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-33"><a href="#cb15-33" aria-hidden="true" tabindex="-1"></a>    <span class="dt">float</span> Distance <span class="op">=</span> FVector<span class="op">::</span>Dist<span class="op">(</span>GetActorLocation<span class="op">(),</span> ClosestPlayer<span class="op">-&gt;</span>GetActorLocation<span class="op">());</span></span>
<span id="cb15-34"><a href="#cb15-34" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-35"><a href="#cb15-35" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> bUseHighDetail <span class="op">=</span> Distance <span class="op">&lt;</span> LODDistance<span class="op">;</span></span>
<span id="cb15-36"><a href="#cb15-36" aria-hidden="true" tabindex="-1"></a>    HighDetailMesh<span class="op">-&gt;</span>SetVisibility<span class="op">(</span>bUseHighDetail<span class="op">);</span></span>
<span id="cb15-37"><a href="#cb15-37" aria-hidden="true" tabindex="-1"></a>    LowDetailMesh<span class="op">-&gt;</span>SetVisibility<span class="op">(!</span>bUseHighDetail<span class="op">);</span></span>
<span id="cb15-38"><a href="#cb15-38" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="内存管理">3. 内存管理</h3>
<div class="sourceCode" id="cb16"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb16-1"><a href="#cb16-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 智能指针使用</span></span>
<span id="cb16-2"><a href="#cb16-2" aria-hidden="true" tabindex="-1"></a>UCLASS<span class="op">()</span></span>
<span id="cb16-3"><a href="#cb16-3" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MYGAME_API UMyGameInstance <span class="op">:</span> <span class="kw">public</span> UGameInstance</span>
<span id="cb16-4"><a href="#cb16-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb16-5"><a href="#cb16-5" aria-hidden="true" tabindex="-1"></a>    GENERATED_BODY<span class="op">()</span></span>
<span id="cb16-6"><a href="#cb16-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb16-7"><a href="#cb16-7" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb16-8"><a href="#cb16-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 使用 TSharedPtr 管理非 UObject 数据</span></span>
<span id="cb16-9"><a href="#cb16-9" aria-hidden="true" tabindex="-1"></a>    TSharedPtr<span class="op">&lt;</span><span class="kw">class</span> FGameData<span class="op">&gt;</span> GameData<span class="op">;</span></span>
<span id="cb16-10"><a href="#cb16-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb16-11"><a href="#cb16-11" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 使用 TWeakPtr 避免循环引用</span></span>
<span id="cb16-12"><a href="#cb16-12" aria-hidden="true" tabindex="-1"></a>    TWeakPtr<span class="op">&lt;</span><span class="kw">class</span> FPlayerData<span class="op">&gt;</span> CurrentPlayerData<span class="op">;</span></span>
<span id="cb16-13"><a href="#cb16-13" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb16-14"><a href="#cb16-14" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb16-15"><a href="#cb16-15" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> InitializeGameData<span class="op">();</span></span>
<span id="cb16-16"><a href="#cb16-16" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> CleanupGameData<span class="op">();</span></span>
<span id="cb16-17"><a href="#cb16-17" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<hr />
<p><em>Unreal Engine 提供了业界领先的 3D 开发能力，适合从独立游戏到 AAA
级商业项目。更多详细信息请参考 <a
href="https://docs.unrealengine.com/">Unreal Engine
官方文档</a></em></p>
</div><div lang="en" style="display: none;"><h1>[English translation pending]</h1><p>The content for this page has not been translated yet. Please check back later.</p></div>
                </main>
            </div>
        </section>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">Qt 3D 开发指南</h4>
                    <p data-i18n="footer-subtitle">专业的跨平台 3D 应用开发解决方案</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.</p>
            </div>
        </div>
    </footer>

    <script src="../../assets/js/prism.js"></script>
    <script src="../../assets/js/docs.js"></script>
</body>
</html> 