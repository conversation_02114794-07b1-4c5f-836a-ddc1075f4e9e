# Cocos Creator 国际化与本地化指南

## 目录
1. [概述](#概述)
2. [Cocos 国际化架构](#cocos-国际化架构)
3. [多语言文本管理](#多语言文本管理)
4. [资源本地化](#资源本地化)
5. [移动端国际化](#移动端国际化)
6. [亚洲市场特殊需求](#亚洲市场特殊需求)
7. [发布平台适配](#发布平台适配)
8. [最佳实践](#最佳实践)

## 概述

Cocos Creator 作为全球领先的移动游戏引擎，在国际化方面有着丰富的经验和强大的支持。凭借在中国手游市场40%的占有率和全球20%的市场份额，Cocos 深度理解不同地区和文化的游戏本地化需求，特别是在亚洲市场有着独特的优势。

### 主要特性
- **多语言支持**：完整的多语言文本管理系统
- **资源本地化**：图片、音频、字体的本地化管理
- **亚洲语言优化**：对中文、日文、韩文的特殊优化
- **移动端优化**：针对移动设备的国际化性能优化
- **发布平台集成**：与各大发布平台的本地化工具集成
- **热更新支持**：本地化内容的热更新能力

## Cocos 国际化架构

### 多语言管理系统
```typescript
// 语言管理器
import { sys } from 'cc';

export enum LanguageType {
    CHINESE = 'zh',
    ENGLISH = 'en',
    JAPANESE = 'ja',
    KOREAN = 'ko',
    SPANISH = 'es',
    FRENCH = 'fr',
    GERMAN = 'de',
    RUSSIAN = 'ru',
    PORTUGUESE = 'pt',
    THAI = 'th',
    VIETNAMESE = 'vi',
    INDONESIAN = 'id'
}

export class LanguageManager {
    private static instance: LanguageManager;
    private currentLanguage: LanguageType = LanguageType.ENGLISH;
    private languageData: Map<LanguageType, any> = new Map();
    private fallbackLanguage: LanguageType = LanguageType.ENGLISH;

    static getInstance(): LanguageManager {
        if (!LanguageManager.instance) {
            LanguageManager.instance = new LanguageManager();
        }
        return LanguageManager.instance;
    }

    async initialize() {
        // 检测系统语言
        this.detectSystemLanguage();

        // 加载语言数据
        await this.loadLanguageData();

        // 应用语言设置
        this.applyLanguage();
    }

    private detectSystemLanguage() {
        const systemLang = sys.languageCode;

        // 映射系统语言代码到支持的语言
        const languageMap: { [key: string]: LanguageType } = {
            'zh': LanguageType.CHINESE,
            'zh-CN': LanguageType.CHINESE,
            'zh-TW': LanguageType.CHINESE,
            'en': LanguageType.ENGLISH,
            'ja': LanguageType.JAPANESE,
            'ko': LanguageType.KOREAN,
            'es': LanguageType.SPANISH,
            'fr': LanguageType.FRENCH,
            'de': LanguageType.GERMAN,
            'ru': LanguageType.RUSSIAN,
            'pt': LanguageType.PORTUGUESE,
            'th': LanguageType.THAI,
            'vi': LanguageType.VIETNAMESE,
            'id': LanguageType.INDONESIAN
        };

        this.currentLanguage = languageMap[systemLang] || LanguageType.ENGLISH;

        // 从本地存储读取用户设置
        const savedLang = sys.localStorage.getItem('game_language');
        if (savedLang && Object.values(LanguageType).includes(savedLang as LanguageType)) {
            this.currentLanguage = savedLang as LanguageType;
        }
    }

    async loadLanguageData() {
        // 加载所有支持的语言数据
        const languages = Object.values(LanguageType);

        for (const lang of languages) {
            try {
                const data = await this.loadLanguageFile(lang);
                this.languageData.set(lang, data);
            } catch (error) {
                console.warn(`Failed to load language data for ${lang}:`, error);
            }
        }
    }

    private async loadLanguageFile(language: LanguageType): Promise<any> {
        return new Promise((resolve, reject) => {
            const path = `i18n/${language}`;

            // 使用 Cocos 资源管理系统加载
            resources.load(path, (err, data) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(data.json);
                }
            });
        });
    }

    setLanguage(language: LanguageType) {
        if (this.languageData.has(language)) {
            this.currentLanguage = language;

            // 保存到本地存储
            sys.localStorage.setItem('game_language', language);

            // 应用新语言
            this.applyLanguage();

            // 发送语言变化事件
            this.notifyLanguageChanged();
        }
    }

    getCurrentLanguage(): LanguageType {
        return this.currentLanguage;
    }

    getText(key: string, params?: { [key: string]: string }): string {
        let text = this.getTextFromLanguage(this.currentLanguage, key);

        // 如果当前语言没有找到，使用回退语言
        if (!text && this.currentLanguage !== this.fallbackLanguage) {
            text = this.getTextFromLanguage(this.fallbackLanguage, key);
        }

        // 如果还是没有找到，返回键名
        if (!text) {
            console.warn(`Missing translation for key: ${key}`);
            return key;
        }

        // 参数替换
        if (params) {
            Object.keys(params).forEach(param => {
                text = text.replace(new RegExp(`{${param}}`, 'g'), params[param]);
            });
        }

        return text;
    }

    private getTextFromLanguage(language: LanguageType, key: string): string {
        const data = this.languageData.get(language);
        if (!data) return '';

        // 支持嵌套键名，如 "ui.menu.start"
        const keys = key.split('.');
        let value = data;

        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return '';
            }
        }

        return typeof value === 'string' ? value : '';
    }

    private applyLanguage() {
        // 通知所有本地化组件更新
        this.notifyLanguageChanged();

        // 更新字体设置
        this.updateFontSettings();
    }

    private updateFontSettings() {
        // 根据语言设置合适的字体
        const fontSettings = this.getFontSettingsForLanguage(this.currentLanguage);
        FontManager.getInstance().setDefaultFont(fontSettings);
    }

    private getFontSettingsForLanguage(language: LanguageType) {
        const fontMap = {
            [LanguageType.CHINESE]: 'fonts/NotoSansCJK',
            [LanguageType.JAPANESE]: 'fonts/NotoSansJP',
            [LanguageType.KOREAN]: 'fonts/NotoSansKR',
            [LanguageType.THAI]: 'fonts/NotoSansThai',
            [LanguageType.VIETNAMESE]: 'fonts/NotoSansVietnamese'
        };

        return fontMap[language] || 'fonts/NotoSans';
    }

    private notifyLanguageChanged() {
        // 发送全局事件
        GameEventManager.getInstance().emit('language-changed', this.currentLanguage);
    }

    // 获取当前语言的文本方向
    isRTL(): boolean {
        // 阿拉伯语、希伯来语等从右到左的语言
        const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        return rtlLanguages.includes(this.currentLanguage);
    }

    // 获取当前语言的数字格式
    formatNumber(num: number): string {
        const locale = this.getLocaleString();
        return new Intl.NumberFormat(locale).format(num);
    }

    // 获取当前语言的货币格式
    formatCurrency(amount: number, currency: string = 'USD'): string {
        const locale = this.getLocaleString();
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency
        }).format(amount);
    }

    private getLocaleString(): string {
        const localeMap = {
            [LanguageType.CHINESE]: 'zh-CN',
            [LanguageType.ENGLISH]: 'en-US',
            [LanguageType.JAPANESE]: 'ja-JP',
            [LanguageType.KOREAN]: 'ko-KR',
            [LanguageType.SPANISH]: 'es-ES',
            [LanguageType.FRENCH]: 'fr-FR',
            [LanguageType.GERMAN]: 'de-DE',
            [LanguageType.RUSSIAN]: 'ru-RU',
            [LanguageType.PORTUGUESE]: 'pt-BR',
            [LanguageType.THAI]: 'th-TH',
            [LanguageType.VIETNAMESE]: 'vi-VN',
            [LanguageType.INDONESIAN]: 'id-ID'
        };

        return localeMap[this.currentLanguage] || 'en-US';
    }
}
```