// 文档页面专用脚本
document.addEventListener('DOMContentLoaded', () => {
    showCurrentLangContent();
    generateTableOfContents();
    setupLanguageSwitch();
});

// 获取当前语言
function getCurrentLang() {
    if (window.location.pathname.includes('/en/')) return 'en';
    return 'zh';
}

// 显示当前语言内容，隐藏另一种
function showCurrentLangContent() {
    const lang = getCurrentLang();
    document.querySelectorAll('.doc-content > div[lang]').forEach(div => {
        if (div.getAttribute('lang') === lang) {
            div.style.display = '';
        } else {
            div.style.display = 'none';
        }
    });
    // 切换标题栏
    updatePageTitle(lang);
}

// 生成文档目录（只针对当前语言内容）
function generateTableOfContents() {
    const lang = getCurrentLang();
    const content = document.querySelector(`.doc-content > div[lang="${lang}"]`);
    const toc = document.querySelector('.toc-container');
    if (!content || !toc) return;
    toc.innerHTML = '';
    const headings = content.querySelectorAll('h1, h2, h3, h4');
    if (headings.length === 0) return;
    const tocList = document.createElement('ul');
    const tocStack = [{ element: tocList, level: 0 }];
    headings.forEach((heading, index) => {
        if (!heading.id) {
            heading.id = `heading-${index}`;
        }
        const level = parseInt(heading.tagName.charAt(1));
        const listItem = document.createElement('li');
        const link = document.createElement('a');
        link.textContent = heading.textContent;
        link.href = `#${heading.id}`;
        link.addEventListener('click', (e) => {
            e.preventDefault();
            heading.scrollIntoView({ behavior: 'smooth' });
            window.history.pushState(null, '', link.href);
        });
        listItem.appendChild(link);
        while (level <= tocStack[tocStack.length - 1].level) {
            tocStack.pop();
        }
        if (level > tocStack[tocStack.length - 1].level) {
            const newList = document.createElement('ul');
            tocStack[tocStack.length - 1].element.lastChild?.appendChild(newList);
            tocStack.push({ element: newList, level });
        }
        tocStack[tocStack.length - 1].element.appendChild(listItem);
    });
    toc.appendChild(tocList);
    // 监听滚动，高亮当前阅读的部分
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const id = entry.target.id;
                document.querySelectorAll('.toc-container a').forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${id}`) {
                        link.classList.add('active');
                    }
                });
            }
        });
    }, { threshold: 0.5 });
    headings.forEach(heading => observer.observe(heading));
}

// 动态切换页面标题
function updatePageTitle(lang) {
    const content = document.querySelector(`.doc-content > div[lang="${lang}"]`);
    let title = document.title;
    if (content) {
        const h1 = content.querySelector('h1');
        if (h1) {
            title = h1.textContent.trim();
        }
    }
    document.title = title;
}

// 语言切换功能
function setupLanguageSwitch() {
    const languageSelect = document.getElementById('languageSelect');
    if (!languageSelect) return;
    // 初始化下拉框
    const currentLang = getCurrentLang();
    languageSelect.value = currentLang;
    languageSelect.addEventListener('change', (e) => {
        const newLang = e.target.value;
        const currentPath = window.location.pathname;
        // 只切换 /zh/ <-> /en/，文件名不变
        let newPath = currentPath;
        if (newLang === 'en') {
            newPath = currentPath.replace('/zh/', '/en/');
        } else {
            newPath = currentPath.replace('/en/', '/zh/');
        }
        // 如果路径没有 /zh/ 或 /en/，则插入
        if (!newPath.includes('/zh/') && !newPath.includes('/en/')) {
            const parts = newPath.split('/');
            const filename = parts.pop();
            newPath = `/docs/${newLang}/${filename}`;
        }
        window.location.pathname = newPath;
    });
} 