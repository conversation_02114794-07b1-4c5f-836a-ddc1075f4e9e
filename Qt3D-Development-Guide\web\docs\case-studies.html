<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国际化案例分享 | 3D 引擎开发指南</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text">3D 引擎指南</span>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link">引擎对比</a>
                <a href="../index.html#features" class="nav-link">特性分析</a>
                <a href="../index.html#i18n" class="nav-link">国际化</a>
                <a href="../index.html#docs" class="nav-link">文档</a>
            </div>
        </div>
    </nav>
    <section class="hero">
        <div class="hero-container">
            <h1 class="hero-title">国际化案例分享</h1>
            <p class="hero-subtitle">精选实际国际化项目案例，展示多语言和本地化最佳实践。</p>
        </div>
    </section>
    <section class="case-studies">
        <div class="container">
            <h2 class="section-title">主流引擎本地化多语言开发流程</h2>
            <div class="engines-grid" style="margin-bottom:2em;">
                <div class="engine-showcase unreal">
                    <div class="engine-header">
                        <div class="engine-logo unreal-logo"><span>UE</span></div>
                        <h3>Unreal Engine</h3>
                    </div>
                    <div class="workflow-svg"><img class="workflow-img" src="../assets/images/diagrams/unreal-i18n.png" data-img="../assets/images/diagrams/unreal-i18n.png" alt="Unreal本地化流程"></div>
                    <p class="engine-tagline">Unreal支持基于文本资源的本地化，流程规范，适合大型项目。</p>
                </div>
                <div class="engine-showcase unity">
                    <div class="engine-header">
                        <div class="engine-logo unity-logo"><span>Unity</span></div>
                        <h3>Unity</h3>
                    </div>
                    <div class="workflow-svg"><img class="workflow-img" src="../assets/images/diagrams/unity-i18n.png" data-img="../assets/images/diagrams/unity-i18n.png" alt="Unity本地化流程"></div>
                    <p class="engine-tagline">Unity通过插件和资源表实现灵活的多语言本地化。</p>
                </div>
                <div class="engine-showcase kanzi">
                    <div class="engine-header">
                        <div class="engine-logo kanzi-logo"><span>Kanzi</span></div>
                        <h3>Kanzi</h3>
                    </div>
                    <div class="workflow-svg"><img class="workflow-img" src="../assets/images/diagrams/kanzi-i18n.png" data-img="../assets/images/diagrams/kanzi-i18n.png" alt="Kanzi本地化流程"></div>
                    <p class="engine-tagline">Kanzi Studio支持可视化本地化资源管理，适合嵌入式UI。</p>
                </div>
                <div class="engine-showcase qt3d">
                    <div class="engine-header">
                        <div class="engine-logo"><img src="../assets/images/qt-logo.svg" alt="Qt 3D"></div>
                        <h3>Qt 3D</h3>
                    </div>
                    <div class="workflow-svg"><img class="workflow-img" src="../assets/images/diagrams/qt3d-i18n.png" data-img="../assets/images/diagrams/qt3d-i18n.png" alt="Qt3D本地化流程"></div>
                    <p class="engine-tagline">Qt原生支持多语言，流程自动化程度高。</p>
                </div>
                <div class="engine-showcase cocos">
                    <div class="engine-header">
                        <div class="engine-logo cocos-logo"><span>Cocos</span></div>
                        <h3>Cocos</h3>
                    </div>
                    <div class="workflow-svg"><img class="workflow-img" src="../assets/images/diagrams/cocos-i18n.png" data-img="../assets/images/diagrams/cocos-i18n.png" alt="Cocos本地化流程"></div>
                    <p class="engine-tagline">Cocos通过JSON/CSV资源表实现多语言，适合手游和轻量应用。</p>
                </div>
                <div class="engine-showcase godot">
                    <div class="engine-header">
                        <div class="engine-logo godot-logo"><span>Godot</span></div>
                        <h3>Godot</h3>
                    </div>
                    <div class="workflow-svg"><img class="workflow-img" src="../assets/images/diagrams/godot-i18n.png" data-img="../assets/images/diagrams/godot-i18n.png" alt="Godot本地化流程"></div>
                    <p class="engine-tagline">Godot支持CSV/PO格式本地化，流程简洁高效。</p>
                </div>
            </div>

                <!-- 新增内容：多语言扩展工时说明 -->
            <div class="i18n-time-summary" style="margin:2em 0 2em 0;padding:1.5em 1.5em 1em 1.5em;background:#fafbfc;border-radius:10px;box-shadow:0 2px 8px rgba(0,0,0,0.04);">
                <h3 style="margin-top:0;font-size:1.25em;color:#2d6fa1;letter-spacing:1px;">多语言扩展工时与复杂语言适配说明</h3>
                <p style="margin-bottom:1em;line-height:1.8;">在项目已具备本地化框架的前提下，<strong>新增一种语言</strong>时，主要涉及文本资源导入、界面适配与测试。</p>
                <p style="margin-bottom:1em;line-height:1.8;">各主流引擎的技术集成工时如下（不含翻译本身）：</p>
                <div style="overflow-x:auto;">
                <style>
                .i18n-time-summary-table {
                  width: 100%;
                  border-collapse: separate;
                  border-spacing: 0;
                  font-size: 1em;
                  background: #fff;
                  border-radius: 8px;
                  overflow: hidden;
                  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
                }
                .i18n-time-summary-table th {
                  background: #e3eef8;
                  color: #205080;
                  font-weight: 600;
                  padding: 10px 12px;
                  border-bottom: 1.5px solid #d0dbe6;
                  text-align: left;
                }
                .i18n-time-summary-table td {
                  padding: 10px 12px;
                  border-bottom: 1px solid #f0f4f8;
                  color: #333;
                  background: #fff;
                  transition: background 0.2s;
                }
                .i18n-time-summary-table tr:last-child td {
                  border-bottom: none;
                }
                .i18n-time-summary-table tr:hover td {
                  background: #f5faff;
                }
                </style>
                <table class="i18n-time-summary-table">
                  <tr>
                    <th>引擎</th>
                    <th>初次实现本地化预计用时（天）</th>
                    <th>新增一种语言预计用时（天）</th>
                  </tr>
                  <tr><td>Unreal</td><td>4 ~ 7</td><td>0.5 ~ 1</td></tr>
                  <tr><td>Unity</td><td>4 ~ 6</td><td>0.5 ~ 1</td></tr>
                  <tr><td>Kanzi</td><td>4 ~ 7</td><td>0.5 ~ 1</td></tr>
                  <tr><td>Qt 3D</td><td>2.5 ~ 4</td><td>0.3 ~ 0.8</td></tr>
                  <tr><td>Cocos</td><td>3 ~ 5</td><td>0.5 ~ 1</td></tr>
                  <tr><td>Godot</td><td>2.5 ~ 3</td><td>0.3 ~ 0.8</td></tr>
                </table>
                </div>
                <ul style="margin-bottom:0.5em;line-height:1.8;padding-left:1.2em;">
                  <li><strong>复杂语言适配：</strong>如新增阿拉伯语、希伯来语等（需RTL布局），需要额外预留 <b>+1 ~ 2天</b> 进行界面镜像、排版和字体适配。</li>
                  <li>欧系语言等一般仅需资源导入和简单测试，流程更快。</li>
                  <li>预计用时包含：新语言资源集成、界面检查、切换测试与bug修正等环节。</li>
                </ul>
            </div>
            <!-- 新增内容结束 -->
            
            <h2 class="section-title">案例列表</h2>
            <div class="case-list">
                <div class="case-card">
                    <h3>1. 吉利博越L：多语言与国际化实践 <span style="font-size:0.7em;color:#888;">（基于Kanzi引擎）</span></h3>
                    <p>博越L作为吉利基于CMA架构打造的旗舰SUV，面向全球市场，支持多语言车机系统和本地化体验。其Flyme Auto智能座舱系统，基于Kanzi引擎开发，支持多语种界面切换，结合AI云动力模型，实现不同地区用户的智能交互和本地化服务。<br>
                    <strong>国际化亮点：</strong>
                        <li>支持多语言车机界面，适配全球用户。</li>
                        <li>智能语音交互，覆盖多语种识别。</li>
                        <li>本地化内容推送与远程诊断服务。</li>
                        <li>多区域法规与安全标准适配。</li>
                        <li>支持左右舵切换，适配不同国家驾驶习惯。</li>
                    </p>
                    <div class="multilingual-gallery">
                        <div class="multilingual-item">
                            <img class="multilingual-img" src="../assets/images/multilingual/00_Cluster/英文.png" data-img="../assets/images/multilingual/00_Cluster/英文.png" alt="英文界面">
                            <div class="multilingual-label">英文</div>
                        </div>
                        <div class="multilingual-item">
                            <img class="multilingual-img" src="../assets/images/multilingual/00_Cluster/西班牙语.png" data-img="../assets/images/multilingual/00_Cluster/西班牙语.png" alt="西班牙语界面">
                            <div class="multilingual-label">西班牙语</div>
                        </div>
                        <div class="multilingual-item">
                            <img class="multilingual-img" src="../assets/images/multilingual/00_Cluster/阿拉伯语.png" data-img="../assets/images/multilingual/00_Cluster/阿拉伯语.png" alt="阿拉伯语界面">
                            <div class="multilingual-label">阿拉伯语</div>
                        </div>
                        <div class="multilingual-item">
                            <img class="multilingual-img" src="../assets/images/multilingual/00_Cluster/俄罗斯语言.png" data-img="../assets/images/multilingual/00_Cluster/俄罗斯语言.png" alt="俄语界面">
                            <div class="multilingual-label">俄语</div>
                        </div>
                    </div>
                </div>
                <div class="case-card">
                    <h3>2. 多语言主题桌面系统：国际化适配与交互创新 <span style="font-size:0.7em;color:#888;">（基于Unreal引擎）</span></h3>
                    <p>该桌面系统面向全球车载市场，基于Unreal引擎开发，支持多分辨率，界面可灵活切换多语言，适配不同国家和地区用户。<br>
                    <strong>国际化亮点：</strong>
                        <li>多语言适配，支持多分辨率和横竖屏，界面可灵活切换。</li>
                        <li>丰富主题，千余种风格在线切换，满足不同国家用户审美。</li>
                        <li>多形态交互，上滑、翻页、画中画等多种交互方式，兼容多终端。</li>
                        <li>昼夜模式，支持多种切换方式，适应不同用车环境。</li>
                        <li>快捷自定义，桌面图标和功能可自定义，适应不同地区操作习惯。</li>
                        <li>品牌定制，支持专属主题定制，满足国际化品牌需求。</li>
                        <li>支持左右舵切换，适配不同国家驾驶习惯。</li>
                    </p>
                    <div class="dofun-video-box" style="display:flex;flex-direction:column;align-items:center;justify-content:flex-start;">
                        <img src="../assets/images/dofun-video-cover.png" alt="桌面系统演示视频" class="dofun-video-cover" id="dofunVideoTrigger" style="cursor:pointer;max-width:100%;border-radius:10px;box-shadow:0 2px 8px rgba(0,0,0,0.08);">
                        <p style="text-align:center;color:#888;">点击图片播放演示视频<br>（视频中展示了左右舵切换效果）</p>
                    </div>
                </div>
                <div class="case-card">
                    <h3>国际化多语言实践总结</h3>
                    <p>国际化不仅仅是语言的切换，更包括本地化内容、法规适配、智能交互和用户体验的全面提升。通过多语言界面、智能语音、云端服务和本地化适配，能够有效提升产品在全球市场的竞争力和用户满意度。</p>
                </div>
            </div>
            <a href="../index.html#docs" class="btn btn-secondary" style="margin-top:2em;">返回主页</a>
        </div>
    </section>
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Qt 3D 开发指南</h4>
                    <p>专业的跨平台 3D 应用开发解决方案</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.</p>
            </div>
        </div>
    </footer>
    <!-- 流程图放大模态框 -->
    <div id="workflowModal" class="workflow-modal" style="display:none;">
        <div class="workflow-modal-content">
            <span class="workflow-modal-close">&times;</span>
            <div class="workflow-modal-toolbar">
                <button id="zoomInBtn" title="放大">＋</button>
                <button id="zoomOutBtn" title="缩小">－</button>
                <button id="zoomFitBtn" title="自适应">适应</button>
            </div>
            <div class="workflow-modal-imgbox">
                <img id="workflowModalImg" src="" alt="流程图大图">
            </div>
        </div>
    </div>
    <!-- 视频弹窗 -->
    <div id="dofunVideoModal" class="workflow-modal" style="display:none;">
      <div class="workflow-modal-content">
        <span class="workflow-modal-close">&times;</span>
        <div class="dofun-video-modal-box" style="width:80vw;max-width:900px;">
          <video id="dofunVideoPlayer" src="" controls style="width:100%;border-radius:10px;background:#000;" poster="../assets/images/dofun-video-cover.png"></video>
        </div>
      </div>
    </div>
    <script>
    let zoomLevel = 1;
    let fitMode = true;
    const modalImg = document.getElementById('workflowModalImg');
    const zoomInBtn = document.getElementById('zoomInBtn');
    const zoomOutBtn = document.getElementById('zoomOutBtn');
    const zoomFitBtn = document.getElementById('zoomFitBtn');
    function updateImgZoom() {
        if (fitMode) {
            modalImg.style.width = '100%';
            modalImg.style.height = 'auto';
            modalImg.style.maxWidth = '100%';
            modalImg.style.maxHeight = '70vh';
            modalImg.style.transform = '';
        } else {
            modalImg.style.width = '';
            modalImg.style.height = '';
            modalImg.style.maxWidth = 'none';
            modalImg.style.maxHeight = 'none';
            modalImg.style.transform = `scale(${zoomLevel})`;
            modalImg.style.transformOrigin = 'center center';
        }
    }
    zoomInBtn.onclick = function() {
        fitMode = false;
        zoomLevel = Math.min(zoomLevel + 0.2, 5);
        updateImgZoom();
    };
    zoomOutBtn.onclick = function() {
        fitMode = false;
        zoomLevel = Math.max(zoomLevel - 0.2, 0.2);
        updateImgZoom();
    };
    zoomFitBtn.onclick = function() {
        fitMode = true;
        updateImgZoom();
    };
    document.querySelectorAll('.workflow-img').forEach(function(img){
        img.addEventListener('click', function(){
            var modal = document.getElementById('workflowModal');
            var modalImg = document.getElementById('workflowModalImg');
            modalImg.src = img.getAttribute('data-img');
            modal.style.display = 'flex';
            zoomLevel = 1;
            fitMode = true;
            updateImgZoom();
        });
    });
    document.querySelector('.workflow-modal-close').onclick = function(){
        document.getElementById('workflowModal').style.display = 'none';
    };
    document.getElementById('workflowModal').onclick = function(e){
        if(e.target === this) this.style.display = 'none';
    };
    // 视频弹窗逻辑
    if(document.getElementById('dofunVideoTrigger')){
      document.getElementById('dofunVideoTrigger').onclick = function() {
          var modal = document.getElementById('dofunVideoModal');
          var player = document.getElementById('dofunVideoPlayer');
          // TODO: 替换为你的视频文件路径
          player.src = '../assets/video/dofun-demo.mp4';
          modal.style.display = 'flex';
          player.play();
      };
    }
    document.querySelectorAll('.workflow-modal-close').forEach(function(btn){
        btn.onclick = function(){
            var modal = btn.closest('.workflow-modal');
            if(modal) {
                modal.style.display = 'none';
                var player = modal.querySelector('video');
                if(player) player.pause();
            }
        };
    });
    if(document.getElementById('dofunVideoModal')){
      document.getElementById('dofunVideoModal').onclick = function(e){
          if(e.target === this) {
              this.style.display = 'none';
              var player = this.querySelector('video');
              if(player) player.pause();
          }
      };
    }
    document.querySelectorAll('.multilingual-img').forEach(function(img){
        img.addEventListener('click', function(){
            var modal = document.getElementById('workflowModal');
            var modalImg = document.getElementById('workflowModalImg');
            modalImg.src = img.getAttribute('data-img');
            modal.style.display = 'flex';
            zoomLevel = 1;
            fitMode = true;
            updateImgZoom();
        });
    });
    </script>
</body>
</html> 