/* 文档页面专用样式 */
.doc-layout {
    display: flex;
    margin-top: 64px;
    min-height: calc(100vh - 64px);
    gap: 32px;
}

/* 左侧目录样式 */
.doc-sidebar {
    width: 280px;
    flex-shrink: 0;
    background: var(--bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    height: fit-content;
    position: sticky;
    top: 64px;
    overflow-y: auto;
    padding: 32px 0 32px 0;
    margin-bottom: 32px;
    transition: var(--transition-fast);
}

.toc-container {
    padding: 0 24px;
}

.toc-container ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc-container li {
    margin: 8px 0;
}

.toc-container a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 15px;
    line-height: 1.5;
    display: block;
    padding: 6px 16px;
    border-radius: 8px;
    transition: var(--transition-fast);
    font-weight: 400;
}

.toc-container a:hover {
    color: var(--primary-color);
    background: var(--bg-tertiary);
}

.toc-container a.active {
    color: var(--primary-color);
    background: var(--bg-tertiary);
    font-weight: 600;
}

/* 二级目录缩进 */
.toc-container ul ul {
    padding-left: 18px;
}

/* 主要内容区域样式 */
.doc-section {
    flex-grow: 1;
    padding: 48px 56px;
    background: var(--bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    min-width: 0;
    margin-bottom: 32px;
    transition: var(--transition-fast);
}

.doc-container {
    max-width: 900px;
    margin: 0 auto;
}

.doc-content {
    font-size: 17px;
    line-height: 1.7;
    color: var(--text-color);
    font-family: var(--font-system);
}

/* 文档内容样式 */
.doc-content h1 {
    font-size: 40px;
    margin: 0 0 36px;
    padding-bottom: 18px;
    border-bottom: 1px solid var(--border-color);
    font-weight: 700;
    color: var(--text-color);
}

.doc-content h2 {
    font-size: 28px;
    margin: 48px 0 24px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-color);
}

.doc-content h3 {
    font-size: 22px;
    margin: 32px 0 16px;
    font-weight: 600;
    color: var(--text-color);
}

.doc-content h4 {
    font-size: 18px;
    margin: 24px 0 12px;
    font-weight: 500;
    color: var(--text-color);
}

.doc-content p {
    margin: 16px 0;
    line-height: 1.7;
}

.doc-content ul,
.doc-content ol {
    padding-left: 28px;
    margin: 16px 0;
}

.doc-content li {
    margin: 8px 0;
}

.doc-content pre {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 18px;
    margin: 18px 0;
    overflow-x: auto;
    font-size: 15px;
    box-shadow: var(--shadow-light);
}

.doc-content code {
    font-family: var(--font-mono);
    font-size: 15px;
    padding: 2px 8px;
    background: var(--bg-secondary);
    border-radius: 5px;
}

.doc-content pre code {
    padding: 0;
    background: none;
}

/* 按钮风格复用主站 */
.doc-content .btn,
.toc-container .btn {
    display: inline-block;
    padding: 8px 20px;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-fast);
    cursor: pointer;
    border: none;
    outline: none;
}
.doc-content .btn-primary {
    background: var(--primary-color);
    color: #fff;
    box-shadow: var(--shadow-light);
}
.doc-content .btn-primary:hover {
    background: #005ecb;
}
.doc-content .btn-secondary {
    background: var(--bg-secondary);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}
.doc-content .btn-secondary:hover {
    background: var(--primary-color);
    color: #fff;
}

/* 响应式布局 */
@media (max-width: 1024px) {
    .doc-layout {
        flex-direction: column;
        gap: 0;
    }
    .doc-sidebar {
        width: 100%;
        height: auto;
        position: relative;
        top: 0;
        margin-bottom: 0;
        margin-top: 0;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }
    .doc-section {
        padding: 32px 16px;
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }
}

@media (max-width: 600px) {
    .doc-content h1 {
        font-size: 28px;
    }
    .doc-content h2 {
        font-size: 22px;
    }
    .doc-content h3 {
        font-size: 18px;
    }
    .doc-content h4 {
        font-size: 15px;
    }
    .doc-section {
        padding: 18px 4px;
    }
} 