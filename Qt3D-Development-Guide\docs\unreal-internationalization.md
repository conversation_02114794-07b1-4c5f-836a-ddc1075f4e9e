# Unreal Engine 国际化与本地化指南

## 目录
1. [概述](#概述)
2. [文本本地化系统](#文本本地化系统)
3. [资源本地化](#资源本地化)
4. [音频本地化](#音频本地化)
5. [Blueprint 国际化](#blueprint-国际化)
6. [C++ 国际化](#c-国际化)
7. [本地化工具](#本地化工具)
8. [最佳实践](#最佳实践)

## 概述

Unreal Engine 提供了完整的国际化（i18n）和本地化（l10n）系统，支持文本、音频、纹理等各种资源的多语言管理。UE 的本地化系统设计用于支持 AAA 级游戏的复杂本地化需求，包括实时语言切换、复杂的文本格式化和大规模翻译管理。

### 主要特性
- **统一文本系统**：FText 系统支持完整的国际化功能
- **实时语言切换**：运行时动态切换语言
- **复杂文本格式化**：支持复数、性别、数字格式化等
- **翻译编辑器**：内置的翻译管理工具
- **本地化仪表板**：可视化的本地化进度管理
- **自动文本收集**：自动收集需要翻译的文本

## 文本本地化系统

### FText 基础使用
```cpp
// 基本的 FText 使用
#include "Internationalization/Text.h"

UCLASS()
class MYGAME_API AMyActor : public AActor
{
    GENERATED_BODY()
    
public:
    AMyActor();
    
protected:
    // 本地化文本属性
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Localization")
    FText WelcomeMessage;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Localization")
    FText PlayerName;
    
public:
    // 获取本地化文本
    UFUNCTION(BlueprintCallable, Category = "Localization")
    FText GetLocalizedWelcome();
    
    // 格式化文本
    UFUNCTION(BlueprintCallable, Category = "Localization")
    FText GetFormattedMessage(const FString& PlayerName, int32 Level);
};

// 实现
AMyActor::AMyActor()
{
    // 使用 LOCTEXT 宏定义本地化文本
    WelcomeMessage = LOCTEXT("WelcomeMessage", "Welcome to the game!");
    PlayerName = LOCTEXT("PlayerName", "Player");
}

FText AMyActor::GetLocalizedWelcome()
{
    return WelcomeMessage;
}

FText AMyActor::GetFormattedMessage(const FString& PlayerName, int32 Level)
{
    // 使用 FText::Format 进行文本格式化
    FFormatNamedArguments Args;
    Args.Add(TEXT("PlayerName"), FText::FromString(PlayerName));
    Args.Add(TEXT("Level"), FText::AsNumber(Level));
    
    return FText::Format(
        LOCTEXT("FormattedMessage", "Hello {PlayerName}, you are level {Level}!"),
        Args
    );
}

// 在 .cpp 文件末尾定义本地化命名空间
#define LOCTEXT_NAMESPACE "MyActor"
// ... 类实现 ...
#undef LOCTEXT_NAMESPACE
```

### 复数形式处理
```cpp
// 复数形式的文本处理
UCLASS()
class MYGAME_API UInventoryWidget : public UUserWidget
{
    GENERATED_BODY()
    
public:
    UFUNCTION(BlueprintCallable, Category = "UI")
    FText GetItemCountText(int32 ItemCount);
    
    UFUNCTION(BlueprintCallable, Category = "UI")
    FText GetTimeRemainingText(float TimeInSeconds);
};

FText UInventoryWidget::GetItemCountText(int32 ItemCount)
{
    // 使用复数形式
    return FText::Format(
        LOCTEXT("ItemCount", "You have {0} {0}|plural(one=item,other=items)"),
        FText::AsNumber(ItemCount)
    );
}

FText UInventoryWidget::GetTimeRemainingText(float TimeInSeconds)
{
    int32 Minutes = FMath::FloorToInt(TimeInSeconds / 60.0f);
    int32 Seconds = FMath::FloorToInt(TimeInSeconds) % 60;
    
    if (Minutes > 0)
    {
        return FText::Format(
            LOCTEXT("TimeWithMinutes", "{0} {0}|plural(one=minute,other=minutes) and {1} {1}|plural(one=second,other=seconds) remaining"),
            FText::AsNumber(Minutes),
            FText::AsNumber(Seconds)
        );
    }
    else
    {
        return FText::Format(
            LOCTEXT("TimeSecondsOnly", "{0} {0}|plural(one=second,other=seconds) remaining"),
            FText::AsNumber(Seconds)
        );
    }
}
```

### 数字和日期格式化
```cpp
// 数字和日期的本地化格式化
UCLASS()
class MYGAME_API ULocalizationHelper : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()
    
public:
    // 格式化货币
    UFUNCTION(BlueprintCallable, Category = "Localization")
    static FText FormatCurrency(float Amount, const FString& CurrencyCode = TEXT("USD"));
    
    // 格式化百分比
    UFUNCTION(BlueprintCallable, Category = "Localization")
    static FText FormatPercentage(float Value);
    
    // 格式化日期
    UFUNCTION(BlueprintCallable, Category = "Localization")
    static FText FormatDate(const FDateTime& DateTime);
    
    // 格式化时间
    UFUNCTION(BlueprintCallable, Category = "Localization")
    static FText FormatTime(const FDateTime& DateTime);
};

FText ULocalizationHelper::FormatCurrency(float Amount, const FString& CurrencyCode)
{
    return FText::AsCurrencyBase(
        FMath::RoundToInt(Amount * 100), // 转换为分
        CurrencyCode
    );
}

FText ULocalizationHelper::FormatPercentage(float Value)
{
    return FText::AsPercent(Value);
}

FText ULocalizationHelper::FormatDate(const FDateTime& DateTime)
{
    return FText::AsDate(DateTime, EDateTimeStyle::Short);
}

FText ULocalizationHelper::FormatTime(const FDateTime& DateTime)
{
    return FText::AsTime(DateTime, EDateTimeStyle::Short);
}
```

## 资源本地化

### 纹理本地化
```cpp
// 本地化纹理管理
UCLASS()
class MYGAME_API ULocalizedTextureManager : public UObject
{
    GENERATED_BODY()
    
public:
    // 本地化纹理映射
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Localization")
    TMap<FString, TSoftObjectPtr<UTexture2D>> LocalizedTextures;
    
    // 获取当前语言的纹理
    UFUNCTION(BlueprintCallable, Category = "Localization")
    UTexture2D* GetLocalizedTexture(const FString& TextureKey);
    
    // 预加载本地化纹理
    UFUNCTION(BlueprintCallable, Category = "Localization")
    void PreloadLocalizedTextures();
    
private:
    // 缓存已加载的纹理
    UPROPERTY()
    TMap<FString, UTexture2D*> LoadedTextures;
};

UTexture2D* ULocalizedTextureManager::GetLocalizedTexture(const FString& TextureKey)
{
    // 获取当前语言代码
    FString CurrentCulture = FInternationalization::Get().GetCurrentCulture()->GetName();
    
    // 构建本地化键
    FString LocalizedKey = FString::Printf(TEXT("%s_%s"), *TextureKey, *CurrentCulture);
    
    // 检查缓存
    if (LoadedTextures.Contains(LocalizedKey))
    {
        return LoadedTextures[LocalizedKey];
    }
    
    // 检查是否有本地化版本
    if (LocalizedTextures.Contains(LocalizedKey))
    {
        TSoftObjectPtr<UTexture2D> SoftTexture = LocalizedTextures[LocalizedKey];
        UTexture2D* Texture = SoftTexture.LoadSynchronous();
        
        if (Texture)
        {
            LoadedTextures.Add(LocalizedKey, Texture);
            return Texture;
        }
    }
    
    // 回退到默认版本
    FString DefaultKey = FString::Printf(TEXT("%s_en"), *TextureKey);
    if (LocalizedTextures.Contains(DefaultKey))
    {
        TSoftObjectPtr<UTexture2D> SoftTexture = LocalizedTextures[DefaultKey];
        UTexture2D* Texture = SoftTexture.LoadSynchronous();
        
        if (Texture)
        {
            LoadedTextures.Add(LocalizedKey, Texture);
            return Texture;
        }
    }
    
    return nullptr;
}
```

### 本地化资源组件
```cpp
// 自动本地化的图像组件
UCLASS(ClassGroup=(UI), meta=(BlueprintSpawnableComponent))
class MYGAME_API ULocalizedImageComponent : public UImage
{
    GENERATED_BODY()
    
public:
    ULocalizedImageComponent();
    
protected:
    // 本地化纹理键
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Localization")
    FString TextureKey;
    
    // 纹理管理器引用
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Localization")
    ULocalizedTextureManager* TextureManager;
    
    virtual void BeginPlay() override;
    
    // 语言变化回调
    UFUNCTION()
    void OnCultureChanged();
    
    // 更新本地化纹理
    UFUNCTION(BlueprintCallable, Category = "Localization")
    void UpdateLocalizedTexture();
};

ULocalizedImageComponent::ULocalizedImageComponent()
{
    // 绑定语言变化事件
    FInternationalization::Get().OnCultureChanged().AddUObject(this, &ULocalizedImageComponent::OnCultureChanged);
}

void ULocalizedImageComponent::BeginPlay()
{
    Super::BeginPlay();
    UpdateLocalizedTexture();
}

void ULocalizedImageComponent::OnCultureChanged()
{
    UpdateLocalizedTexture();
}

void ULocalizedImageComponent::UpdateLocalizedTexture()
{
    if (TextureManager && !TextureKey.IsEmpty())
    {
        UTexture2D* LocalizedTexture = TextureManager->GetLocalizedTexture(TextureKey);
        if (LocalizedTexture)
        {
            SetBrushFromTexture(LocalizedTexture);
        }
    }
}
```

## 音频本地化

### 本地化音频系统
```cpp
// 本地化音频管理器
UCLASS()
class MYGAME_API ULocalizedAudioManager : public UGameInstanceSubsystem
{
    GENERATED_BODY()
    
public:
    // 本地化音频映射
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Localization")
    TMap<FString, TSoftObjectPtr<USoundBase>> LocalizedAudioClips;
    
    // 播放本地化音频
    UFUNCTION(BlueprintCallable, Category = "Audio")
    void PlayLocalizedAudio(const FString& AudioKey, AActor* Owner = nullptr);
    
    // 获取本地化音频剪辑
    UFUNCTION(BlueprintCallable, Category = "Audio")
    USoundBase* GetLocalizedAudioClip(const FString& AudioKey);
    
    // 预加载语音包
    UFUNCTION(BlueprintCallable, Category = "Audio")
    void PreloadVoicePackage(const FString& LanguageCode);
    
protected:
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    
private:
    // 音频缓存
    UPROPERTY()
    TMap<FString, USoundBase*> AudioCache;
    
    // 当前加载的语音包
    FString CurrentVoicePackage;
};

void ULocalizedAudioManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    // 监听语言变化
    FInternationalization::Get().OnCultureChanged().AddUObject(this, &ULocalizedAudioManager::OnCultureChanged);
}

USoundBase* ULocalizedAudioManager::GetLocalizedAudioClip(const FString& AudioKey)
{
    FString CurrentCulture = FInternationalization::Get().GetCurrentCulture()->GetName();
    FString LocalizedKey = FString::Printf(TEXT("%s_%s"), *AudioKey, *CurrentCulture);
    
    // 检查缓存
    if (AudioCache.Contains(LocalizedKey))
    {
        return AudioCache[LocalizedKey];
    }
    
    // 加载本地化音频
    if (LocalizedAudioClips.Contains(LocalizedKey))
    {
        TSoftObjectPtr<USoundBase> SoftAudio = LocalizedAudioClips[LocalizedKey];
        USoundBase* AudioClip = SoftAudio.LoadSynchronous();
        
        if (AudioClip)
        {
            AudioCache.Add(LocalizedKey, AudioClip);
            return AudioClip;
        }
    }
    
    // 回退到英文版本
    FString DefaultKey = FString::Printf(TEXT("%s_en"), *AudioKey);
    if (LocalizedAudioClips.Contains(DefaultKey))
    {
        TSoftObjectPtr<USoundBase> SoftAudio = LocalizedAudioClips[DefaultKey];
        USoundBase* AudioClip = SoftAudio.LoadSynchronous();
        
        if (AudioClip)
        {
            AudioCache.Add(LocalizedKey, AudioClip);
            return AudioClip;
        }
    }
    
    return nullptr;
}

void ULocalizedAudioManager::PlayLocalizedAudio(const FString& AudioKey, AActor* Owner)
{
    USoundBase* AudioClip = GetLocalizedAudioClip(AudioKey);
    if (AudioClip)
    {
        if (Owner)
        {
            UGameplayStatics::PlaySoundAtLocation(this, AudioClip, Owner->GetActorLocation());
        }
        else
        {
            UGameplayStatics::PlaySound2D(this, AudioClip);
        }
    }
}
```

## Blueprint 国际化

### Blueprint 文本节点
```cpp
// 自定义 Blueprint 节点用于本地化
UCLASS()
class MYGAME_API ULocalizationBlueprintLibrary : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()
    
public:
    // 获取本地化文本
    UFUNCTION(BlueprintCallable, Category = "Localization", 
              meta = (CallInEditor = "true"))
    static FText GetLocalizedText(const FString& Namespace, const FString& Key, const FString& SourceString);
    
    // 格式化本地化文本
    UFUNCTION(BlueprintCallable, Category = "Localization")
    static FText FormatLocalizedText(const FText& Pattern, const TArray<FText>& Arguments);
    
    // 获取当前语言
    UFUNCTION(BlueprintCallable, Category = "Localization")
    static FString GetCurrentLanguage();
    
    // 设置语言
    UFUNCTION(BlueprintCallable, Category = "Localization")
    static bool SetLanguage(const FString& LanguageCode);
    
    // 获取可用语言列表
    UFUNCTION(BlueprintCallable, Category = "Localization")
    static TArray<FString> GetAvailableLanguages();
};

FText ULocalizationBlueprintLibrary::GetLocalizedText(const FString& Namespace, const FString& Key, const FString& SourceString)
{
    return FText::FromStringTable(FName(*Namespace), FName(*Key), SourceString);
}

FText ULocalizationBlueprintLibrary::FormatLocalizedText(const FText& Pattern, const TArray<FText>& Arguments)
{
    FFormatOrderedArguments Args;
    for (const FText& Arg : Arguments)
    {
        Args.Add(Arg);
    }
    
    return FText::Format(Pattern, Args);
}

FString ULocalizationBlueprintLibrary::GetCurrentLanguage()
{
    return FInternationalization::Get().GetCurrentCulture()->GetName();
}

bool ULocalizationBlueprintLibrary::SetLanguage(const FString& LanguageCode)
{
    return FInternationalization::Get().SetCurrentCulture(LanguageCode);
}

TArray<FString> ULocalizationBlueprintLibrary::GetAvailableLanguages()
{
    TArray<FString> Languages;
    
    // 获取可用的文化列表
    TArray<FString> CultureNames;
    FInternationalization::Get().GetCultureNames(CultureNames);
    
    for (const FString& CultureName : CultureNames)
    {
        Languages.Add(CultureName);
    }
    
    return Languages;
}
```

## 本地化工具

### 自动文本收集
```cpp
// 自定义文本收集器
UCLASS()
class MYGAME_API UCustomTextCollector : public UObject
{
    GENERATED_BODY()
    
public:
    // 收集项目中的所有文本
    UFUNCTION(CallInEditor = true, Category = "Localization")
    void CollectProjectTexts();
    
    // 导出文本到 CSV
    UFUNCTION(CallInEditor = true, Category = "Localization")
    void ExportTextsToCSV(const FString& FilePath);
    
    // 从 CSV 导入翻译
    UFUNCTION(CallInEditor = true, Category = "Localization")
    void ImportTranslationsFromCSV(const FString& FilePath);
    
private:
    // 收集的文本条目
    UPROPERTY()
    TArray<FTextCollectionEntry> CollectedTexts;
    
    // 扫描 Blueprint 资源
    void ScanBlueprintAssets();
    
    // 扫描 C++ 源文件
    void ScanCppSourceFiles();
    
    // 扫描 UMG 资源
    void ScanUMGAssets();
};

USTRUCT()
struct FTextCollectionEntry
{
    GENERATED_BODY()
    
    UPROPERTY()
    FString Namespace;
    
    UPROPERTY()
    FString Key;
    
    UPROPERTY()
    FString SourceString;
    
    UPROPERTY()
    FString AssetPath;
    
    UPROPERTY()
    TMap<FString, FString> Translations;
};
```

### 翻译验证工具
```cpp
// 翻译质量验证
UCLASS()
class MYGAME_API UTranslationValidator : public UObject
{
    GENERATED_BODY()
    
public:
    // 验证翻译完整性
    UFUNCTION(CallInEditor = true, Category = "Localization")
    void ValidateTranslationCompleteness();
    
    // 检查文本长度
    UFUNCTION(CallInEditor = true, Category = "Localization")
    void CheckTextLength();
    
    // 验证格式化参数
    UFUNCTION(CallInEditor = true, Category = "Localization")
    void ValidateFormatArguments();
    
    // 生成验证报告
    UFUNCTION(CallInEditor = true, Category = "Localization")
    void GenerateValidationReport(const FString& OutputPath);
    
private:
    USTRUCT()
    struct FValidationIssue
    {
        GENERATED_BODY()
        
        FString IssueType;
        FString Namespace;
        FString Key;
        FString Language;
        FString Description;
    };
    
    TArray<FValidationIssue> ValidationIssues;
    
    // 检查单个翻译条目
    void ValidateTranslationEntry(const FString& Namespace, const FString& Key, 
                                 const FString& SourceText, const FString& TranslatedText, 
                                 const FString& Language);
};
```

## 最佳实践

### 1. 文本组织
```cpp
// 使用命名空间组织文本
#define LOCTEXT_NAMESPACE "GameUI"

class UGameUIWidget : public UUserWidget
{
    // UI 相关文本
    FText MenuTitle = LOCTEXT("MenuTitle", "Main Menu");
    FText StartButton = LOCTEXT("StartButton", "Start Game");
    FText SettingsButton = LOCTEXT("SettingsButton", "Settings");
    FText ExitButton = LOCTEXT("ExitButton", "Exit");
};

#undef LOCTEXT_NAMESPACE

#define LOCTEXT_NAMESPACE "GameplayMessages"

class UGameplayMessageWidget : public UUserWidget
{
    // 游戏内消息
    FText LevelComplete = LOCTEXT("LevelComplete", "Level Complete!");
    FText GameOver = LOCTEXT("GameOver", "Game Over");
    FText NewHighScore = LOCTEXT("NewHighScore", "New High Score!");
};

#undef LOCTEXT_NAMESPACE
```

### 2. 性能优化
```cpp
// 文本缓存管理
UCLASS()
class MYGAME_API ULocalizedTextCache : public UGameInstanceSubsystem
{
    GENERATED_BODY()
    
public:
    // 获取缓存的文本
    FText GetCachedText(const FString& Namespace, const FString& Key);
    
    // 预加载常用文本
    void PreloadCommonTexts();
    
    // 清理缓存
    void ClearCache();
    
protected:
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    
private:
    // 文本缓存
    TMap<FString, FText> TextCache;
    
    // 生成缓存键
    FString GenerateCacheKey(const FString& Namespace, const FString& Key);
};

FText ULocalizedTextCache::GetCachedText(const FString& Namespace, const FString& Key)
{
    FString CacheKey = GenerateCacheKey(Namespace, Key);
    
    if (TextCache.Contains(CacheKey))
    {
        return TextCache[CacheKey];
    }
    
    // 创建并缓存文本
    FText NewText = FText::FromStringTable(FName(*Namespace), FName(*Key));
    TextCache.Add(CacheKey, NewText);
    
    return NewText;
}
```

### 3. 测试和调试
```cpp
// 本地化测试工具
UCLASS()
class MYGAME_API ULocalizationTestHelper : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()
    
public:
    // 启用伪本地化
    UFUNCTION(BlueprintCallable, Category = "Localization Debug")
    static void EnablePseudoLocalization(bool bEnable);
    
    // 显示文本键
    UFUNCTION(BlueprintCallable, Category = "Localization Debug")
    static void ShowTextKeys(bool bShow);
    
    // 测试所有语言
    UFUNCTION(BlueprintCallable, Category = "Localization Debug")
    static void TestAllLanguages();
    
    // 生成缺失翻译报告
    UFUNCTION(BlueprintCallable, Category = "Localization Debug")
    static void GenerateMissingTranslationReport();
};

void ULocalizationTestHelper::EnablePseudoLocalization(bool bEnable)
{
    if (bEnable)
    {
        // 设置伪本地化文化
        FInternationalization::Get().SetCurrentCulture(TEXT("qps-ploc"));
    }
    else
    {
        // 恢复默认文化
        FInternationalization::Get().SetCurrentCulture(TEXT("en"));
    }
}
```

---

*Unreal Engine 的国际化系统为 AAA 级游戏提供了企业级的本地化解决方案。更多详细信息请参考 [Unreal Engine 国际化文档](https://docs.unrealengine.com/5.3/en-US/localization-and-internationalization-in-unreal-engine/)*
