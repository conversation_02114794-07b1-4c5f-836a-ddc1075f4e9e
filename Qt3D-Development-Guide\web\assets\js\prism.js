/* PrismJS 简化版代码高亮 */
(function() {
    'use strict';

    // 基础Prism对象
    var Prism = {
        languages: {},
        plugins: {},
        
        highlight: function(text, grammar, language) {
            var tokens = this.tokenize(text, grammar);
            return this.Token.stringify(tokens, language);
        },
        
        tokenize: function(text, grammar) {
            var strarr = [text];
            var rest = grammar.rest;
            
            if (rest) {
                for (var token in rest) {
                    grammar[token] = rest[token];
                }
                delete grammar.rest;
            }
            
            tokenloop: for (var token in grammar) {
                if (!grammar.hasOwnProperty(token) || !grammar[token]) {
                    continue;
                }
                
                var patterns = grammar[token];
                patterns = (typeof patterns === "object" && !Array.isArray(patterns)) ? [patterns] : patterns;
                
                for (var j = 0; j < patterns.length; ++j) {
                    var pattern = patterns[j];
                    var inside = pattern.inside;
                    var lookbehind = !!pattern.lookbehind;
                    var greedy = !!pattern.greedy;
                    var lookbehindLength = 0;
                    var alias = pattern.alias;
                    
                    if (greedy && !pattern.pattern.global) {
                        var flags = pattern.pattern.toString().match(/[imuy]*$/)[0];
                        pattern.pattern = RegExp(pattern.pattern.source, flags + "g");
                    }
                    
                    pattern = pattern.pattern || pattern;
                    
                    for (var i = 0, pos = 0; i < strarr.length; pos += strarr[i].length, ++i) {
                        var str = strarr[i];
                        
                        if (strarr.length > text.length) {
                            break tokenloop;
                        }
                        
                        if (str instanceof Token) {
                            continue;
                        }
                        
                        if (greedy && i != strarr.length - 1) {
                            pattern.lastIndex = pos;
                            var match = pattern.exec(text);
                            if (!match) {
                                break;
                            }
                            
                            var from = match.index + (lookbehind ? match[1].length : 0);
                            var to = match.index + match[0].length;
                            var k = i;
                            var p = pos;
                            
                            for (var len = strarr.length; k < len && (p < to || (!strarr[k].type && !strarr[k - 1].greedy)); ++k) {
                                p += strarr[k].length;
                                if (from >= p) {
                                    ++i;
                                    pos = p;
                                }
                            }
                            
                            if (strarr[i] instanceof Token) {
                                continue;
                            }
                            
                            delNum = k - i;
                            str = text.slice(pos, p);
                            match.index -= pos;
                        } else {
                            pattern.lastIndex = 0;
                            var match = pattern.exec(str);
                            var delNum = 1;
                        }
                        
                        if (!match) {
                            if (greedy) {
                                break;
                            }
                            continue;
                        }
                        
                        if (lookbehind) {
                            lookbehindLength = match[1] ? match[1].length : 0;
                        }
                        
                        var from = match.index + lookbehindLength;
                        var match = match[0].slice(lookbehindLength);
                        var to = from + match.length;
                        var before = str.slice(0, from);
                        var after = str.slice(to);
                        
                        var args = [i, delNum];
                        
                        if (before) {
                            ++i;
                            pos += before.length;
                            args.push(before);
                        }
                        
                        var wrapped = new Token(token, inside ? Prism.tokenize(match, inside) : match, alias, match, greedy);
                        args.push(wrapped);
                        
                        if (after) {
                            args.push(after);
                        }
                        
                        Array.prototype.splice.apply(strarr, args);
                        
                        if (delNum != 1) {
                            Prism.matchGrammar(text, strarr, grammar, i, pos, true, token);
                        }
                        
                        if (greedy) {
                            break;
                        }
                    }
                }
            }
            
            return strarr;
        },
        
        Token: function(type, content, alias, matchedStr, greedy) {
            this.type = type;
            this.content = content;
            this.alias = alias;
            this.length = (matchedStr || "").length|0;
            this.greedy = !!greedy;
        }
    };
    
    Prism.Token.stringify = function(o, language) {
        if (typeof o == "string") {
            return o;
        }
        
        if (Array.isArray(o)) {
            return o.map(function(element) {
                return Prism.Token.stringify(element, language);
            }).join('');
        }
        
        var env = {
            type: o.type,
            content: Prism.Token.stringify(o.content, language),
            tag: 'span',
            classes: ['token', o.type],
            attributes: {},
            language: language
        };
        
        if (o.alias) {
            var aliases = Array.isArray(o.alias) ? o.alias : [o.alias];
            Array.prototype.push.apply(env.classes, aliases);
        }
        
        var attributes = '';
        for (var name in env.attributes) {
            attributes += ' ' + name + '="' + (env.attributes[name] || '').replace(/"/g, '&quot;') + '"';
        }
        
        return '<' + env.tag + ' class="' + env.classes.join(' ') + '"' + attributes + '>' + env.content + '</' + env.tag + '>';
    };
    
    function Token(type, content, alias, matchedStr, greedy) {
        this.type = type;
        this.content = content;
        this.alias = alias;
        this.length = (matchedStr || "").length|0;
        this.greedy = !!greedy;
    }
    Token.stringify = Prism.Token.stringify;
    
    // C++ 语法定义
    Prism.languages.cpp = Prism.languages.extend('c', {
        'class-name': {
            pattern: /(\b(?:class|struct|enum|union)\s+|\b(?:typename|template)\s*<[^<>]*>\s*)\w+/,
            lookbehind: true
        },
        'keyword': /\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|class|compl|const|constexpr|const_cast|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|false|final|float|for|friend|goto|if|inline|int|long|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|true|try|typedef|typeid|typename|union|unsigned|using|virtual|void|volatile|wchar_t|while)\b/,
        'number': /(?:\b0b[01']+|\b0x(?:[\da-f']*\.)?[\da-f']+(?:p[+-]?\d'*\d*)?|(?:\b\d'*\d*\.?\d'*\d*|\B\.\d'*\d*)(?:e[+-]?\d'*\d*)?)[ful]*/i,
        'operator': />>=?|<<=?|->|([-+&|:])\1|[?:~]|[-+*/%&|^!=<>]=?/,
        'boolean': /\b(?:true|false)\b/
    });
    
    // QML 语法定义
    Prism.languages.qml = {
        'comment': [
            {
                pattern: /(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,
                lookbehind: true
            },
            {
                pattern: /(^|[^\\:])\/\/.*/,
                lookbehind: true
            }
        ],
        'string': {
            pattern: /(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,
            greedy: true
        },
        'property': /\b[a-z_]\w*(?=\s*:)/i,
        'keyword': /\b(?:import|as|property|signal|function|var|let|const|if|else|for|while|do|switch|case|default|break|continue|return|try|catch|finally|throw|new|delete|typeof|instanceof|in|of|void|null|undefined|true|false)\b/,
        'function': /[a-z_]\w*(?=\s*\()/i,
        'number': /\b(?:0x[\da-f]+|\d*\.?\d+(?:e[+-]?\d+)?)\b/i,
        'operator': /[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,
        'punctuation': /[{}[\];(),.:]/
    };
    
    // Bash 语法定义
    Prism.languages.bash = {
        'shebang': {
            pattern: /^#!\s*\/.*/,
            alias: 'important'
        },
        'comment': {
            pattern: /(^|[^"{\\$])#.*/,
            lookbehind: true
        },
        'function-name': [
            {
                pattern: /(\bfunction\s+)\w+(?=(?:\s*\(?:\s*\))?\s*\{)/,
                lookbehind: true,
                alias: 'function'
            },
            {
                pattern: /\b\w+(?=\s*\(\s*\)\s*\{)/,
                alias: 'function'
            }
        ],
        'for-or-select': {
            pattern: /(\b(?:for|select)\s+)\w+(?=\s+in\s)/,
            alias: 'variable',
            lookbehind: true
        },
        'assign-left': {
            pattern: /(^|[\s;|&]|[<>]\()\w+(?=\+?=)/,
            inside: {
                'environment': {
                    pattern: RegExp("(^|[\\s;|&]|[<>]\\()" + "(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_SUBSHELL|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)(?=\\+?=)"),
                    alias: 'environment',
                    lookbehind: true
                }
            },
            alias: 'variable',
            lookbehind: true
        },
        'string': [
            {
                pattern: /((?:^|[^<])<<-?\s*)(\w+?)\s*(?:\r?\n|\r)(?:[\s\S])*?(?:\r?\n|\r)\2/,
                lookbehind: true,
                greedy: true,
                inside: null
            },
            {
                pattern: /((?:^|[^<])<<-?\s*)(["'])(\w+)\2\s*(?:\r?\n|\r)(?:[\s\S])*?(?:\r?\n|\r)\3/,
                lookbehind: true,
                greedy: true
            },
            {
                pattern: /(^|[^\\](?:\\\\)*)(["'])(?:\\[\s\S]|\$\([^)]+\)|\$(?!\()|`[^`]+`|(?!\2)[^\\`$])*\2/,
                lookbehind: true,
                greedy: true,
                inside: null
            }
        ],
        'environment': {
            pattern: /\$?\b[A-Z_][A-Z0-9_]*\b/,
            alias: 'constant'
        },
        'variable': [
            {
                pattern: /\$?\(\([\s\S]+?\)\)/,
                greedy: true,
                inside: {
                    'variable': [
                        {
                            pattern: /(^\$\(\([\s\S]+)\)\)$/,
                            lookbehind: true
                        },
                        /^\$\(\(/
                    ],
                    'number': /\b0x[\dA-Fa-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:[Ee]-?\d+)?/,
                    'operator': /--?|-=|\+\+?|\+=|!=?|~|\*\*?|\*=|\/=?|%=?|<<=?|>>=?|<=?|>=?|==?|&&?|&=|\^=?|\|\|?|\|=|\?|:/,
                    'punctuation': /\(\(?|\)\)?|,|;/
                }
            },
            {
                pattern: /\$\((?:\([^)]+\)|[^()])+\)|`[^`]+`/,
                greedy: true,
                inside: {
                    'variable': /^\$\(|^`|\)$|`$/
                }
            },
            {
                pattern: /\$\{[^}]+\}/,
                greedy: true,
                inside: {
                    'operator': /:[-=?+]?|[!\/]|##?|%%?|\^\^?|,,?/,
                    'punctuation': /[\[\]]/,
                    'environment': {
                        pattern: RegExp("(\\{)" + "(BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_SUBSHELL|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)" + "(?=\\}|:)"),
                        lookbehind: true,
                        alias: 'environment'
                    }
                }
            },
            /\$(?:\w+|[#?*!@$])/
        ],
        'entity': /\\(?:[abceEfnrtv\\"]|O?[0-7]{1,3}|x[0-9a-fA-F]{1,2}|u[0-9a-fA-F]{4}|U[0-9a-fA-F]{8})/,
        'keyword': /\b(?:if|then|else|elif|fi|for|while|in|case|esac|function|select|do|done|until)\b/,
        'builtin': /\b(?:alias|bg|bind|break|builtin|caller|cd|command|compgen|complete|compopt|continue|declare|dirs|disown|echo|enable|eval|exec|exit|export|fc|fg|getopts|hash|help|history|jobs|kill|let|local|logout|mapfile|popd|printf|pushd|pwd|read|readarray|readonly|return|set|shift|shopt|source|suspend|test|times|trap|type|typeset|ulimit|umask|unalias|unset|wait)\b/,
        'boolean': /\b(?:true|false)\b/,
        'file-descriptor': {
            pattern: /\B&\d\b/,
            alias: 'important'
        },
        'operator': /\d?<>|>\||\+=|==?|!=?|=~|<<[<-]?|[&\d]?>>|\d?[<>]&?|&[>&]?|\|[&|]?|<=?|>=?|[=!]=|[&|]{2}|[<>]\(|\)\s*\{|\{|\}|&&|\|\||[?*+@!]/,
        'punctuation': /\$?\(\(?|\)\)?|\.\.|[{}[\];\\]/,
        'number': /(?:\b\d+\.?\d*|\B\.\d+)(?:[Ee]-?\d+)?/
    };
    
    // 自动高亮
    if (typeof document !== 'undefined') {
        document.addEventListener('DOMContentLoaded', function() {
            var elements = document.querySelectorAll('code[class*="language-"], pre[class*="language-"]');
            
            for (var i = 0; i < elements.length; i++) {
                var element = elements[i];
                var language = element.className.match(/language-(\w+)/);
                
                if (language) {
                    language = language[1];
                    var grammar = Prism.languages[language];
                    
                    if (grammar) {
                        var code = element.textContent;
                        element.innerHTML = Prism.highlight(code, grammar, language);
                    }
                }
            }
        });
    }
    
    // 导出Prism对象
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = Prism;
    } else if (typeof window !== 'undefined') {
        window.Prism = Prism;
    }
})();
