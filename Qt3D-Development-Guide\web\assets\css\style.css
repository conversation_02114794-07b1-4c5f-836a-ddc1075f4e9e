/* 基础样式 - 苹果风格 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 苹果风格色彩系统 */
    --primary-color: #007AFF;
    --secondary-color: #5856D6;
    --accent-color: #FF9500;
    --success-color: #34C759;
    --text-color: #1d1d1f;
    --text-secondary: #86868b;
    --text-light: #a1a1a6;
    --bg-color: #ffffff;
    --bg-secondary: #f5f5f7;
    --bg-tertiary: #fafafa;
    --border-color: #d2d2d7;
    --shadow-light: 0 4px 16px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.12);
    --shadow-heavy: 0 16px 64px rgba(0, 0, 0, 0.16);
    --border-radius: 12px;
    --border-radius-large: 18px;
    --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-fast: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    /* 苹果字体系统 */
    --font-system: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

body {
    font-family: var(--font-system);
    line-height: 1.47059;
    color: var(--text-color);
    background-color: var(--bg-color);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 17px;
    font-weight: 400;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏 - 苹果风格 */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(251, 251, 253, 0.8);
    backdrop-filter: saturate(180%) blur(20px);
    border-bottom: 0.5px solid rgba(0, 0, 0, 0.08);
    z-index: 1000;
    transition: var(--transition);
}

.nav-container {
    max-width: 1024px;
    margin: 0 auto;
    padding: 0 22px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 44px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-img {
    width: 24px;
    height: 24px;
}

.logo-text {
    font-size: 21px;
    font-weight: 600;
    color: var(--text-color);
    letter-spacing: -0.022em;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 32px;
}

.nav-link {
    text-decoration: none;
    color: var(--text-color);
    font-size: 12px;
    font-weight: 400;
    letter-spacing: -0.01em;
    transition: var(--transition-fast);
    position: relative;
    padding: 8px 0;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 1px;
    background: var(--primary-color);
    transition: var(--transition-fast);
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 100%;
}

.language-selector select {
    padding: 4px 8px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-color);
    font-size: 12px;
    font-family: var(--font-system);
    cursor: pointer;
    transition: var(--transition-fast);
    -webkit-appearance: none;
    appearance: none;
}

.language-selector select:hover {
    border-color: var(--primary-color);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 4px;
}

.nav-toggle span {
    width: 18px;
    height: 1px;
    background: var(--text-color);
    margin: 2px 0;
    transition: var(--transition-fast);
    border-radius: 1px;
}

/* 英雄区域 - 苹果风格 */
.hero {
    padding: 88px 0 96px;
    background: var(--bg-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1024px;
    margin: 0 auto;
    padding: 0 22px;
    text-align: center;
}

.hero-title {
    font-size: 56px;
    font-weight: 600;
    line-height: 1.07143;
    letter-spacing: -0.005em;
    margin-bottom: 24px;
    color: var(--text-color);
}

.hero-subtitle {
    font-size: 28px;
    font-weight: 400;
    line-height: 1.14286;
    letter-spacing: 0.007em;
    color: var(--text-secondary);
    margin-bottom: 24px;
}

.hero-description {
    font-size: 21px;
    font-weight: 400;
    line-height: 1.381;
    letter-spacing: 0.011em;
    color: var(--text-secondary);
    margin-bottom: 48px;
    max-width: 640px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 980px;
    text-decoration: none;
    font-weight: 400;
    font-size: 17px;
    line-height: 1.23536;
    letter-spacing: -0.022em;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    min-width: 120px;
    text-align: center;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #0051D5;
    transform: scale(1.02);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.02);
}

/* 3D 立方体动画 */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    perspective: 1000px;
}

.hero-cube {
    width: 200px;
    height: 200px;
    position: relative;
    transform-style: preserve-3d;
    animation: rotate 20s infinite linear;
}

.cube-face {
    position: absolute;
    width: 200px;
    height: 200px;
    border: 2px solid var(--primary-color);
    background: rgba(65, 205, 82, 0.1);
    backdrop-filter: blur(10px);
}

.cube-front { transform: rotateY(0deg) translateZ(100px); }
.cube-back { transform: rotateY(180deg) translateZ(100px); }
.cube-right { transform: rotateY(90deg) translateZ(100px); }
.cube-left { transform: rotateY(-90deg) translateZ(100px); }
.cube-top { transform: rotateX(90deg) translateZ(100px); }
.cube-bottom { transform: rotateX(-90deg) translateZ(100px); }

@keyframes rotate {
    0% { transform: rotateX(0deg) rotateY(0deg); }
    100% { transform: rotateX(360deg) rotateY(360deg); }
}

/* 引擎展示区域 - 苹果风格 */
.engines-showcase {
    padding: 96px 0;
    background: var(--bg-secondary);
}

.section-title {
    text-align: center;
    font-size: 48px;
    font-weight: 600;
    line-height: 1.08349;
    letter-spacing: -0.003em;
    margin-bottom: 64px;
    color: var(--text-color);
}

.engines-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2em;
    margin: 2em 0;
    max-width: 1024px;
}

.engine-showcase {
    background: var(--bg-color);
    border-radius: var(--border-radius-large);
    padding: 32px;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.engine-showcase:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.engine-header {
    text-align: center;
    margin-bottom: 24px;
}

.engine-logo {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    background: var(--bg-secondary);
}

.engine-logo img {
    width: 40px;
    height: 40px;
}

.unity-logo, .unreal-logo, .kanzi-logo {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.unity-logo {
    background: linear-gradient(135deg, #000000, #333333);
    color: white;
}

.unreal-logo {
    background: linear-gradient(135deg, #0E4B99, #2196F3);
    color: white;
}

.kanzi-logo {
    background: linear-gradient(135deg, #8E44AD, #9B59B6);
    color: white;
}

.cocos-logo {
    background: linear-gradient(135deg, #55ACEE, #1DA1F2);
    color: white;
}

.godot-logo {
    background: linear-gradient(135deg, #478CBF, #5DADE2);
    color: white;
}

.engine-showcase h3 {
    font-size: 24px;
    font-weight: 600;
    line-height: 1.16667;
    letter-spacing: 0.009em;
    margin-bottom: 8px;
    color: var(--text-color);
}

.engine-tagline {
    font-size: 15px;
    color: var(--text-secondary);
    margin: 0.5em 0 0.5em 0;
    text-align: center;
}

.engine-highlights ul {
    list-style: none;
    padding: 0;
    margin-bottom: 32px;
}

.engine-highlights li {
    padding: 8px 0;
    font-size: 15px;
    line-height: 1.33337;
    color: var(--text-secondary);
    position: relative;
    padding-left: 20px;
}

.engine-highlights li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: 600;
}

.engine-actions {
    display: flex;
    gap: 12px;
    flex-direction: column;
}

.engine-demo {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.engine-demo h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-color);
}

.engine-demo p {
    font-size: 14px;
    line-height: 1.4;
    color: var(--text-secondary);
    margin: 0;
}

.engine-showcase.unity .engine-demo {
    border-left-color: #ff6b35;
}

.engine-showcase.unreal .engine-demo {
    border-left-color: #0e4b99;
}

.engine-showcase.kanzi .engine-demo {
    border-left-color: #8e44ad;
}

.engine-showcase.cocos .engine-demo {
    border-left-color: #55acee;
}

.engine-showcase.godot .engine-demo {
    border-left-color: #478cbf;
}

.engine-actions {
    display: flex;
    gap: 12px;
    flex-direction: column;
}

.engine-actions .btn {
    padding: 8px 16px;
    font-size: 15px;
    border-radius: 8px;
    min-width: auto;
}

/* 技术特性对比区域 */
.features-comparison {
    padding: 96px 0;
    background: var(--bg-color);
}

.features-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 48px;
    gap: 8px;
    flex-wrap: wrap;
}

.tab-button {
    padding: 12px 24px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.tab-button:hover {
    background: var(--bg-tertiary);
    color: var(--text-color);
}

.tab-button.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 32px;
    max-width: 1024px;
    margin: 0 auto;
}

.feature-comparison {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 24px;
    transition: var(--transition);
}

.feature-comparison:hover {
    box-shadow: var(--shadow-light);
    transform: translateY(-2px);
}

.feature-comparison h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-color);
    padding-bottom: 12px;
    border-bottom: 2px solid var(--border-color);
}

.feature-comparison ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-comparison li {
    padding: 8px 0;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-secondary);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-comparison li:last-child {
    border-bottom: none;
}

.feature-name {
    font-weight: 600;
    color: var(--text-color);
    display: inline-block;
    min-width: 80px;
}

.data-sources {
    margin-top: 48px;
    padding: 24px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.data-sources h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-color);
}

.data-sources p {
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.data-sources p:last-child {
    margin-bottom: 0;
}

.data-sources a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.data-sources a:hover {
    text-decoration: underline;
}

.data-sources .disclaimer {
    font-style: italic;
    color: var(--text-tertiary);
    font-size: 12px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid var(--border-color);
}

/* 开发效率对比样式 */
.efficiency-details {
    margin-top: 48px;
    padding: 32px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-large);
}

.efficiency-details h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 24px;
    color: var(--text-color);
    text-align: center;
}

.efficiency-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 32px;
}

.metric-card {
    background: var(--bg-color);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow-light);
}

.metric-card h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-color);
    text-align: center;
}

.metric-comparison {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.engine-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    min-width: 80px;
    text-align: right;
}

.metric-bar {
    flex: 1;
    height: 20px;
    background: var(--bg-tertiary);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 10px;
    transition: width 0.8s ease;
}

.metric-value {
    font-size: 12px;
    color: var(--text-secondary);
    min-width: 40px;
    text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .efficiency-metrics {
        grid-template-columns: 1fr;
    }

    .metric-item {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .engine-name {
        text-align: left;
        min-width: auto;
    }
}

/* 国际化展示区域 */
.internationalization-showcase {
    padding: 96px 0;
    background: var(--bg-secondary);
}

.i18n-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
    margin-bottom: 64px;
}

.i18n-engine {
    background: var(--bg-color);
    border-radius: var(--border-radius-large);
    padding: 32px;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.i18n-engine:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.i18n-engine h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 24px;
    color: var(--text-color);
    text-align: center;
    padding-bottom: 16px;
    border-bottom: 2px solid var(--border-color);
}

.i18n-features {
    margin-bottom: 24px;
}

.i18n-feature {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 20px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: 8px;
}

.feature-icon {
    font-size: 24px;
    flex-shrink: 0;
    margin-top: 4px;
}

.feature-content h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-color);
}

.feature-content p {
    font-size: 14px;
    line-height: 1.4;
    color: var(--text-secondary);
    margin: 0;
}

.language-demo {
    padding: 20px;
    background: var(--bg-tertiary);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.language-demo h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-color);
}

.language-samples {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.lang-sample {
    padding: 8px 12px;
    background: var(--bg-color);
    border-radius: 6px;
    font-size: 14px;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.lang-sample:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.tool-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tool-item {
    padding: 6px 12px;
    background: var(--primary-color);
    color: white;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.i18n-comparison-table {
    background: var(--bg-color);
    border-radius: var(--border-radius-large);
    padding: 32px;
    box-shadow: var(--shadow-light);
}

.i18n-comparison-table h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 24px;
    color: var(--text-color);
    text-align: center;
}

.table-container {
    overflow-x: auto;
}

.i18n-comparison-table table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: #fff;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    border-radius: 10px;
    overflow: hidden;
    margin: 24px 0;
}

.i18n-comparison-table th,
.i18n-comparison-table td {
    padding: 16px 12px;
    text-align: center;
    border-bottom: 1.5px solid #e5e7eb;
    font-size: 1rem;
}

.i18n-comparison-table th {
    background: #f5f7fa;
    font-weight: bold;
    color: #222;
    border-bottom: 2.5px solid #d1d5db;
}

.i18n-comparison-table tr:last-child td {
    border-bottom: none;
}

.i18n-comparison-table tbody tr:hover {
    background: #f0f6ff;
    transition: background 0.2s;
}

.i18n-comparison-table td {
    color: #333;
}

.i18n-comparison-table th:first-child, .i18n-comparison-table td:first-child {
    background: #f9fafb;
    font-weight: 500;
}

.i18n-comparison-table th, .i18n-comparison-table td {
    min-width: 110px;
}

@media (max-width: 900px) {
    .i18n-comparison-table th, .i18n-comparison-table td {
        padding: 10px 4px;
        font-size: 0.95rem;
    }
}

@media (max-width: 600px) {
    .i18n-comparison-table, .i18n-comparison-table thead, .i18n-comparison-table tbody, .i18n-comparison-table tr, .i18n-comparison-table th, .i18n-comparison-table td {
        display: block;
        width: 100%;
    }
    .i18n-comparison-table thead {
        display: none;
    }
    .i18n-comparison-table tr {
        margin-bottom: 18px;
        box-shadow: 0 1px 4px rgba(0,0,0,0.04);
        border-radius: 8px;
        background: #fff;
        overflow: hidden;
    }
    .i18n-comparison-table td {
        text-align: left;
        padding: 10px 8px;
        border-bottom: 1px solid #f0f0f0;
        position: relative;
    }
    .i18n-comparison-table td:before {
        content: attr(data-i18n);
        font-weight: bold;
        color: #888;
        display: block;
        margin-bottom: 4px;
        font-size: 0.92em;
    }
}

.i18n-overview {
    text-align: center;
    margin-bottom: 32px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.i18n-overview p {
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-secondary);
    margin: 0;
}

.comparison-note {
    margin-top: 24px;
    padding: 16px;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--accent-color);
}

.comparison-note p {
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-secondary);
    margin: 0;
}

/* 对比区域 */
.comparison {
    padding: 100px 0;
    background: var(--bg-light);
}

.comparison-intro {
    text-align: center;
    margin-bottom: 60px;
}

.comparison-intro p {
    font-size: 1.2rem;
    color: var(--text-light);
}

.comparison-table-container {
    overflow-x: auto;
    margin-bottom: 60px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.comparison-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: #fff;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    border-radius: 10px;
    overflow: hidden;
    margin: 24px 0;
}

.comparison-table th,
.comparison-table td {
    padding: 16px 12px;
    text-align: center;
    border-bottom: 1.5px solid #e5e7eb;
    font-size: 1rem;
}

.comparison-table th {
    background: #f5f7fa;
    font-weight: bold;
    color: #222;
    border-bottom: 2.5px solid #d1d5db;
}

.comparison-table tr:last-child td {
    border-bottom: none;
}

.comparison-table tbody tr:hover {
    background: #f0f6ff;
    transition: background 0.2s;
}

.comparison-table td {
    color: #333;
}

.comparison-table th:first-child, .comparison-table td:first-child {
    background: #f9fafb;
    font-weight: 500;
}

.comparison-table th, .comparison-table td {
    min-width: 110px;
}

@media (max-width: 900px) {
    .comparison-table th, .comparison-table td {
        padding: 10px 4px;
        font-size: 0.95rem;
    }
}

@media (max-width: 600px) {
    .comparison-table, .comparison-table thead, .comparison-table tbody, .comparison-table tr, .comparison-table th, .comparison-table td {
        display: block;
        width: 100%;
    }
    .comparison-table thead {
        display: none;
    }
    .comparison-table tr {
        margin-bottom: 18px;
        box-shadow: 0 1px 4px rgba(0,0,0,0.04);
        border-radius: 8px;
        background: #fff;
        overflow: hidden;
    }
    .comparison-table td {
        text-align: left;
        padding: 10px 8px;
        border-bottom: 1px solid #f0f0f0;
        position: relative;
    }
    .comparison-table td:before {
        content: attr(data-i18n);
        font-weight: bold;
        color: #888;
        display: block;
        margin-bottom: 4px;
        font-size: 0.92em;
    }
}

.rating {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.rating.excellent {
    background: #d4edda;
    color: #155724;
}

.rating.good {
    background: #fff3cd;
    color: #856404;
}

.rating.moderate {
    background: #ffeaa7;
    color: #6c5ce7;
}

.rating.hard,
.rating.limited {
    background: #f8d7da;
    color: #721c24;
}

.rating.easy {
    background: #d1ecf1;
    color: #0c5460;
}

.comparison-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.engine-card {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.engine-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.engine-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid;
}

.engine-card.qt3d h3 { border-color: var(--primary-color); color: var(--primary-color); }
.engine-card.unity h3 { border-color: #ff6b35; color: #ff6b35; }
.engine-card.unreal h3 { border-color: #0e4b99; color: #0e4b99; }
.engine-card.kanzi h3 { border-color: #8e44ad; color: #8e44ad; }

.engine-pros h4,
.engine-use-cases h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-color);
}

.engine-pros ul,
.engine-use-cases ul {
    list-style: none;
    padding-left: 0;
}

.engine-pros li,
.engine-use-cases li {
    padding: 5px 0;
    color: var(--text-light);
    position: relative;
    padding-left: 20px;
}

.engine-pros li::before,
.engine-use-cases li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* 国际化区域 */
.internationalization {
    padding: 100px 0;
    background: var(--bg-color);
}

.i18n-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.i18n-text h3 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-color);
}

.i18n-text p {
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 30px;
    line-height: 1.7;
}

.i18n-features {
    list-style: none;
    margin-bottom: 40px;
}

.i18n-features li {
    padding: 10px 0;
    color: var(--text-light);
    position: relative;
    padding-left: 30px;
    font-size: 1.1rem;
}

.i18n-features li::before {
    content: '🌍';
    position: absolute;
    left: 0;
}

.language-samples {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.language-sample {
    padding: 30px;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
    cursor: pointer;
}

.language-sample:last-child {
    border-bottom: none;
}

.language-sample:hover,
.language-sample.active {
    background: rgba(65, 205, 82, 0.05);
}

.language-sample h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-color);
}

.language-sample p {
    font-size: 1.1rem;
    color: var(--text-light);
}

/* 文档区域 */
.documentation {
    padding: 100px 0;
    background: var(--bg-light);
}

.docs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.doc-card {
    background: white;
    padding: 40px 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.doc-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.doc-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.doc-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
}

.doc-card p {
    color: var(--text-light);
    margin-bottom: 25px;
    line-height: 1.6;
}

.doc-link {
    display: inline-block;
    padding: 12px 25px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.doc-link:hover {
    background: #36b946;
    transform: translateY(-2px);
}

/* 文档页面样式 */
.doc-layout {
    display: flex;
    margin-top: 44px; /* 导航栏高度 */
    min-height: calc(100vh - 44px);
}

/* 左侧目录样式 */
.doc-sidebar {
    width: 280px;
    flex-shrink: 0;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    height: calc(100vh - 44px);
    position: sticky;
    top: 44px;
    overflow-y: auto;
    padding: 24px 0;
}

.toc-container {
    padding: 0 16px;
}

.toc-container ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc-container li {
    margin: 8px 0;
}

.toc-container a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 14px;
    line-height: 1.4;
    display: block;
    padding: 4px 12px;
    border-radius: 6px;
    transition: var(--transition-fast);
}

.toc-container a:hover {
    color: var(--primary-color);
    background: var(--bg-tertiary);
}

.toc-container a.active {
    color: var(--primary-color);
    background: var(--bg-tertiary);
    font-weight: 500;
}

/* 二级目录缩进 */
.toc-container ul ul {
    padding-left: 16px;
}

/* 主要内容区域样式优化 */
.doc-section {
    flex-grow: 1;
    padding: 32px 48px;
    background: var(--bg-color);
}

.doc-container {
    max-width: 800px;
    margin: 0 auto;
}

.doc-content {
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
}

/* 优化标题样式 */
.doc-content h1 {
    font-size: 36px;
    margin: 0 0 32px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.doc-content h2 {
    font-size: 28px;
    margin: 48px 0 24px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.doc-content h3 {
    font-size: 22px;
    margin: 32px 0 16px;
}

.doc-content h4 {
    font-size: 18px;
    margin: 24px 0 12px;
}

/* 优化段落和列表样式 */
.doc-content p {
    margin: 16px 0;
    line-height: 1.7;
}

.doc-content ul,
.doc-content ol {
    padding-left: 24px;
    margin: 16px 0;
}

.doc-content li {
    margin: 8px 0;
}

/* 优化代码块样式 */
.doc-content pre {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 16px;
    margin: 16px 0;
    overflow-x: auto;
}

.doc-content code {
    font-family: var(--font-mono);
    font-size: 14px;
    padding: 2px 6px;
    background: var(--bg-secondary);
    border-radius: 4px;
}

.doc-content pre code {
    padding: 0;
    background: none;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .doc-layout {
        flex-direction: column;
    }

    .doc-sidebar {
        width: 100%;
        height: auto;
        position: relative;
        top: 0;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .doc-section {
        padding: 24px 16px;
    }

    .doc-content h1 {
        font-size: 28px;
    }

    .doc-content h2 {
        font-size: 24px;
    }

    .doc-content h3 {
        font-size: 20px;
    }

    .doc-content h4 {
        font-size: 16px;
    }
}

/* 页脚 */
.footer {
    background: #2c3e50;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.footer-section p {
    color: #bdc3c7;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 20px;
    text-align: center;
    color: #bdc3c7;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .i18n-content {
        grid-template-columns: 1fr;
    }
    
    .features-grid,
    .comparison-cards,
    .docs-grid {
        grid-template-columns: 1fr;
    }
    
    .hero-cube {
        width: 150px;
        height: 150px;
    }
    
    .cube-face {
        width: 150px;
        height: 150px;
    }
    
    .cube-front { transform: rotateY(0deg) translateZ(75px); }
    .cube-back { transform: rotateY(180deg) translateZ(75px); }
    .cube-right { transform: rotateY(90deg) translateZ(75px); }
    .cube-left { transform: rotateY(-90deg) translateZ(75px); }
    .cube-top { transform: rotateX(90deg) translateZ(75px); }
    .cube-bottom { transform: rotateX(-90deg) translateZ(75px); }
}

/* 流程图SVG适配样式（优化） */
.workflow-svg {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    margin: 1em 0 0.5em 0;
    min-height: 120px;
    max-height: 320px;
    overflow-x: auto;
    overflow-y: hidden;
    background: #fafafa;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}
.workflow-svg img, .workflow-svg svg {
    max-width: 100%;
    max-height: 300px;
    height: auto;
    display: block;
    margin: 0 auto;
}
/* 保证卡片内容紧凑 */
.engine-showcase .engine-tagline {
    font-size: 15px;
    color: var(--text-secondary);
    margin: 0.5em 0 0.5em 0;
    text-align: center;
}
/* 六宫格适配优化 */
.engines-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2em;
    margin: 2em 0;
}

/* 流程图放大模态框样式（优化放大显示） */
.workflow-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0; top: 0; width: 100vw; height: 100vh;
    background: rgba(0,0,0,0.6);
    justify-content: center;
    align-items: center;
}
.workflow-modal-content {
    background: #fff;
    border-radius: 12px;
    padding: 1.5em;
    max-width: 95vw;
    max-height: 90vh;
    box-shadow: 0 8px 32px rgba(0,0,0,0.18);
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    overflow: auto; /* 允许内容滚动 */
}
.workflow-modal-content img {
    max-width: none;   /* 不限制最大宽度 */
    max-height: none;  /* 不限制最大高度 */
    width: auto;
    height: auto;
    display: block;
}
.workflow-modal-close {
    position: absolute;
    top: 12px; right: 18px;
    font-size: 2em;
    color: #888;
    cursor: pointer;
    font-weight: bold;
    z-index: 2;
}
.workflow-img {
    cursor: zoom-in;
    transition: box-shadow 0.2s;
}
.workflow-img:hover {
    box-shadow: 0 0 0 2px var(--primary-color, #007AFF);
}

/* 流程图弹窗缩放按钮样式 */
.workflow-modal-toolbar {
    display: flex;
    gap: 1em;
    justify-content: center;
    margin-bottom: 1em;
    position: sticky;
    top: 0;
    z-index: 2;
    background: #fff;
    padding-top: 0.5em;
}
.workflow-modal-toolbar button {
    background: var(--bg-secondary, #f5f5f7);
    border: none;
    border-radius: 6px;
    padding: 0.5em 1.2em;
    font-size: 1.2em;
    cursor: pointer;
    box-shadow: 0 1px 4px rgba(0,0,0,0.06);
    transition: background 0.2s;
}
.workflow-modal-toolbar button:hover {
    background: var(--primary-color, #007AFF);
    color: #fff;
}

/* 流程图弹窗图片滚动与按钮固定 */
.workflow-modal-toolbar {
    display: flex;
    gap: 1em;
    justify-content: center;
    margin-bottom: 1em;
    position: sticky;
    top: 0;
    z-index: 2;
    background: #fff;
    padding-top: 0.5em;
}
.workflow-modal-imgbox {
    width: 100%;
    max-height: 70vh;
    overflow: auto;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}
.workflow-modal-imgbox img {
    display: block;
    margin: 0 auto;
}

/* 兜风多语言与特色功能区块样式 */
.dofun-i18n-feature {
    margin: 2em 0 2em 0;
}
.dofun-feature-content {
    display: flex;
    flex-wrap: wrap;
    gap: 2em;
    align-items: flex-start;
}
.dofun-feature-content ul {
    flex: 2 1 320px;
    font-size: 1.1em;
    line-height: 2;
    margin: 0;
    padding-left: 1.5em;
}
.dofun-video-box {
    flex: 1 1 320px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}
.dofun-video-cover {
    width: 100%;
    max-width: 400px;
    cursor: pointer;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.dofun-video-modal-box {
    width: 80vw;
    max-width: 900px;
}
@media (max-width: 900px) {
  .dofun-feature-content {
    flex-direction: column;
    gap: 1.5em;
  }
  .dofun-video-box, .dofun-feature-content ul {
    flex: 1 1 100%;
    max-width: 100%;
  }
  .dofun-video-cover {
    max-width: 100%;
  }
}

/* 案例卡片间距优化 */
.case-list .case-card {
    margin-bottom: 5em;
}

/* 多语言图片田字格布局优化 */
.multilingual-gallery {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 1em;
    justify-items: center;
    align-items: center;
    margin: 1em 0;
    width: 100%;
    max-width: 420px;
    margin-left: auto;
    margin-right: auto;
}
.multilingual-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}
.multilingual-item img {
    max-width: 100%;
    max-height: 240px;
    min-height: 120px;
    width: 100%;
    object-fit: contain;
    border-radius: 12px;
    box-shadow: 0 1px 8px rgba(0,0,0,0.10);
    background: #fff;
}
.multilingual-label {
    margin-top: 0.3em;
    font-size: 1em;
    color: #888;
    text-align: center;
    letter-spacing: 0.1em;
}
