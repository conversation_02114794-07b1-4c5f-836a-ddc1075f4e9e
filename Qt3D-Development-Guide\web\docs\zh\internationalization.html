<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qt 3D 国际化与本地化指南</title> <!-- The title will be replaced for each page -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/docs.css">
    <link rel="stylesheet" href="../../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html" class="logo-text" data-i18n="nav-logo-text">3D 引擎指南</a>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="../index.html#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="../index.html#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="../index.html#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 文档内容区域 -->
    <div class="doc-layout container">
        <!-- 左侧目录 -->
        <aside class="doc-sidebar card">
            <div class="toc-container">
                <!-- 目录内容将通过JavaScript动态生成 -->
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <section class="doc-section card">
            <div class="doc-container">
                <main class="doc-content">
                    <div lang="zh"><h1 id="qt-3d-国际化与本地化指南">Qt 3D 国际化与本地化指南</h1>
<h2 id="目录">目录</h2>
<ol type="1">
<li><a href="#概述">概述</a></li>
<li><a href="#qt-国际化框架">Qt 国际化框架</a></li>
<li><a href="#3d-应用中的国际化挑战">3D 应用中的国际化挑战</a></li>
<li><a href="#文本国际化">文本国际化</a></li>
<li><a href="#资源本地化">资源本地化</a></li>
<li><a href="#用户界面适配">用户界面适配</a></li>
<li><a href="#最佳实践">最佳实践</a></li>
<li><a href="#工具与工作流">工具与工作流</a></li>
</ol>
<h2 id="概述">概述</h2>
<p>Qt 3D
应用的国际化（i18n）和本地化（l10n）是将应用程序适配到不同语言、地区和文化的过程。Qt
框架提供了完整的国际化支持，使 3D 应用能够轻松支持多语言和多地区。</p>
<h3 id="国际化的重要性">国际化的重要性</h3>
<ul>
<li><strong>全球市场</strong>：扩大应用的目标用户群体</li>
<li><strong>用户体验</strong>：提供本地化的用户体验</li>
<li><strong>法规遵循</strong>：满足不同地区的法规要求</li>
<li><strong>竞争优势</strong>：在本地市场获得竞争优势</li>
<li><strong>文化适应</strong>：尊重不同文化的使用习惯</li>
</ul>
<h2 id="qt-国际化框架">Qt 国际化框架</h2>
<h3 id="核心组件">核心组件</h3>
<h4 id="qtranslator">1. QTranslator</h4>
<div class="sourceCode" id="cb1"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 创建翻译器</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="ex">QTranslator</span> translator<span class="op">;</span></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> <span class="op">(</span>translator<span class="op">.</span>load<span class="op">(</span><span class="st">&quot;myapp_zh_CN.qm&quot;</span><span class="op">,</span> <span class="st">&quot;:/translations&quot;</span><span class="op">))</span> <span class="op">{</span></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a>    <span class="ex">QApplication::installTranslator</span><span class="op">(&amp;</span>translator<span class="op">);</span></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h4 id="tr-函数">2. tr() 函数</h4>
<div class="sourceCode" id="cb2"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 在 C++ 中标记可翻译字符串</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a><span class="ex">QString</span> message <span class="op">=</span> <span class="fu">tr</span><span class="op">(</span><span class="st">&quot;Welcome to 3D World&quot;</span><span class="op">);</span></span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a><span class="ex">QString</span> info <span class="op">=</span> <span class="fu">tr</span><span class="op">(</span><span class="st">&quot;Loading model: %1&quot;</span><span class="op">).</span>arg<span class="op">(</span>modelName<span class="op">);</span></span></code></pre></div>
<h4 id="qml-中的国际化">3. QML 中的国际化</h4>
<div class="sourceCode" id="cb3"><pre
class="sourceCode qml"><code class="sourceCode qml"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="co">// QML 中的翻译</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a><span class="ot">Text</span> {</span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a>    <span class="dt">text</span><span class="op">:</span> <span class="fu">qsTr</span>(<span class="st">&quot;3D Scene Controls&quot;</span>)</span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a><span class="ot">Text</span> {</span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a>    <span class="dt">text</span><span class="op">:</span> <span class="fu">qsTr</span>(<span class="st">&quot;Rotation: %1°&quot;</span>)<span class="op">.</span><span class="fu">arg</span>(rotationAngle)</span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<h3 id="语言环境支持">语言环境支持</h3>
<h4 id="qlocale-类">1. QLocale 类</h4>
<div class="sourceCode" id="cb4"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 获取系统语言环境</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a><span class="ex">QLocale</span> systemLocale <span class="op">=</span> <span class="ex">QLocale::system</span><span class="op">();</span></span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a><span class="ex">QString</span> language <span class="op">=</span> systemLocale<span class="op">.</span>languageToString<span class="op">(</span>systemLocale<span class="op">.</span>language<span class="op">());</span></span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a><span class="ex">QString</span> country <span class="op">=</span> systemLocale<span class="op">.</span>countryToString<span class="op">(</span>systemLocale<span class="op">.</span>country<span class="op">());</span></span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a><span class="co">// 设置特定语言环境</span></span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a><span class="ex">QLocale::setDefault</span><span class="op">(</span><span class="ex">QLocale</span><span class="op">(</span><span class="ex">QLocale::Chinese</span><span class="op">,</span> <span class="ex">QLocale::China</span><span class="op">));</span></span></code></pre></div>
<h4 id="动态语言切换">2. 动态语言切换</h4>
<div class="sourceCode" id="cb5"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> LanguageManager <span class="op">:</span> <span class="kw">public</span> <span class="ex">QObject</span> <span class="op">{</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Q_OBJECT</span></span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> switchLanguage<span class="op">(</span><span class="at">const</span> <span class="ex">QString</span><span class="op">&amp;</span> languageCode<span class="op">)</span> <span class="op">{</span></span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 移除当前翻译器</span></span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span><span class="va">m_currentTranslator</span><span class="op">)</span> <span class="op">{</span></span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a>            <span class="ex">QApplication::removeTranslator</span><span class="op">(</span><span class="va">m_currentTranslator</span><span class="op">);</span></span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a>            <span class="kw">delete</span> <span class="va">m_currentTranslator</span><span class="op">;</span></span>
<span id="cb5-10"><a href="#cb5-10" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb5-11"><a href="#cb5-11" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb5-12"><a href="#cb5-12" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载新的翻译文件</span></span>
<span id="cb5-13"><a href="#cb5-13" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_currentTranslator</span> <span class="op">=</span> <span class="kw">new</span> <span class="ex">QTranslator</span><span class="op">(</span><span class="kw">this</span><span class="op">);</span></span>
<span id="cb5-14"><a href="#cb5-14" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span><span class="va">m_currentTranslator</span><span class="op">-&gt;</span>load<span class="op">(</span><span class="ex">QString</span><span class="op">(</span><span class="st">&quot;app_%1.qm&quot;</span><span class="op">).</span>arg<span class="op">(</span>languageCode<span class="op">),</span> <span class="st">&quot;:/translations&quot;</span><span class="op">))</span> <span class="op">{</span></span>
<span id="cb5-15"><a href="#cb5-15" aria-hidden="true" tabindex="-1"></a>            <span class="ex">QApplication::installTranslator</span><span class="op">(</span><span class="va">m_currentTranslator</span><span class="op">);</span></span>
<span id="cb5-16"><a href="#cb5-16" aria-hidden="true" tabindex="-1"></a>            <span class="ex">emit</span> languageChanged<span class="op">();</span></span>
<span id="cb5-17"><a href="#cb5-17" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb5-18"><a href="#cb5-18" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-19"><a href="#cb5-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-20"><a href="#cb5-20" aria-hidden="true" tabindex="-1"></a><span class="ex">signals</span><span class="op">:</span></span>
<span id="cb5-21"><a href="#cb5-21" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> languageChanged<span class="op">();</span></span>
<span id="cb5-22"><a href="#cb5-22" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-23"><a href="#cb5-23" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb5-24"><a href="#cb5-24" aria-hidden="true" tabindex="-1"></a>    <span class="ex">QTranslator</span><span class="op">*</span> <span class="va">m_currentTranslator</span> <span class="op">=</span> <span class="kw">nullptr</span><span class="op">;</span></span>
<span id="cb5-25"><a href="#cb5-25" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="d-应用中的国际化挑战">3D 应用中的国际化挑战</h2>
<h3 id="d-文本渲染">1. 3D 文本渲染</h3>
<div class="sourceCode" id="cb6"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 3D 场景中的文本实体</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DExtras::QText3DEntity</span> <span class="op">*</span>text3D <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DExtras::QText3DEntity</span><span class="op">();</span></span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a>text3D<span class="op">-&gt;</span>setText<span class="op">(</span><span class="fu">tr</span><span class="op">(</span><span class="st">&quot;3D Text in Scene&quot;</span><span class="op">));</span></span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a>text3D<span class="op">-&gt;</span>setFont<span class="op">(</span><span class="ex">QFont</span><span class="op">(</span><span class="st">&quot;Arial&quot;</span><span class="op">,</span> <span class="dv">24</span><span class="op">));</span></span>
<span id="cb6-5"><a href="#cb6-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-6"><a href="#cb6-6" aria-hidden="true" tabindex="-1"></a><span class="co">// 支持 Unicode 字符</span></span>
<span id="cb6-7"><a href="#cb6-7" aria-hidden="true" tabindex="-1"></a>text3D<span class="op">-&gt;</span>setText<span class="op">(</span><span class="fu">tr</span><span class="op">(</span><span class="st">&quot;你好世界&quot;</span><span class="op">));</span> <span class="co">// 中文</span></span>
<span id="cb6-8"><a href="#cb6-8" aria-hidden="true" tabindex="-1"></a>text3D<span class="op">-&gt;</span>setText<span class="op">(</span><span class="fu">tr</span><span class="op">(</span><span class="st">&quot;こんにちは&quot;</span><span class="op">));</span> <span class="co">// 日文</span></span>
<span id="cb6-9"><a href="#cb6-9" aria-hidden="true" tabindex="-1"></a>text3D<span class="op">-&gt;</span>setText<span class="op">(</span><span class="fu">tr</span><span class="op">(</span><span class="st">&quot;مرحبا&quot;</span><span class="op">));</span> <span class="co">// 阿拉伯文</span></span></code></pre></div>
<h3 id="字体支持">2. 字体支持</h3>
<div class="sourceCode" id="cb7"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 加载支持多语言的字体</span></span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a><span class="ex">QFontDatabase</span> fontDb<span class="op">;</span></span>
<span id="cb7-3"><a href="#cb7-3" aria-hidden="true" tabindex="-1"></a>fontDb<span class="op">.</span>addApplicationFont<span class="op">(</span><span class="st">&quot;:/fonts/NotoSansCJK-Regular.ttc&quot;</span><span class="op">);</span></span>
<span id="cb7-4"><a href="#cb7-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-5"><a href="#cb7-5" aria-hidden="true" tabindex="-1"></a><span class="co">// 为不同语言设置合适的字体</span></span>
<span id="cb7-6"><a href="#cb7-6" aria-hidden="true" tabindex="-1"></a><span class="ex">QFont</span> getLocalizedFont<span class="op">(</span><span class="at">const</span> <span class="ex">QLocale</span><span class="op">&amp;</span> locale<span class="op">)</span> <span class="op">{</span></span>
<span id="cb7-7"><a href="#cb7-7" aria-hidden="true" tabindex="-1"></a>    <span class="cf">switch</span> <span class="op">(</span>locale<span class="op">.</span>language<span class="op">())</span> <span class="op">{</span></span>
<span id="cb7-8"><a href="#cb7-8" aria-hidden="true" tabindex="-1"></a>        <span class="cf">case</span> <span class="ex">QLocale::Chinese:</span></span>
<span id="cb7-9"><a href="#cb7-9" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> <span class="ex">QFont</span><span class="op">(</span><span class="st">&quot;Noto Sans CJK SC&quot;</span><span class="op">,</span> <span class="dv">12</span><span class="op">);</span></span>
<span id="cb7-10"><a href="#cb7-10" aria-hidden="true" tabindex="-1"></a>        <span class="cf">case</span> <span class="ex">QLocale::Japanese:</span></span>
<span id="cb7-11"><a href="#cb7-11" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> <span class="ex">QFont</span><span class="op">(</span><span class="st">&quot;Noto Sans CJK JP&quot;</span><span class="op">,</span> <span class="dv">12</span><span class="op">);</span></span>
<span id="cb7-12"><a href="#cb7-12" aria-hidden="true" tabindex="-1"></a>        <span class="cf">case</span> <span class="ex">QLocale::Korean:</span></span>
<span id="cb7-13"><a href="#cb7-13" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> <span class="ex">QFont</span><span class="op">(</span><span class="st">&quot;Noto Sans CJK KR&quot;</span><span class="op">,</span> <span class="dv">12</span><span class="op">);</span></span>
<span id="cb7-14"><a href="#cb7-14" aria-hidden="true" tabindex="-1"></a>        <span class="cf">case</span> <span class="ex">QLocale::Arabic:</span></span>
<span id="cb7-15"><a href="#cb7-15" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> <span class="ex">QFont</span><span class="op">(</span><span class="st">&quot;Noto Sans Arabic&quot;</span><span class="op">,</span> <span class="dv">12</span><span class="op">);</span></span>
<span id="cb7-16"><a href="#cb7-16" aria-hidden="true" tabindex="-1"></a>        <span class="cf">default</span><span class="op">:</span></span>
<span id="cb7-17"><a href="#cb7-17" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> <span class="ex">QFont</span><span class="op">(</span><span class="st">&quot;Noto Sans&quot;</span><span class="op">,</span> <span class="dv">12</span><span class="op">);</span></span>
<span id="cb7-18"><a href="#cb7-18" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-19"><a href="#cb7-19" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="文本方向支持">3. 文本方向支持</h3>
<div class="sourceCode" id="cb8"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 支持从右到左的文本（如阿拉伯语、希伯来语）</span></span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> setupTextDirection<span class="op">(</span><span class="ex">Qt3DExtras::QText3DEntity</span><span class="op">*</span> textEntity<span class="op">,</span> <span class="at">const</span> <span class="ex">QLocale</span><span class="op">&amp;</span> locale<span class="op">)</span> <span class="op">{</span></span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>locale<span class="op">.</span>textDirection<span class="op">()</span> <span class="op">==</span> <span class="ex">Qt::RightToLeft</span><span class="op">)</span> <span class="op">{</span></span>
<span id="cb8-4"><a href="#cb8-4" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 调整文本对齐和布局</span></span>
<span id="cb8-5"><a href="#cb8-5" aria-hidden="true" tabindex="-1"></a>        textEntity<span class="op">-&gt;</span>setProperty<span class="op">(</span><span class="st">&quot;textDirection&quot;</span><span class="op">,</span> <span class="ex">Qt::RightToLeft</span><span class="op">);</span></span>
<span id="cb8-6"><a href="#cb8-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-7"><a href="#cb8-7" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="文本国际化">文本国际化</h2>
<h3 id="字符串提取">1. 字符串提取</h3>
<div class="sourceCode" id="cb9"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 使用 tr() 标记所有用户可见的字符串</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> Scene3DController <span class="op">:</span> <span class="kw">public</span> <span class="ex">QObject</span> <span class="op">{</span></span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Q_OBJECT</span></span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> showLoadingMessage<span class="op">()</span> <span class="op">{</span></span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a>        <span class="ex">emit</span> statusChanged<span class="op">(</span><span class="fu">tr</span><span class="op">(</span><span class="st">&quot;Loading 3D model...&quot;</span><span class="op">));</span></span>
<span id="cb9-8"><a href="#cb9-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-9"><a href="#cb9-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-10"><a href="#cb9-10" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> showError<span class="op">(</span><span class="at">const</span> <span class="ex">QString</span><span class="op">&amp;</span> error<span class="op">)</span> <span class="op">{</span></span>
<span id="cb9-11"><a href="#cb9-11" aria-hidden="true" tabindex="-1"></a>        <span class="ex">emit</span> statusChanged<span class="op">(</span><span class="fu">tr</span><span class="op">(</span><span class="st">&quot;Error: %1&quot;</span><span class="op">).</span>arg<span class="op">(</span>error<span class="op">));</span></span>
<span id="cb9-12"><a href="#cb9-12" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-13"><a href="#cb9-13" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-14"><a href="#cb9-14" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> showModelInfo<span class="op">(</span><span class="dt">int</span> vertices<span class="op">,</span> <span class="dt">int</span> faces<span class="op">)</span> <span class="op">{</span></span>
<span id="cb9-15"><a href="#cb9-15" aria-hidden="true" tabindex="-1"></a>        <span class="ex">emit</span> statusChanged<span class="op">(</span><span class="fu">tr</span><span class="op">(</span><span class="st">&quot;Model loaded: </span><span class="sc">%n</span><span class="st"> vertices, </span><span class="sc">%n</span><span class="st"> faces&quot;</span><span class="op">,</span> <span class="st">&quot;&quot;</span><span class="op">,</span> vertices<span class="op">));</span></span>
<span id="cb9-16"><a href="#cb9-16" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-17"><a href="#cb9-17" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-18"><a href="#cb9-18" aria-hidden="true" tabindex="-1"></a><span class="ex">signals</span><span class="op">:</span></span>
<span id="cb9-19"><a href="#cb9-19" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> statusChanged<span class="op">(</span><span class="at">const</span> <span class="ex">QString</span><span class="op">&amp;</span> message<span class="op">);</span></span>
<span id="cb9-20"><a href="#cb9-20" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="复数形式处理">2. 复数形式处理</h3>
<div class="sourceCode" id="cb10"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 处理复数形式</span></span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a><span class="ex">QString</span> getObjectCountText<span class="op">(</span><span class="dt">int</span> count<span class="op">)</span> <span class="op">{</span></span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> <span class="fu">tr</span><span class="op">(</span><span class="st">&quot;</span><span class="sc">%n</span><span class="st"> object(s) selected&quot;</span><span class="op">,</span> <span class="st">&quot;&quot;</span><span class="op">,</span> count<span class="op">);</span></span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb10-5"><a href="#cb10-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-6"><a href="#cb10-6" aria-hidden="true" tabindex="-1"></a><span class="co">// 在翻译文件中：</span></span>
<span id="cb10-7"><a href="#cb10-7" aria-hidden="true" tabindex="-1"></a><span class="co">// &lt;message numerus=&quot;yes&quot;&gt;</span></span>
<span id="cb10-8"><a href="#cb10-8" aria-hidden="true" tabindex="-1"></a><span class="co">//     &lt;source&gt;%n object(s) selected&lt;/source&gt;</span></span>
<span id="cb10-9"><a href="#cb10-9" aria-hidden="true" tabindex="-1"></a><span class="co">//     &lt;translation&gt;</span></span>
<span id="cb10-10"><a href="#cb10-10" aria-hidden="true" tabindex="-1"></a><span class="co">//         &lt;numerusform&gt;选中了 %n 个对象&lt;/numerusform&gt;</span></span>
<span id="cb10-11"><a href="#cb10-11" aria-hidden="true" tabindex="-1"></a><span class="co">//     &lt;/translation&gt;</span></span>
<span id="cb10-12"><a href="#cb10-12" aria-hidden="true" tabindex="-1"></a><span class="co">// &lt;/message&gt;</span></span></code></pre></div>
<h3 id="上下文相关翻译">3. 上下文相关翻译</h3>
<div class="sourceCode" id="cb11"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 使用上下文区分相同的英文单词</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MaterialEditor <span class="op">:</span> <span class="kw">public</span> <span class="ex">QObject</span> <span class="op">{</span></span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Q_OBJECT</span></span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a>    <span class="ex">QString</span> getMaterialProperty<span class="op">()</span> <span class="op">{</span></span>
<span id="cb11-7"><a href="#cb11-7" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="fu">tr</span><span class="op">(</span><span class="st">&quot;Color&quot;</span><span class="op">,</span> <span class="st">&quot;material property&quot;</span><span class="op">);</span> <span class="co">// 材质属性中的颜色</span></span>
<span id="cb11-8"><a href="#cb11-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-9"><a href="#cb11-9" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb11-10"><a href="#cb11-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-11"><a href="#cb11-11" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> LightEditor <span class="op">:</span> <span class="kw">public</span> <span class="ex">QObject</span> <span class="op">{</span></span>
<span id="cb11-12"><a href="#cb11-12" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Q_OBJECT</span></span>
<span id="cb11-13"><a href="#cb11-13" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-14"><a href="#cb11-14" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb11-15"><a href="#cb11-15" aria-hidden="true" tabindex="-1"></a>    <span class="ex">QString</span> getLightProperty<span class="op">()</span> <span class="op">{</span></span>
<span id="cb11-16"><a href="#cb11-16" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="fu">tr</span><span class="op">(</span><span class="st">&quot;Color&quot;</span><span class="op">,</span> <span class="st">&quot;light property&quot;</span><span class="op">);</span> <span class="co">// 光源属性中的颜色</span></span>
<span id="cb11-17"><a href="#cb11-17" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-18"><a href="#cb11-18" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="资源本地化">资源本地化</h2>
<h3 id="纹理和图像">1. 纹理和图像</h3>
<div class="sourceCode" id="cb12"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 根据语言环境加载不同的纹理</span></span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a><span class="ex">QString</span> getLocalizedTexture<span class="op">(</span><span class="at">const</span> <span class="ex">QString</span><span class="op">&amp;</span> baseName<span class="op">,</span> <span class="at">const</span> <span class="ex">QLocale</span><span class="op">&amp;</span> locale<span class="op">)</span> <span class="op">{</span></span>
<span id="cb12-3"><a href="#cb12-3" aria-hidden="true" tabindex="-1"></a>    <span class="ex">QString</span> localizedPath <span class="op">=</span> <span class="ex">QString</span><span class="op">(</span><span class="st">&quot;:/textures/%1_%2.png&quot;</span><span class="op">)</span></span>
<span id="cb12-4"><a href="#cb12-4" aria-hidden="true" tabindex="-1"></a>                           <span class="op">.</span>arg<span class="op">(</span>baseName<span class="op">)</span></span>
<span id="cb12-5"><a href="#cb12-5" aria-hidden="true" tabindex="-1"></a>                           <span class="op">.</span>arg<span class="op">(</span>locale<span class="op">.</span>name<span class="op">());</span></span>
<span id="cb12-6"><a href="#cb12-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-7"><a href="#cb12-7" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span><span class="ex">QFile::exists</span><span class="op">(</span>localizedPath<span class="op">))</span> <span class="op">{</span></span>
<span id="cb12-8"><a href="#cb12-8" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> localizedPath<span class="op">;</span></span>
<span id="cb12-9"><a href="#cb12-9" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-10"><a href="#cb12-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-11"><a href="#cb12-11" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 回退到默认纹理</span></span>
<span id="cb12-12"><a href="#cb12-12" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> <span class="ex">QString</span><span class="op">(</span><span class="st">&quot;:/textures/%1.png&quot;</span><span class="op">).</span>arg<span class="op">(</span>baseName<span class="op">);</span></span>
<span id="cb12-13"><a href="#cb12-13" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb12-14"><a href="#cb12-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-15"><a href="#cb12-15" aria-hidden="true" tabindex="-1"></a><span class="co">// 使用本地化纹理</span></span>
<span id="cb12-16"><a href="#cb12-16" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DRender::QTexture2D</span><span class="op">*</span> texture <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QTexture2D</span><span class="op">();</span></span>
<span id="cb12-17"><a href="#cb12-17" aria-hidden="true" tabindex="-1"></a>texture<span class="op">-&gt;</span>setSource<span class="op">(</span><span class="ex">QUrl</span><span class="op">(</span>getLocalizedTexture<span class="op">(</span><span class="st">&quot;ui_button&quot;</span><span class="op">,</span> <span class="ex">QLocale::system</span><span class="op">())));</span></span></code></pre></div>
<h3 id="d-模型本地化">2. 3D 模型本地化</h3>
<div class="sourceCode" id="cb13"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 加载本地化的 3D 模型（如包含文字的模型）</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> LocalizedModelLoader <span class="op">{</span></span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb13-4"><a href="#cb13-4" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Qt3DRender::QMesh</span><span class="op">*</span> loadLocalizedModel<span class="op">(</span><span class="at">const</span> <span class="ex">QString</span><span class="op">&amp;</span> modelName<span class="op">,</span> <span class="at">const</span> <span class="ex">QLocale</span><span class="op">&amp;</span> locale<span class="op">)</span> <span class="op">{</span></span>
<span id="cb13-5"><a href="#cb13-5" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QString</span> localizedPath <span class="op">=</span> <span class="ex">QString</span><span class="op">(</span><span class="st">&quot;:/models/%1_%2.obj&quot;</span><span class="op">)</span></span>
<span id="cb13-6"><a href="#cb13-6" aria-hidden="true" tabindex="-1"></a>                               <span class="op">.</span>arg<span class="op">(</span>modelName<span class="op">)</span></span>
<span id="cb13-7"><a href="#cb13-7" aria-hidden="true" tabindex="-1"></a>                               <span class="op">.</span>arg<span class="op">(</span>locale<span class="op">.</span>name<span class="op">());</span></span>
<span id="cb13-8"><a href="#cb13-8" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb13-9"><a href="#cb13-9" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span><span class="ex">QFile::exists</span><span class="op">(</span>localizedPath<span class="op">))</span> <span class="op">{</span></span>
<span id="cb13-10"><a href="#cb13-10" aria-hidden="true" tabindex="-1"></a>            <span class="ex">Qt3DRender::QMesh</span><span class="op">*</span> mesh <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QMesh</span><span class="op">();</span></span>
<span id="cb13-11"><a href="#cb13-11" aria-hidden="true" tabindex="-1"></a>            mesh<span class="op">-&gt;</span>setSource<span class="op">(</span><span class="ex">QUrl</span><span class="op">(</span>localizedPath<span class="op">));</span></span>
<span id="cb13-12"><a href="#cb13-12" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> mesh<span class="op">;</span></span>
<span id="cb13-13"><a href="#cb13-13" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb13-14"><a href="#cb13-14" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb13-15"><a href="#cb13-15" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 回退到默认模型</span></span>
<span id="cb13-16"><a href="#cb13-16" aria-hidden="true" tabindex="-1"></a>        <span class="ex">Qt3DRender::QMesh</span><span class="op">*</span> mesh <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QMesh</span><span class="op">();</span></span>
<span id="cb13-17"><a href="#cb13-17" aria-hidden="true" tabindex="-1"></a>        mesh<span class="op">-&gt;</span>setSource<span class="op">(</span><span class="ex">QUrl</span><span class="op">(</span><span class="ex">QString</span><span class="op">(</span><span class="st">&quot;:/models/%1.obj&quot;</span><span class="op">).</span>arg<span class="op">(</span>modelName<span class="op">)));</span></span>
<span id="cb13-18"><a href="#cb13-18" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> mesh<span class="op">;</span></span>
<span id="cb13-19"><a href="#cb13-19" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-20"><a href="#cb13-20" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="音频本地化">3. 音频本地化</h3>
<div class="sourceCode" id="cb14"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb14-1"><a href="#cb14-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 本地化音频资源</span></span>
<span id="cb14-2"><a href="#cb14-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> AudioManager <span class="op">{</span></span>
<span id="cb14-3"><a href="#cb14-3" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb14-4"><a href="#cb14-4" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> playLocalizedSound<span class="op">(</span><span class="at">const</span> <span class="ex">QString</span><span class="op">&amp;</span> soundName<span class="op">)</span> <span class="op">{</span></span>
<span id="cb14-5"><a href="#cb14-5" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QString</span> localizedPath <span class="op">=</span> <span class="ex">QString</span><span class="op">(</span><span class="st">&quot;:/audio/%1_%2.wav&quot;</span><span class="op">)</span></span>
<span id="cb14-6"><a href="#cb14-6" aria-hidden="true" tabindex="-1"></a>                               <span class="op">.</span>arg<span class="op">(</span>soundName<span class="op">)</span></span>
<span id="cb14-7"><a href="#cb14-7" aria-hidden="true" tabindex="-1"></a>                               <span class="op">.</span>arg<span class="op">(</span><span class="ex">QLocale::system</span><span class="op">().</span>name<span class="op">());</span></span>
<span id="cb14-8"><a href="#cb14-8" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb14-9"><a href="#cb14-9" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span><span class="ex">QFile::exists</span><span class="op">(</span>localizedPath<span class="op">))</span> <span class="op">{</span></span>
<span id="cb14-10"><a href="#cb14-10" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 播放本地化音频</span></span>
<span id="cb14-11"><a href="#cb14-11" aria-hidden="true" tabindex="-1"></a>            playSound<span class="op">(</span>localizedPath<span class="op">);</span></span>
<span id="cb14-12"><a href="#cb14-12" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span> <span class="cf">else</span> <span class="op">{</span></span>
<span id="cb14-13"><a href="#cb14-13" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 播放默认音频</span></span>
<span id="cb14-14"><a href="#cb14-14" aria-hidden="true" tabindex="-1"></a>            playSound<span class="op">(</span><span class="ex">QString</span><span class="op">(</span><span class="st">&quot;:/audio/%1.wav&quot;</span><span class="op">).</span>arg<span class="op">(</span>soundName<span class="op">));</span></span>
<span id="cb14-15"><a href="#cb14-15" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb14-16"><a href="#cb14-16" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb14-17"><a href="#cb14-17" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb14-18"><a href="#cb14-18" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb14-19"><a href="#cb14-19" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> playSound<span class="op">(</span><span class="at">const</span> <span class="ex">QString</span><span class="op">&amp;</span> path<span class="op">)</span> <span class="op">{</span></span>
<span id="cb14-20"><a href="#cb14-20" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 音频播放实现</span></span>
<span id="cb14-21"><a href="#cb14-21" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb14-22"><a href="#cb14-22" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="用户界面适配">用户界面适配</h2>
<h3 id="布局适配">1. 布局适配</h3>
<div class="sourceCode" id="cb15"><pre
class="sourceCode qml"><code class="sourceCode qml"><span id="cb15-1"><a href="#cb15-1" aria-hidden="true" tabindex="-1"></a><span class="co">// QML 中的自适应布局</span></span>
<span id="cb15-2"><a href="#cb15-2" aria-hidden="true" tabindex="-1"></a><span class="ot">Rectangle</span> {</span>
<span id="cb15-3"><a href="#cb15-3" aria-hidden="true" tabindex="-1"></a>    <span class="dt">width</span><span class="op">:</span> parent<span class="op">.</span><span class="at">width</span></span>
<span id="cb15-4"><a href="#cb15-4" aria-hidden="true" tabindex="-1"></a>    <span class="dt">height</span><span class="op">:</span> <span class="dv">50</span></span>
<span id="cb15-5"><a href="#cb15-5" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-6"><a href="#cb15-6" aria-hidden="true" tabindex="-1"></a>    <span class="ot">Text</span> {</span>
<span id="cb15-7"><a href="#cb15-7" aria-hidden="true" tabindex="-1"></a>        <span class="dt">id</span><span class="op">:</span> labelText</span>
<span id="cb15-8"><a href="#cb15-8" aria-hidden="true" tabindex="-1"></a>        <span class="dt">text</span><span class="op">:</span> <span class="fu">qsTr</span>(<span class="st">&quot;Camera Position:&quot;</span>)</span>
<span id="cb15-9"><a href="#cb15-9" aria-hidden="true" tabindex="-1"></a>        anchors<span class="op">.</span><span class="at">left</span><span class="op">:</span> parent<span class="op">.</span><span class="at">left</span></span>
<span id="cb15-10"><a href="#cb15-10" aria-hidden="true" tabindex="-1"></a>        anchors<span class="op">.</span><span class="at">verticalCenter</span><span class="op">:</span> parent<span class="op">.</span><span class="at">verticalCenter</span></span>
<span id="cb15-11"><a href="#cb15-11" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb15-12"><a href="#cb15-12" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 根据文本长度调整布局</span></span>
<span id="cb15-13"><a href="#cb15-13" aria-hidden="true" tabindex="-1"></a>        <span class="dt">onTextChanged</span><span class="op">:</span> {</span>
<span id="cb15-14"><a href="#cb15-14" aria-hidden="true" tabindex="-1"></a>            parent<span class="op">.</span><span class="fu">adjustLayout</span>()</span>
<span id="cb15-15"><a href="#cb15-15" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb15-16"><a href="#cb15-16" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb15-17"><a href="#cb15-17" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-18"><a href="#cb15-18" aria-hidden="true" tabindex="-1"></a>    TextField {</span>
<span id="cb15-19"><a href="#cb15-19" aria-hidden="true" tabindex="-1"></a>        anchors<span class="op">.</span><span class="at">left</span><span class="op">:</span> labelText<span class="op">.</span><span class="at">right</span></span>
<span id="cb15-20"><a href="#cb15-20" aria-hidden="true" tabindex="-1"></a>        anchors<span class="op">.</span><span class="at">leftMargin</span><span class="op">:</span> <span class="dv">10</span></span>
<span id="cb15-21"><a href="#cb15-21" aria-hidden="true" tabindex="-1"></a>        anchors<span class="op">.</span><span class="at">right</span><span class="op">:</span> parent<span class="op">.</span><span class="at">right</span></span>
<span id="cb15-22"><a href="#cb15-22" aria-hidden="true" tabindex="-1"></a>        anchors<span class="op">.</span><span class="at">verticalCenter</span><span class="op">:</span> parent<span class="op">.</span><span class="at">verticalCenter</span></span>
<span id="cb15-23"><a href="#cb15-23" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb15-24"><a href="#cb15-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-25"><a href="#cb15-25" aria-hidden="true" tabindex="-1"></a>    <span class="kw">function</span> <span class="fu">adjustLayout</span>() {</span>
<span id="cb15-26"><a href="#cb15-26" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 根据标签文本长度调整布局</span></span>
<span id="cb15-27"><a href="#cb15-27" aria-hidden="true" tabindex="-1"></a>        <span class="kw">var</span> textWidth <span class="op">=</span> labelText<span class="op">.</span><span class="at">contentWidth</span></span>
<span id="cb15-28"><a href="#cb15-28" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (textWidth <span class="op">&gt;</span> parent<span class="op">.</span><span class="at">width</span> <span class="op">*</span> <span class="fl">0.4</span>) {</span>
<span id="cb15-29"><a href="#cb15-29" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 切换到垂直布局</span></span>
<span id="cb15-30"><a href="#cb15-30" aria-hidden="true" tabindex="-1"></a>            labelText<span class="op">.</span><span class="at">anchors</span><span class="op">.</span><span class="at">left</span> <span class="op">=</span> parent<span class="op">.</span><span class="at">left</span></span>
<span id="cb15-31"><a href="#cb15-31" aria-hidden="true" tabindex="-1"></a>            labelText<span class="op">.</span><span class="at">anchors</span><span class="op">.</span><span class="at">top</span> <span class="op">=</span> parent<span class="op">.</span><span class="at">top</span></span>
<span id="cb15-32"><a href="#cb15-32" aria-hidden="true" tabindex="-1"></a>            <span class="co">// ... 调整其他控件位置</span></span>
<span id="cb15-33"><a href="#cb15-33" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb15-34"><a href="#cb15-34" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb15-35"><a href="#cb15-35" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<h3 id="文本大小适配">2. 文本大小适配</h3>
<div class="sourceCode" id="cb16"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb16-1"><a href="#cb16-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 根据语言调整文本大小</span></span>
<span id="cb16-2"><a href="#cb16-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> TextSizeManager <span class="op">{</span></span>
<span id="cb16-3"><a href="#cb16-3" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb16-4"><a href="#cb16-4" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> <span class="dt">int</span> getOptimalFontSize<span class="op">(</span><span class="at">const</span> <span class="ex">QLocale</span><span class="op">&amp;</span> locale<span class="op">,</span> <span class="dt">int</span> baseFontSize<span class="op">)</span> <span class="op">{</span></span>
<span id="cb16-5"><a href="#cb16-5" aria-hidden="true" tabindex="-1"></a>        <span class="cf">switch</span> <span class="op">(</span>locale<span class="op">.</span>language<span class="op">())</span> <span class="op">{</span></span>
<span id="cb16-6"><a href="#cb16-6" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> <span class="ex">QLocale::Chinese:</span></span>
<span id="cb16-7"><a href="#cb16-7" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> <span class="ex">QLocale::Japanese:</span></span>
<span id="cb16-8"><a href="#cb16-8" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> <span class="ex">QLocale::Korean:</span></span>
<span id="cb16-9"><a href="#cb16-9" aria-hidden="true" tabindex="-1"></a>                <span class="co">// CJK 字符通常需要稍大的字体</span></span>
<span id="cb16-10"><a href="#cb16-10" aria-hidden="true" tabindex="-1"></a>                <span class="cf">return</span> baseFontSize <span class="op">+</span> <span class="dv">2</span><span class="op">;</span></span>
<span id="cb16-11"><a href="#cb16-11" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> <span class="ex">QLocale::Arabic:</span></span>
<span id="cb16-12"><a href="#cb16-12" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> <span class="ex">QLocale::Hebrew:</span></span>
<span id="cb16-13"><a href="#cb16-13" aria-hidden="true" tabindex="-1"></a>                <span class="co">// 阿拉伯语和希伯来语可能需要调整</span></span>
<span id="cb16-14"><a href="#cb16-14" aria-hidden="true" tabindex="-1"></a>                <span class="cf">return</span> baseFontSize <span class="op">+</span> <span class="dv">1</span><span class="op">;</span></span>
<span id="cb16-15"><a href="#cb16-15" aria-hidden="true" tabindex="-1"></a>            <span class="cf">default</span><span class="op">:</span></span>
<span id="cb16-16"><a href="#cb16-16" aria-hidden="true" tabindex="-1"></a>                <span class="cf">return</span> baseFontSize<span class="op">;</span></span>
<span id="cb16-17"><a href="#cb16-17" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb16-18"><a href="#cb16-18" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb16-19"><a href="#cb16-19" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="最佳实践">最佳实践</h2>
<h3 id="开发阶段">1. 开发阶段</h3>
<ul>
<li><strong>早期规划</strong>：在项目开始时就考虑国际化需求</li>
<li><strong>字符串外部化</strong>：所有用户可见的字符串都使用 tr()
函数</li>
<li><strong>避免硬编码</strong>：不要在代码中硬编码文本、图像路径等</li>
<li><strong>Unicode 支持</strong>：确保应用完全支持 Unicode</li>
<li><strong>测试驱动</strong>：为每种目标语言创建测试用例</li>
</ul>
<h3 id="设计原则">2. 设计原则</h3>
<div class="sourceCode" id="cb17"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb17-1"><a href="#cb17-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 好的做法</span></span>
<span id="cb17-2"><a href="#cb17-2" aria-hidden="true" tabindex="-1"></a><span class="ex">QString</span> message <span class="op">=</span> <span class="fu">tr</span><span class="op">(</span><span class="st">&quot;Welcome, %1!&quot;</span><span class="op">).</span>arg<span class="op">(</span>userName<span class="op">);</span></span>
<span id="cb17-3"><a href="#cb17-3" aria-hidden="true" tabindex="-1"></a><span class="ex">QString</span> info <span class="op">=</span> <span class="fu">tr</span><span class="op">(</span><span class="st">&quot;File size: %1 MB&quot;</span><span class="op">).</span>arg<span class="op">(</span>fileSize<span class="op">);</span></span>
<span id="cb17-4"><a href="#cb17-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-5"><a href="#cb17-5" aria-hidden="true" tabindex="-1"></a><span class="co">// 避免的做法</span></span>
<span id="cb17-6"><a href="#cb17-6" aria-hidden="true" tabindex="-1"></a><span class="ex">QString</span> message <span class="op">=</span> <span class="st">&quot;Welcome, &quot;</span> <span class="op">+</span> userName <span class="op">+</span> <span class="st">&quot;!&quot;</span><span class="op">;</span> <span class="co">// 硬编码</span></span>
<span id="cb17-7"><a href="#cb17-7" aria-hidden="true" tabindex="-1"></a><span class="ex">QString</span> info <span class="op">=</span> <span class="ex">QString::number</span><span class="op">(</span>fileSize<span class="op">)</span> <span class="op">+</span> <span class="st">&quot; MB&quot;</span><span class="op">;</span> <span class="co">// 无法翻译</span></span></code></pre></div>
<h3 id="资源组织">3. 资源组织</h3>
<pre><code>resources/
├── translations/
│   ├── app_zh_CN.ts
│   ├── app_ja_JP.ts
│   ├── app_ko_KR.ts
│   └── app_ar_SA.ts
├── fonts/
│   ├── NotoSansCJK-Regular.ttc
│   └── NotoSansArabic-Regular.ttf
├── textures/
│   ├── ui_button.png
│   ├── ui_button_zh_CN.png
│   └── ui_button_ar_SA.png
└── models/
    ├── sign.obj
    ├── sign_zh_CN.obj
    └── sign_ar_SA.obj</code></pre>
<h2 id="工具与工作流">工具与工作流</h2>
<h3 id="qt-linguist-工具链">1. Qt Linguist 工具链</h3>
<div class="sourceCode" id="cb19"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb19-1"><a href="#cb19-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 提取可翻译字符串</span></span>
<span id="cb19-2"><a href="#cb19-2" aria-hidden="true" tabindex="-1"></a><span class="ex">lupdate</span> myapp.pro</span>
<span id="cb19-3"><a href="#cb19-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-4"><a href="#cb19-4" aria-hidden="true" tabindex="-1"></a><span class="co"># 编译翻译文件</span></span>
<span id="cb19-5"><a href="#cb19-5" aria-hidden="true" tabindex="-1"></a><span class="ex">lrelease</span> myapp.pro</span>
<span id="cb19-6"><a href="#cb19-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-7"><a href="#cb19-7" aria-hidden="true" tabindex="-1"></a><span class="co"># 使用 Qt Linguist 进行翻译</span></span>
<span id="cb19-8"><a href="#cb19-8" aria-hidden="true" tabindex="-1"></a><span class="ex">linguist</span> translations/myapp_zh_CN.ts</span></code></pre></div>
<h3 id="自动化工作流">2. 自动化工作流</h3>
<div class="sourceCode" id="cb20"><pre
class="sourceCode cmake"><code class="sourceCode cmake"><span id="cb20-1"><a href="#cb20-1" aria-hidden="true" tabindex="-1"></a><span class="co"># CMake 中的国际化支持</span></span>
<span id="cb20-2"><a href="#cb20-2" aria-hidden="true" tabindex="-1"></a><span class="kw">find_package</span>(Qt6 <span class="ot">REQUIRED</span> <span class="ot">COMPONENTS</span> LinguistTools)</span>
<span id="cb20-3"><a href="#cb20-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb20-4"><a href="#cb20-4" aria-hidden="true" tabindex="-1"></a><span class="kw">set</span>(TS_FILES</span>
<span id="cb20-5"><a href="#cb20-5" aria-hidden="true" tabindex="-1"></a>    translations/myapp_zh_CN.ts</span>
<span id="cb20-6"><a href="#cb20-6" aria-hidden="true" tabindex="-1"></a>    translations/myapp_ja_JP.ts</span>
<span id="cb20-7"><a href="#cb20-7" aria-hidden="true" tabindex="-1"></a>    translations/myapp_ko_KR.ts</span>
<span id="cb20-8"><a href="#cb20-8" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb20-9"><a href="#cb20-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb20-10"><a href="#cb20-10" aria-hidden="true" tabindex="-1"></a><span class="fu">qt6_add_translations</span>(myapp TS_FILES <span class="dv">${TS_FILES}</span>)</span></code></pre></div>
<h3 id="持续集成">3. 持续集成</h3>
<div class="sourceCode" id="cb21"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb21-1"><a href="#cb21-1" aria-hidden="true" tabindex="-1"></a><span class="co"># GitHub Actions 示例</span></span>
<span id="cb21-2"><a href="#cb21-2" aria-hidden="true" tabindex="-1"></a><span class="kw">-</span><span class="at"> </span><span class="fu">name</span><span class="kw">:</span><span class="at"> Update translations</span></span>
<span id="cb21-3"><a href="#cb21-3" aria-hidden="true" tabindex="-1"></a><span class="fu">  run</span><span class="kw">: </span><span class="ch">|</span></span>
<span id="cb21-4"><a href="#cb21-4" aria-hidden="true" tabindex="-1"></a>    lupdate src/ -ts translations/*.ts</span>
<span id="cb21-5"><a href="#cb21-5" aria-hidden="true" tabindex="-1"></a>    lrelease translations/*.ts</span>
<span id="cb21-6"><a href="#cb21-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb21-7"><a href="#cb21-7" aria-hidden="true" tabindex="-1"></a><span class="kw">-</span><span class="at"> </span><span class="fu">name</span><span class="kw">:</span><span class="at"> Test translations</span></span>
<span id="cb21-8"><a href="#cb21-8" aria-hidden="true" tabindex="-1"></a><span class="fu">  run</span><span class="kw">: </span><span class="ch">|</span></span>
<span id="cb21-9"><a href="#cb21-9" aria-hidden="true" tabindex="-1"></a>    python scripts/check_translations.py</span></code></pre></div>
<hr />
<p><em>Qt 3D 的国际化支持让您的 3D
应用能够轻松适应全球市场，提供优秀的本地化用户体验</em></p>
</div><div lang="en" style="display: none;"><h1>[English translation pending]</h1><p>The content for this page has not been translated yet. Please check back later.</p></div>
                </main>
            </div>
        </section>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">Qt 3D 开发指南</h4>
                    <p data-i18n="footer-subtitle">专业的跨平台 3D 应用开发解决方案</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.</p>
            </div>
        </div>
    </footer>

    <script src="../../assets/js/prism.js"></script>
    <script src="../../assets/js/docs.js"></script>
</body>
</html> 