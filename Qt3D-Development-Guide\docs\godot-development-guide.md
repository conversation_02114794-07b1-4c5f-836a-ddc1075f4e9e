# Godot Engine 开发指南

## 目录
1. [概述](#概述)
2. [技术特性](#技术特性)
3. [开发环境搭建](#开发环境搭建)
4. [核心概念](#核心概念)
5. [2D/3D 开发](#2d3d-开发)
6. [脚本编程](#脚本编程)
7. [跨平台发布](#跨平台发布)
8. [最佳实践](#最佳实践)

## 概述

Godot Engine 是一个完全免费的开源游戏引擎，采用 MIT 许可证发布。作为近年来快速崛起的游戏引擎，Godot 在 2024 年 GMTK 游戏开发大赛中使用率从 19% 跃升至 37%，超越 Unity 成为最受欢迎的引擎。

### 主要优势
- **完全免费开源**：MIT 许可证，无版权费用和使用限制
- **轻量级设计**：引擎体积小，启动快速，资源占用低
- **节点系统**：直观的场景驱动设计，易于理解和使用
- **多语言支持**：GDScript、C#、C++ 等多种编程语言
- **2D/3D 一体化**：专门的 2D 渲染管线和现代 3D 渲染器
- **活跃社区**：快速发展的开源社区和丰富的学习资源

## 技术特性

### 渲染系统
- **Vulkan 渲染器**：Godot 4.0 引入的现代渲染器
- **OpenGL 兼容**：支持低端设备的 OpenGL 渲染
- **专用 2D 渲染**：独立的 2D 渲染管线，性能优化
- **现代 3D 特性**：PBR 材质、全局光照、阴影映射
- **可扩展渲染**：支持自定义渲染管线

### 节点和场景系统
- **节点架构**：基于节点的组合式设计
- **场景实例化**：可重用的场景组件
- **信号系统**：类型安全的事件通信
- **组合优于继承**：灵活的组件组合方式
- **可视化编辑**：直观的场景树编辑器

### 脚本系统
- **GDScript**：Python 风格的专用脚本语言
- **C# 支持**：完整的 .NET 平台支持
- **GDExtension**：C++ 扩展 API，无需重编译引擎
- **可选静态类型**：GDScript 支持静态类型检查
- **热重载**：脚本修改即时生效

## 开发环境搭建

### 系统要求
- **操作系统**：Windows 10+, macOS 10.15+, Linux (64-bit)
- **内存**：最少 4GB RAM，推荐 8GB+
- **存储**：至少 1GB 可用空间
- **显卡**：支持 OpenGL 3.3+ 或 Vulkan

### 安装步骤

#### 1. 下载 Godot Engine
```bash
# 访问官网下载
https://godotengine.org/download/

# 选择版本
# - Godot 4.x: 最新版本，推荐新项目使用
# - Godot 3.x: LTS 版本，稳定性优先
```

#### 2. 安装开发环境
```bash
# Godot 是绿色软件，无需安装
# 直接运行下载的可执行文件

# 可选：安装 C# 支持
# 下载 .NET 版本的 Godot
# 安装 .NET SDK 6.0+
```

#### 3. 创建第一个项目
```gdscript
# 1. 启动 Godot Engine
# 2. 点击 "新建项目"
# 3. 选择项目路径和名称
# 4. 选择渲染器（Vulkan/OpenGL）
# 5. 点击 "创建并编辑"
```

## 核心概念

### 节点和场景
```gdscript
# 节点是 Godot 的基本构建块
# 每个节点都有特定的功能

# 常用节点类型：
# - Node: 基础节点
# - Node2D: 2D 节点
# - Node3D: 3D 节点
# - Control: UI 节点
# - RigidBody2D/3D: 物理体
# - Area2D/3D: 区域检测

# 场景是节点的集合，可以保存和重用
# 场景可以实例化到其他场景中
```

### 信号系统
```gdscript
# 定义信号
signal health_changed(new_health)
signal player_died

# 连接信号
func _ready():
    # 连接到函数
    health_changed.connect(_on_health_changed)
    
    # 连接到其他节点的函数
    player_died.connect(game_manager._on_player_died)

# 发射信号
func take_damage(damage):
    health -= damage
    health_changed.emit(health)
    
    if health <= 0:
        player_died.emit()

# 信号处理函数
func _on_health_changed(new_health):
    print("Health is now: ", new_health)
```

### 资源系统
```gdscript
# 自定义资源类
class_name PlayerData
extends Resource

@export var player_name: String
@export var level: int
@export var experience: int
@export var inventory: Array[String]

# 保存资源
func save_player_data(data: PlayerData):
    ResourceSaver.save(data, "user://player_data.tres")

# 加载资源
func load_player_data() -> PlayerData:
    if ResourceLoader.exists("user://player_data.tres"):
        return load("user://player_data.tres")
    else:
        return PlayerData.new()
```

## 2D/3D 开发

### 2D 游戏开发
```gdscript
# 2D 角色控制器
extends CharacterBody2D

@export var speed = 300.0
@export var jump_velocity = -400.0

# 获取重力值
var gravity = ProjectSettings.get_setting("physics/2d/default_gravity")

func _physics_process(delta):
    # 添加重力
    if not is_on_floor():
        velocity.y += gravity * delta
    
    # 处理跳跃
    if Input.is_action_just_pressed("ui_accept") and is_on_floor():
        velocity.y = jump_velocity
    
    # 处理移动
    var direction = Input.get_axis("ui_left", "ui_right")
    if direction:
        velocity.x = direction * speed
    else:
        velocity.x = move_toward(velocity.x, 0, speed)
    
    move_and_slide()
```

### 3D 游戏开发
```gdscript
# 3D 第一人称控制器
extends CharacterBody3D

@export var speed = 5.0
@export var jump_velocity = 4.5
@export var sensitivity = 0.01

@onready var head = $Head
@onready var camera = $Head/Camera3D

var gravity = ProjectSettings.get_setting("physics/3d/default_gravity")

func _ready():
    Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)

func _unhandled_input(event):
    if event is InputEventMouseMotion:
        head.rotate_y(-event.relative.x * sensitivity)
        camera.rotate_x(-event.relative.y * sensitivity)
        camera.rotation.x = clamp(camera.rotation.x, deg_to_rad(-90), deg_to_rad(90))

func _physics_process(delta):
    # 添加重力
    if not is_on_floor():
        velocity.y -= gravity * delta
    
    # 处理跳跃
    if Input.is_action_just_pressed("ui_accept") and is_on_floor():
        velocity.y = jump_velocity
    
    # 处理移动
    var input_dir = Input.get_vector("ui_left", "ui_right", "ui_up", "ui_down")
    var direction = (head.transform.basis * Vector3(input_dir.x, 0, input_dir.y)).normalized()
    
    if direction:
        velocity.x = direction.x * speed
        velocity.z = direction.z * speed
    else:
        velocity.x = move_toward(velocity.x, 0, speed)
        velocity.z = move_toward(velocity.z, 0, speed)
    
    move_and_slide()
```

## 脚本编程

### GDScript 基础
```gdscript
# GDScript 是 Python 风格的脚本语言
# 支持可选的静态类型

# 变量声明
var health: int = 100
var player_name: String = "Player"
var position: Vector2 = Vector2.ZERO

# 常量
const MAX_HEALTH = 100
const GRAVITY = 980

# 枚举
enum State {
    IDLE,
    RUNNING,
    JUMPING,
    FALLING
}

# 函数定义
func take_damage(amount: int) -> void:
    health -= amount
    health = max(0, health)
    
    if health == 0:
        die()

func get_health_percentage() -> float:
    return float(health) / MAX_HEALTH

# 属性（getter/setter）
var _speed: float = 100.0

var speed: float:
    get:
        return _speed
    set(value):
        _speed = max(0, value)

# 数组和字典
var inventory: Array[String] = ["sword", "potion", "key"]
var stats: Dictionary = {
    "strength": 10,
    "agility": 15,
    "intelligence": 8
}

# 类型提示和空值检查
func find_enemy(name: String) -> Enemy:
    for enemy in enemies:
        if enemy.name == name:
            return enemy
    return null

# 使用空值检查
var enemy = find_enemy("goblin")
if enemy != null:
    enemy.take_damage(10)
```

### C# 支持
```csharp
// C# 脚本示例
using Godot;

public partial class Player : CharacterBody2D
{
    [Export]
    public float Speed { get; set; } = 300.0f;
    
    [Export]
    public float JumpVelocity { get; set; } = -400.0f;
    
    // 信号定义
    [Signal]
    public delegate void HealthChangedEventHandler(int newHealth);
    
    private int _health = 100;
    public int Health
    {
        get => _health;
        set
        {
            _health = Mathf.Max(0, value);
            EmitSignal(SignalName.HealthChanged, _health);
        }
    }
    
    public override void _Ready()
    {
        // 连接信号
        HealthChanged += OnHealthChanged;
    }
    
    public override void _PhysicsProcess(double delta)
    {
        Vector2 velocity = Velocity;
        
        // 添加重力
        if (!IsOnFloor())
            velocity.Y += GetGravity() * (float)delta;
        
        // 处理跳跃
        if (Input.IsActionJustPressed("ui_accept") && IsOnFloor())
            velocity.Y = JumpVelocity;
        
        // 处理移动
        Vector2 direction = Input.GetVector("ui_left", "ui_right", "ui_up", "ui_down");
        if (direction != Vector2.Zero)
        {
            velocity.X = direction.X * Speed;
        }
        else
        {
            velocity.X = Mathf.MoveToward(Velocity.X, 0, Speed);
        }
        
        Velocity = velocity;
        MoveAndSlide();
    }
    
    private void OnHealthChanged(int newHealth)
    {
        GD.Print($"Health changed to: {newHealth}");
    }
    
    private float GetGravity()
    {
        return ProjectSettings.GetSetting("physics/2d/default_gravity").AsSingle();
    }
}
```

## 跨平台发布

### 支持的平台
```gdscript
# 桌面平台
# - Windows (x86, x64)
# - macOS (Intel, Apple Silicon)
# - Linux (x86, x64, ARM)

# 移动平台
# - Android (ARM, x86)
# - iOS (ARM64)

# Web 平台
# - HTML5 (WebAssembly)

# 游戏机平台（需要第三方发布商）
# - Nintendo Switch
# - PlayStation
# - Xbox
```

### 导出设置
```gdscript
# 项目设置中配置导出模板
# 1. 项目 -> 导出
# 2. 添加导出预设
# 3. 选择目标平台
# 4. 配置平台特定设置
# 5. 导出项目

# Android 导出示例配置
# - 包名：com.yourcompany.yourgame
# - 版本号：1.0
# - 最小 SDK：21 (Android 5.0)
# - 目标 SDK：33 (Android 13)
# - 架构：arm64-v8a, armeabi-v7a

# iOS 导出示例配置
# - Bundle ID：com.yourcompany.yourgame
# - 版本：1.0
# - 最小 iOS 版本：12.0
# - 设备系列：iPhone, iPad
```

## 最佳实践

### 1. 项目结构
```
project/
├── scenes/              # 场景文件
│   ├── main/           # 主场景
│   ├── ui/             # UI 场景
│   └── levels/         # 关卡场景
├── scripts/            # 脚本文件
│   ├── player/         # 玩家相关
│   ├── enemies/        # 敌人相关
│   └── managers/       # 管理器
├── assets/             # 资源文件
│   ├── textures/       # 纹理
│   ├── models/         # 3D 模型
│   ├── audio/          # 音频
│   └── fonts/          # 字体
├── autoload/           # 自动加载脚本
└── resources/          # 自定义资源
```

### 2. 代码规范
```gdscript
# 使用 snake_case 命名变量和函数
var player_health: int = 100
func calculate_damage(base_damage: int) -> int:
    return base_damage * damage_multiplier

# 使用 PascalCase 命名类和常量
class_name PlayerController
const MAX_HEALTH = 100

# 使用类型提示
func get_player_by_id(id: int) -> Player:
    return players.get(id)

# 使用信号而不是直接调用
signal player_died
signal health_changed(new_health: int)

# 优先使用组合而不是继承
# 创建小的、专用的节点
# 通过场景实例化组合功能
```

### 3. 性能优化
```gdscript
# 对象池模式
class_name ObjectPool
extends Node

var pool: Array[Node] = []
var scene: PackedScene

func _init(scene_path: String, initial_size: int = 10):
    scene = load(scene_path)
    for i in initial_size:
        var instance = scene.instantiate()
        instance.set_process(false)
        instance.visible = false
        pool.append(instance)
        add_child(instance)

func get_object() -> Node:
    if pool.size() > 0:
        var obj = pool.pop_back()
        obj.set_process(true)
        obj.visible = true
        return obj
    else:
        return scene.instantiate()

func return_object(obj: Node):
    obj.set_process(false)
    obj.visible = false
    pool.append(obj)

# 使用 _physics_process 处理物理
# 使用 _process 处理 UI 和非物理逻辑
# 避免在 _process 中进行昂贵的计算
```

### 4. 调试技巧
```gdscript
# 使用内置调试器
# 设置断点：点击行号
# 查看变量：调试器面板
# 单步执行：F10, F11

# 使用 print 调试
print("Player position: ", global_position)
print_rich("[color=red]Error:[/color] Invalid state")

# 使用断言
assert(health >= 0, "Health cannot be negative")
assert(player != null, "Player reference is null")

# 远程调试
# 在移动设备上运行时可以远程调试
# 项目 -> 项目设置 -> 网络 -> 远程端口
```

---

*Godot Engine 以其开源免费、易于学习和强大功能在独立游戏开发者中广受欢迎。更多详细信息请参考 [Godot 官方文档](https://docs.godotengine.org/)*
