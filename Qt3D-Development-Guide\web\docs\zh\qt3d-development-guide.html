<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qt 3D 引擎开发指南</title> <!-- The title will be replaced for each page -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/docs.css">
    <link rel="stylesheet" href="../../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html" class="logo-text" data-i18n="nav-logo-text">3D 引擎指南</a>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="../index.html#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="../index.html#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="../index.html#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 文档内容区域 -->
    <div class="doc-layout container">
        <!-- 左侧目录 -->
        <aside class="doc-sidebar card">
            <div class="toc-container">
                <!-- 目录内容将通过JavaScript动态生成 -->
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <section class="doc-section card">
            <div class="doc-container">
                <main class="doc-content">
                    <div lang="zh"><h1 id="qt-3d-引擎开发指南">Qt 3D 引擎开发指南</h1>
<h2 id="目录">目录</h2>
<ol type="1">
<li><a href="#概述">概述</a></li>
<li><a href="#技术特性">技术特性</a></li>
<li><a href="#开发环境搭建">开发环境搭建</a></li>
<li><a href="#核心架构">核心架构</a></li>
<li><a href="#开发流程">开发流程</a></li>
<li><a href="#最佳实践">最佳实践</a></li>
<li><a href="#性能优化">性能优化</a></li>
<li><a href="#调试与测试">调试与测试</a></li>
</ol>
<h2 id="概述">概述</h2>
<p>Qt 3D 是 Qt 框架的一部分，提供了功能丰富的 3D
图形渲染和场景管理功能。它基于现代图形
API（OpenGL、Vulkan、DirectX），为开发者提供了高级的 3D
应用程序开发能力。</p>
<h3 id="主要优势">主要优势</h3>
<ul>
<li><strong>跨平台支持</strong>：支持
Windows、Linux、macOS、Android、iOS 等多个平台</li>
<li><strong>现代图形 API</strong>：支持 OpenGL、Vulkan、DirectX 12
等现代图形 API</li>
<li><strong>声明式编程</strong>：支持 QML 声明式编程，简化 3D
场景构建</li>
<li><strong>高性能渲染</strong>：基于多线程渲染架构，提供高效的图形渲染性能</li>
<li><strong>丰富的材质系统</strong>：内置多种材质和着色器，支持自定义材质</li>
<li><strong>完整的生态系统</strong>：与 Qt 生态系统完美集成，可复用现有
Qt 技能</li>
</ul>
<h2 id="技术特性">技术特性</h2>
<h3 id="渲染引擎">渲染引擎</h3>
<ul>
<li><strong>多线程渲染架构</strong>：渲染线程与主线程分离，确保 UI
响应性</li>
<li><strong>现代图形 API 支持</strong>：
<ul>
<li>OpenGL 3.2+ / OpenGL ES 3.0+</li>
<li>Vulkan 1.0+</li>
<li>DirectX 12（Windows）</li>
<li>Metal（macOS/iOS）</li>
</ul></li>
<li><strong>延迟渲染管线</strong>：支持延迟渲染和前向渲染</li>
<li><strong>HDR 渲染</strong>：支持高动态范围渲染</li>
<li><strong>多重采样抗锯齿</strong>：MSAA、FXAA 等抗锯齿技术</li>
</ul>
<h3 id="场景管理">场景管理</h3>
<ul>
<li><strong>实体-组件-系统（ECS）架构</strong>：灵活的场景对象管理</li>
<li><strong>层次化场景图</strong>：支持复杂的场景层次结构</li>
<li><strong>空间分割</strong>：八叉树、BSP 树等空间优化算法</li>
<li><strong>视锥剔除</strong>：自动进行视锥剔除优化</li>
<li><strong>LOD 系统</strong>：多级细节模型支持</li>
</ul>
<h3 id="材质与着色器">材质与着色器</h3>
<ul>
<li><strong>基于物理的渲染（PBR）</strong>：支持现代 PBR 材质工作流</li>
<li><strong>自定义着色器</strong>：支持 GLSL、HLSL 着色器编程</li>
<li><strong>材质编辑器</strong>：可视化材质编辑工具</li>
<li><strong>纹理管理</strong>：高效的纹理加载和管理系统</li>
<li><strong>动态材质</strong>：运行时材质参数调整</li>
</ul>
<h3 id="动画系统">动画系统</h3>
<ul>
<li><strong>骨骼动画</strong>：完整的骨骼动画系统</li>
<li><strong>关键帧动画</strong>：支持位置、旋转、缩放动画</li>
<li><strong>动画混合</strong>：多动画混合和过渡</li>
<li><strong>动画状态机</strong>：复杂动画逻辑管理</li>
<li><strong>物理动画</strong>：与物理引擎集成的动画</li>
</ul>
<h3 id="d-图形支持">2D 图形支持</h3>
<ul>
<li><strong>Qt Quick</strong>：基于 QML 的 2D 图形框架</li>
<li><strong>Canvas API</strong>：HTML5 Canvas 风格的 2D 绘图</li>
<li><strong>SVG 支持</strong>：矢量图形的完整支持</li>
<li><strong>图像处理</strong>：丰富的图像滤镜和效果</li>
<li><strong>2D 动画</strong>：属性动画、路径动画、粒子系统</li>
</ul>
<h3 id="d3d-融合开发">2D/3D 融合开发</h3>
<ul>
<li><strong>混合渲染</strong>：在同一场景中同时渲染 2D 和 3D 内容</li>
<li><strong>UI 叠加</strong>：3D 场景上的 2D UI 界面</li>
<li><strong>纹理映射</strong>：将 2D 内容作为 3D 对象的纹理</li>
<li><strong>坐标转换</strong>：2D 屏幕坐标与 3D 世界坐标的转换</li>
<li><strong>交互集成</strong>：2D UI 控件与 3D 场景的交互</li>
</ul>
<h2 id="开发环境搭建">开发环境搭建</h2>
<h3 id="系统要求">系统要求</h3>
<ul>
<li><strong>操作系统</strong>：Windows 10+, Ubuntu 18.04+, macOS
10.14+</li>
<li><strong>Qt 版本</strong>：Qt 5.15+ 或 Qt 6.2+</li>
<li><strong>编译器</strong>：
<ul>
<li>Windows: MSVC 2019+ 或 MinGW 8.1+</li>
<li>Linux: GCC 7+ 或 Clang 8+</li>
<li>macOS: Xcode 11+</li>
</ul></li>
<li><strong>图形驱动</strong>：支持 OpenGL 3.2+ 的显卡驱动</li>
</ul>
<h3 id="安装步骤">安装步骤</h3>
<h4 id="安装-qt">1. 安装 Qt</h4>
<div class="sourceCode" id="cb1"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 使用 Qt 在线安装器</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="ex">./qt-unified-linux-x64-online.run</span></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a><span class="co"># 或使用包管理器（Ubuntu）</span></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a><span class="fu">sudo</span> apt-get install qt6-base-dev qt6-3d-dev</span></code></pre></div>
<h4 id="配置开发环境">2. 配置开发环境</h4>
<div class="sourceCode" id="cb2"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 设置环境变量</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a><span class="bu">export</span> <span class="va">QT_DIR</span><span class="op">=</span>/opt/Qt/6.5.0/gcc_64</span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a><span class="bu">export</span> <span class="va">PATH</span><span class="op">=</span><span class="va">$QT_DIR</span>/bin:<span class="va">$PATH</span></span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a><span class="bu">export</span> <span class="va">LD_LIBRARY_PATH</span><span class="op">=</span><span class="va">$QT_DIR</span>/lib:<span class="va">$LD_LIBRARY_PATH</span></span></code></pre></div>
<h4 id="验证安装">3. 验证安装</h4>
<div class="sourceCode" id="cb3"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="co">// test_qt3d.cpp</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a><span class="pp">#include </span><span class="im">&lt;Qt3DCore/QEntity&gt;</span></span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a><span class="pp">#include </span><span class="im">&lt;Qt3DRender/QCamera&gt;</span></span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a><span class="pp">#include </span><span class="im">&lt;QApplication&gt;</span></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a><span class="dt">int</span> main<span class="op">(</span><span class="dt">int</span> argc<span class="op">,</span> <span class="dt">char</span> <span class="op">*</span>argv<span class="op">[])</span></span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a>    <span class="ex">QApplication</span> app<span class="op">(</span>argc<span class="op">,</span> argv<span class="op">);</span></span>
<span id="cb3-9"><a href="#cb3-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb3-10"><a href="#cb3-10" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 创建基本的 3D 场景</span></span>
<span id="cb3-11"><a href="#cb3-11" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Qt3DCore::QEntity</span> <span class="op">*</span>rootEntity <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DCore::QEntity</span><span class="op">;</span></span>
<span id="cb3-12"><a href="#cb3-12" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb3-13"><a href="#cb3-13" aria-hidden="true" tabindex="-1"></a>    <span class="fu">qDebug</span><span class="op">()</span> <span class="op">&lt;&lt;</span> <span class="st">&quot;Qt 3D 安装成功！&quot;</span><span class="op">;</span></span>
<span id="cb3-14"><a href="#cb3-14" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb3-15"><a href="#cb3-15" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="核心架构">核心架构</h2>
<h3 id="ecs-架构模式">ECS 架构模式</h3>
<p>Qt 3D 采用实体-组件-系统（Entity-Component-System）架构：</p>
<div class="sourceCode" id="cb4"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 实体（Entity）- 场景中的对象</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DCore::QEntity</span> <span class="op">*</span>cubeEntity <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DCore::QEntity</span><span class="op">(</span>rootEntity<span class="op">);</span></span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a><span class="co">// 组件（Component）- 对象的属性和行为</span></span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DCore::QTransform</span> <span class="op">*</span>cubeTransform <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DCore::QTransform</span><span class="op">();</span></span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DRender::QMesh</span> <span class="op">*</span>cubeMesh <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QMesh</span><span class="op">();</span></span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DExtras::QPhongMaterial</span> <span class="op">*</span>cubeMaterial <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DExtras::QPhongMaterial</span><span class="op">();</span></span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a><span class="co">// 将组件添加到实体</span></span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a>cubeEntity<span class="op">-&gt;</span>addComponent<span class="op">(</span>cubeTransform<span class="op">);</span></span>
<span id="cb4-11"><a href="#cb4-11" aria-hidden="true" tabindex="-1"></a>cubeEntity<span class="op">-&gt;</span>addComponent<span class="op">(</span>cubeMesh<span class="op">);</span></span>
<span id="cb4-12"><a href="#cb4-12" aria-hidden="true" tabindex="-1"></a>cubeEntity<span class="op">-&gt;</span>addComponent<span class="op">(</span>cubeMaterial<span class="op">);</span></span></code></pre></div>
<h3 id="渲染管线">渲染管线</h3>
<div class="sourceCode" id="cb5"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 创建渲染设置</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DRender::QRenderSettings</span> <span class="op">*</span>renderSettings <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QRenderSettings</span><span class="op">();</span></span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a><span class="co">// 配置渲染管线</span></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DRender::QRenderSurfaceSelector</span> <span class="op">*</span>surfaceSelector <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QRenderSurfaceSelector</span><span class="op">();</span></span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DRender::QViewport</span> <span class="op">*</span>viewport <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QViewport</span><span class="op">(</span>surfaceSelector<span class="op">);</span></span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DRender::QCameraSelector</span> <span class="op">*</span>cameraSelector <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QCameraSelector</span><span class="op">(</span>viewport<span class="op">);</span></span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DRender::QClearBuffers</span> <span class="op">*</span>clearBuffers <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QClearBuffers</span><span class="op">(</span>cameraSelector<span class="op">);</span></span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-10"><a href="#cb5-10" aria-hidden="true" tabindex="-1"></a><span class="co">// 设置清除颜色</span></span>
<span id="cb5-11"><a href="#cb5-11" aria-hidden="true" tabindex="-1"></a>clearBuffers<span class="op">-&gt;</span>setBuffers<span class="op">(</span><span class="ex">Qt3DRender::QClearBuffers::ColorDepthBuffer</span><span class="op">);</span></span>
<span id="cb5-12"><a href="#cb5-12" aria-hidden="true" tabindex="-1"></a>clearBuffers<span class="op">-&gt;</span>setClearColor<span class="op">(</span><span class="ex">QColor</span><span class="op">(</span><span class="dv">64</span><span class="op">,</span> <span class="dv">64</span><span class="op">,</span> <span class="dv">64</span><span class="op">));</span></span></code></pre></div>
<h2 id="开发流程">开发流程</h2>
<h3 id="项目初始化">1. 项目初始化</h3>
<div class="sourceCode" id="cb6"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="co">// main.cpp</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a><span class="pp">#include </span><span class="im">&lt;QApplication&gt;</span></span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a><span class="pp">#include </span><span class="im">&lt;Qt3DExtras/Qt3DWindow&gt;</span></span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a><span class="pp">#include </span><span class="im">&lt;Qt3DExtras/QFirstPersonCameraController&gt;</span></span>
<span id="cb6-5"><a href="#cb6-5" aria-hidden="true" tabindex="-1"></a><span class="pp">#include </span><span class="im">&lt;Qt3DCore/QEntity&gt;</span></span>
<span id="cb6-6"><a href="#cb6-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-7"><a href="#cb6-7" aria-hidden="true" tabindex="-1"></a><span class="dt">int</span> main<span class="op">(</span><span class="dt">int</span> argc<span class="op">,</span> <span class="dt">char</span> <span class="op">*</span>argv<span class="op">[])</span></span>
<span id="cb6-8"><a href="#cb6-8" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb6-9"><a href="#cb6-9" aria-hidden="true" tabindex="-1"></a>    <span class="ex">QApplication</span> app<span class="op">(</span>argc<span class="op">,</span> argv<span class="op">);</span></span>
<span id="cb6-10"><a href="#cb6-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-11"><a href="#cb6-11" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 创建 3D 窗口</span></span>
<span id="cb6-12"><a href="#cb6-12" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Qt3DExtras::Qt3DWindow</span> <span class="op">*</span>view <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DExtras::Qt3DWindow</span><span class="op">();</span></span>
<span id="cb6-13"><a href="#cb6-13" aria-hidden="true" tabindex="-1"></a>    view<span class="op">-&gt;</span>defaultFrameGraph<span class="op">()-&gt;</span>setClearColor<span class="op">(</span><span class="ex">QColor</span><span class="op">(</span><span class="ex">QRgb</span><span class="op">(</span><span class="bn">0x4d4d4f</span><span class="op">)));</span></span>
<span id="cb6-14"><a href="#cb6-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-15"><a href="#cb6-15" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 创建根实体</span></span>
<span id="cb6-16"><a href="#cb6-16" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Qt3DCore::QEntity</span> <span class="op">*</span>rootEntity <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DCore::QEntity</span><span class="op">();</span></span>
<span id="cb6-17"><a href="#cb6-17" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-18"><a href="#cb6-18" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 设置场景</span></span>
<span id="cb6-19"><a href="#cb6-19" aria-hidden="true" tabindex="-1"></a>    view<span class="op">-&gt;</span>setRootEntity<span class="op">(</span>rootEntity<span class="op">);</span></span>
<span id="cb6-20"><a href="#cb6-20" aria-hidden="true" tabindex="-1"></a>    view<span class="op">-&gt;</span>show<span class="op">();</span></span>
<span id="cb6-21"><a href="#cb6-21" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-22"><a href="#cb6-22" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> app<span class="op">.</span>exec<span class="op">();</span></span>
<span id="cb6-23"><a href="#cb6-23" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="场景构建">2. 场景构建</h3>
<div class="sourceCode" id="cb7"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 创建相机</span></span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DRender::QCamera</span> <span class="op">*</span>cameraEntity <span class="op">=</span> view<span class="op">-&gt;</span>camera<span class="op">();</span></span>
<span id="cb7-3"><a href="#cb7-3" aria-hidden="true" tabindex="-1"></a>cameraEntity<span class="op">-&gt;</span>lens<span class="op">()-&gt;</span>setPerspectiveProjection<span class="op">(</span><span class="fl">45.0</span><span class="bu">f</span><span class="op">,</span> <span class="fl">16.0</span><span class="bu">f</span><span class="op">/</span><span class="fl">9.0</span><span class="bu">f</span><span class="op">,</span> <span class="fl">0.1</span><span class="bu">f</span><span class="op">,</span> <span class="fl">1000.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb7-4"><a href="#cb7-4" aria-hidden="true" tabindex="-1"></a>cameraEntity<span class="op">-&gt;</span>setPosition<span class="op">(</span><span class="ex">QVector3D</span><span class="op">(</span><span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="fl">20.0</span><span class="bu">f</span><span class="op">));</span></span>
<span id="cb7-5"><a href="#cb7-5" aria-hidden="true" tabindex="-1"></a>cameraEntity<span class="op">-&gt;</span>setUpVector<span class="op">(</span><span class="ex">QVector3D</span><span class="op">(</span><span class="dv">0</span><span class="op">,</span> <span class="dv">1</span><span class="op">,</span> <span class="dv">0</span><span class="op">));</span></span>
<span id="cb7-6"><a href="#cb7-6" aria-hidden="true" tabindex="-1"></a>cameraEntity<span class="op">-&gt;</span>setViewCenter<span class="op">(</span><span class="ex">QVector3D</span><span class="op">(</span><span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">));</span></span>
<span id="cb7-7"><a href="#cb7-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-8"><a href="#cb7-8" aria-hidden="true" tabindex="-1"></a><span class="co">// 添加相机控制器</span></span>
<span id="cb7-9"><a href="#cb7-9" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DExtras::QFirstPersonCameraController</span> <span class="op">*</span>camController <span class="op">=</span> </span>
<span id="cb7-10"><a href="#cb7-10" aria-hidden="true" tabindex="-1"></a>    <span class="kw">new</span> <span class="ex">Qt3DExtras::QFirstPersonCameraController</span><span class="op">(</span>rootEntity<span class="op">);</span></span>
<span id="cb7-11"><a href="#cb7-11" aria-hidden="true" tabindex="-1"></a>camController<span class="op">-&gt;</span>setCamera<span class="op">(</span>cameraEntity<span class="op">);</span></span></code></pre></div>
<h3 id="几何体创建">3. 几何体创建</h3>
<div class="sourceCode" id="cb8"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 创建立方体</span></span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DCore::QEntity</span> <span class="op">*</span>cubeEntity <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DCore::QEntity</span><span class="op">(</span>rootEntity<span class="op">);</span></span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-4"><a href="#cb8-4" aria-hidden="true" tabindex="-1"></a><span class="co">// 几何体组件</span></span>
<span id="cb8-5"><a href="#cb8-5" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DExtras::QCuboidMesh</span> <span class="op">*</span>cubeMesh <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DExtras::QCuboidMesh</span><span class="op">();</span></span>
<span id="cb8-6"><a href="#cb8-6" aria-hidden="true" tabindex="-1"></a>cubeMesh<span class="op">-&gt;</span>setXExtent<span class="op">(</span><span class="fl">2.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb8-7"><a href="#cb8-7" aria-hidden="true" tabindex="-1"></a>cubeMesh<span class="op">-&gt;</span>setYExtent<span class="op">(</span><span class="fl">2.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb8-8"><a href="#cb8-8" aria-hidden="true" tabindex="-1"></a>cubeMesh<span class="op">-&gt;</span>setZExtent<span class="op">(</span><span class="fl">2.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb8-9"><a href="#cb8-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-10"><a href="#cb8-10" aria-hidden="true" tabindex="-1"></a><span class="co">// 变换组件</span></span>
<span id="cb8-11"><a href="#cb8-11" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DCore::QTransform</span> <span class="op">*</span>cubeTransform <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DCore::QTransform</span><span class="op">();</span></span>
<span id="cb8-12"><a href="#cb8-12" aria-hidden="true" tabindex="-1"></a>cubeTransform<span class="op">-&gt;</span>setTranslation<span class="op">(</span><span class="ex">QVector3D</span><span class="op">(</span><span class="fl">0.0</span><span class="bu">f</span><span class="op">,</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">,</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">));</span></span>
<span id="cb8-13"><a href="#cb8-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-14"><a href="#cb8-14" aria-hidden="true" tabindex="-1"></a><span class="co">// 材质组件</span></span>
<span id="cb8-15"><a href="#cb8-15" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DExtras::QPhongMaterial</span> <span class="op">*</span>cubeMaterial <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DExtras::QPhongMaterial</span><span class="op">();</span></span>
<span id="cb8-16"><a href="#cb8-16" aria-hidden="true" tabindex="-1"></a>cubeMaterial<span class="op">-&gt;</span>setDiffuse<span class="op">(</span><span class="ex">QColor</span><span class="op">(</span><span class="ex">QRgb</span><span class="op">(</span><span class="bn">0xa69929</span><span class="op">)));</span></span>
<span id="cb8-17"><a href="#cb8-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-18"><a href="#cb8-18" aria-hidden="true" tabindex="-1"></a><span class="co">// 添加组件到实体</span></span>
<span id="cb8-19"><a href="#cb8-19" aria-hidden="true" tabindex="-1"></a>cubeEntity<span class="op">-&gt;</span>addComponent<span class="op">(</span>cubeMesh<span class="op">);</span></span>
<span id="cb8-20"><a href="#cb8-20" aria-hidden="true" tabindex="-1"></a>cubeEntity<span class="op">-&gt;</span>addComponent<span class="op">(</span>cubeTransform<span class="op">);</span></span>
<span id="cb8-21"><a href="#cb8-21" aria-hidden="true" tabindex="-1"></a>cubeEntity<span class="op">-&gt;</span>addComponent<span class="op">(</span>cubeMaterial<span class="op">);</span></span></code></pre></div>
<h3 id="动画实现">4. 动画实现</h3>
<div class="sourceCode" id="cb9"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 创建动画</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a><span class="ex">QPropertyAnimation</span> <span class="op">*</span>cubeRotateTransformAnimation <span class="op">=</span> <span class="kw">new</span> <span class="ex">QPropertyAnimation</span><span class="op">(</span>cubeTransform<span class="op">);</span></span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a>cubeRotateTransformAnimation<span class="op">-&gt;</span>setTargetObject<span class="op">(</span>cubeTransform<span class="op">);</span></span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a>cubeRotateTransformAnimation<span class="op">-&gt;</span>setPropertyName<span class="op">(</span><span class="st">&quot;rotation&quot;</span><span class="op">);</span></span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a>cubeRotateTransformAnimation<span class="op">-&gt;</span>setStartValue<span class="op">(</span><span class="ex">QVariant::fromValue</span><span class="op">(</span><span class="ex">QQuaternion::fromAxisAndAngle</span><span class="op">(</span><span class="ex">QVector3D</span><span class="op">(</span><span class="dv">1</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">),</span> <span class="dv">0</span><span class="op">)));</span></span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a>cubeRotateTransformAnimation<span class="op">-&gt;</span>setEndValue<span class="op">(</span><span class="ex">QVariant::fromValue</span><span class="op">(</span><span class="ex">QQuaternion::fromAxisAndAngle</span><span class="op">(</span><span class="ex">QVector3D</span><span class="op">(</span><span class="dv">1</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">),</span> <span class="dv">360</span><span class="op">)));</span></span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a>cubeRotateTransformAnimation<span class="op">-&gt;</span>setDuration<span class="op">(</span><span class="dv">5000</span><span class="op">);</span></span>
<span id="cb9-8"><a href="#cb9-8" aria-hidden="true" tabindex="-1"></a>cubeRotateTransformAnimation<span class="op">-&gt;</span>setLoopCount<span class="op">(-</span><span class="dv">1</span><span class="op">);</span></span>
<span id="cb9-9"><a href="#cb9-9" aria-hidden="true" tabindex="-1"></a>cubeRotateTransformAnimation<span class="op">-&gt;</span>start<span class="op">();</span></span></code></pre></div>
<h2 id="d3d-融合开发-1">2D/3D 融合开发</h2>
<h3 id="qml-中的-2d3d-混合场景">1. QML 中的 2D/3D 混合场景</h3>
<div class="sourceCode" id="cb10"><pre
class="sourceCode qml"><code class="sourceCode qml"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span><span class="im"> QtQuick 2.15</span></span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span><span class="im"> QtQuick.Scene3D 2.15</span></span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span><span class="im"> Qt3D.Core 2.15</span></span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span><span class="im"> Qt3D.Render 2.15</span></span>
<span id="cb10-5"><a href="#cb10-5" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span><span class="im"> Qt3D.Extras 2.15</span></span>
<span id="cb10-6"><a href="#cb10-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-7"><a href="#cb10-7" aria-hidden="true" tabindex="-1"></a>ApplicationWindow {</span>
<span id="cb10-8"><a href="#cb10-8" aria-hidden="true" tabindex="-1"></a>    <span class="dt">width</span><span class="op">:</span> <span class="dv">1200</span></span>
<span id="cb10-9"><a href="#cb10-9" aria-hidden="true" tabindex="-1"></a>    <span class="dt">height</span><span class="op">:</span> <span class="dv">800</span></span>
<span id="cb10-10"><a href="#cb10-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-11"><a href="#cb10-11" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 2D 背景</span></span>
<span id="cb10-12"><a href="#cb10-12" aria-hidden="true" tabindex="-1"></a>    <span class="ot">Rectangle</span> {</span>
<span id="cb10-13"><a href="#cb10-13" aria-hidden="true" tabindex="-1"></a>        anchors<span class="op">.</span><span class="at">fill</span><span class="op">:</span> parent</span>
<span id="cb10-14"><a href="#cb10-14" aria-hidden="true" tabindex="-1"></a>        <span class="dt">gradient</span><span class="op">:</span> <span class="ot">Gradient</span> {</span>
<span id="cb10-15"><a href="#cb10-15" aria-hidden="true" tabindex="-1"></a>            <span class="ot">GradientStop</span> { <span class="dt">position</span><span class="op">:</span> <span class="fl">0.0</span><span class="op">;</span> <span class="dt">color</span><span class="op">:</span> <span class="st">&quot;#87CEEB&quot;</span> }</span>
<span id="cb10-16"><a href="#cb10-16" aria-hidden="true" tabindex="-1"></a>            <span class="ot">GradientStop</span> { <span class="dt">position</span><span class="op">:</span> <span class="fl">1.0</span><span class="op">;</span> <span class="dt">color</span><span class="op">:</span> <span class="st">&quot;#98FB98&quot;</span> }</span>
<span id="cb10-17"><a href="#cb10-17" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb10-18"><a href="#cb10-18" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-19"><a href="#cb10-19" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-20"><a href="#cb10-20" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 3D 场景</span></span>
<span id="cb10-21"><a href="#cb10-21" aria-hidden="true" tabindex="-1"></a>    Scene3D {</span>
<span id="cb10-22"><a href="#cb10-22" aria-hidden="true" tabindex="-1"></a>        <span class="dt">id</span><span class="op">:</span> scene3d</span>
<span id="cb10-23"><a href="#cb10-23" aria-hidden="true" tabindex="-1"></a>        anchors<span class="op">.</span><span class="at">fill</span><span class="op">:</span> parent</span>
<span id="cb10-24"><a href="#cb10-24" aria-hidden="true" tabindex="-1"></a>        <span class="dt">aspects</span><span class="op">:</span> [<span class="st">&quot;input&quot;</span><span class="op">,</span> <span class="st">&quot;logic&quot;</span>]</span>
<span id="cb10-25"><a href="#cb10-25" aria-hidden="true" tabindex="-1"></a>        <span class="dt">cameraAspectRatioMode</span><span class="op">:</span> <span class="ex">Scene3D</span><span class="op">.</span><span class="at">AutomaticAspectRatio</span></span>
<span id="cb10-26"><a href="#cb10-26" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-27"><a href="#cb10-27" aria-hidden="true" tabindex="-1"></a>        Entity {</span>
<span id="cb10-28"><a href="#cb10-28" aria-hidden="true" tabindex="-1"></a>            <span class="dt">id</span><span class="op">:</span> sceneRoot</span>
<span id="cb10-29"><a href="#cb10-29" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-30"><a href="#cb10-30" aria-hidden="true" tabindex="-1"></a>            Camera {</span>
<span id="cb10-31"><a href="#cb10-31" aria-hidden="true" tabindex="-1"></a>                <span class="dt">id</span><span class="op">:</span> camera</span>
<span id="cb10-32"><a href="#cb10-32" aria-hidden="true" tabindex="-1"></a>                <span class="dt">projectionType</span><span class="op">:</span> <span class="ex">CameraLens</span><span class="op">.</span><span class="at">PerspectiveProjection</span></span>
<span id="cb10-33"><a href="#cb10-33" aria-hidden="true" tabindex="-1"></a>                <span class="dt">fieldOfView</span><span class="op">:</span> <span class="dv">45</span></span>
<span id="cb10-34"><a href="#cb10-34" aria-hidden="true" tabindex="-1"></a>                <span class="dt">aspectRatio</span><span class="op">:</span> <span class="dv">16</span><span class="op">/</span><span class="dv">9</span></span>
<span id="cb10-35"><a href="#cb10-35" aria-hidden="true" tabindex="-1"></a>                <span class="dt">nearPlane</span><span class="op">:</span> <span class="fl">0.1</span></span>
<span id="cb10-36"><a href="#cb10-36" aria-hidden="true" tabindex="-1"></a>                <span class="dt">farPlane</span><span class="op">:</span> <span class="fl">1000.0</span></span>
<span id="cb10-37"><a href="#cb10-37" aria-hidden="true" tabindex="-1"></a>                <span class="dt">position</span><span class="op">:</span> <span class="ex">Qt</span><span class="op">.</span><span class="fu">vector3d</span>(<span class="fl">0.0</span><span class="op">,</span> <span class="fl">0.0</span><span class="op">,</span> <span class="fl">20.0</span>)</span>
<span id="cb10-38"><a href="#cb10-38" aria-hidden="true" tabindex="-1"></a>                <span class="dt">upVector</span><span class="op">:</span> <span class="ex">Qt</span><span class="op">.</span><span class="fu">vector3d</span>(<span class="fl">0.0</span><span class="op">,</span> <span class="fl">1.0</span><span class="op">,</span> <span class="fl">0.0</span>)</span>
<span id="cb10-39"><a href="#cb10-39" aria-hidden="true" tabindex="-1"></a>                <span class="dt">viewCenter</span><span class="op">:</span> <span class="ex">Qt</span><span class="op">.</span><span class="fu">vector3d</span>(<span class="fl">0.0</span><span class="op">,</span> <span class="fl">0.0</span><span class="op">,</span> <span class="fl">0.0</span>)</span>
<span id="cb10-40"><a href="#cb10-40" aria-hidden="true" tabindex="-1"></a>            }</span>
<span id="cb10-41"><a href="#cb10-41" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-42"><a href="#cb10-42" aria-hidden="true" tabindex="-1"></a>            <span class="dt">components</span><span class="op">:</span> [</span>
<span id="cb10-43"><a href="#cb10-43" aria-hidden="true" tabindex="-1"></a>                RenderSettings {</span>
<span id="cb10-44"><a href="#cb10-44" aria-hidden="true" tabindex="-1"></a>                    <span class="dt">activeFrameGraph</span><span class="op">:</span> ForwardRenderer {</span>
<span id="cb10-45"><a href="#cb10-45" aria-hidden="true" tabindex="-1"></a>                        <span class="dt">clearColor</span><span class="op">:</span> <span class="ex">Qt</span><span class="op">.</span><span class="fu">rgba</span>(<span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="dv">0</span>) <span class="co">// 透明背景</span></span>
<span id="cb10-46"><a href="#cb10-46" aria-hidden="true" tabindex="-1"></a>                        <span class="dt">camera</span><span class="op">:</span> camera</span>
<span id="cb10-47"><a href="#cb10-47" aria-hidden="true" tabindex="-1"></a>                    }</span>
<span id="cb10-48"><a href="#cb10-48" aria-hidden="true" tabindex="-1"></a>                }<span class="op">,</span></span>
<span id="cb10-49"><a href="#cb10-49" aria-hidden="true" tabindex="-1"></a>                InputSettings { }</span>
<span id="cb10-50"><a href="#cb10-50" aria-hidden="true" tabindex="-1"></a>            ]</span>
<span id="cb10-51"><a href="#cb10-51" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-52"><a href="#cb10-52" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 3D 立方体</span></span>
<span id="cb10-53"><a href="#cb10-53" aria-hidden="true" tabindex="-1"></a>            Entity {</span>
<span id="cb10-54"><a href="#cb10-54" aria-hidden="true" tabindex="-1"></a>                <span class="dt">id</span><span class="op">:</span> cubeEntity</span>
<span id="cb10-55"><a href="#cb10-55" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-56"><a href="#cb10-56" aria-hidden="true" tabindex="-1"></a>                <span class="dt">components</span><span class="op">:</span> [</span>
<span id="cb10-57"><a href="#cb10-57" aria-hidden="true" tabindex="-1"></a>                    CuboidMesh {</span>
<span id="cb10-58"><a href="#cb10-58" aria-hidden="true" tabindex="-1"></a>                        <span class="dt">xExtent</span><span class="op">:</span> <span class="dv">4</span></span>
<span id="cb10-59"><a href="#cb10-59" aria-hidden="true" tabindex="-1"></a>                        <span class="dt">yExtent</span><span class="op">:</span> <span class="dv">4</span></span>
<span id="cb10-60"><a href="#cb10-60" aria-hidden="true" tabindex="-1"></a>                        <span class="dt">zExtent</span><span class="op">:</span> <span class="dv">4</span></span>
<span id="cb10-61"><a href="#cb10-61" aria-hidden="true" tabindex="-1"></a>                    }<span class="op">,</span></span>
<span id="cb10-62"><a href="#cb10-62" aria-hidden="true" tabindex="-1"></a>                    PhongMaterial {</span>
<span id="cb10-63"><a href="#cb10-63" aria-hidden="true" tabindex="-1"></a>                        <span class="dt">diffuse</span><span class="op">:</span> <span class="st">&quot;lightblue&quot;</span></span>
<span id="cb10-64"><a href="#cb10-64" aria-hidden="true" tabindex="-1"></a>                        <span class="dt">ambient</span><span class="op">:</span> <span class="st">&quot;blue&quot;</span></span>
<span id="cb10-65"><a href="#cb10-65" aria-hidden="true" tabindex="-1"></a>                        <span class="dt">specular</span><span class="op">:</span> <span class="st">&quot;white&quot;</span></span>
<span id="cb10-66"><a href="#cb10-66" aria-hidden="true" tabindex="-1"></a>                        <span class="dt">shininess</span><span class="op">:</span> <span class="fl">150.0</span></span>
<span id="cb10-67"><a href="#cb10-67" aria-hidden="true" tabindex="-1"></a>                    }<span class="op">,</span></span>
<span id="cb10-68"><a href="#cb10-68" aria-hidden="true" tabindex="-1"></a>                    Transform {</span>
<span id="cb10-69"><a href="#cb10-69" aria-hidden="true" tabindex="-1"></a>                        <span class="dt">id</span><span class="op">:</span> cubeTransform</span>
<span id="cb10-70"><a href="#cb10-70" aria-hidden="true" tabindex="-1"></a>                        <span class="dt">translation</span><span class="op">:</span> <span class="ex">Qt</span><span class="op">.</span><span class="fu">vector3d</span>(<span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="dv">0</span>)</span>
<span id="cb10-71"><a href="#cb10-71" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-72"><a href="#cb10-72" aria-hidden="true" tabindex="-1"></a>                        <span class="ot">PropertyAnimation</span> on rotationY {</span>
<span id="cb10-73"><a href="#cb10-73" aria-hidden="true" tabindex="-1"></a>                            <span class="dt">loops</span><span class="op">:</span> <span class="ex">Animation</span><span class="op">.</span><span class="at">Infinite</span></span>
<span id="cb10-74"><a href="#cb10-74" aria-hidden="true" tabindex="-1"></a>                            <span class="dt">from</span><span class="op">:</span> <span class="dv">0</span></span>
<span id="cb10-75"><a href="#cb10-75" aria-hidden="true" tabindex="-1"></a>                            <span class="dt">to</span><span class="op">:</span> <span class="dv">360</span></span>
<span id="cb10-76"><a href="#cb10-76" aria-hidden="true" tabindex="-1"></a>                            <span class="dt">duration</span><span class="op">:</span> <span class="dv">5000</span></span>
<span id="cb10-77"><a href="#cb10-77" aria-hidden="true" tabindex="-1"></a>                        }</span>
<span id="cb10-78"><a href="#cb10-78" aria-hidden="true" tabindex="-1"></a>                    }</span>
<span id="cb10-79"><a href="#cb10-79" aria-hidden="true" tabindex="-1"></a>                ]</span>
<span id="cb10-80"><a href="#cb10-80" aria-hidden="true" tabindex="-1"></a>            }</span>
<span id="cb10-81"><a href="#cb10-81" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb10-82"><a href="#cb10-82" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-83"><a href="#cb10-83" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-84"><a href="#cb10-84" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 2D UI 叠加层</span></span>
<span id="cb10-85"><a href="#cb10-85" aria-hidden="true" tabindex="-1"></a>    <span class="ot">Rectangle</span> {</span>
<span id="cb10-86"><a href="#cb10-86" aria-hidden="true" tabindex="-1"></a>        <span class="dt">id</span><span class="op">:</span> uiPanel</span>
<span id="cb10-87"><a href="#cb10-87" aria-hidden="true" tabindex="-1"></a>        <span class="dt">width</span><span class="op">:</span> <span class="dv">300</span></span>
<span id="cb10-88"><a href="#cb10-88" aria-hidden="true" tabindex="-1"></a>        <span class="dt">height</span><span class="op">:</span> <span class="dv">200</span></span>
<span id="cb10-89"><a href="#cb10-89" aria-hidden="true" tabindex="-1"></a>        <span class="dt">x</span><span class="op">:</span> <span class="dv">20</span></span>
<span id="cb10-90"><a href="#cb10-90" aria-hidden="true" tabindex="-1"></a>        <span class="dt">y</span><span class="op">:</span> <span class="dv">20</span></span>
<span id="cb10-91"><a href="#cb10-91" aria-hidden="true" tabindex="-1"></a>        <span class="dt">color</span><span class="op">:</span> <span class="ex">Qt</span><span class="op">.</span><span class="fu">rgba</span>(<span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="fl">0.7</span>)</span>
<span id="cb10-92"><a href="#cb10-92" aria-hidden="true" tabindex="-1"></a>        <span class="dt">radius</span><span class="op">:</span> <span class="dv">10</span></span>
<span id="cb10-93"><a href="#cb10-93" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-94"><a href="#cb10-94" aria-hidden="true" tabindex="-1"></a>        <span class="ot">Column</span> {</span>
<span id="cb10-95"><a href="#cb10-95" aria-hidden="true" tabindex="-1"></a>            anchors<span class="op">.</span><span class="at">centerIn</span><span class="op">:</span> parent</span>
<span id="cb10-96"><a href="#cb10-96" aria-hidden="true" tabindex="-1"></a>            <span class="dt">spacing</span><span class="op">:</span> <span class="dv">10</span></span>
<span id="cb10-97"><a href="#cb10-97" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-98"><a href="#cb10-98" aria-hidden="true" tabindex="-1"></a>            <span class="ot">Text</span> {</span>
<span id="cb10-99"><a href="#cb10-99" aria-hidden="true" tabindex="-1"></a>                <span class="dt">text</span><span class="op">:</span> <span class="st">&quot;3D 场景控制&quot;</span></span>
<span id="cb10-100"><a href="#cb10-100" aria-hidden="true" tabindex="-1"></a>                <span class="dt">color</span><span class="op">:</span> <span class="st">&quot;white&quot;</span></span>
<span id="cb10-101"><a href="#cb10-101" aria-hidden="true" tabindex="-1"></a>                font<span class="op">.</span><span class="at">pixelSize</span><span class="op">:</span> <span class="dv">18</span></span>
<span id="cb10-102"><a href="#cb10-102" aria-hidden="true" tabindex="-1"></a>                font<span class="op">.</span><span class="at">bold</span><span class="op">:</span> <span class="kw">true</span></span>
<span id="cb10-103"><a href="#cb10-103" aria-hidden="true" tabindex="-1"></a>            }</span>
<span id="cb10-104"><a href="#cb10-104" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-105"><a href="#cb10-105" aria-hidden="true" tabindex="-1"></a>            Slider {</span>
<span id="cb10-106"><a href="#cb10-106" aria-hidden="true" tabindex="-1"></a>                <span class="dt">id</span><span class="op">:</span> rotationSpeedSlider</span>
<span id="cb10-107"><a href="#cb10-107" aria-hidden="true" tabindex="-1"></a>                <span class="dt">from</span><span class="op">:</span> <span class="dv">0</span></span>
<span id="cb10-108"><a href="#cb10-108" aria-hidden="true" tabindex="-1"></a>                <span class="dt">to</span><span class="op">:</span> <span class="dv">10</span></span>
<span id="cb10-109"><a href="#cb10-109" aria-hidden="true" tabindex="-1"></a>                <span class="dt">value</span><span class="op">:</span> <span class="dv">5</span></span>
<span id="cb10-110"><a href="#cb10-110" aria-hidden="true" tabindex="-1"></a>                <span class="dt">onValueChanged</span><span class="op">:</span> {</span>
<span id="cb10-111"><a href="#cb10-111" aria-hidden="true" tabindex="-1"></a>                    <span class="co">// 控制 3D 动画速度</span></span>
<span id="cb10-112"><a href="#cb10-112" aria-hidden="true" tabindex="-1"></a>                    cubeTransform<span class="op">.</span><span class="at">rotationY</span> <span class="op">=</span> value <span class="op">*</span> <span class="dv">36</span></span>
<span id="cb10-113"><a href="#cb10-113" aria-hidden="true" tabindex="-1"></a>                }</span>
<span id="cb10-114"><a href="#cb10-114" aria-hidden="true" tabindex="-1"></a>            }</span>
<span id="cb10-115"><a href="#cb10-115" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-116"><a href="#cb10-116" aria-hidden="true" tabindex="-1"></a>            <span class="ot">Text</span> {</span>
<span id="cb10-117"><a href="#cb10-117" aria-hidden="true" tabindex="-1"></a>                <span class="dt">text</span><span class="op">:</span> <span class="st">&quot;旋转速度: &quot;</span> <span class="op">+</span> rotationSpeedSlider<span class="op">.</span><span class="at">value</span><span class="op">.</span><span class="fu">toFixed</span>(<span class="dv">1</span>)</span>
<span id="cb10-118"><a href="#cb10-118" aria-hidden="true" tabindex="-1"></a>                <span class="dt">color</span><span class="op">:</span> <span class="st">&quot;white&quot;</span></span>
<span id="cb10-119"><a href="#cb10-119" aria-hidden="true" tabindex="-1"></a>                font<span class="op">.</span><span class="at">pixelSize</span><span class="op">:</span> <span class="dv">14</span></span>
<span id="cb10-120"><a href="#cb10-120" aria-hidden="true" tabindex="-1"></a>            }</span>
<span id="cb10-121"><a href="#cb10-121" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb10-122"><a href="#cb10-122" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-123"><a href="#cb10-123" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-124"><a href="#cb10-124" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 2D 信息显示</span></span>
<span id="cb10-125"><a href="#cb10-125" aria-hidden="true" tabindex="-1"></a>    <span class="ot">Text</span> {</span>
<span id="cb10-126"><a href="#cb10-126" aria-hidden="true" tabindex="-1"></a>        anchors<span class="op">.</span><span class="at">bottom</span><span class="op">:</span> parent<span class="op">.</span><span class="at">bottom</span></span>
<span id="cb10-127"><a href="#cb10-127" aria-hidden="true" tabindex="-1"></a>        anchors<span class="op">.</span><span class="at">right</span><span class="op">:</span> parent<span class="op">.</span><span class="at">right</span></span>
<span id="cb10-128"><a href="#cb10-128" aria-hidden="true" tabindex="-1"></a>        anchors<span class="op">.</span><span class="at">margins</span><span class="op">:</span> <span class="dv">20</span></span>
<span id="cb10-129"><a href="#cb10-129" aria-hidden="true" tabindex="-1"></a>        <span class="dt">text</span><span class="op">:</span> <span class="st">&quot;2D/3D 融合演示&quot;</span></span>
<span id="cb10-130"><a href="#cb10-130" aria-hidden="true" tabindex="-1"></a>        <span class="dt">color</span><span class="op">:</span> <span class="st">&quot;darkblue&quot;</span></span>
<span id="cb10-131"><a href="#cb10-131" aria-hidden="true" tabindex="-1"></a>        font<span class="op">.</span><span class="at">pixelSize</span><span class="op">:</span> <span class="dv">16</span></span>
<span id="cb10-132"><a href="#cb10-132" aria-hidden="true" tabindex="-1"></a>        font<span class="op">.</span><span class="at">bold</span><span class="op">:</span> <span class="kw">true</span></span>
<span id="cb10-133"><a href="#cb10-133" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-134"><a href="#cb10-134" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<h3 id="d-纹理中的-2d-内容">2. 3D 纹理中的 2D 内容</h3>
<div class="sourceCode" id="cb11"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 创建 2D 内容作为 3D 纹理</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> Dynamic2DTexture <span class="op">:</span> <span class="kw">public</span> <span class="ex">Qt3DRender::QPaintedTextureImage</span></span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Q_OBJECT</span></span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb11-7"><a href="#cb11-7" aria-hidden="true" tabindex="-1"></a>    Dynamic2DTexture<span class="op">(</span><span class="ex">QNode</span> <span class="op">*</span>parent <span class="op">=</span> <span class="kw">nullptr</span><span class="op">)</span></span>
<span id="cb11-8"><a href="#cb11-8" aria-hidden="true" tabindex="-1"></a>        <span class="op">:</span> <span class="ex">Qt3DRender::QPaintedTextureImage</span><span class="op">(</span>parent<span class="op">)</span></span>
<span id="cb11-9"><a href="#cb11-9" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-10"><a href="#cb11-10" aria-hidden="true" tabindex="-1"></a>        setSize<span class="op">(</span><span class="ex">QSize</span><span class="op">(</span><span class="dv">512</span><span class="op">,</span> <span class="dv">512</span><span class="op">));</span></span>
<span id="cb11-11"><a href="#cb11-11" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-12"><a href="#cb11-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-13"><a href="#cb11-13" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb11-14"><a href="#cb11-14" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> paint<span class="op">(</span><span class="ex">QPainter</span> <span class="op">*</span>painter<span class="op">)</span> <span class="kw">override</span></span>
<span id="cb11-15"><a href="#cb11-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-16"><a href="#cb11-16" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 绘制 2D 内容</span></span>
<span id="cb11-17"><a href="#cb11-17" aria-hidden="true" tabindex="-1"></a>        painter<span class="op">-&gt;</span>fillRect<span class="op">(</span>rect<span class="op">(),</span> <span class="ex">Qt::white</span><span class="op">);</span></span>
<span id="cb11-18"><a href="#cb11-18" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-19"><a href="#cb11-19" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 绘制图表</span></span>
<span id="cb11-20"><a href="#cb11-20" aria-hidden="true" tabindex="-1"></a>        painter<span class="op">-&gt;</span>setPen<span class="op">(</span><span class="ex">QPen</span><span class="op">(</span><span class="ex">Qt::blue</span><span class="op">,</span> <span class="dv">3</span><span class="op">));</span></span>
<span id="cb11-21"><a href="#cb11-21" aria-hidden="true" tabindex="-1"></a>        painter<span class="op">-&gt;</span>drawLine<span class="op">(</span><span class="dv">50</span><span class="op">,</span> <span class="dv">400</span><span class="op">,</span> <span class="dv">450</span><span class="op">,</span> <span class="dv">100</span><span class="op">);</span></span>
<span id="cb11-22"><a href="#cb11-22" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-23"><a href="#cb11-23" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 绘制文字</span></span>
<span id="cb11-24"><a href="#cb11-24" aria-hidden="true" tabindex="-1"></a>        painter<span class="op">-&gt;</span>setPen<span class="op">(</span><span class="ex">Qt::black</span><span class="op">);</span></span>
<span id="cb11-25"><a href="#cb11-25" aria-hidden="true" tabindex="-1"></a>        painter<span class="op">-&gt;</span>setFont<span class="op">(</span><span class="ex">QFont</span><span class="op">(</span><span class="st">&quot;Arial&quot;</span><span class="op">,</span> <span class="dv">24</span><span class="op">));</span></span>
<span id="cb11-26"><a href="#cb11-26" aria-hidden="true" tabindex="-1"></a>        painter<span class="op">-&gt;</span>drawText<span class="op">(</span>rect<span class="op">(),</span> <span class="ex">Qt::AlignCenter</span><span class="op">,</span> <span class="st">&quot;动态 2D 内容&quot;</span><span class="op">);</span></span>
<span id="cb11-27"><a href="#cb11-27" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-28"><a href="#cb11-28" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 绘制实时数据</span></span>
<span id="cb11-29"><a href="#cb11-29" aria-hidden="true" tabindex="-1"></a>        painter<span class="op">-&gt;</span>setPen<span class="op">(</span><span class="ex">QPen</span><span class="op">(</span><span class="ex">Qt::red</span><span class="op">,</span> <span class="dv">2</span><span class="op">));</span></span>
<span id="cb11-30"><a href="#cb11-30" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="dt">int</span> i <span class="op">=</span> <span class="dv">0</span><span class="op">;</span> i <span class="op">&lt;</span> <span class="dv">10</span><span class="op">;</span> <span class="op">++</span>i<span class="op">)</span> <span class="op">{</span></span>
<span id="cb11-31"><a href="#cb11-31" aria-hidden="true" tabindex="-1"></a>            <span class="dt">int</span> x <span class="op">=</span> <span class="dv">50</span> <span class="op">+</span> i <span class="op">*</span> <span class="dv">40</span><span class="op">;</span></span>
<span id="cb11-32"><a href="#cb11-32" aria-hidden="true" tabindex="-1"></a>            <span class="dt">int</span> y <span class="op">=</span> <span class="dv">300</span> <span class="op">-</span> <span class="fu">qrand</span><span class="op">()</span> <span class="op">%</span> <span class="dv">200</span><span class="op">;</span></span>
<span id="cb11-33"><a href="#cb11-33" aria-hidden="true" tabindex="-1"></a>            painter<span class="op">-&gt;</span>drawEllipse<span class="op">(</span>x<span class="op">-</span><span class="dv">5</span><span class="op">,</span> y<span class="op">-</span><span class="dv">5</span><span class="op">,</span> <span class="dv">10</span><span class="op">,</span> <span class="dv">10</span><span class="op">);</span></span>
<span id="cb11-34"><a href="#cb11-34" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb11-35"><a href="#cb11-35" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-36"><a href="#cb11-36" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb11-37"><a href="#cb11-37" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-38"><a href="#cb11-38" aria-hidden="true" tabindex="-1"></a><span class="co">// 在 3D 场景中使用</span></span>
<span id="cb11-39"><a href="#cb11-39" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DCore::QEntity</span> <span class="op">*</span>planeEntity <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DCore::QEntity</span><span class="op">(</span>rootEntity<span class="op">);</span></span>
<span id="cb11-40"><a href="#cb11-40" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-41"><a href="#cb11-41" aria-hidden="true" tabindex="-1"></a><span class="co">// 平面几何体</span></span>
<span id="cb11-42"><a href="#cb11-42" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DExtras::QPlaneMesh</span> <span class="op">*</span>planeMesh <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DExtras::QPlaneMesh</span><span class="op">();</span></span>
<span id="cb11-43"><a href="#cb11-43" aria-hidden="true" tabindex="-1"></a>planeMesh<span class="op">-&gt;</span>setWidth<span class="op">(</span><span class="fl">10.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb11-44"><a href="#cb11-44" aria-hidden="true" tabindex="-1"></a>planeMesh<span class="op">-&gt;</span>setHeight<span class="op">(</span><span class="fl">10.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb11-45"><a href="#cb11-45" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-46"><a href="#cb11-46" aria-hidden="true" tabindex="-1"></a><span class="co">// 使用动态 2D 纹理的材质</span></span>
<span id="cb11-47"><a href="#cb11-47" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DExtras::QTextureMaterial</span> <span class="op">*</span>planeMaterial <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DExtras::QTextureMaterial</span><span class="op">();</span></span>
<span id="cb11-48"><a href="#cb11-48" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DRender::QTexture2D</span> <span class="op">*</span>texture <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QTexture2D</span><span class="op">();</span></span>
<span id="cb11-49"><a href="#cb11-49" aria-hidden="true" tabindex="-1"></a>texture<span class="op">-&gt;</span>addTextureImage<span class="op">(</span><span class="kw">new</span> Dynamic2DTexture<span class="op">());</span></span>
<span id="cb11-50"><a href="#cb11-50" aria-hidden="true" tabindex="-1"></a>planeMaterial<span class="op">-&gt;</span>setTexture<span class="op">(</span>texture<span class="op">);</span></span>
<span id="cb11-51"><a href="#cb11-51" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-52"><a href="#cb11-52" aria-hidden="true" tabindex="-1"></a><span class="co">// 变换</span></span>
<span id="cb11-53"><a href="#cb11-53" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DCore::QTransform</span> <span class="op">*</span>planeTransform <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DCore::QTransform</span><span class="op">();</span></span>
<span id="cb11-54"><a href="#cb11-54" aria-hidden="true" tabindex="-1"></a>planeTransform<span class="op">-&gt;</span>setRotationX<span class="op">(</span><span class="dv">90</span><span class="op">);</span></span>
<span id="cb11-55"><a href="#cb11-55" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-56"><a href="#cb11-56" aria-hidden="true" tabindex="-1"></a>planeEntity<span class="op">-&gt;</span>addComponent<span class="op">(</span>planeMesh<span class="op">);</span></span>
<span id="cb11-57"><a href="#cb11-57" aria-hidden="true" tabindex="-1"></a>planeEntity<span class="op">-&gt;</span>addComponent<span class="op">(</span>planeMaterial<span class="op">);</span></span>
<span id="cb11-58"><a href="#cb11-58" aria-hidden="true" tabindex="-1"></a>planeEntity<span class="op">-&gt;</span>addComponent<span class="op">(</span>planeTransform<span class="op">);</span></span></code></pre></div>
<h3 id="坐标系转换">3. 坐标系转换</h3>
<div class="sourceCode" id="cb12"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 3D 世界坐标到 2D 屏幕坐标</span></span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> CoordinateConverter <span class="op">:</span> <span class="kw">public</span> <span class="ex">QObject</span></span>
<span id="cb12-3"><a href="#cb12-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb12-4"><a href="#cb12-4" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Q_OBJECT</span></span>
<span id="cb12-5"><a href="#cb12-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-6"><a href="#cb12-6" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb12-7"><a href="#cb12-7" aria-hidden="true" tabindex="-1"></a>    <span class="ex">QPointF</span> worldToScreen<span class="op">(</span><span class="at">const</span> <span class="ex">QVector3D</span><span class="op">&amp;</span> worldPos<span class="op">,</span></span>
<span id="cb12-8"><a href="#cb12-8" aria-hidden="true" tabindex="-1"></a>                         <span class="ex">Qt3DRender::QCamera</span><span class="op">*</span> camera<span class="op">,</span></span>
<span id="cb12-9"><a href="#cb12-9" aria-hidden="true" tabindex="-1"></a>                         <span class="at">const</span> <span class="ex">QSize</span><span class="op">&amp;</span> viewportSize<span class="op">)</span></span>
<span id="cb12-10"><a href="#cb12-10" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-11"><a href="#cb12-11" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 获取视图矩阵和投影矩阵</span></span>
<span id="cb12-12"><a href="#cb12-12" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QMatrix4x4</span> viewMatrix <span class="op">=</span> camera<span class="op">-&gt;</span>viewMatrix<span class="op">();</span></span>
<span id="cb12-13"><a href="#cb12-13" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QMatrix4x4</span> projMatrix <span class="op">=</span> camera<span class="op">-&gt;</span>projectionMatrix<span class="op">();</span></span>
<span id="cb12-14"><a href="#cb12-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-15"><a href="#cb12-15" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 世界坐标转换到裁剪坐标</span></span>
<span id="cb12-16"><a href="#cb12-16" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QVector4D</span> clipPos <span class="op">=</span> projMatrix <span class="op">*</span> viewMatrix <span class="op">*</span> <span class="ex">QVector4D</span><span class="op">(</span>worldPos<span class="op">,</span> <span class="fl">1.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb12-17"><a href="#cb12-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-18"><a href="#cb12-18" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 透视除法</span></span>
<span id="cb12-19"><a href="#cb12-19" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>clipPos<span class="op">.</span>w<span class="op">()</span> <span class="op">!=</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">)</span> <span class="op">{</span></span>
<span id="cb12-20"><a href="#cb12-20" aria-hidden="true" tabindex="-1"></a>            clipPos <span class="op">/=</span> clipPos<span class="op">.</span>w<span class="op">();</span></span>
<span id="cb12-21"><a href="#cb12-21" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb12-22"><a href="#cb12-22" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-23"><a href="#cb12-23" aria-hidden="true" tabindex="-1"></a>        <span class="co">// NDC 坐标转换到屏幕坐标</span></span>
<span id="cb12-24"><a href="#cb12-24" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> screenX <span class="op">=</span> <span class="op">(</span>clipPos<span class="op">.</span>x<span class="op">()</span> <span class="op">+</span> <span class="fl">1.0</span><span class="bu">f</span><span class="op">)</span> <span class="op">*</span> <span class="fl">0.5</span><span class="bu">f</span> <span class="op">*</span> viewportSize<span class="op">.</span>width<span class="op">();</span></span>
<span id="cb12-25"><a href="#cb12-25" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> screenY <span class="op">=</span> <span class="op">(</span><span class="fl">1.0</span><span class="bu">f</span> <span class="op">-</span> clipPos<span class="op">.</span>y<span class="op">())</span> <span class="op">*</span> <span class="fl">0.5</span><span class="bu">f</span> <span class="op">*</span> viewportSize<span class="op">.</span>height<span class="op">();</span></span>
<span id="cb12-26"><a href="#cb12-26" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-27"><a href="#cb12-27" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="ex">QPointF</span><span class="op">(</span>screenX<span class="op">,</span> screenY<span class="op">);</span></span>
<span id="cb12-28"><a href="#cb12-28" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-29"><a href="#cb12-29" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-30"><a href="#cb12-30" aria-hidden="true" tabindex="-1"></a>    <span class="ex">QVector3D</span> screenToWorld<span class="op">(</span><span class="at">const</span> <span class="ex">QPointF</span><span class="op">&amp;</span> screenPos<span class="op">,</span></span>
<span id="cb12-31"><a href="#cb12-31" aria-hidden="true" tabindex="-1"></a>                           <span class="dt">float</span> depth<span class="op">,</span></span>
<span id="cb12-32"><a href="#cb12-32" aria-hidden="true" tabindex="-1"></a>                           <span class="ex">Qt3DRender::QCamera</span><span class="op">*</span> camera<span class="op">,</span></span>
<span id="cb12-33"><a href="#cb12-33" aria-hidden="true" tabindex="-1"></a>                           <span class="at">const</span> <span class="ex">QSize</span><span class="op">&amp;</span> viewportSize<span class="op">)</span></span>
<span id="cb12-34"><a href="#cb12-34" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-35"><a href="#cb12-35" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 屏幕坐标转换到 NDC</span></span>
<span id="cb12-36"><a href="#cb12-36" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> ndcX <span class="op">=</span> <span class="op">(</span><span class="fl">2.0</span><span class="bu">f</span> <span class="op">*</span> screenPos<span class="op">.</span>x<span class="op">())</span> <span class="op">/</span> viewportSize<span class="op">.</span>width<span class="op">()</span> <span class="op">-</span> <span class="fl">1.0</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb12-37"><a href="#cb12-37" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> ndcY <span class="op">=</span> <span class="fl">1.0</span><span class="bu">f</span> <span class="op">-</span> <span class="op">(</span><span class="fl">2.0</span><span class="bu">f</span> <span class="op">*</span> screenPos<span class="op">.</span>y<span class="op">())</span> <span class="op">/</span> viewportSize<span class="op">.</span>height<span class="op">();</span></span>
<span id="cb12-38"><a href="#cb12-38" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-39"><a href="#cb12-39" aria-hidden="true" tabindex="-1"></a>        <span class="co">// NDC 转换到世界坐标</span></span>
<span id="cb12-40"><a href="#cb12-40" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QMatrix4x4</span> viewMatrix <span class="op">=</span> camera<span class="op">-&gt;</span>viewMatrix<span class="op">();</span></span>
<span id="cb12-41"><a href="#cb12-41" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QMatrix4x4</span> projMatrix <span class="op">=</span> camera<span class="op">-&gt;</span>projectionMatrix<span class="op">();</span></span>
<span id="cb12-42"><a href="#cb12-42" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QMatrix4x4</span> invMatrix <span class="op">=</span> <span class="op">(</span>projMatrix <span class="op">*</span> viewMatrix<span class="op">).</span>inverted<span class="op">();</span></span>
<span id="cb12-43"><a href="#cb12-43" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-44"><a href="#cb12-44" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QVector4D</span> worldPos <span class="op">=</span> invMatrix <span class="op">*</span> <span class="ex">QVector4D</span><span class="op">(</span>ndcX<span class="op">,</span> ndcY<span class="op">,</span> depth<span class="op">,</span> <span class="fl">1.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb12-45"><a href="#cb12-45" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-46"><a href="#cb12-46" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>worldPos<span class="op">.</span>w<span class="op">()</span> <span class="op">!=</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">)</span> <span class="op">{</span></span>
<span id="cb12-47"><a href="#cb12-47" aria-hidden="true" tabindex="-1"></a>            worldPos <span class="op">/=</span> worldPos<span class="op">.</span>w<span class="op">();</span></span>
<span id="cb12-48"><a href="#cb12-48" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb12-49"><a href="#cb12-49" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-50"><a href="#cb12-50" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> worldPos<span class="op">.</span>toVector3D<span class="op">();</span></span>
<span id="cb12-51"><a href="#cb12-51" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-52"><a href="#cb12-52" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="d-ui-与-3d-场景交互">4. 2D UI 与 3D 场景交互</h3>
<div class="sourceCode" id="cb13"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 鼠标拾取 3D 对象</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> ObjectPicker <span class="op">:</span> <span class="kw">public</span> <span class="ex">QObject</span></span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb13-4"><a href="#cb13-4" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Q_OBJECT</span></span>
<span id="cb13-5"><a href="#cb13-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-6"><a href="#cb13-6" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="ex">slots</span><span class="op">:</span></span>
<span id="cb13-7"><a href="#cb13-7" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> handleMouseClick<span class="op">(</span><span class="at">const</span> <span class="ex">QPointF</span><span class="op">&amp;</span> position<span class="op">)</span></span>
<span id="cb13-8"><a href="#cb13-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-9"><a href="#cb13-9" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建射线</span></span>
<span id="cb13-10"><a href="#cb13-10" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QVector3D</span> rayOrigin <span class="op">=</span> camera<span class="op">-&gt;</span>position<span class="op">();</span></span>
<span id="cb13-11"><a href="#cb13-11" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QVector3D</span> rayDirection <span class="op">=</span> screenToWorldDirection<span class="op">(</span>position<span class="op">);</span></span>
<span id="cb13-12"><a href="#cb13-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-13"><a href="#cb13-13" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 检测与 3D 对象的交集</span></span>
<span id="cb13-14"><a href="#cb13-14" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="kw">auto</span><span class="op">*</span> entity <span class="op">:</span> scene3DEntities<span class="op">)</span> <span class="op">{</span></span>
<span id="cb13-15"><a href="#cb13-15" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> <span class="op">(</span>rayIntersectsEntity<span class="op">(</span>rayOrigin<span class="op">,</span> rayDirection<span class="op">,</span> entity<span class="op">))</span> <span class="op">{</span></span>
<span id="cb13-16"><a href="#cb13-16" aria-hidden="true" tabindex="-1"></a>                <span class="co">// 显示 2D 信息面板</span></span>
<span id="cb13-17"><a href="#cb13-17" aria-hidden="true" tabindex="-1"></a>                showInfoPanel<span class="op">(</span>entity<span class="op">,</span> position<span class="op">);</span></span>
<span id="cb13-18"><a href="#cb13-18" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb13-19"><a href="#cb13-19" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb13-20"><a href="#cb13-20" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb13-21"><a href="#cb13-21" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-22"><a href="#cb13-22" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-23"><a href="#cb13-23" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb13-24"><a href="#cb13-24" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> showInfoPanel<span class="op">(</span><span class="ex">Qt3DCore::QEntity</span><span class="op">*</span> entity<span class="op">,</span> <span class="at">const</span> <span class="ex">QPointF</span><span class="op">&amp;</span> screenPos<span class="op">)</span></span>
<span id="cb13-25"><a href="#cb13-25" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-26"><a href="#cb13-26" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建 2D 信息面板</span></span>
<span id="cb13-27"><a href="#cb13-27" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QQuickItem</span><span class="op">*</span> infoPanel <span class="op">=</span> <span class="fu">qmlEngine</span><span class="op">-&gt;</span>create<span class="op">(</span><span class="st">&quot;InfoPanel.qml&quot;</span><span class="op">);</span></span>
<span id="cb13-28"><a href="#cb13-28" aria-hidden="true" tabindex="-1"></a>        infoPanel<span class="op">-&gt;</span>setProperty<span class="op">(</span><span class="st">&quot;targetEntity&quot;</span><span class="op">,</span> <span class="ex">QVariant::fromValue</span><span class="op">(</span>entity<span class="op">));</span></span>
<span id="cb13-29"><a href="#cb13-29" aria-hidden="true" tabindex="-1"></a>        infoPanel<span class="op">-&gt;</span>setProperty<span class="op">(</span><span class="st">&quot;x&quot;</span><span class="op">,</span> screenPos<span class="op">.</span>x<span class="op">());</span></span>
<span id="cb13-30"><a href="#cb13-30" aria-hidden="true" tabindex="-1"></a>        infoPanel<span class="op">-&gt;</span>setProperty<span class="op">(</span><span class="st">&quot;y&quot;</span><span class="op">,</span> screenPos<span class="op">.</span>y<span class="op">());</span></span>
<span id="cb13-31"><a href="#cb13-31" aria-hidden="true" tabindex="-1"></a>        infoPanel<span class="op">-&gt;</span>setParent<span class="op">(</span>rootItem<span class="op">);</span></span>
<span id="cb13-32"><a href="#cb13-32" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-33"><a href="#cb13-33" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-34"><a href="#cb13-34" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> rayIntersectsEntity<span class="op">(</span><span class="at">const</span> <span class="ex">QVector3D</span><span class="op">&amp;</span> rayOrigin<span class="op">,</span></span>
<span id="cb13-35"><a href="#cb13-35" aria-hidden="true" tabindex="-1"></a>                           <span class="at">const</span> <span class="ex">QVector3D</span><span class="op">&amp;</span> rayDirection<span class="op">,</span></span>
<span id="cb13-36"><a href="#cb13-36" aria-hidden="true" tabindex="-1"></a>                           <span class="ex">Qt3DCore::QEntity</span><span class="op">*</span> entity<span class="op">)</span></span>
<span id="cb13-37"><a href="#cb13-37" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-38"><a href="#cb13-38" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 简化的包围盒检测</span></span>
<span id="cb13-39"><a href="#cb13-39" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 实际应用中需要更精确的碰撞检测</span></span>
<span id="cb13-40"><a href="#cb13-40" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">true</span><span class="op">;</span> <span class="co">// 占位符</span></span>
<span id="cb13-41"><a href="#cb13-41" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-42"><a href="#cb13-42" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="最佳实践">最佳实践</h2>
<h3 id="性能优化原则">1. 性能优化原则</h3>
<ul>
<li><strong>批量渲染</strong>：合并相似的几何体以减少绘制调用</li>
<li><strong>纹理优化</strong>：使用纹理图集和压缩纹理格式</li>
<li><strong>LOD 管理</strong>：根据距离动态调整模型细节</li>
<li><strong>视锥剔除</strong>：只渲染可见的对象</li>
<li><strong>内存管理</strong>：及时释放不需要的资源</li>
</ul>
<h3 id="代码组织">2. 代码组织</h3>
<div class="sourceCode" id="cb14"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb14-1"><a href="#cb14-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 推荐的项目结构</span></span>
<span id="cb14-2"><a href="#cb14-2" aria-hidden="true" tabindex="-1"></a>src<span class="op">/</span></span>
<span id="cb14-3"><a href="#cb14-3" aria-hidden="true" tabindex="-1"></a>├── main<span class="op">.</span>cpp</span>
<span id="cb14-4"><a href="#cb14-4" aria-hidden="true" tabindex="-1"></a>├── scene<span class="op">/</span></span>
<span id="cb14-5"><a href="#cb14-5" aria-hidden="true" tabindex="-1"></a>│   ├── SceneManager<span class="op">.</span>h<span class="op">/</span>cpp</span>
<span id="cb14-6"><a href="#cb14-6" aria-hidden="true" tabindex="-1"></a>│   ├── Entity<span class="op">.</span>h<span class="op">/</span>cpp</span>
<span id="cb14-7"><a href="#cb14-7" aria-hidden="true" tabindex="-1"></a>│   └── Component<span class="op">.</span>h<span class="op">/</span>cpp</span>
<span id="cb14-8"><a href="#cb14-8" aria-hidden="true" tabindex="-1"></a>├── render<span class="op">/</span></span>
<span id="cb14-9"><a href="#cb14-9" aria-hidden="true" tabindex="-1"></a>│   ├── Renderer<span class="op">.</span>h<span class="op">/</span>cpp</span>
<span id="cb14-10"><a href="#cb14-10" aria-hidden="true" tabindex="-1"></a>│   ├── Material<span class="op">.</span>h<span class="op">/</span>cpp</span>
<span id="cb14-11"><a href="#cb14-11" aria-hidden="true" tabindex="-1"></a>│   └── Shader<span class="op">.</span>h<span class="op">/</span>cpp</span>
<span id="cb14-12"><a href="#cb14-12" aria-hidden="true" tabindex="-1"></a>├── assets<span class="op">/</span></span>
<span id="cb14-13"><a href="#cb14-13" aria-hidden="true" tabindex="-1"></a>│   ├── models<span class="op">/</span></span>
<span id="cb14-14"><a href="#cb14-14" aria-hidden="true" tabindex="-1"></a>│   ├── textures<span class="op">/</span></span>
<span id="cb14-15"><a href="#cb14-15" aria-hidden="true" tabindex="-1"></a>│   └── shaders<span class="op">/</span></span>
<span id="cb14-16"><a href="#cb14-16" aria-hidden="true" tabindex="-1"></a>└── utils<span class="op">/</span></span>
<span id="cb14-17"><a href="#cb14-17" aria-hidden="true" tabindex="-1"></a>    ├── MathUtils<span class="op">.</span>h<span class="op">/</span>cpp</span>
<span id="cb14-18"><a href="#cb14-18" aria-hidden="true" tabindex="-1"></a>    └── ResourceManager<span class="op">.</span>h<span class="op">/</span>cpp</span></code></pre></div>
<h3 id="资源管理">3. 资源管理</h3>
<div class="sourceCode" id="cb15"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb15-1"><a href="#cb15-1" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> ResourceManager <span class="op">{</span></span>
<span id="cb15-2"><a href="#cb15-2" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb15-3"><a href="#cb15-3" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> ResourceManager<span class="op">&amp;</span> instance<span class="op">()</span> <span class="op">{</span></span>
<span id="cb15-4"><a href="#cb15-4" aria-hidden="true" tabindex="-1"></a>        <span class="at">static</span> ResourceManager instance<span class="op">;</span></span>
<span id="cb15-5"><a href="#cb15-5" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> instance<span class="op">;</span></span>
<span id="cb15-6"><a href="#cb15-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb15-7"><a href="#cb15-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-8"><a href="#cb15-8" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Qt3DRender::QMesh</span><span class="op">*</span> loadMesh<span class="op">(</span><span class="at">const</span> <span class="ex">QString</span><span class="op">&amp;</span> path<span class="op">)</span> <span class="op">{</span></span>
<span id="cb15-9"><a href="#cb15-9" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span><span class="va">m_meshCache</span><span class="op">.</span>contains<span class="op">(</span>path<span class="op">))</span> <span class="op">{</span></span>
<span id="cb15-10"><a href="#cb15-10" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> <span class="va">m_meshCache</span><span class="op">[</span>path<span class="op">];</span></span>
<span id="cb15-11"><a href="#cb15-11" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb15-12"><a href="#cb15-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-13"><a href="#cb15-13" aria-hidden="true" tabindex="-1"></a>        <span class="ex">Qt3DRender::QMesh</span><span class="op">*</span> mesh <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QMesh</span><span class="op">();</span></span>
<span id="cb15-14"><a href="#cb15-14" aria-hidden="true" tabindex="-1"></a>        mesh<span class="op">-&gt;</span>setSource<span class="op">(</span><span class="ex">QUrl::fromLocalFile</span><span class="op">(</span>path<span class="op">));</span></span>
<span id="cb15-15"><a href="#cb15-15" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_meshCache</span><span class="op">[</span>path<span class="op">]</span> <span class="op">=</span> mesh<span class="op">;</span></span>
<span id="cb15-16"><a href="#cb15-16" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> mesh<span class="op">;</span></span>
<span id="cb15-17"><a href="#cb15-17" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb15-18"><a href="#cb15-18" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-19"><a href="#cb15-19" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb15-20"><a href="#cb15-20" aria-hidden="true" tabindex="-1"></a>    <span class="ex">QHash</span><span class="op">&lt;</span><span class="ex">QString</span><span class="op">,</span> <span class="ex">Qt3DRender::QMesh</span><span class="op">*&gt;</span> <span class="va">m_meshCache</span><span class="op">;</span></span>
<span id="cb15-21"><a href="#cb15-21" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="错误处理">4. 错误处理</h3>
<div class="sourceCode" id="cb16"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb16-1"><a href="#cb16-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 检查 OpenGL 错误</span></span>
<span id="cb16-2"><a href="#cb16-2" aria-hidden="true" tabindex="-1"></a><span class="dt">void</span> checkGLError<span class="op">(</span><span class="at">const</span> <span class="ex">QString</span><span class="op">&amp;</span> operation<span class="op">)</span> <span class="op">{</span></span>
<span id="cb16-3"><a href="#cb16-3" aria-hidden="true" tabindex="-1"></a>    GLenum error <span class="op">=</span> glGetError<span class="op">();</span></span>
<span id="cb16-4"><a href="#cb16-4" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(</span>error <span class="op">!=</span> GL_NO_ERROR<span class="op">)</span> <span class="op">{</span></span>
<span id="cb16-5"><a href="#cb16-5" aria-hidden="true" tabindex="-1"></a>        <span class="fu">qWarning</span><span class="op">()</span> <span class="op">&lt;&lt;</span> <span class="st">&quot;OpenGL error in&quot;</span> <span class="op">&lt;&lt;</span> operation <span class="op">&lt;&lt;</span> <span class="st">&quot;:&quot;</span> <span class="op">&lt;&lt;</span> error<span class="op">;</span></span>
<span id="cb16-6"><a href="#cb16-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb16-7"><a href="#cb16-7" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb16-8"><a href="#cb16-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb16-9"><a href="#cb16-9" aria-hidden="true" tabindex="-1"></a><span class="co">// 资源加载错误处理</span></span>
<span id="cb16-10"><a href="#cb16-10" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DRender::QMesh</span><span class="op">*</span> loadMeshSafely<span class="op">(</span><span class="at">const</span> <span class="ex">QString</span><span class="op">&amp;</span> path<span class="op">)</span> <span class="op">{</span></span>
<span id="cb16-11"><a href="#cb16-11" aria-hidden="true" tabindex="-1"></a>    <span class="ex">QFileInfo</span> fileInfo<span class="op">(</span>path<span class="op">);</span></span>
<span id="cb16-12"><a href="#cb16-12" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">(!</span>fileInfo<span class="op">.</span>exists<span class="op">())</span> <span class="op">{</span></span>
<span id="cb16-13"><a href="#cb16-13" aria-hidden="true" tabindex="-1"></a>        <span class="fu">qWarning</span><span class="op">()</span> <span class="op">&lt;&lt;</span> <span class="st">&quot;Mesh file not found:&quot;</span> <span class="op">&lt;&lt;</span> path<span class="op">;</span></span>
<span id="cb16-14"><a href="#cb16-14" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">nullptr</span><span class="op">;</span></span>
<span id="cb16-15"><a href="#cb16-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb16-16"><a href="#cb16-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb16-17"><a href="#cb16-17" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Qt3DRender::QMesh</span><span class="op">*</span> mesh <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QMesh</span><span class="op">();</span></span>
<span id="cb16-18"><a href="#cb16-18" aria-hidden="true" tabindex="-1"></a>    mesh<span class="op">-&gt;</span>setSource<span class="op">(</span><span class="ex">QUrl::fromLocalFile</span><span class="op">(</span>path<span class="op">));</span></span>
<span id="cb16-19"><a href="#cb16-19" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> mesh<span class="op">;</span></span>
<span id="cb16-20"><a href="#cb16-20" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="性能优化">性能优化</h2>
<h3 id="渲染优化">1. 渲染优化</h3>
<ul>
<li><strong>实例化渲染</strong>：对相同几何体使用实例化渲染</li>
<li><strong>纹理压缩</strong>：使用 DXT、ETC、ASTC 等压缩格式</li>
<li><strong>Mipmap
生成</strong>：自动生成多级纹理以提高远距离渲染质量</li>
<li><strong>深度预通道</strong>：使用深度预通道减少过度绘制</li>
</ul>
<h3 id="内存优化">2. 内存优化</h3>
<ul>
<li><strong>对象池</strong>：重用频繁创建销毁的对象</li>
<li><strong>智能指针</strong>：使用 Qt 的智能指针管理内存</li>
<li><strong>资源流式加载</strong>：按需加载大型资源</li>
</ul>
<h3 id="cpu-优化">3. CPU 优化</h3>
<ul>
<li><strong>多线程</strong>：利用 Qt 的多线程能力进行并行计算</li>
<li><strong>空间分割</strong>：使用八叉树等数据结构优化碰撞检测</li>
<li><strong>缓存友好</strong>：优化数据结构以提高缓存命中率</li>
</ul>
<h2 id="调试与测试">调试与测试</h2>
<h3 id="调试工具">1. 调试工具</h3>
<div class="sourceCode" id="cb17"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb17-1"><a href="#cb17-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 启用 Qt 3D 调试输出</span></span>
<span id="cb17-2"><a href="#cb17-2" aria-hidden="true" tabindex="-1"></a><span class="ex">QLoggingCategory::setFilterRules</span><span class="op">(</span><span class="st">&quot;Qt3D.*.debug=true&quot;</span><span class="op">);</span></span>
<span id="cb17-3"><a href="#cb17-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-4"><a href="#cb17-4" aria-hidden="true" tabindex="-1"></a><span class="co">// 性能分析</span></span>
<span id="cb17-5"><a href="#cb17-5" aria-hidden="true" tabindex="-1"></a><span class="ex">Qt3DRender::QRenderSettings</span> <span class="op">*</span>renderSettings <span class="op">=</span> <span class="kw">new</span> <span class="ex">Qt3DRender::QRenderSettings</span><span class="op">();</span></span>
<span id="cb17-6"><a href="#cb17-6" aria-hidden="true" tabindex="-1"></a>renderSettings<span class="op">-&gt;</span>setRenderPolicy<span class="op">(</span><span class="ex">Qt3DRender::QRenderSettings::OnDemand</span><span class="op">);</span></span></code></pre></div>
<h3 id="单元测试">2. 单元测试</h3>
<div class="sourceCode" id="cb18"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb18-1"><a href="#cb18-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 测试几何体创建</span></span>
<span id="cb18-2"><a href="#cb18-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> GeometryTest <span class="op">:</span> <span class="kw">public</span> <span class="ex">QObject</span> <span class="op">{</span></span>
<span id="cb18-3"><a href="#cb18-3" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Q_OBJECT</span></span>
<span id="cb18-4"><a href="#cb18-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb18-5"><a href="#cb18-5" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span> <span class="ex">slots</span><span class="op">:</span></span>
<span id="cb18-6"><a href="#cb18-6" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> testCubeCreation<span class="op">()</span> <span class="op">{</span></span>
<span id="cb18-7"><a href="#cb18-7" aria-hidden="true" tabindex="-1"></a>        <span class="ex">Qt3DExtras::QCuboidMesh</span> cube<span class="op">;</span></span>
<span id="cb18-8"><a href="#cb18-8" aria-hidden="true" tabindex="-1"></a>        cube<span class="op">.</span>setXExtent<span class="op">(</span><span class="fl">2.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb18-9"><a href="#cb18-9" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QCOMPARE</span><span class="op">(</span>cube<span class="op">.</span>xExtent<span class="op">(),</span> <span class="fl">2.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb18-10"><a href="#cb18-10" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb18-11"><a href="#cb18-11" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb18-12"><a href="#cb18-12" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> testTransform<span class="op">()</span> <span class="op">{</span></span>
<span id="cb18-13"><a href="#cb18-13" aria-hidden="true" tabindex="-1"></a>        <span class="ex">Qt3DCore::QTransform</span> transform<span class="op">;</span></span>
<span id="cb18-14"><a href="#cb18-14" aria-hidden="true" tabindex="-1"></a>        transform<span class="op">.</span>setTranslation<span class="op">(</span><span class="ex">QVector3D</span><span class="op">(</span><span class="dv">1</span><span class="op">,</span> <span class="dv">2</span><span class="op">,</span> <span class="dv">3</span><span class="op">));</span></span>
<span id="cb18-15"><a href="#cb18-15" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QCOMPARE</span><span class="op">(</span>transform<span class="op">.</span>translation<span class="op">(),</span> <span class="ex">QVector3D</span><span class="op">(</span><span class="dv">1</span><span class="op">,</span> <span class="dv">2</span><span class="op">,</span> <span class="dv">3</span><span class="op">));</span></span>
<span id="cb18-16"><a href="#cb18-16" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb18-17"><a href="#cb18-17" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="性能测试">3. 性能测试</h3>
<div class="sourceCode" id="cb19"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb19-1"><a href="#cb19-1" aria-hidden="true" tabindex="-1"></a><span class="co">// FPS 计算</span></span>
<span id="cb19-2"><a href="#cb19-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> FPSCounter <span class="op">:</span> <span class="kw">public</span> <span class="ex">QObject</span> <span class="op">{</span></span>
<span id="cb19-3"><a href="#cb19-3" aria-hidden="true" tabindex="-1"></a>    <span class="ex">Q_OBJECT</span></span>
<span id="cb19-4"><a href="#cb19-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-5"><a href="#cb19-5" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="ex">slots</span><span class="op">:</span></span>
<span id="cb19-6"><a href="#cb19-6" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> update<span class="op">()</span> <span class="op">{</span></span>
<span id="cb19-7"><a href="#cb19-7" aria-hidden="true" tabindex="-1"></a>        <span class="at">static</span> <span class="dt">int</span> frameCount <span class="op">=</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb19-8"><a href="#cb19-8" aria-hidden="true" tabindex="-1"></a>        <span class="at">static</span> <span class="ex">QTime</span> lastTime <span class="op">=</span> <span class="ex">QTime::currentTime</span><span class="op">();</span></span>
<span id="cb19-9"><a href="#cb19-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-10"><a href="#cb19-10" aria-hidden="true" tabindex="-1"></a>        frameCount<span class="op">++;</span></span>
<span id="cb19-11"><a href="#cb19-11" aria-hidden="true" tabindex="-1"></a>        <span class="ex">QTime</span> currentTime <span class="op">=</span> <span class="ex">QTime::currentTime</span><span class="op">();</span></span>
<span id="cb19-12"><a href="#cb19-12" aria-hidden="true" tabindex="-1"></a>        <span class="dt">int</span> elapsed <span class="op">=</span> lastTime<span class="op">.</span>msecsTo<span class="op">(</span>currentTime<span class="op">);</span></span>
<span id="cb19-13"><a href="#cb19-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-14"><a href="#cb19-14" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>elapsed <span class="op">&gt;=</span> <span class="dv">1000</span><span class="op">)</span> <span class="op">{</span></span>
<span id="cb19-15"><a href="#cb19-15" aria-hidden="true" tabindex="-1"></a>            <span class="dt">float</span> fps <span class="op">=</span> frameCount <span class="op">*</span> <span class="fl">1000.0</span><span class="bu">f</span> <span class="op">/</span> elapsed<span class="op">;</span></span>
<span id="cb19-16"><a href="#cb19-16" aria-hidden="true" tabindex="-1"></a>            <span class="fu">qDebug</span><span class="op">()</span> <span class="op">&lt;&lt;</span> <span class="st">&quot;FPS:&quot;</span> <span class="op">&lt;&lt;</span> fps<span class="op">;</span></span>
<span id="cb19-17"><a href="#cb19-17" aria-hidden="true" tabindex="-1"></a>            frameCount <span class="op">=</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb19-18"><a href="#cb19-18" aria-hidden="true" tabindex="-1"></a>            lastTime <span class="op">=</span> currentTime<span class="op">;</span></span>
<span id="cb19-19"><a href="#cb19-19" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb19-20"><a href="#cb19-20" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb19-21"><a href="#cb19-21" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<hr />
<p><em>本文档持续更新中，更多详细信息请参考 <a
href="https://doc.qt.io/qt-6/qt3d-index.html">Qt 3D
官方文档</a></em></p>
</div><div lang="en" style="display: none;"><h1>[English translation pending]</h1><p>The content for this page has not been translated yet. Please check back later.</p></div>
                </main>
            </div>
        </section>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">Qt 3D 开发指南</h4>
                    <p data-i18n="footer-subtitle">专业的跨平台 3D 应用开发解决方案</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.</p>
            </div>
        </div>
    </footer>

    <script src="../../assets/js/prism.js"></script>
    <script src="../../assets/js/docs.js"></script>
</body>
</html> 