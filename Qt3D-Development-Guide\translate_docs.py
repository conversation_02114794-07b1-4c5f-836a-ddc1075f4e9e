import os
import re
import json
import time
import urllib.request
import urllib.parse
from pathlib import Path
from typing import List, Tuple

class MarkdownTranslator:
    def __init__(self):
        self.base_url = "https://translate.googleapis.com/translate_a/single"
        
    def split_markdown(self, content: str) -> List[Tuple[str, bool]]:
        """
        将 Markdown 内容分割成可翻译和不可翻译的部分
        返回一个列表，每个元素是 (文本, 是否可翻译) 的元组
        """
        # 定义不需要翻译的模式
        patterns = [
            r'```[\s\S]*?```',  # 代码块
            r'`[^`]+`',         # 内联代码
            r'!\[.*?\]\(.*?\)', # 图片
            r'\[.*?\]\(.*?\)',  # 链接
            r'---[\s\S]*?---'   # Front matter
        ]
        
        # 将所有模式合并成一个正则表达式
        combined_pattern = '|'.join(f'({p})' for p in patterns)
        
        # 分割文本
        parts = []
        last_end = 0
        
        for match in re.finditer(combined_pattern, content):
            start, end = match.span()
            if start > last_end:
                # 添加可翻译的文本
                parts.append((content[last_end:start], True))
            # 添加不可翻译的文本
            parts.append((content[start:end], False))
            last_end = end
            
        if last_end < len(content):
            parts.append((content[last_end:], True))
            
        return parts

    def translate_text(self, text: str) -> str:
        """
        使用 Google Translate API 翻译文本
        """
        try:
            # 确保文本不为空且包含可翻译的内容
            if not text.strip():
                return text

            # 构建请求参数
            params = {
                'client': 'gtx',
                'sl': 'zh-CN',
                'tl': 'en',
                'dt': 't',
                'q': text
            }
            
            # 构建 URL
            url = f"{self.base_url}?{urllib.parse.urlencode(params)}"
            
            # 添加请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            # 发送请求
            request = urllib.request.Request(url, headers=headers)
            with urllib.request.urlopen(request) as response:
                data = response.read()
                
            # 解析响应
            result = json.loads(data.decode())
            translated_text = ''.join(x[0] for x in result[0] if x[0])
            
            return translated_text
                
        except Exception as e:
            print(f"翻译出错: {str(e)}")
            return text

    def translate_markdown(self, content: str) -> str:
        """
        翻译 Markdown 文档，保持格式不变
        """
        parts = self.split_markdown(content)
        translated_parts = []
        
        for text, should_translate in parts:
            if should_translate and text.strip():
                # 将长文本分成较小的块进行翻译
                max_length = 1000
                if len(text) > max_length:
                    # 按句子分割
                    sentences = text.split('。')
                    translated_sentences = []
                    current_part = ''
                    
                    for sentence in sentences:
                        if len(current_part) + len(sentence) < max_length:
                            current_part += sentence + '。'
                        else:
                            # 翻译当前部分
                            if current_part:
                                translated_sentences.append(self.translate_text(current_part))
                            current_part = sentence + '。'
                            time.sleep(1)  # 添加延迟以避免请求过快
                    
                    # 翻译最后一部分
                    if current_part:
                        translated_sentences.append(self.translate_text(current_part))
                        
                    translated_text = ' '.join(translated_sentences)
                else:
                    translated_text = self.translate_text(text)
                    time.sleep(1)  # 添加延迟以避免请求过快
                
                translated_parts.append(translated_text)
            else:
                translated_parts.append(text)
                
        return ''.join(translated_parts)

def process_docs(test_file=None):
    translator = MarkdownTranslator()
    docs_dir = Path('docs')
    output_dir = Path('docs_en')
    
    # 创建输出目录
    output_dir.mkdir(exist_ok=True)
    
    # 如果指定了测试文件，只处理该文件
    if test_file:
        md_files = [docs_dir / test_file]
    else:
        md_files = list(docs_dir.glob('*.md'))
    
    # 处理文件
    for md_file in md_files:
        if not md_file.exists():
            print(f"文件不存在: {md_file}")
            continue
            
        print(f"正在处理 {md_file}...")
        
        try:
            # 读取源文件
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 翻译内容
            translated_content = translator.translate_markdown(content)
            
            # 保存翻译后的文件
            output_file = output_dir / md_file.name
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(translated_content)
                
            print(f"已完成翻译: {md_file.name} -> {output_file}")
            
        except Exception as e:
            print(f"处理文件 {md_file} 时出错: {str(e)}")
        
        # 添加延迟以避免 API 限制
        time.sleep(2)

if __name__ == '__main__':
    print("开始翻译文档...")
    
    # 测试模式：只翻译一个小文件
    test_file = 'internationalization.md'  # 选择一个相对较小的文件进行测试
    process_docs(test_file)
    
    print("翻译完成！") 