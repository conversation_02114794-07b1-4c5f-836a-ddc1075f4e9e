<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kanzi 国际化与本地化指南</title> <!-- The title will be replaced for each page -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/docs.css">
    <link rel="stylesheet" href="../../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html" class="logo-text" data-i18n="nav-logo-text">3D 引擎指南</a>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="../index.html#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="../index.html#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="../index.html#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 文档内容区域 -->
    <div class="doc-layout container">
        <!-- 左侧目录 -->
        <aside class="doc-sidebar card">
            <div class="toc-container">
                <!-- 目录内容将通过JavaScript动态生成 -->
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <section class="doc-section card">
            <div class="doc-container">
                <main class="doc-content">
                    <div lang="zh"><h1 id="kanzi-国际化与本地化指南">Kanzi 国际化与本地化指南</h1>
<h2 id="目录">目录</h2>
<ol type="1">
<li><a href="#概述">概述</a></li>
<li><a href="#kanzi-本地化架构">Kanzi 本地化架构</a></li>
<li><a href="#文本本地化">文本本地化</a></li>
<li><a href="#资源本地化">资源本地化</a></li>
<li><a href="#hmi-界面适配">HMI 界面适配</a></li>
<li><a href="#汽车行业特殊需求">汽车行业特殊需求</a></li>
<li><a href="#kanzi-studio-工具">Kanzi Studio 工具</a></li>
<li><a href="#最佳实践">最佳实践</a></li>
</ol>
<h2 id="概述">概述</h2>
<p>Kanzi 作为专业的汽车 HMI
开发平台，提供了完整的国际化和本地化解决方案，特别针对汽车行业的多语言需求进行了优化。Kanzi
的本地化系统支持实时语言切换、复杂的文本布局、多文化界面适配等功能，确保汽车
HMI 系统能够满足全球不同市场的需求。</p>
<h3 id="主要特性">主要特性</h3>
<ul>
<li><strong>实时语言切换</strong>：运行时无缝切换语言，无需重启系统</li>
<li><strong>多文化界面适配</strong>：支持从右到左文本、不同字体系统</li>
<li><strong>资源本地化</strong>：图标、图片、音频的多语言版本管理</li>
<li><strong>汽车标准兼容</strong>：符合汽车行业的安全和质量标准</li>
<li><strong>性能优化</strong>：针对嵌入式系统的内存和性能优化</li>
<li><strong>工具链集成</strong>：Kanzi Studio
提供完整的本地化工作流</li>
</ul>
<h2 id="kanzi-本地化架构">Kanzi 本地化架构</h2>
<h3 id="本地化数据管理">本地化数据管理</h3>
<div class="sourceCode" id="cb1"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Kanzi 本地化数据源</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="pp">#include </span><span class="im">&lt;kanzi/kanzi.hpp&gt;</span></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a><span class="pp">#include </span><span class="im">&lt;kanzi/core/resource/resource_manager.hpp&gt;</span></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> <span class="kw">namespace</span> kanzi<span class="op">;</span></span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-7"><a href="#cb1-7" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> LocalizationDataSource <span class="op">:</span> <span class="kw">public</span> DataObject</span>
<span id="cb1-8"><a href="#cb1-8" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb1-9"><a href="#cb1-9" aria-hidden="true" tabindex="-1"></a>    KZ_METACLASS_BEGIN<span class="op">(</span>LocalizationDataSource<span class="op">,</span> DataObject<span class="op">,</span> <span class="st">&quot;LocalizationDataSource&quot;</span><span class="op">)</span></span>
<span id="cb1-10"><a href="#cb1-10" aria-hidden="true" tabindex="-1"></a>        KZ_METACLASS_PROPERTY_TYPE<span class="op">(</span>CurrentLanguageProperty<span class="op">)</span></span>
<span id="cb1-11"><a href="#cb1-11" aria-hidden="true" tabindex="-1"></a>        KZ_METACLASS_PROPERTY_TYPE<span class="op">(</span>AvailableLanguagesProperty<span class="op">)</span></span>
<span id="cb1-12"><a href="#cb1-12" aria-hidden="true" tabindex="-1"></a>    KZ_METACLASS_END<span class="op">()</span></span>
<span id="cb1-13"><a href="#cb1-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-14"><a href="#cb1-14" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb1-15"><a href="#cb1-15" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> PropertyType<span class="op">&lt;</span>string<span class="op">&gt;</span> CurrentLanguageProperty<span class="op">;</span></span>
<span id="cb1-16"><a href="#cb1-16" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> PropertyType<span class="op">&lt;</span>StringVector<span class="op">&gt;</span> AvailableLanguagesProperty<span class="op">;</span></span>
<span id="cb1-17"><a href="#cb1-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-18"><a href="#cb1-18" aria-hidden="true" tabindex="-1"></a>    <span class="kw">explicit</span> LocalizationDataSource<span class="op">(</span>Domain<span class="op">*</span> domain<span class="op">,</span> string_view name<span class="op">)</span></span>
<span id="cb1-19"><a href="#cb1-19" aria-hidden="true" tabindex="-1"></a>        <span class="op">:</span> DataObject<span class="op">(</span>domain<span class="op">,</span> name<span class="op">)</span></span>
<span id="cb1-20"><a href="#cb1-20" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb1-21"><a href="#cb1-21" aria-hidden="true" tabindex="-1"></a>        initializeLanguages<span class="op">();</span></span>
<span id="cb1-22"><a href="#cb1-22" aria-hidden="true" tabindex="-1"></a>        loadCurrentLanguage<span class="op">();</span></span>
<span id="cb1-23"><a href="#cb1-23" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-24"><a href="#cb1-24" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-25"><a href="#cb1-25" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> setLanguage<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span></span>
<span id="cb1-26"><a href="#cb1-26" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb1-27"><a href="#cb1-27" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>isLanguageSupported<span class="op">(</span>languageCode<span class="op">))</span></span>
<span id="cb1-28"><a href="#cb1-28" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb1-29"><a href="#cb1-29" aria-hidden="true" tabindex="-1"></a>            setProperty<span class="op">(</span>CurrentLanguageProperty<span class="op">,</span> languageCode<span class="op">);</span></span>
<span id="cb1-30"><a href="#cb1-30" aria-hidden="true" tabindex="-1"></a>            loadLanguageResources<span class="op">(</span>languageCode<span class="op">);</span></span>
<span id="cb1-31"><a href="#cb1-31" aria-hidden="true" tabindex="-1"></a>            notifyLanguageChanged<span class="op">();</span></span>
<span id="cb1-32"><a href="#cb1-32" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb1-33"><a href="#cb1-33" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-34"><a href="#cb1-34" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-35"><a href="#cb1-35" aria-hidden="true" tabindex="-1"></a>    string getCurrentLanguage<span class="op">()</span> <span class="at">const</span></span>
<span id="cb1-36"><a href="#cb1-36" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb1-37"><a href="#cb1-37" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> getProperty<span class="op">(</span>CurrentLanguageProperty<span class="op">);</span></span>
<span id="cb1-38"><a href="#cb1-38" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-39"><a href="#cb1-39" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-40"><a href="#cb1-40" aria-hidden="true" tabindex="-1"></a>    StringVector getAvailableLanguages<span class="op">()</span> <span class="at">const</span></span>
<span id="cb1-41"><a href="#cb1-41" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb1-42"><a href="#cb1-42" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> getProperty<span class="op">(</span>AvailableLanguagesProperty<span class="op">);</span></span>
<span id="cb1-43"><a href="#cb1-43" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-44"><a href="#cb1-44" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-45"><a href="#cb1-45" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb1-46"><a href="#cb1-46" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> initializeLanguages<span class="op">()</span></span>
<span id="cb1-47"><a href="#cb1-47" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb1-48"><a href="#cb1-48" aria-hidden="true" tabindex="-1"></a>        StringVector languages <span class="op">=</span> <span class="op">{</span></span>
<span id="cb1-49"><a href="#cb1-49" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;en-US&quot;</span><span class="op">,</span>    <span class="co">// 英语（美国）</span></span>
<span id="cb1-50"><a href="#cb1-50" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;zh-CN&quot;</span><span class="op">,</span>    <span class="co">// 简体中文</span></span>
<span id="cb1-51"><a href="#cb1-51" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;ja-JP&quot;</span><span class="op">,</span>    <span class="co">// 日语</span></span>
<span id="cb1-52"><a href="#cb1-52" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;de-DE&quot;</span><span class="op">,</span>    <span class="co">// 德语</span></span>
<span id="cb1-53"><a href="#cb1-53" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;fr-FR&quot;</span><span class="op">,</span>    <span class="co">// 法语</span></span>
<span id="cb1-54"><a href="#cb1-54" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;es-ES&quot;</span><span class="op">,</span>    <span class="co">// 西班牙语</span></span>
<span id="cb1-55"><a href="#cb1-55" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;ar-SA&quot;</span><span class="op">,</span>    <span class="co">// 阿拉伯语</span></span>
<span id="cb1-56"><a href="#cb1-56" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;ko-KR&quot;</span>     <span class="co">// 韩语</span></span>
<span id="cb1-57"><a href="#cb1-57" aria-hidden="true" tabindex="-1"></a>        <span class="op">};</span></span>
<span id="cb1-58"><a href="#cb1-58" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-59"><a href="#cb1-59" aria-hidden="true" tabindex="-1"></a>        setProperty<span class="op">(</span>AvailableLanguagesProperty<span class="op">,</span> languages<span class="op">);</span></span>
<span id="cb1-60"><a href="#cb1-60" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-61"><a href="#cb1-61" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-62"><a href="#cb1-62" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> loadCurrentLanguage<span class="op">()</span></span>
<span id="cb1-63"><a href="#cb1-63" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb1-64"><a href="#cb1-64" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 从系统设置或用户偏好加载当前语言</span></span>
<span id="cb1-65"><a href="#cb1-65" aria-hidden="true" tabindex="-1"></a>        string systemLanguage <span class="op">=</span> getSystemLanguage<span class="op">();</span></span>
<span id="cb1-66"><a href="#cb1-66" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>isLanguageSupported<span class="op">(</span>systemLanguage<span class="op">))</span></span>
<span id="cb1-67"><a href="#cb1-67" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb1-68"><a href="#cb1-68" aria-hidden="true" tabindex="-1"></a>            setProperty<span class="op">(</span>CurrentLanguageProperty<span class="op">,</span> systemLanguage<span class="op">);</span></span>
<span id="cb1-69"><a href="#cb1-69" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb1-70"><a href="#cb1-70" aria-hidden="true" tabindex="-1"></a>        <span class="cf">else</span></span>
<span id="cb1-71"><a href="#cb1-71" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb1-72"><a href="#cb1-72" aria-hidden="true" tabindex="-1"></a>            setProperty<span class="op">(</span>CurrentLanguageProperty<span class="op">,</span> <span class="st">&quot;en-US&quot;</span><span class="op">);</span> <span class="co">// 默认英语</span></span>
<span id="cb1-73"><a href="#cb1-73" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb1-74"><a href="#cb1-74" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-75"><a href="#cb1-75" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-76"><a href="#cb1-76" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> isLanguageSupported<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span> <span class="at">const</span></span>
<span id="cb1-77"><a href="#cb1-77" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb1-78"><a href="#cb1-78" aria-hidden="true" tabindex="-1"></a>        StringVector languages <span class="op">=</span> getAvailableLanguages<span class="op">();</span></span>
<span id="cb1-79"><a href="#cb1-79" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> find<span class="op">(</span>languages<span class="op">.</span>begin<span class="op">(),</span> languages<span class="op">.</span>end<span class="op">(),</span> languageCode<span class="op">)</span> <span class="op">!=</span> languages<span class="op">.</span>end<span class="op">();</span></span>
<span id="cb1-80"><a href="#cb1-80" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-81"><a href="#cb1-81" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-82"><a href="#cb1-82" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> loadLanguageResources<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span></span>
<span id="cb1-83"><a href="#cb1-83" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb1-84"><a href="#cb1-84" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载语言特定的资源包</span></span>
<span id="cb1-85"><a href="#cb1-85" aria-hidden="true" tabindex="-1"></a>        string resourcePath <span class="op">=</span> <span class="st">&quot;localization/&quot;</span> <span class="op">+</span> languageCode <span class="op">+</span> <span class="st">&quot;/&quot;</span><span class="op">;</span></span>
<span id="cb1-86"><a href="#cb1-86" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-87"><a href="#cb1-87" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载文本资源</span></span>
<span id="cb1-88"><a href="#cb1-88" aria-hidden="true" tabindex="-1"></a>        loadTextResources<span class="op">(</span>resourcePath <span class="op">+</span> <span class="st">&quot;strings.json&quot;</span><span class="op">);</span></span>
<span id="cb1-89"><a href="#cb1-89" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-90"><a href="#cb1-90" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载图像资源</span></span>
<span id="cb1-91"><a href="#cb1-91" aria-hidden="true" tabindex="-1"></a>        loadImageResources<span class="op">(</span>resourcePath <span class="op">+</span> <span class="st">&quot;images/&quot;</span><span class="op">);</span></span>
<span id="cb1-92"><a href="#cb1-92" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-93"><a href="#cb1-93" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载音频资源</span></span>
<span id="cb1-94"><a href="#cb1-94" aria-hidden="true" tabindex="-1"></a>        loadAudioResources<span class="op">(</span>resourcePath <span class="op">+</span> <span class="st">&quot;audio/&quot;</span><span class="op">);</span></span>
<span id="cb1-95"><a href="#cb1-95" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-96"><a href="#cb1-96" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-97"><a href="#cb1-97" aria-hidden="true" tabindex="-1"></a>    string getSystemLanguage<span class="op">()</span> <span class="at">const</span></span>
<span id="cb1-98"><a href="#cb1-98" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb1-99"><a href="#cb1-99" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 从系统获取语言设置</span></span>
<span id="cb1-100"><a href="#cb1-100" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 这里是示例实现</span></span>
<span id="cb1-101"><a href="#cb1-101" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="st">&quot;en-US&quot;</span><span class="op">;</span></span>
<span id="cb1-102"><a href="#cb1-102" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-103"><a href="#cb1-103" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-104"><a href="#cb1-104" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> notifyLanguageChanged<span class="op">()</span></span>
<span id="cb1-105"><a href="#cb1-105" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb1-106"><a href="#cb1-106" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 通知所有监听器语言已更改</span></span>
<span id="cb1-107"><a href="#cb1-107" aria-hidden="true" tabindex="-1"></a>        MessageArguments args<span class="op">;</span></span>
<span id="cb1-108"><a href="#cb1-108" aria-hidden="true" tabindex="-1"></a>        dispatchMessage<span class="op">(</span>LanguageChangedMessage<span class="op">::</span>create<span class="op">(</span>args<span class="op">));</span></span>
<span id="cb1-109"><a href="#cb1-109" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-110"><a href="#cb1-110" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb1-111"><a href="#cb1-111" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-112"><a href="#cb1-112" aria-hidden="true" tabindex="-1"></a><span class="co">// 属性定义</span></span>
<span id="cb1-113"><a href="#cb1-113" aria-hidden="true" tabindex="-1"></a>PropertyType<span class="op">&lt;</span>string<span class="op">&gt;</span> LocalizationDataSource<span class="op">::</span>CurrentLanguageProperty<span class="op">(</span></span>
<span id="cb1-114"><a href="#cb1-114" aria-hidden="true" tabindex="-1"></a>    kzMakeFixedString<span class="op">(</span><span class="st">&quot;LocalizationDataSource.CurrentLanguage&quot;</span><span class="op">),</span> <span class="st">&quot;en-US&quot;</span><span class="op">);</span></span>
<span id="cb1-115"><a href="#cb1-115" aria-hidden="true" tabindex="-1"></a>PropertyType<span class="op">&lt;</span>StringVector<span class="op">&gt;</span> LocalizationDataSource<span class="op">::</span>AvailableLanguagesProperty<span class="op">(</span></span>
<span id="cb1-116"><a href="#cb1-116" aria-hidden="true" tabindex="-1"></a>    kzMakeFixedString<span class="op">(</span><span class="st">&quot;LocalizationDataSource.AvailableLanguages&quot;</span><span class="op">),</span> StringVector<span class="op">());</span></span></code></pre></div>
<h3 id="本地化字符串管理">本地化字符串管理</h3>
<div class="sourceCode" id="cb2"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 本地化字符串管理器</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> LocalizedStringManager</span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> LocalizedStringManager<span class="op">&amp;</span> getInstance<span class="op">()</span></span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb2-7"><a href="#cb2-7" aria-hidden="true" tabindex="-1"></a>        <span class="at">static</span> LocalizedStringManager instance<span class="op">;</span></span>
<span id="cb2-8"><a href="#cb2-8" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> instance<span class="op">;</span></span>
<span id="cb2-9"><a href="#cb2-9" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-10"><a href="#cb2-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-11"><a href="#cb2-11" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> loadStrings<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span></span>
<span id="cb2-12"><a href="#cb2-12" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb2-13"><a href="#cb2-13" aria-hidden="true" tabindex="-1"></a>        string filePath <span class="op">=</span> <span class="st">&quot;localization/&quot;</span> <span class="op">+</span> languageCode <span class="op">+</span> <span class="st">&quot;/strings.json&quot;</span><span class="op">;</span></span>
<span id="cb2-14"><a href="#cb2-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-15"><a href="#cb2-15" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 解析 JSON 文件</span></span>
<span id="cb2-16"><a href="#cb2-16" aria-hidden="true" tabindex="-1"></a>        Json<span class="op">::</span>Value root<span class="op">;</span></span>
<span id="cb2-17"><a href="#cb2-17" aria-hidden="true" tabindex="-1"></a>        ifstream file<span class="op">(</span>filePath<span class="op">);</span></span>
<span id="cb2-18"><a href="#cb2-18" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>file<span class="op">.</span>is_open<span class="op">())</span></span>
<span id="cb2-19"><a href="#cb2-19" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb2-20"><a href="#cb2-20" aria-hidden="true" tabindex="-1"></a>            file <span class="op">&gt;&gt;</span> root<span class="op">;</span></span>
<span id="cb2-21"><a href="#cb2-21" aria-hidden="true" tabindex="-1"></a>            parseStringData<span class="op">(</span>root<span class="op">,</span> languageCode<span class="op">);</span></span>
<span id="cb2-22"><a href="#cb2-22" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb2-23"><a href="#cb2-23" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-24"><a href="#cb2-24" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-25"><a href="#cb2-25" aria-hidden="true" tabindex="-1"></a>    string getString<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> key<span class="op">,</span> <span class="at">const</span> string<span class="op">&amp;</span> defaultValue <span class="op">=</span> <span class="st">&quot;&quot;</span><span class="op">)</span> <span class="at">const</span></span>
<span id="cb2-26"><a href="#cb2-26" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb2-27"><a href="#cb2-27" aria-hidden="true" tabindex="-1"></a>        string currentLanguage <span class="op">=</span> getCurrentLanguage<span class="op">();</span></span>
<span id="cb2-28"><a href="#cb2-28" aria-hidden="true" tabindex="-1"></a>        string fullKey <span class="op">=</span> currentLanguage <span class="op">+</span> <span class="st">&quot;.&quot;</span> <span class="op">+</span> key<span class="op">;</span></span>
<span id="cb2-29"><a href="#cb2-29" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-30"><a href="#cb2-30" aria-hidden="true" tabindex="-1"></a>        <span class="kw">auto</span> it <span class="op">=</span> <span class="va">m_strings</span><span class="op">.</span>find<span class="op">(</span>fullKey<span class="op">);</span></span>
<span id="cb2-31"><a href="#cb2-31" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>it <span class="op">!=</span> <span class="va">m_strings</span><span class="op">.</span>end<span class="op">())</span></span>
<span id="cb2-32"><a href="#cb2-32" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb2-33"><a href="#cb2-33" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> it<span class="op">-&gt;</span>second<span class="op">;</span></span>
<span id="cb2-34"><a href="#cb2-34" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb2-35"><a href="#cb2-35" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-36"><a href="#cb2-36" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 回退到英语</span></span>
<span id="cb2-37"><a href="#cb2-37" aria-hidden="true" tabindex="-1"></a>        string fallbackKey <span class="op">=</span> <span class="st">&quot;en-US.&quot;</span> <span class="op">+</span> key<span class="op">;</span></span>
<span id="cb2-38"><a href="#cb2-38" aria-hidden="true" tabindex="-1"></a>        <span class="kw">auto</span> fallbackIt <span class="op">=</span> <span class="va">m_strings</span><span class="op">.</span>find<span class="op">(</span>fallbackKey<span class="op">);</span></span>
<span id="cb2-39"><a href="#cb2-39" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>fallbackIt <span class="op">!=</span> <span class="va">m_strings</span><span class="op">.</span>end<span class="op">())</span></span>
<span id="cb2-40"><a href="#cb2-40" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb2-41"><a href="#cb2-41" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> fallbackIt<span class="op">-&gt;</span>second<span class="op">;</span></span>
<span id="cb2-42"><a href="#cb2-42" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb2-43"><a href="#cb2-43" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-44"><a href="#cb2-44" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> defaultValue<span class="op">;</span></span>
<span id="cb2-45"><a href="#cb2-45" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-46"><a href="#cb2-46" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-47"><a href="#cb2-47" aria-hidden="true" tabindex="-1"></a>    string getFormattedString<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> key<span class="op">,</span> <span class="at">const</span> vector<span class="op">&lt;</span>string<span class="op">&gt;&amp;</span> args<span class="op">)</span> <span class="at">const</span></span>
<span id="cb2-48"><a href="#cb2-48" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb2-49"><a href="#cb2-49" aria-hidden="true" tabindex="-1"></a>        string pattern <span class="op">=</span> getString<span class="op">(</span>key<span class="op">);</span></span>
<span id="cb2-50"><a href="#cb2-50" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> formatString<span class="op">(</span>pattern<span class="op">,</span> args<span class="op">);</span></span>
<span id="cb2-51"><a href="#cb2-51" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-52"><a href="#cb2-52" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-53"><a href="#cb2-53" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb2-54"><a href="#cb2-54" aria-hidden="true" tabindex="-1"></a>    map<span class="op">&lt;</span>string<span class="op">,</span> string<span class="op">&gt;</span> <span class="va">m_strings</span><span class="op">;</span></span>
<span id="cb2-55"><a href="#cb2-55" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-56"><a href="#cb2-56" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> parseStringData<span class="op">(</span><span class="at">const</span> Json<span class="op">::</span>Value<span class="op">&amp;</span> root<span class="op">,</span> <span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span></span>
<span id="cb2-57"><a href="#cb2-57" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb2-58"><a href="#cb2-58" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> <span class="kw">auto</span><span class="op">&amp;</span> key <span class="op">:</span> root<span class="op">.</span>getMemberNames<span class="op">())</span></span>
<span id="cb2-59"><a href="#cb2-59" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb2-60"><a href="#cb2-60" aria-hidden="true" tabindex="-1"></a>            string fullKey <span class="op">=</span> languageCode <span class="op">+</span> <span class="st">&quot;.&quot;</span> <span class="op">+</span> key<span class="op">;</span></span>
<span id="cb2-61"><a href="#cb2-61" aria-hidden="true" tabindex="-1"></a>            <span class="va">m_strings</span><span class="op">[</span>fullKey<span class="op">]</span> <span class="op">=</span> root<span class="op">[</span>key<span class="op">].</span>asString<span class="op">();</span></span>
<span id="cb2-62"><a href="#cb2-62" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb2-63"><a href="#cb2-63" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-64"><a href="#cb2-64" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-65"><a href="#cb2-65" aria-hidden="true" tabindex="-1"></a>    string formatString<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> pattern<span class="op">,</span> <span class="at">const</span> vector<span class="op">&lt;</span>string<span class="op">&gt;&amp;</span> args<span class="op">)</span> <span class="at">const</span></span>
<span id="cb2-66"><a href="#cb2-66" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb2-67"><a href="#cb2-67" aria-hidden="true" tabindex="-1"></a>        string result <span class="op">=</span> pattern<span class="op">;</span></span>
<span id="cb2-68"><a href="#cb2-68" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="dt">size_t</span> i <span class="op">=</span> <span class="dv">0</span><span class="op">;</span> i <span class="op">&lt;</span> args<span class="op">.</span>size<span class="op">();</span> <span class="op">++</span>i<span class="op">)</span></span>
<span id="cb2-69"><a href="#cb2-69" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb2-70"><a href="#cb2-70" aria-hidden="true" tabindex="-1"></a>            string placeholder <span class="op">=</span> <span class="st">&quot;{&quot;</span> <span class="op">+</span> to_string<span class="op">(</span>i<span class="op">)</span> <span class="op">+</span> <span class="st">&quot;}&quot;</span><span class="op">;</span></span>
<span id="cb2-71"><a href="#cb2-71" aria-hidden="true" tabindex="-1"></a>            <span class="dt">size_t</span> pos <span class="op">=</span> result<span class="op">.</span>find<span class="op">(</span>placeholder<span class="op">);</span></span>
<span id="cb2-72"><a href="#cb2-72" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> <span class="op">(</span>pos <span class="op">!=</span> string<span class="op">::</span>npos<span class="op">)</span></span>
<span id="cb2-73"><a href="#cb2-73" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb2-74"><a href="#cb2-74" aria-hidden="true" tabindex="-1"></a>                result<span class="op">.</span>replace<span class="op">(</span>pos<span class="op">,</span> placeholder<span class="op">.</span>length<span class="op">(),</span> args<span class="op">[</span>i<span class="op">]);</span></span>
<span id="cb2-75"><a href="#cb2-75" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb2-76"><a href="#cb2-76" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb2-77"><a href="#cb2-77" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> result<span class="op">;</span></span>
<span id="cb2-78"><a href="#cb2-78" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-79"><a href="#cb2-79" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-80"><a href="#cb2-80" aria-hidden="true" tabindex="-1"></a>    string getCurrentLanguage<span class="op">()</span> <span class="at">const</span></span>
<span id="cb2-81"><a href="#cb2-81" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb2-82"><a href="#cb2-82" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 获取当前语言设置</span></span>
<span id="cb2-83"><a href="#cb2-83" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="st">&quot;en-US&quot;</span><span class="op">;</span> <span class="co">// 示例</span></span>
<span id="cb2-84"><a href="#cb2-84" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-85"><a href="#cb2-85" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="文本本地化">文本本地化</h2>
<h3 id="本地化文本组件">本地化文本组件</h3>
<div class="sourceCode" id="cb3"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 本地化文本节点</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> LocalizedTextNode <span class="op">:</span> <span class="kw">public</span> Text2D</span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a>    KZ_COMPONENT<span class="op">(</span>LocalizedTextNode<span class="op">)</span></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> PropertyType<span class="op">&lt;</span>string<span class="op">&gt;</span> TextKeyProperty<span class="op">;</span></span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> PropertyType<span class="op">&lt;</span>StringVector<span class="op">&gt;</span> FormatArgumentsProperty<span class="op">;</span></span>
<span id="cb3-9"><a href="#cb3-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-10"><a href="#cb3-10" aria-hidden="true" tabindex="-1"></a>    <span class="kw">explicit</span> LocalizedTextNode<span class="op">(</span>Domain<span class="op">*</span> domain<span class="op">,</span> string_view name<span class="op">)</span></span>
<span id="cb3-11"><a href="#cb3-11" aria-hidden="true" tabindex="-1"></a>        <span class="op">:</span> Text2D<span class="op">(</span>domain<span class="op">,</span> name<span class="op">)</span></span>
<span id="cb3-12"><a href="#cb3-12" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-13"><a href="#cb3-13" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 监听语言变化</span></span>
<span id="cb3-14"><a href="#cb3-14" aria-hidden="true" tabindex="-1"></a>        addPropertyNotificationHandler<span class="op">(</span>TextKeyProperty<span class="op">,</span></span>
<span id="cb3-15"><a href="#cb3-15" aria-hidden="true" tabindex="-1"></a>            bind<span class="op">(&amp;</span>LocalizedTextNode<span class="op">::</span>onTextKeyChanged<span class="op">,</span> <span class="kw">this</span><span class="op">,</span> placeholders<span class="op">::</span>_1<span class="op">));</span></span>
<span id="cb3-16"><a href="#cb3-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-17"><a href="#cb3-17" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 监听格式参数变化</span></span>
<span id="cb3-18"><a href="#cb3-18" aria-hidden="true" tabindex="-1"></a>        addPropertyNotificationHandler<span class="op">(</span>FormatArgumentsProperty<span class="op">,</span></span>
<span id="cb3-19"><a href="#cb3-19" aria-hidden="true" tabindex="-1"></a>            bind<span class="op">(&amp;</span>LocalizedTextNode<span class="op">::</span>onFormatArgumentsChanged<span class="op">,</span> <span class="kw">this</span><span class="op">,</span> placeholders<span class="op">::</span>_1<span class="op">));</span></span>
<span id="cb3-20"><a href="#cb3-20" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-21"><a href="#cb3-21" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-22"><a href="#cb3-22" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> initialize<span class="op">()</span> <span class="kw">override</span></span>
<span id="cb3-23"><a href="#cb3-23" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-24"><a href="#cb3-24" aria-hidden="true" tabindex="-1"></a>        Text2D<span class="op">::</span>initialize<span class="op">();</span></span>
<span id="cb3-25"><a href="#cb3-25" aria-hidden="true" tabindex="-1"></a>        updateLocalizedText<span class="op">();</span></span>
<span id="cb3-26"><a href="#cb3-26" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-27"><a href="#cb3-27" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 注册语言变化监听</span></span>
<span id="cb3-28"><a href="#cb3-28" aria-hidden="true" tabindex="-1"></a>        registerLanguageChangeListener<span class="op">();</span></span>
<span id="cb3-29"><a href="#cb3-29" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-30"><a href="#cb3-30" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-31"><a href="#cb3-31" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb3-32"><a href="#cb3-32" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> onTextKeyChanged<span class="op">(</span>PropertyObject<span class="op">&amp;</span> object<span class="op">)</span></span>
<span id="cb3-33"><a href="#cb3-33" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-34"><a href="#cb3-34" aria-hidden="true" tabindex="-1"></a>        updateLocalizedText<span class="op">();</span></span>
<span id="cb3-35"><a href="#cb3-35" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-36"><a href="#cb3-36" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-37"><a href="#cb3-37" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> onFormatArgumentsChanged<span class="op">(</span>PropertyObject<span class="op">&amp;</span> object<span class="op">)</span></span>
<span id="cb3-38"><a href="#cb3-38" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-39"><a href="#cb3-39" aria-hidden="true" tabindex="-1"></a>        updateLocalizedText<span class="op">();</span></span>
<span id="cb3-40"><a href="#cb3-40" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-41"><a href="#cb3-41" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-42"><a href="#cb3-42" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> updateLocalizedText<span class="op">()</span></span>
<span id="cb3-43"><a href="#cb3-43" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-44"><a href="#cb3-44" aria-hidden="true" tabindex="-1"></a>        string textKey <span class="op">=</span> getProperty<span class="op">(</span>TextKeyProperty<span class="op">);</span></span>
<span id="cb3-45"><a href="#cb3-45" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>textKey<span class="op">.</span>empty<span class="op">())</span> <span class="cf">return</span><span class="op">;</span></span>
<span id="cb3-46"><a href="#cb3-46" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-47"><a href="#cb3-47" aria-hidden="true" tabindex="-1"></a>        StringVector formatArgs <span class="op">=</span> getProperty<span class="op">(</span>FormatArgumentsProperty<span class="op">);</span></span>
<span id="cb3-48"><a href="#cb3-48" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-49"><a href="#cb3-49" aria-hidden="true" tabindex="-1"></a>        string localizedText<span class="op">;</span></span>
<span id="cb3-50"><a href="#cb3-50" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>formatArgs<span class="op">.</span>empty<span class="op">())</span></span>
<span id="cb3-51"><a href="#cb3-51" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb3-52"><a href="#cb3-52" aria-hidden="true" tabindex="-1"></a>            localizedText <span class="op">=</span> LocalizedStringManager<span class="op">::</span>getInstance<span class="op">().</span>getString<span class="op">(</span>textKey<span class="op">);</span></span>
<span id="cb3-53"><a href="#cb3-53" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb3-54"><a href="#cb3-54" aria-hidden="true" tabindex="-1"></a>        <span class="cf">else</span></span>
<span id="cb3-55"><a href="#cb3-55" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb3-56"><a href="#cb3-56" aria-hidden="true" tabindex="-1"></a>            vector<span class="op">&lt;</span>string<span class="op">&gt;</span> args<span class="op">(</span>formatArgs<span class="op">.</span>begin<span class="op">(),</span> formatArgs<span class="op">.</span>end<span class="op">());</span></span>
<span id="cb3-57"><a href="#cb3-57" aria-hidden="true" tabindex="-1"></a>            localizedText <span class="op">=</span> LocalizedStringManager<span class="op">::</span>getInstance<span class="op">().</span>getFormattedString<span class="op">(</span>textKey<span class="op">,</span> args<span class="op">);</span></span>
<span id="cb3-58"><a href="#cb3-58" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb3-59"><a href="#cb3-59" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-60"><a href="#cb3-60" aria-hidden="true" tabindex="-1"></a>        setText<span class="op">(</span>localizedText<span class="op">);</span></span>
<span id="cb3-61"><a href="#cb3-61" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-62"><a href="#cb3-62" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 根据语言调整字体和布局</span></span>
<span id="cb3-63"><a href="#cb3-63" aria-hidden="true" tabindex="-1"></a>        adjustForCurrentLanguage<span class="op">();</span></span>
<span id="cb3-64"><a href="#cb3-64" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-65"><a href="#cb3-65" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-66"><a href="#cb3-66" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> adjustForCurrentLanguage<span class="op">()</span></span>
<span id="cb3-67"><a href="#cb3-67" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-68"><a href="#cb3-68" aria-hidden="true" tabindex="-1"></a>        string currentLanguage <span class="op">=</span> getCurrentLanguage<span class="op">();</span></span>
<span id="cb3-69"><a href="#cb3-69" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-70"><a href="#cb3-70" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 设置合适的字体</span></span>
<span id="cb3-71"><a href="#cb3-71" aria-hidden="true" tabindex="-1"></a>        FontSharedPtr font <span class="op">=</span> getLocalizedFont<span class="op">(</span>currentLanguage<span class="op">);</span></span>
<span id="cb3-72"><a href="#cb3-72" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>font<span class="op">)</span></span>
<span id="cb3-73"><a href="#cb3-73" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb3-74"><a href="#cb3-74" aria-hidden="true" tabindex="-1"></a>            setFont<span class="op">(</span>font<span class="op">);</span></span>
<span id="cb3-75"><a href="#cb3-75" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb3-76"><a href="#cb3-76" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-77"><a href="#cb3-77" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 调整文本对齐方式</span></span>
<span id="cb3-78"><a href="#cb3-78" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>isRightToLeftLanguage<span class="op">(</span>currentLanguage<span class="op">))</span></span>
<span id="cb3-79"><a href="#cb3-79" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb3-80"><a href="#cb3-80" aria-hidden="true" tabindex="-1"></a>            setHorizontalAlignment<span class="op">(</span>TextConcept<span class="op">::</span>HorizontalAlignment<span class="op">::</span>Right<span class="op">);</span></span>
<span id="cb3-81"><a href="#cb3-81" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb3-82"><a href="#cb3-82" aria-hidden="true" tabindex="-1"></a>        <span class="cf">else</span></span>
<span id="cb3-83"><a href="#cb3-83" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb3-84"><a href="#cb3-84" aria-hidden="true" tabindex="-1"></a>            setHorizontalAlignment<span class="op">(</span>TextConcept<span class="op">::</span>HorizontalAlignment<span class="op">::</span>Left<span class="op">);</span></span>
<span id="cb3-85"><a href="#cb3-85" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb3-86"><a href="#cb3-86" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-87"><a href="#cb3-87" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 调整字体大小</span></span>
<span id="cb3-88"><a href="#cb3-88" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> fontSize <span class="op">=</span> getLocalizedFontSize<span class="op">(</span>currentLanguage<span class="op">);</span></span>
<span id="cb3-89"><a href="#cb3-89" aria-hidden="true" tabindex="-1"></a>        setFontSize<span class="op">(</span>fontSize<span class="op">);</span></span>
<span id="cb3-90"><a href="#cb3-90" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-91"><a href="#cb3-91" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-92"><a href="#cb3-92" aria-hidden="true" tabindex="-1"></a>    FontSharedPtr getLocalizedFont<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span></span>
<span id="cb3-93"><a href="#cb3-93" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-94"><a href="#cb3-94" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 根据语言选择合适的字体</span></span>
<span id="cb3-95"><a href="#cb3-95" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>languageCode<span class="op">.</span>find<span class="op">(</span><span class="st">&quot;zh&quot;</span><span class="op">)</span> <span class="op">==</span> <span class="dv">0</span><span class="op">)</span> <span class="co">// 中文</span></span>
<span id="cb3-96"><a href="#cb3-96" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb3-97"><a href="#cb3-97" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> getResourceManager<span class="op">()-&gt;</span>acquireResource<span class="op">&lt;</span>Font<span class="op">&gt;(</span><span class="st">&quot;NotoSansCJK&quot;</span><span class="op">);</span></span>
<span id="cb3-98"><a href="#cb3-98" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb3-99"><a href="#cb3-99" aria-hidden="true" tabindex="-1"></a>        <span class="cf">else</span> <span class="cf">if</span> <span class="op">(</span>languageCode<span class="op">.</span>find<span class="op">(</span><span class="st">&quot;ja&quot;</span><span class="op">)</span> <span class="op">==</span> <span class="dv">0</span><span class="op">)</span> <span class="co">// 日文</span></span>
<span id="cb3-100"><a href="#cb3-100" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb3-101"><a href="#cb3-101" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> getResourceManager<span class="op">()-&gt;</span>acquireResource<span class="op">&lt;</span>Font<span class="op">&gt;(</span><span class="st">&quot;NotoSansJP&quot;</span><span class="op">);</span></span>
<span id="cb3-102"><a href="#cb3-102" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb3-103"><a href="#cb3-103" aria-hidden="true" tabindex="-1"></a>        <span class="cf">else</span> <span class="cf">if</span> <span class="op">(</span>languageCode<span class="op">.</span>find<span class="op">(</span><span class="st">&quot;ar&quot;</span><span class="op">)</span> <span class="op">==</span> <span class="dv">0</span><span class="op">)</span> <span class="co">// 阿拉伯文</span></span>
<span id="cb3-104"><a href="#cb3-104" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb3-105"><a href="#cb3-105" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> getResourceManager<span class="op">()-&gt;</span>acquireResource<span class="op">&lt;</span>Font<span class="op">&gt;(</span><span class="st">&quot;NotoSansArabic&quot;</span><span class="op">);</span></span>
<span id="cb3-106"><a href="#cb3-106" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb3-107"><a href="#cb3-107" aria-hidden="true" tabindex="-1"></a>        <span class="cf">else</span> <span class="cf">if</span> <span class="op">(</span>languageCode<span class="op">.</span>find<span class="op">(</span><span class="st">&quot;ko&quot;</span><span class="op">)</span> <span class="op">==</span> <span class="dv">0</span><span class="op">)</span> <span class="co">// 韩文</span></span>
<span id="cb3-108"><a href="#cb3-108" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb3-109"><a href="#cb3-109" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> getResourceManager<span class="op">()-&gt;</span>acquireResource<span class="op">&lt;</span>Font<span class="op">&gt;(</span><span class="st">&quot;NotoSansKR&quot;</span><span class="op">);</span></span>
<span id="cb3-110"><a href="#cb3-110" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb3-111"><a href="#cb3-111" aria-hidden="true" tabindex="-1"></a>        <span class="cf">else</span> <span class="co">// 默认拉丁字体</span></span>
<span id="cb3-112"><a href="#cb3-112" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb3-113"><a href="#cb3-113" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> getResourceManager<span class="op">()-&gt;</span>acquireResource<span class="op">&lt;</span>Font<span class="op">&gt;(</span><span class="st">&quot;NotoSans&quot;</span><span class="op">);</span></span>
<span id="cb3-114"><a href="#cb3-114" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb3-115"><a href="#cb3-115" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-116"><a href="#cb3-116" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-117"><a href="#cb3-117" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> isRightToLeftLanguage<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span></span>
<span id="cb3-118"><a href="#cb3-118" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-119"><a href="#cb3-119" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> languageCode<span class="op">.</span>find<span class="op">(</span><span class="st">&quot;ar&quot;</span><span class="op">)</span> <span class="op">==</span> <span class="dv">0</span> <span class="op">||</span> languageCode<span class="op">.</span>find<span class="op">(</span><span class="st">&quot;he&quot;</span><span class="op">)</span> <span class="op">==</span> <span class="dv">0</span> <span class="op">||</span></span>
<span id="cb3-120"><a href="#cb3-120" aria-hidden="true" tabindex="-1"></a>               languageCode<span class="op">.</span>find<span class="op">(</span><span class="st">&quot;fa&quot;</span><span class="op">)</span> <span class="op">==</span> <span class="dv">0</span> <span class="op">||</span> languageCode<span class="op">.</span>find<span class="op">(</span><span class="st">&quot;ur&quot;</span><span class="op">)</span> <span class="op">==</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb3-121"><a href="#cb3-121" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-122"><a href="#cb3-122" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-123"><a href="#cb3-123" aria-hidden="true" tabindex="-1"></a>    <span class="dt">float</span> getLocalizedFontSize<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span></span>
<span id="cb3-124"><a href="#cb3-124" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-125"><a href="#cb3-125" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 某些语言可能需要调整字体大小</span></span>
<span id="cb3-126"><a href="#cb3-126" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>languageCode<span class="op">.</span>find<span class="op">(</span><span class="st">&quot;zh&quot;</span><span class="op">)</span> <span class="op">==</span> <span class="dv">0</span> <span class="op">||</span> languageCode<span class="op">.</span>find<span class="op">(</span><span class="st">&quot;ja&quot;</span><span class="op">)</span> <span class="op">==</span> <span class="dv">0</span> <span class="op">||</span> languageCode<span class="op">.</span>find<span class="op">(</span><span class="st">&quot;ko&quot;</span><span class="op">)</span> <span class="op">==</span> <span class="dv">0</span><span class="op">)</span></span>
<span id="cb3-127"><a href="#cb3-127" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb3-128"><a href="#cb3-128" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> getFontSize<span class="op">()</span> <span class="op">*</span> <span class="fl">1.1</span><span class="bu">f</span><span class="op">;</span> <span class="co">// CJK 字符稍大一些</span></span>
<span id="cb3-129"><a href="#cb3-129" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb3-130"><a href="#cb3-130" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> getFontSize<span class="op">();</span></span>
<span id="cb3-131"><a href="#cb3-131" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-132"><a href="#cb3-132" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-133"><a href="#cb3-133" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> registerLanguageChangeListener<span class="op">()</span></span>
<span id="cb3-134"><a href="#cb3-134" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-135"><a href="#cb3-135" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 注册语言变化监听器</span></span>
<span id="cb3-136"><a href="#cb3-136" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 实际实现中需要连接到语言变化事件</span></span>
<span id="cb3-137"><a href="#cb3-137" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-138"><a href="#cb3-138" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-139"><a href="#cb3-139" aria-hidden="true" tabindex="-1"></a>    string getCurrentLanguage<span class="op">()</span></span>
<span id="cb3-140"><a href="#cb3-140" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-141"><a href="#cb3-141" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 获取当前语言设置</span></span>
<span id="cb3-142"><a href="#cb3-142" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="st">&quot;en-US&quot;</span><span class="op">;</span> <span class="co">// 示例</span></span>
<span id="cb3-143"><a href="#cb3-143" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-144"><a href="#cb3-144" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb3-145"><a href="#cb3-145" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-146"><a href="#cb3-146" aria-hidden="true" tabindex="-1"></a><span class="co">// 属性定义</span></span>
<span id="cb3-147"><a href="#cb3-147" aria-hidden="true" tabindex="-1"></a>PropertyType<span class="op">&lt;</span>string<span class="op">&gt;</span> LocalizedTextNode<span class="op">::</span>TextKeyProperty<span class="op">(</span></span>
<span id="cb3-148"><a href="#cb3-148" aria-hidden="true" tabindex="-1"></a>    kzMakeFixedString<span class="op">(</span><span class="st">&quot;LocalizedTextNode.TextKey&quot;</span><span class="op">),</span> <span class="st">&quot;&quot;</span><span class="op">);</span></span>
<span id="cb3-149"><a href="#cb3-149" aria-hidden="true" tabindex="-1"></a>PropertyType<span class="op">&lt;</span>StringVector<span class="op">&gt;</span> LocalizedTextNode<span class="op">::</span>FormatArgumentsProperty<span class="op">(</span></span>
<span id="cb3-150"><a href="#cb3-150" aria-hidden="true" tabindex="-1"></a>    kzMakeFixedString<span class="op">(</span><span class="st">&quot;LocalizedTextNode.FormatArguments&quot;</span><span class="op">),</span> StringVector<span class="op">());</span></span></code></pre></div>
<h2 id="资源本地化">资源本地化</h2>
<h3 id="本地化图像管理">本地化图像管理</h3>
<div class="sourceCode" id="cb4"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 本地化图像资源管理器</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> LocalizedImageManager</span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> LocalizedImageManager<span class="op">&amp;</span> getInstance<span class="op">()</span></span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a>        <span class="at">static</span> LocalizedImageManager instance<span class="op">;</span></span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> instance<span class="op">;</span></span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-11"><a href="#cb4-11" aria-hidden="true" tabindex="-1"></a>    TextureSharedPtr getLocalizedTexture<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> textureKey<span class="op">)</span></span>
<span id="cb4-12"><a href="#cb4-12" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-13"><a href="#cb4-13" aria-hidden="true" tabindex="-1"></a>        string currentLanguage <span class="op">=</span> getCurrentLanguage<span class="op">();</span></span>
<span id="cb4-14"><a href="#cb4-14" aria-hidden="true" tabindex="-1"></a>        string localizedKey <span class="op">=</span> textureKey <span class="op">+</span> <span class="st">&quot;_&quot;</span> <span class="op">+</span> currentLanguage<span class="op">;</span></span>
<span id="cb4-15"><a href="#cb4-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-16"><a href="#cb4-16" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 检查缓存</span></span>
<span id="cb4-17"><a href="#cb4-17" aria-hidden="true" tabindex="-1"></a>        <span class="kw">auto</span> it <span class="op">=</span> <span class="va">m_textureCache</span><span class="op">.</span>find<span class="op">(</span>localizedKey<span class="op">);</span></span>
<span id="cb4-18"><a href="#cb4-18" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>it <span class="op">!=</span> <span class="va">m_textureCache</span><span class="op">.</span>end<span class="op">())</span></span>
<span id="cb4-19"><a href="#cb4-19" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb4-20"><a href="#cb4-20" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> it<span class="op">-&gt;</span>second<span class="op">;</span></span>
<span id="cb4-21"><a href="#cb4-21" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb4-22"><a href="#cb4-22" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-23"><a href="#cb4-23" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 尝试加载本地化版本</span></span>
<span id="cb4-24"><a href="#cb4-24" aria-hidden="true" tabindex="-1"></a>        string localizedPath <span class="op">=</span> <span class="st">&quot;localization/&quot;</span> <span class="op">+</span> currentLanguage <span class="op">+</span> <span class="st">&quot;/images/&quot;</span> <span class="op">+</span> textureKey <span class="op">+</span> <span class="st">&quot;.png&quot;</span><span class="op">;</span></span>
<span id="cb4-25"><a href="#cb4-25" aria-hidden="true" tabindex="-1"></a>        TextureSharedPtr texture <span class="op">=</span> loadTexture<span class="op">(</span>localizedPath<span class="op">);</span></span>
<span id="cb4-26"><a href="#cb4-26" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-27"><a href="#cb4-27" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(!</span>texture<span class="op">)</span></span>
<span id="cb4-28"><a href="#cb4-28" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb4-29"><a href="#cb4-29" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 回退到默认版本</span></span>
<span id="cb4-30"><a href="#cb4-30" aria-hidden="true" tabindex="-1"></a>            string defaultPath <span class="op">=</span> <span class="st">&quot;localization/en-US/images/&quot;</span> <span class="op">+</span> textureKey <span class="op">+</span> <span class="st">&quot;.png&quot;</span><span class="op">;</span></span>
<span id="cb4-31"><a href="#cb4-31" aria-hidden="true" tabindex="-1"></a>            texture <span class="op">=</span> loadTexture<span class="op">(</span>defaultPath<span class="op">);</span></span>
<span id="cb4-32"><a href="#cb4-32" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb4-33"><a href="#cb4-33" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-34"><a href="#cb4-34" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>texture<span class="op">)</span></span>
<span id="cb4-35"><a href="#cb4-35" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb4-36"><a href="#cb4-36" aria-hidden="true" tabindex="-1"></a>            <span class="va">m_textureCache</span><span class="op">[</span>localizedKey<span class="op">]</span> <span class="op">=</span> texture<span class="op">;</span></span>
<span id="cb4-37"><a href="#cb4-37" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb4-38"><a href="#cb4-38" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-39"><a href="#cb4-39" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> texture<span class="op">;</span></span>
<span id="cb4-40"><a href="#cb4-40" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-41"><a href="#cb4-41" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-42"><a href="#cb4-42" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> preloadLanguageTextures<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span></span>
<span id="cb4-43"><a href="#cb4-43" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-44"><a href="#cb4-44" aria-hidden="true" tabindex="-1"></a>        string imagePath <span class="op">=</span> <span class="st">&quot;localization/&quot;</span> <span class="op">+</span> languageCode <span class="op">+</span> <span class="st">&quot;/images/&quot;</span><span class="op">;</span></span>
<span id="cb4-45"><a href="#cb4-45" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-46"><a href="#cb4-46" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 预加载常用图标</span></span>
<span id="cb4-47"><a href="#cb4-47" aria-hidden="true" tabindex="-1"></a>        vector<span class="op">&lt;</span>string<span class="op">&gt;</span> commonIcons <span class="op">=</span> <span class="op">{</span></span>
<span id="cb4-48"><a href="#cb4-48" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;home_icon&quot;</span><span class="op">,</span> <span class="st">&quot;settings_icon&quot;</span><span class="op">,</span> <span class="st">&quot;navigation_icon&quot;</span><span class="op">,</span></span>
<span id="cb4-49"><a href="#cb4-49" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;music_icon&quot;</span><span class="op">,</span> <span class="st">&quot;phone_icon&quot;</span><span class="op">,</span> <span class="st">&quot;climate_icon&quot;</span><span class="op">,</span></span>
<span id="cb4-50"><a href="#cb4-50" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;fuel_icon&quot;</span><span class="op">,</span> <span class="st">&quot;speed_icon&quot;</span><span class="op">,</span> <span class="st">&quot;warning_icon&quot;</span></span>
<span id="cb4-51"><a href="#cb4-51" aria-hidden="true" tabindex="-1"></a>        <span class="op">};</span></span>
<span id="cb4-52"><a href="#cb4-52" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-53"><a href="#cb4-53" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> icon <span class="op">:</span> commonIcons<span class="op">)</span></span>
<span id="cb4-54"><a href="#cb4-54" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb4-55"><a href="#cb4-55" aria-hidden="true" tabindex="-1"></a>            string localizedKey <span class="op">=</span> icon <span class="op">+</span> <span class="st">&quot;_&quot;</span> <span class="op">+</span> languageCode<span class="op">;</span></span>
<span id="cb4-56"><a href="#cb4-56" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> <span class="op">(</span><span class="va">m_textureCache</span><span class="op">.</span>find<span class="op">(</span>localizedKey<span class="op">)</span> <span class="op">==</span> <span class="va">m_textureCache</span><span class="op">.</span>end<span class="op">())</span></span>
<span id="cb4-57"><a href="#cb4-57" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb4-58"><a href="#cb4-58" aria-hidden="true" tabindex="-1"></a>                TextureSharedPtr texture <span class="op">=</span> loadTexture<span class="op">(</span>imagePath <span class="op">+</span> icon <span class="op">+</span> <span class="st">&quot;.png&quot;</span><span class="op">);</span></span>
<span id="cb4-59"><a href="#cb4-59" aria-hidden="true" tabindex="-1"></a>                <span class="cf">if</span> <span class="op">(</span>texture<span class="op">)</span></span>
<span id="cb4-60"><a href="#cb4-60" aria-hidden="true" tabindex="-1"></a>                <span class="op">{</span></span>
<span id="cb4-61"><a href="#cb4-61" aria-hidden="true" tabindex="-1"></a>                    <span class="va">m_textureCache</span><span class="op">[</span>localizedKey<span class="op">]</span> <span class="op">=</span> texture<span class="op">;</span></span>
<span id="cb4-62"><a href="#cb4-62" aria-hidden="true" tabindex="-1"></a>                <span class="op">}</span></span>
<span id="cb4-63"><a href="#cb4-63" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb4-64"><a href="#cb4-64" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb4-65"><a href="#cb4-65" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-66"><a href="#cb4-66" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-67"><a href="#cb4-67" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> clearCache<span class="op">()</span></span>
<span id="cb4-68"><a href="#cb4-68" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-69"><a href="#cb4-69" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_textureCache</span><span class="op">.</span>clear<span class="op">();</span></span>
<span id="cb4-70"><a href="#cb4-70" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-71"><a href="#cb4-71" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-72"><a href="#cb4-72" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb4-73"><a href="#cb4-73" aria-hidden="true" tabindex="-1"></a>    map<span class="op">&lt;</span>string<span class="op">,</span> TextureSharedPtr<span class="op">&gt;</span> <span class="va">m_textureCache</span><span class="op">;</span></span>
<span id="cb4-74"><a href="#cb4-74" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-75"><a href="#cb4-75" aria-hidden="true" tabindex="-1"></a>    TextureSharedPtr loadTexture<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> path<span class="op">)</span></span>
<span id="cb4-76"><a href="#cb4-76" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-77"><a href="#cb4-77" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 实际的纹理加载实现</span></span>
<span id="cb4-78"><a href="#cb4-78" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> Texture<span class="op">::</span>createFromFile<span class="op">(</span>getDomain<span class="op">(),</span> path<span class="op">);</span></span>
<span id="cb4-79"><a href="#cb4-79" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-80"><a href="#cb4-80" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-81"><a href="#cb4-81" aria-hidden="true" tabindex="-1"></a>    string getCurrentLanguage<span class="op">()</span></span>
<span id="cb4-82"><a href="#cb4-82" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-83"><a href="#cb4-83" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 获取当前语言设置</span></span>
<span id="cb4-84"><a href="#cb4-84" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="st">&quot;en-US&quot;</span><span class="op">;</span> <span class="co">// 示例</span></span>
<span id="cb4-85"><a href="#cb4-85" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-86"><a href="#cb4-86" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="本地化图像组件">本地化图像组件</h3>
<div class="sourceCode" id="cb5"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 自动本地化的图像节点</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> LocalizedImageNode <span class="op">:</span> <span class="kw">public</span> Image2D</span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a>    KZ_COMPONENT<span class="op">(</span>LocalizedImageNode<span class="op">)</span></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> PropertyType<span class="op">&lt;</span>string<span class="op">&gt;</span> ImageKeyProperty<span class="op">;</span></span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a>    <span class="kw">explicit</span> LocalizedImageNode<span class="op">(</span>Domain<span class="op">*</span> domain<span class="op">,</span> string_view name<span class="op">)</span></span>
<span id="cb5-10"><a href="#cb5-10" aria-hidden="true" tabindex="-1"></a>        <span class="op">:</span> Image2D<span class="op">(</span>domain<span class="op">,</span> name<span class="op">)</span></span>
<span id="cb5-11"><a href="#cb5-11" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb5-12"><a href="#cb5-12" aria-hidden="true" tabindex="-1"></a>        addPropertyNotificationHandler<span class="op">(</span>ImageKeyProperty<span class="op">,</span></span>
<span id="cb5-13"><a href="#cb5-13" aria-hidden="true" tabindex="-1"></a>            bind<span class="op">(&amp;</span>LocalizedImageNode<span class="op">::</span>onImageKeyChanged<span class="op">,</span> <span class="kw">this</span><span class="op">,</span> placeholders<span class="op">::</span>_1<span class="op">));</span></span>
<span id="cb5-14"><a href="#cb5-14" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-15"><a href="#cb5-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-16"><a href="#cb5-16" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> initialize<span class="op">()</span> <span class="kw">override</span></span>
<span id="cb5-17"><a href="#cb5-17" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb5-18"><a href="#cb5-18" aria-hidden="true" tabindex="-1"></a>        Image2D<span class="op">::</span>initialize<span class="op">();</span></span>
<span id="cb5-19"><a href="#cb5-19" aria-hidden="true" tabindex="-1"></a>        updateLocalizedImage<span class="op">();</span></span>
<span id="cb5-20"><a href="#cb5-20" aria-hidden="true" tabindex="-1"></a>        registerLanguageChangeListener<span class="op">();</span></span>
<span id="cb5-21"><a href="#cb5-21" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-22"><a href="#cb5-22" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-23"><a href="#cb5-23" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb5-24"><a href="#cb5-24" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> onImageKeyChanged<span class="op">(</span>PropertyObject<span class="op">&amp;</span> object<span class="op">)</span></span>
<span id="cb5-25"><a href="#cb5-25" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb5-26"><a href="#cb5-26" aria-hidden="true" tabindex="-1"></a>        updateLocalizedImage<span class="op">();</span></span>
<span id="cb5-27"><a href="#cb5-27" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-28"><a href="#cb5-28" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-29"><a href="#cb5-29" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> updateLocalizedImage<span class="op">()</span></span>
<span id="cb5-30"><a href="#cb5-30" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb5-31"><a href="#cb5-31" aria-hidden="true" tabindex="-1"></a>        string imageKey <span class="op">=</span> getProperty<span class="op">(</span>ImageKeyProperty<span class="op">);</span></span>
<span id="cb5-32"><a href="#cb5-32" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>imageKey<span class="op">.</span>empty<span class="op">())</span> <span class="cf">return</span><span class="op">;</span></span>
<span id="cb5-33"><a href="#cb5-33" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-34"><a href="#cb5-34" aria-hidden="true" tabindex="-1"></a>        TextureSharedPtr localizedTexture <span class="op">=</span></span>
<span id="cb5-35"><a href="#cb5-35" aria-hidden="true" tabindex="-1"></a>            LocalizedImageManager<span class="op">::</span>getInstance<span class="op">().</span>getLocalizedTexture<span class="op">(</span>imageKey<span class="op">);</span></span>
<span id="cb5-36"><a href="#cb5-36" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-37"><a href="#cb5-37" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>localizedTexture<span class="op">)</span></span>
<span id="cb5-38"><a href="#cb5-38" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb5-39"><a href="#cb5-39" aria-hidden="true" tabindex="-1"></a>            setTexture<span class="op">(</span>localizedTexture<span class="op">);</span></span>
<span id="cb5-40"><a href="#cb5-40" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb5-41"><a href="#cb5-41" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-42"><a href="#cb5-42" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-43"><a href="#cb5-43" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> registerLanguageChangeListener<span class="op">()</span></span>
<span id="cb5-44"><a href="#cb5-44" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb5-45"><a href="#cb5-45" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 注册语言变化监听器</span></span>
<span id="cb5-46"><a href="#cb5-46" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 当语言变化时重新加载图像</span></span>
<span id="cb5-47"><a href="#cb5-47" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-48"><a href="#cb5-48" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb5-49"><a href="#cb5-49" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-50"><a href="#cb5-50" aria-hidden="true" tabindex="-1"></a>PropertyType<span class="op">&lt;</span>string<span class="op">&gt;</span> LocalizedImageNode<span class="op">::</span>ImageKeyProperty<span class="op">(</span></span>
<span id="cb5-51"><a href="#cb5-51" aria-hidden="true" tabindex="-1"></a>    kzMakeFixedString<span class="op">(</span><span class="st">&quot;LocalizedImageNode.ImageKey&quot;</span><span class="op">),</span> <span class="st">&quot;&quot;</span><span class="op">);</span></span></code></pre></div>
<h2 id="hmi-界面适配">HMI 界面适配</h2>
<h3 id="汽车仪表盘本地化">汽车仪表盘本地化</h3>
<div class="sourceCode" id="cb6"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 本地化汽车仪表盘</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> LocalizedCarDashboard <span class="op">:</span> <span class="kw">public</span> Node2D</span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb6-5"><a href="#cb6-5" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> Node2DSharedPtr create<span class="op">(</span>Domain<span class="op">*</span> domain<span class="op">,</span> string_view name<span class="op">)</span></span>
<span id="cb6-6"><a href="#cb6-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-7"><a href="#cb6-7" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> Node2DSharedPtr<span class="op">(</span><span class="kw">new</span> LocalizedCarDashboard<span class="op">(</span>domain<span class="op">,</span> name<span class="op">));</span></span>
<span id="cb6-8"><a href="#cb6-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-9"><a href="#cb6-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-10"><a href="#cb6-10" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb6-11"><a href="#cb6-11" aria-hidden="true" tabindex="-1"></a>    <span class="kw">explicit</span> LocalizedCarDashboard<span class="op">(</span>Domain<span class="op">*</span> domain<span class="op">,</span> string_view name<span class="op">)</span></span>
<span id="cb6-12"><a href="#cb6-12" aria-hidden="true" tabindex="-1"></a>        <span class="op">:</span> Node2D<span class="op">(</span>domain<span class="op">,</span> name<span class="op">)</span></span>
<span id="cb6-13"><a href="#cb6-13" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-14"><a href="#cb6-14" aria-hidden="true" tabindex="-1"></a>        initialize<span class="op">();</span></span>
<span id="cb6-15"><a href="#cb6-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-16"><a href="#cb6-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-17"><a href="#cb6-17" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> initialize<span class="op">()</span></span>
<span id="cb6-18"><a href="#cb6-18" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-19"><a href="#cb6-19" aria-hidden="true" tabindex="-1"></a>        createSpeedometer<span class="op">();</span></span>
<span id="cb6-20"><a href="#cb6-20" aria-hidden="true" tabindex="-1"></a>        createNavigationDisplay<span class="op">();</span></span>
<span id="cb6-21"><a href="#cb6-21" aria-hidden="true" tabindex="-1"></a>        createClimateControls<span class="op">();</span></span>
<span id="cb6-22"><a href="#cb6-22" aria-hidden="true" tabindex="-1"></a>        createMediaControls<span class="op">();</span></span>
<span id="cb6-23"><a href="#cb6-23" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-24"><a href="#cb6-24" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 监听语言变化</span></span>
<span id="cb6-25"><a href="#cb6-25" aria-hidden="true" tabindex="-1"></a>        registerLanguageChangeListener<span class="op">();</span></span>
<span id="cb6-26"><a href="#cb6-26" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-27"><a href="#cb6-27" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-28"><a href="#cb6-28" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb6-29"><a href="#cb6-29" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> createSpeedometer<span class="op">()</span></span>
<span id="cb6-30"><a href="#cb6-30" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-31"><a href="#cb6-31" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 速度表背景</span></span>
<span id="cb6-32"><a href="#cb6-32" aria-hidden="true" tabindex="-1"></a>        LocalizedImageNode<span class="op">*</span> speedometerBg <span class="op">=</span> <span class="kw">new</span> LocalizedImageNode<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;SpeedometerBackground&quot;</span><span class="op">);</span></span>
<span id="cb6-33"><a href="#cb6-33" aria-hidden="true" tabindex="-1"></a>        speedometerBg<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedImageNode<span class="op">::</span>ImageKeyProperty<span class="op">,</span> <span class="st">&quot;speedometer_bg&quot;</span><span class="op">);</span></span>
<span id="cb6-34"><a href="#cb6-34" aria-hidden="true" tabindex="-1"></a>        speedometerBg<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">200</span><span class="op">,</span> <span class="dv">200</span><span class="op">));</span></span>
<span id="cb6-35"><a href="#cb6-35" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span>speedometerBg<span class="op">);</span></span>
<span id="cb6-36"><a href="#cb6-36" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-37"><a href="#cb6-37" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 速度单位文本（km/h 或 mph）</span></span>
<span id="cb6-38"><a href="#cb6-38" aria-hidden="true" tabindex="-1"></a>        LocalizedTextNode<span class="op">*</span> speedUnit <span class="op">=</span> <span class="kw">new</span> LocalizedTextNode<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;SpeedUnit&quot;</span><span class="op">);</span></span>
<span id="cb6-39"><a href="#cb6-39" aria-hidden="true" tabindex="-1"></a>        speedUnit<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedTextNode<span class="op">::</span>TextKeyProperty<span class="op">,</span> <span class="st">&quot;speed_unit&quot;</span><span class="op">);</span></span>
<span id="cb6-40"><a href="#cb6-40" aria-hidden="true" tabindex="-1"></a>        speedUnit<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">200</span><span class="op">,</span> <span class="dv">350</span><span class="op">));</span></span>
<span id="cb6-41"><a href="#cb6-41" aria-hidden="true" tabindex="-1"></a>        speedUnit<span class="op">-&gt;</span>setHorizontalAlignment<span class="op">(</span>TextConcept<span class="op">::</span>HorizontalAlignment<span class="op">::</span>Center<span class="op">);</span></span>
<span id="cb6-42"><a href="#cb6-42" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span>speedUnit<span class="op">);</span></span>
<span id="cb6-43"><a href="#cb6-43" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-44"><a href="#cb6-44" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 当前速度显示</span></span>
<span id="cb6-45"><a href="#cb6-45" aria-hidden="true" tabindex="-1"></a>        LocalizedTextNode<span class="op">*</span> currentSpeed <span class="op">=</span> <span class="kw">new</span> LocalizedTextNode<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;CurrentSpeed&quot;</span><span class="op">);</span></span>
<span id="cb6-46"><a href="#cb6-46" aria-hidden="true" tabindex="-1"></a>        currentSpeed<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedTextNode<span class="op">::</span>TextKeyProperty<span class="op">,</span> <span class="st">&quot;current_speed&quot;</span><span class="op">);</span></span>
<span id="cb6-47"><a href="#cb6-47" aria-hidden="true" tabindex="-1"></a>        currentSpeed<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">200</span><span class="op">,</span> <span class="dv">250</span><span class="op">));</span></span>
<span id="cb6-48"><a href="#cb6-48" aria-hidden="true" tabindex="-1"></a>        currentSpeed<span class="op">-&gt;</span>setFontSize<span class="op">(</span><span class="dv">36</span><span class="op">);</span></span>
<span id="cb6-49"><a href="#cb6-49" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span>currentSpeed<span class="op">);</span></span>
<span id="cb6-50"><a href="#cb6-50" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-51"><a href="#cb6-51" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-52"><a href="#cb6-52" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> createNavigationDisplay<span class="op">()</span></span>
<span id="cb6-53"><a href="#cb6-53" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-54"><a href="#cb6-54" aria-hidden="true" tabindex="-1"></a>        Node2D<span class="op">*</span> navPanel <span class="op">=</span> Node2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;NavigationPanel&quot;</span><span class="op">);</span></span>
<span id="cb6-55"><a href="#cb6-55" aria-hidden="true" tabindex="-1"></a>        navPanel<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">500</span><span class="op">,</span> <span class="dv">100</span><span class="op">));</span></span>
<span id="cb6-56"><a href="#cb6-56" aria-hidden="true" tabindex="-1"></a>        navPanel<span class="op">-&gt;</span>setWidth<span class="op">(</span><span class="dv">300</span><span class="op">);</span></span>
<span id="cb6-57"><a href="#cb6-57" aria-hidden="true" tabindex="-1"></a>        navPanel<span class="op">-&gt;</span>setHeight<span class="op">(</span><span class="dv">200</span><span class="op">);</span></span>
<span id="cb6-58"><a href="#cb6-58" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-59"><a href="#cb6-59" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 导航指示图标</span></span>
<span id="cb6-60"><a href="#cb6-60" aria-hidden="true" tabindex="-1"></a>        LocalizedImageNode<span class="op">*</span> navIcon <span class="op">=</span> <span class="kw">new</span> LocalizedImageNode<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;NavigationIcon&quot;</span><span class="op">);</span></span>
<span id="cb6-61"><a href="#cb6-61" aria-hidden="true" tabindex="-1"></a>        navIcon<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedImageNode<span class="op">::</span>ImageKeyProperty<span class="op">,</span> <span class="st">&quot;nav_turn_right&quot;</span><span class="op">);</span></span>
<span id="cb6-62"><a href="#cb6-62" aria-hidden="true" tabindex="-1"></a>        navIcon<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">20</span><span class="op">,</span> <span class="dv">20</span><span class="op">));</span></span>
<span id="cb6-63"><a href="#cb6-63" aria-hidden="true" tabindex="-1"></a>        navPanel<span class="op">-&gt;</span>addChild<span class="op">(</span>navIcon<span class="op">);</span></span>
<span id="cb6-64"><a href="#cb6-64" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-65"><a href="#cb6-65" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 距离显示</span></span>
<span id="cb6-66"><a href="#cb6-66" aria-hidden="true" tabindex="-1"></a>        LocalizedTextNode<span class="op">*</span> distanceText <span class="op">=</span> <span class="kw">new</span> LocalizedTextNode<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;DistanceText&quot;</span><span class="op">);</span></span>
<span id="cb6-67"><a href="#cb6-67" aria-hidden="true" tabindex="-1"></a>        distanceText<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedTextNode<span class="op">::</span>TextKeyProperty<span class="op">,</span> <span class="st">&quot;distance_to_turn&quot;</span><span class="op">);</span></span>
<span id="cb6-68"><a href="#cb6-68" aria-hidden="true" tabindex="-1"></a>        StringVector args <span class="op">=</span> <span class="op">{</span><span class="st">&quot;500&quot;</span><span class="op">,</span> <span class="st">&quot;meters&quot;</span><span class="op">};</span></span>
<span id="cb6-69"><a href="#cb6-69" aria-hidden="true" tabindex="-1"></a>        distanceText<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedTextNode<span class="op">::</span>FormatArgumentsProperty<span class="op">,</span> args<span class="op">);</span></span>
<span id="cb6-70"><a href="#cb6-70" aria-hidden="true" tabindex="-1"></a>        distanceText<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">80</span><span class="op">,</span> <span class="dv">30</span><span class="op">));</span></span>
<span id="cb6-71"><a href="#cb6-71" aria-hidden="true" tabindex="-1"></a>        navPanel<span class="op">-&gt;</span>addChild<span class="op">(</span>distanceText<span class="op">);</span></span>
<span id="cb6-72"><a href="#cb6-72" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-73"><a href="#cb6-73" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 街道名称</span></span>
<span id="cb6-74"><a href="#cb6-74" aria-hidden="true" tabindex="-1"></a>        LocalizedTextNode<span class="op">*</span> streetName <span class="op">=</span> <span class="kw">new</span> LocalizedTextNode<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;StreetName&quot;</span><span class="op">);</span></span>
<span id="cb6-75"><a href="#cb6-75" aria-hidden="true" tabindex="-1"></a>        streetName<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedTextNode<span class="op">::</span>TextKeyProperty<span class="op">,</span> <span class="st">&quot;turn_onto_street&quot;</span><span class="op">);</span></span>
<span id="cb6-76"><a href="#cb6-76" aria-hidden="true" tabindex="-1"></a>        StringVector streetArgs <span class="op">=</span> <span class="op">{</span><span class="st">&quot;Main Street&quot;</span><span class="op">};</span></span>
<span id="cb6-77"><a href="#cb6-77" aria-hidden="true" tabindex="-1"></a>        streetName<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedTextNode<span class="op">::</span>FormatArgumentsProperty<span class="op">,</span> streetArgs<span class="op">);</span></span>
<span id="cb6-78"><a href="#cb6-78" aria-hidden="true" tabindex="-1"></a>        streetName<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">20</span><span class="op">,</span> <span class="dv">80</span><span class="op">));</span></span>
<span id="cb6-79"><a href="#cb6-79" aria-hidden="true" tabindex="-1"></a>        navPanel<span class="op">-&gt;</span>addChild<span class="op">(</span>streetName<span class="op">);</span></span>
<span id="cb6-80"><a href="#cb6-80" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-81"><a href="#cb6-81" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span>navPanel<span class="op">);</span></span>
<span id="cb6-82"><a href="#cb6-82" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-83"><a href="#cb6-83" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-84"><a href="#cb6-84" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> createClimateControls<span class="op">()</span></span>
<span id="cb6-85"><a href="#cb6-85" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-86"><a href="#cb6-86" aria-hidden="true" tabindex="-1"></a>        Node2D<span class="op">*</span> climatePanel <span class="op">=</span> Node2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;ClimatePanel&quot;</span><span class="op">);</span></span>
<span id="cb6-87"><a href="#cb6-87" aria-hidden="true" tabindex="-1"></a>        climatePanel<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">50</span><span class="op">,</span> <span class="dv">400</span><span class="op">));</span></span>
<span id="cb6-88"><a href="#cb6-88" aria-hidden="true" tabindex="-1"></a>        climatePanel<span class="op">-&gt;</span>setWidth<span class="op">(</span><span class="dv">250</span><span class="op">);</span></span>
<span id="cb6-89"><a href="#cb6-89" aria-hidden="true" tabindex="-1"></a>        climatePanel<span class="op">-&gt;</span>setHeight<span class="op">(</span><span class="dv">150</span><span class="op">);</span></span>
<span id="cb6-90"><a href="#cb6-90" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-91"><a href="#cb6-91" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 温度显示</span></span>
<span id="cb6-92"><a href="#cb6-92" aria-hidden="true" tabindex="-1"></a>        LocalizedTextNode<span class="op">*</span> tempDisplay <span class="op">=</span> <span class="kw">new</span> LocalizedTextNode<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;TemperatureDisplay&quot;</span><span class="op">);</span></span>
<span id="cb6-93"><a href="#cb6-93" aria-hidden="true" tabindex="-1"></a>        tempDisplay<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedTextNode<span class="op">::</span>TextKeyProperty<span class="op">,</span> <span class="st">&quot;temperature_display&quot;</span><span class="op">);</span></span>
<span id="cb6-94"><a href="#cb6-94" aria-hidden="true" tabindex="-1"></a>        StringVector tempArgs <span class="op">=</span> <span class="op">{</span><span class="st">&quot;22&quot;</span><span class="op">,</span> <span class="st">&quot;°C&quot;</span><span class="op">};</span></span>
<span id="cb6-95"><a href="#cb6-95" aria-hidden="true" tabindex="-1"></a>        tempDisplay<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedTextNode<span class="op">::</span>FormatArgumentsProperty<span class="op">,</span> tempArgs<span class="op">);</span></span>
<span id="cb6-96"><a href="#cb6-96" aria-hidden="true" tabindex="-1"></a>        tempDisplay<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">20</span><span class="op">,</span> <span class="dv">20</span><span class="op">));</span></span>
<span id="cb6-97"><a href="#cb6-97" aria-hidden="true" tabindex="-1"></a>        tempDisplay<span class="op">-&gt;</span>setFontSize<span class="op">(</span><span class="dv">24</span><span class="op">);</span></span>
<span id="cb6-98"><a href="#cb6-98" aria-hidden="true" tabindex="-1"></a>        climatePanel<span class="op">-&gt;</span>addChild<span class="op">(</span>tempDisplay<span class="op">);</span></span>
<span id="cb6-99"><a href="#cb6-99" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-100"><a href="#cb6-100" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 气候控制按钮</span></span>
<span id="cb6-101"><a href="#cb6-101" aria-hidden="true" tabindex="-1"></a>        createClimateButton<span class="op">(</span>climatePanel<span class="op">,</span> <span class="st">&quot;auto_climate&quot;</span><span class="op">,</span> Vector2<span class="op">(</span><span class="dv">20</span><span class="op">,</span> <span class="dv">60</span><span class="op">));</span></span>
<span id="cb6-102"><a href="#cb6-102" aria-hidden="true" tabindex="-1"></a>        createClimateButton<span class="op">(</span>climatePanel<span class="op">,</span> <span class="st">&quot;ac_button&quot;</span><span class="op">,</span> Vector2<span class="op">(</span><span class="dv">80</span><span class="op">,</span> <span class="dv">60</span><span class="op">));</span></span>
<span id="cb6-103"><a href="#cb6-103" aria-hidden="true" tabindex="-1"></a>        createClimateButton<span class="op">(</span>climatePanel<span class="op">,</span> <span class="st">&quot;heat_button&quot;</span><span class="op">,</span> Vector2<span class="op">(</span><span class="dv">140</span><span class="op">,</span> <span class="dv">60</span><span class="op">));</span></span>
<span id="cb6-104"><a href="#cb6-104" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-105"><a href="#cb6-105" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span>climatePanel<span class="op">);</span></span>
<span id="cb6-106"><a href="#cb6-106" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-107"><a href="#cb6-107" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-108"><a href="#cb6-108" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> createClimateButton<span class="op">(</span>Node2D<span class="op">*</span> parent<span class="op">,</span> <span class="at">const</span> string<span class="op">&amp;</span> buttonKey<span class="op">,</span> <span class="at">const</span> Vector2<span class="op">&amp;</span> position<span class="op">)</span></span>
<span id="cb6-109"><a href="#cb6-109" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-110"><a href="#cb6-110" aria-hidden="true" tabindex="-1"></a>        Node2D<span class="op">*</span> button <span class="op">=</span> Node2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> buttonKey <span class="op">+</span> <span class="st">&quot;_button&quot;</span><span class="op">);</span></span>
<span id="cb6-111"><a href="#cb6-111" aria-hidden="true" tabindex="-1"></a>        button<span class="op">-&gt;</span>setTranslation<span class="op">(</span>position<span class="op">);</span></span>
<span id="cb6-112"><a href="#cb6-112" aria-hidden="true" tabindex="-1"></a>        button<span class="op">-&gt;</span>setWidth<span class="op">(</span><span class="dv">50</span><span class="op">);</span></span>
<span id="cb6-113"><a href="#cb6-113" aria-hidden="true" tabindex="-1"></a>        button<span class="op">-&gt;</span>setHeight<span class="op">(</span><span class="dv">40</span><span class="op">);</span></span>
<span id="cb6-114"><a href="#cb6-114" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-115"><a href="#cb6-115" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 按钮图标</span></span>
<span id="cb6-116"><a href="#cb6-116" aria-hidden="true" tabindex="-1"></a>        LocalizedImageNode<span class="op">*</span> icon <span class="op">=</span> <span class="kw">new</span> LocalizedImageNode<span class="op">(</span>getDomain<span class="op">(),</span> buttonKey <span class="op">+</span> <span class="st">&quot;_icon&quot;</span><span class="op">);</span></span>
<span id="cb6-117"><a href="#cb6-117" aria-hidden="true" tabindex="-1"></a>        icon<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedImageNode<span class="op">::</span>ImageKeyProperty<span class="op">,</span> buttonKey<span class="op">);</span></span>
<span id="cb6-118"><a href="#cb6-118" aria-hidden="true" tabindex="-1"></a>        icon<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">10</span><span class="op">,</span> <span class="dv">5</span><span class="op">));</span></span>
<span id="cb6-119"><a href="#cb6-119" aria-hidden="true" tabindex="-1"></a>        button<span class="op">-&gt;</span>addChild<span class="op">(</span>icon<span class="op">);</span></span>
<span id="cb6-120"><a href="#cb6-120" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-121"><a href="#cb6-121" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 按钮文本</span></span>
<span id="cb6-122"><a href="#cb6-122" aria-hidden="true" tabindex="-1"></a>        LocalizedTextNode<span class="op">*</span> label <span class="op">=</span> <span class="kw">new</span> LocalizedTextNode<span class="op">(</span>getDomain<span class="op">(),</span> buttonKey <span class="op">+</span> <span class="st">&quot;_label&quot;</span><span class="op">);</span></span>
<span id="cb6-123"><a href="#cb6-123" aria-hidden="true" tabindex="-1"></a>        label<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedTextNode<span class="op">::</span>TextKeyProperty<span class="op">,</span> buttonKey<span class="op">);</span></span>
<span id="cb6-124"><a href="#cb6-124" aria-hidden="true" tabindex="-1"></a>        label<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">25</span><span class="op">,</span> <span class="dv">25</span><span class="op">));</span></span>
<span id="cb6-125"><a href="#cb6-125" aria-hidden="true" tabindex="-1"></a>        label<span class="op">-&gt;</span>setFontSize<span class="op">(</span><span class="dv">10</span><span class="op">);</span></span>
<span id="cb6-126"><a href="#cb6-126" aria-hidden="true" tabindex="-1"></a>        label<span class="op">-&gt;</span>setHorizontalAlignment<span class="op">(</span>TextConcept<span class="op">::</span>HorizontalAlignment<span class="op">::</span>Center<span class="op">);</span></span>
<span id="cb6-127"><a href="#cb6-127" aria-hidden="true" tabindex="-1"></a>        button<span class="op">-&gt;</span>addChild<span class="op">(</span>label<span class="op">);</span></span>
<span id="cb6-128"><a href="#cb6-128" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-129"><a href="#cb6-129" aria-hidden="true" tabindex="-1"></a>        parent<span class="op">-&gt;</span>addChild<span class="op">(</span>button<span class="op">);</span></span>
<span id="cb6-130"><a href="#cb6-130" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-131"><a href="#cb6-131" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-132"><a href="#cb6-132" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> createMediaControls<span class="op">()</span></span>
<span id="cb6-133"><a href="#cb6-133" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-134"><a href="#cb6-134" aria-hidden="true" tabindex="-1"></a>        Node2D<span class="op">*</span> mediaPanel <span class="op">=</span> Node2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;MediaPanel&quot;</span><span class="op">);</span></span>
<span id="cb6-135"><a href="#cb6-135" aria-hidden="true" tabindex="-1"></a>        mediaPanel<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">350</span><span class="op">,</span> <span class="dv">400</span><span class="op">));</span></span>
<span id="cb6-136"><a href="#cb6-136" aria-hidden="true" tabindex="-1"></a>        mediaPanel<span class="op">-&gt;</span>setWidth<span class="op">(</span><span class="dv">300</span><span class="op">);</span></span>
<span id="cb6-137"><a href="#cb6-137" aria-hidden="true" tabindex="-1"></a>        mediaPanel<span class="op">-&gt;</span>setHeight<span class="op">(</span><span class="dv">150</span><span class="op">);</span></span>
<span id="cb6-138"><a href="#cb6-138" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-139"><a href="#cb6-139" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 当前播放信息</span></span>
<span id="cb6-140"><a href="#cb6-140" aria-hidden="true" tabindex="-1"></a>        LocalizedTextNode<span class="op">*</span> nowPlaying <span class="op">=</span> <span class="kw">new</span> LocalizedTextNode<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;NowPlaying&quot;</span><span class="op">);</span></span>
<span id="cb6-141"><a href="#cb6-141" aria-hidden="true" tabindex="-1"></a>        nowPlaying<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedTextNode<span class="op">::</span>TextKeyProperty<span class="op">,</span> <span class="st">&quot;now_playing&quot;</span><span class="op">);</span></span>
<span id="cb6-142"><a href="#cb6-142" aria-hidden="true" tabindex="-1"></a>        nowPlaying<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">20</span><span class="op">,</span> <span class="dv">20</span><span class="op">));</span></span>
<span id="cb6-143"><a href="#cb6-143" aria-hidden="true" tabindex="-1"></a>        nowPlaying<span class="op">-&gt;</span>setFontSize<span class="op">(</span><span class="dv">14</span><span class="op">);</span></span>
<span id="cb6-144"><a href="#cb6-144" aria-hidden="true" tabindex="-1"></a>        mediaPanel<span class="op">-&gt;</span>addChild<span class="op">(</span>nowPlaying<span class="op">);</span></span>
<span id="cb6-145"><a href="#cb6-145" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-146"><a href="#cb6-146" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 歌曲信息（这里可能不需要本地化，但格式需要适配）</span></span>
<span id="cb6-147"><a href="#cb6-147" aria-hidden="true" tabindex="-1"></a>        Text2D<span class="op">*</span> songInfo <span class="op">=</span> Text2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;SongInfo&quot;</span><span class="op">);</span></span>
<span id="cb6-148"><a href="#cb6-148" aria-hidden="true" tabindex="-1"></a>        songInfo<span class="op">-&gt;</span>setText<span class="op">(</span><span class="st">&quot;Artist - Song Title&quot;</span><span class="op">);</span></span>
<span id="cb6-149"><a href="#cb6-149" aria-hidden="true" tabindex="-1"></a>        songInfo<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">20</span><span class="op">,</span> <span class="dv">40</span><span class="op">));</span></span>
<span id="cb6-150"><a href="#cb6-150" aria-hidden="true" tabindex="-1"></a>        songInfo<span class="op">-&gt;</span>setFontSize<span class="op">(</span><span class="dv">16</span><span class="op">);</span></span>
<span id="cb6-151"><a href="#cb6-151" aria-hidden="true" tabindex="-1"></a>        mediaPanel<span class="op">-&gt;</span>addChild<span class="op">(</span>songInfo<span class="op">);</span></span>
<span id="cb6-152"><a href="#cb6-152" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-153"><a href="#cb6-153" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 媒体控制按钮</span></span>
<span id="cb6-154"><a href="#cb6-154" aria-hidden="true" tabindex="-1"></a>        createMediaButton<span class="op">(</span>mediaPanel<span class="op">,</span> <span class="st">&quot;prev_track&quot;</span><span class="op">,</span> Vector2<span class="op">(</span><span class="dv">50</span><span class="op">,</span> <span class="dv">80</span><span class="op">));</span></span>
<span id="cb6-155"><a href="#cb6-155" aria-hidden="true" tabindex="-1"></a>        createMediaButton<span class="op">(</span>mediaPanel<span class="op">,</span> <span class="st">&quot;play_pause&quot;</span><span class="op">,</span> Vector2<span class="op">(</span><span class="dv">100</span><span class="op">,</span> <span class="dv">80</span><span class="op">));</span></span>
<span id="cb6-156"><a href="#cb6-156" aria-hidden="true" tabindex="-1"></a>        createMediaButton<span class="op">(</span>mediaPanel<span class="op">,</span> <span class="st">&quot;next_track&quot;</span><span class="op">,</span> Vector2<span class="op">(</span><span class="dv">150</span><span class="op">,</span> <span class="dv">80</span><span class="op">));</span></span>
<span id="cb6-157"><a href="#cb6-157" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-158"><a href="#cb6-158" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span>mediaPanel<span class="op">);</span></span>
<span id="cb6-159"><a href="#cb6-159" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-160"><a href="#cb6-160" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-161"><a href="#cb6-161" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> createMediaButton<span class="op">(</span>Node2D<span class="op">*</span> parent<span class="op">,</span> <span class="at">const</span> string<span class="op">&amp;</span> buttonKey<span class="op">,</span> <span class="at">const</span> Vector2<span class="op">&amp;</span> position<span class="op">)</span></span>
<span id="cb6-162"><a href="#cb6-162" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-163"><a href="#cb6-163" aria-hidden="true" tabindex="-1"></a>        Node2D<span class="op">*</span> button <span class="op">=</span> Node2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> buttonKey <span class="op">+</span> <span class="st">&quot;_button&quot;</span><span class="op">);</span></span>
<span id="cb6-164"><a href="#cb6-164" aria-hidden="true" tabindex="-1"></a>        button<span class="op">-&gt;</span>setTranslation<span class="op">(</span>position<span class="op">);</span></span>
<span id="cb6-165"><a href="#cb6-165" aria-hidden="true" tabindex="-1"></a>        button<span class="op">-&gt;</span>setWidth<span class="op">(</span><span class="dv">40</span><span class="op">);</span></span>
<span id="cb6-166"><a href="#cb6-166" aria-hidden="true" tabindex="-1"></a>        button<span class="op">-&gt;</span>setHeight<span class="op">(</span><span class="dv">40</span><span class="op">);</span></span>
<span id="cb6-167"><a href="#cb6-167" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-168"><a href="#cb6-168" aria-hidden="true" tabindex="-1"></a>        LocalizedImageNode<span class="op">*</span> icon <span class="op">=</span> <span class="kw">new</span> LocalizedImageNode<span class="op">(</span>getDomain<span class="op">(),</span> buttonKey <span class="op">+</span> <span class="st">&quot;_icon&quot;</span><span class="op">);</span></span>
<span id="cb6-169"><a href="#cb6-169" aria-hidden="true" tabindex="-1"></a>        icon<span class="op">-&gt;</span>setProperty<span class="op">(</span>LocalizedImageNode<span class="op">::</span>ImageKeyProperty<span class="op">,</span> buttonKey<span class="op">);</span></span>
<span id="cb6-170"><a href="#cb6-170" aria-hidden="true" tabindex="-1"></a>        icon<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">5</span><span class="op">,</span> <span class="dv">5</span><span class="op">));</span></span>
<span id="cb6-171"><a href="#cb6-171" aria-hidden="true" tabindex="-1"></a>        button<span class="op">-&gt;</span>addChild<span class="op">(</span>icon<span class="op">);</span></span>
<span id="cb6-172"><a href="#cb6-172" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-173"><a href="#cb6-173" aria-hidden="true" tabindex="-1"></a>        parent<span class="op">-&gt;</span>addChild<span class="op">(</span>button<span class="op">);</span></span>
<span id="cb6-174"><a href="#cb6-174" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-175"><a href="#cb6-175" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-176"><a href="#cb6-176" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> registerLanguageChangeListener<span class="op">()</span></span>
<span id="cb6-177"><a href="#cb6-177" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-178"><a href="#cb6-178" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 注册语言变化监听器</span></span>
<span id="cb6-179"><a href="#cb6-179" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 当语言变化时，所有本地化组件会自动更新</span></span>
<span id="cb6-180"><a href="#cb6-180" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-181"><a href="#cb6-181" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="汽车行业特殊需求">汽车行业特殊需求</h2>
<h3 id="安全关键信息本地化">安全关键信息本地化</h3>
<div class="sourceCode" id="cb7"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 安全关键信息管理器</span></span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> SafetyCriticalMessageManager</span>
<span id="cb7-3"><a href="#cb7-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb7-4"><a href="#cb7-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb7-5"><a href="#cb7-5" aria-hidden="true" tabindex="-1"></a>    <span class="kw">enum</span> <span class="kw">class</span> MessagePriority</span>
<span id="cb7-6"><a href="#cb7-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-7"><a href="#cb7-7" aria-hidden="true" tabindex="-1"></a>        Low<span class="op">,</span></span>
<span id="cb7-8"><a href="#cb7-8" aria-hidden="true" tabindex="-1"></a>        Medium<span class="op">,</span></span>
<span id="cb7-9"><a href="#cb7-9" aria-hidden="true" tabindex="-1"></a>        High<span class="op">,</span></span>
<span id="cb7-10"><a href="#cb7-10" aria-hidden="true" tabindex="-1"></a>        Critical</span>
<span id="cb7-11"><a href="#cb7-11" aria-hidden="true" tabindex="-1"></a>    <span class="op">};</span></span>
<span id="cb7-12"><a href="#cb7-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-13"><a href="#cb7-13" aria-hidden="true" tabindex="-1"></a>    <span class="kw">struct</span> SafetyMessage</span>
<span id="cb7-14"><a href="#cb7-14" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-15"><a href="#cb7-15" aria-hidden="true" tabindex="-1"></a>        string messageKey<span class="op">;</span></span>
<span id="cb7-16"><a href="#cb7-16" aria-hidden="true" tabindex="-1"></a>        MessagePriority priority<span class="op">;</span></span>
<span id="cb7-17"><a href="#cb7-17" aria-hidden="true" tabindex="-1"></a>        <span class="dt">bool</span> requiresAcknowledgment<span class="op">;</span></span>
<span id="cb7-18"><a href="#cb7-18" aria-hidden="true" tabindex="-1"></a>        chrono<span class="op">::</span>milliseconds displayDuration<span class="op">;</span></span>
<span id="cb7-19"><a href="#cb7-19" aria-hidden="true" tabindex="-1"></a>        string iconKey<span class="op">;</span></span>
<span id="cb7-20"><a href="#cb7-20" aria-hidden="true" tabindex="-1"></a>    <span class="op">};</span></span>
<span id="cb7-21"><a href="#cb7-21" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-22"><a href="#cb7-22" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> showSafetyMessage<span class="op">(</span><span class="at">const</span> SafetyMessage<span class="op">&amp;</span> message<span class="op">)</span></span>
<span id="cb7-23"><a href="#cb7-23" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-24"><a href="#cb7-24" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 安全消息始终使用驾驶员首选语言</span></span>
<span id="cb7-25"><a href="#cb7-25" aria-hidden="true" tabindex="-1"></a>        string driverLanguage <span class="op">=</span> getDriverPreferredLanguage<span class="op">();</span></span>
<span id="cb7-26"><a href="#cb7-26" aria-hidden="true" tabindex="-1"></a>        string previousLanguage <span class="op">=</span> getCurrentLanguage<span class="op">();</span></span>
<span id="cb7-27"><a href="#cb7-27" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-28"><a href="#cb7-28" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 临时切换到驾驶员语言</span></span>
<span id="cb7-29"><a href="#cb7-29" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>driverLanguage <span class="op">!=</span> previousLanguage<span class="op">)</span></span>
<span id="cb7-30"><a href="#cb7-30" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb7-31"><a href="#cb7-31" aria-hidden="true" tabindex="-1"></a>            setLanguage<span class="op">(</span>driverLanguage<span class="op">);</span></span>
<span id="cb7-32"><a href="#cb7-32" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb7-33"><a href="#cb7-33" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-34"><a href="#cb7-34" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 显示安全消息</span></span>
<span id="cb7-35"><a href="#cb7-35" aria-hidden="true" tabindex="-1"></a>        displayMessage<span class="op">(</span>message<span class="op">);</span></span>
<span id="cb7-36"><a href="#cb7-36" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-37"><a href="#cb7-37" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 恢复之前的语言设置</span></span>
<span id="cb7-38"><a href="#cb7-38" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>driverLanguage <span class="op">!=</span> previousLanguage<span class="op">)</span></span>
<span id="cb7-39"><a href="#cb7-39" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb7-40"><a href="#cb7-40" aria-hidden="true" tabindex="-1"></a>            setLanguage<span class="op">(</span>previousLanguage<span class="op">);</span></span>
<span id="cb7-41"><a href="#cb7-41" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb7-42"><a href="#cb7-42" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-43"><a href="#cb7-43" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-44"><a href="#cb7-44" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> registerSafetyMessages<span class="op">()</span></span>
<span id="cb7-45"><a href="#cb7-45" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-46"><a href="#cb7-46" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 注册各种安全消息</span></span>
<span id="cb7-47"><a href="#cb7-47" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_safetyMessages</span><span class="op">[</span><span class="st">&quot;engine_warning&quot;</span><span class="op">]</span> <span class="op">=</span> <span class="op">{</span></span>
<span id="cb7-48"><a href="#cb7-48" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;engine_warning&quot;</span><span class="op">,</span></span>
<span id="cb7-49"><a href="#cb7-49" aria-hidden="true" tabindex="-1"></a>            MessagePriority<span class="op">::</span>Critical<span class="op">,</span></span>
<span id="cb7-50"><a href="#cb7-50" aria-hidden="true" tabindex="-1"></a>            <span class="kw">true</span><span class="op">,</span></span>
<span id="cb7-51"><a href="#cb7-51" aria-hidden="true" tabindex="-1"></a>            chrono<span class="op">::</span>milliseconds<span class="op">(</span><span class="dv">0</span><span class="op">),</span> <span class="co">// 持续显示直到确认</span></span>
<span id="cb7-52"><a href="#cb7-52" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;warning_engine&quot;</span></span>
<span id="cb7-53"><a href="#cb7-53" aria-hidden="true" tabindex="-1"></a>        <span class="op">};</span></span>
<span id="cb7-54"><a href="#cb7-54" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-55"><a href="#cb7-55" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_safetyMessages</span><span class="op">[</span><span class="st">&quot;low_fuel&quot;</span><span class="op">]</span> <span class="op">=</span> <span class="op">{</span></span>
<span id="cb7-56"><a href="#cb7-56" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;low_fuel_warning&quot;</span><span class="op">,</span></span>
<span id="cb7-57"><a href="#cb7-57" aria-hidden="true" tabindex="-1"></a>            MessagePriority<span class="op">::</span>High<span class="op">,</span></span>
<span id="cb7-58"><a href="#cb7-58" aria-hidden="true" tabindex="-1"></a>            <span class="kw">false</span><span class="op">,</span></span>
<span id="cb7-59"><a href="#cb7-59" aria-hidden="true" tabindex="-1"></a>            chrono<span class="op">::</span>milliseconds<span class="op">(</span><span class="dv">5000</span><span class="op">),</span></span>
<span id="cb7-60"><a href="#cb7-60" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;warning_fuel&quot;</span></span>
<span id="cb7-61"><a href="#cb7-61" aria-hidden="true" tabindex="-1"></a>        <span class="op">};</span></span>
<span id="cb7-62"><a href="#cb7-62" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-63"><a href="#cb7-63" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_safetyMessages</span><span class="op">[</span><span class="st">&quot;seatbelt_reminder&quot;</span><span class="op">]</span> <span class="op">=</span> <span class="op">{</span></span>
<span id="cb7-64"><a href="#cb7-64" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;seatbelt_reminder&quot;</span><span class="op">,</span></span>
<span id="cb7-65"><a href="#cb7-65" aria-hidden="true" tabindex="-1"></a>            MessagePriority<span class="op">::</span>Medium<span class="op">,</span></span>
<span id="cb7-66"><a href="#cb7-66" aria-hidden="true" tabindex="-1"></a>            <span class="kw">false</span><span class="op">,</span></span>
<span id="cb7-67"><a href="#cb7-67" aria-hidden="true" tabindex="-1"></a>            chrono<span class="op">::</span>milliseconds<span class="op">(</span><span class="dv">3000</span><span class="op">),</span></span>
<span id="cb7-68"><a href="#cb7-68" aria-hidden="true" tabindex="-1"></a>            <span class="st">&quot;warning_seatbelt&quot;</span></span>
<span id="cb7-69"><a href="#cb7-69" aria-hidden="true" tabindex="-1"></a>        <span class="op">};</span></span>
<span id="cb7-70"><a href="#cb7-70" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-71"><a href="#cb7-71" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-72"><a href="#cb7-72" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb7-73"><a href="#cb7-73" aria-hidden="true" tabindex="-1"></a>    map<span class="op">&lt;</span>string<span class="op">,</span> SafetyMessage<span class="op">&gt;</span> <span class="va">m_safetyMessages</span><span class="op">;</span></span>
<span id="cb7-74"><a href="#cb7-74" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-75"><a href="#cb7-75" aria-hidden="true" tabindex="-1"></a>    string getDriverPreferredLanguage<span class="op">()</span></span>
<span id="cb7-76"><a href="#cb7-76" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-77"><a href="#cb7-77" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 从驾驶员配置文件获取首选语言</span></span>
<span id="cb7-78"><a href="#cb7-78" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="st">&quot;en-US&quot;</span><span class="op">;</span> <span class="co">// 示例</span></span>
<span id="cb7-79"><a href="#cb7-79" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-80"><a href="#cb7-80" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-81"><a href="#cb7-81" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> displayMessage<span class="op">(</span><span class="at">const</span> SafetyMessage<span class="op">&amp;</span> message<span class="op">)</span></span>
<span id="cb7-82"><a href="#cb7-82" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-83"><a href="#cb7-83" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 实现安全消息显示逻辑</span></span>
<span id="cb7-84"><a href="#cb7-84" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 包括图标、文本、优先级处理等</span></span>
<span id="cb7-85"><a href="#cb7-85" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-86"><a href="#cb7-86" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="法规遵循和标准化">法规遵循和标准化</h3>
<div class="sourceCode" id="cb8"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 汽车法规遵循管理器</span></span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> AutomotiveComplianceManager</span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb8-4"><a href="#cb8-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb8-5"><a href="#cb8-5" aria-hidden="true" tabindex="-1"></a>    <span class="kw">struct</span> RegionalRequirements</span>
<span id="cb8-6"><a href="#cb8-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-7"><a href="#cb8-7" aria-hidden="true" tabindex="-1"></a>        string region<span class="op">;</span></span>
<span id="cb8-8"><a href="#cb8-8" aria-hidden="true" tabindex="-1"></a>        vector<span class="op">&lt;</span>string<span class="op">&gt;</span> requiredLanguages<span class="op">;</span></span>
<span id="cb8-9"><a href="#cb8-9" aria-hidden="true" tabindex="-1"></a>        map<span class="op">&lt;</span>string<span class="op">,</span> string<span class="op">&gt;</span> unitPreferences<span class="op">;</span> <span class="co">// 速度、温度等单位</span></span>
<span id="cb8-10"><a href="#cb8-10" aria-hidden="true" tabindex="-1"></a>        <span class="dt">bool</span> requiresRightHandDrive<span class="op">;</span></span>
<span id="cb8-11"><a href="#cb8-11" aria-hidden="true" tabindex="-1"></a>        vector<span class="op">&lt;</span>string<span class="op">&gt;</span> mandatoryWarnings<span class="op">;</span></span>
<span id="cb8-12"><a href="#cb8-12" aria-hidden="true" tabindex="-1"></a>    <span class="op">};</span></span>
<span id="cb8-13"><a href="#cb8-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-14"><a href="#cb8-14" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> configureForRegion<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> regionCode<span class="op">)</span></span>
<span id="cb8-15"><a href="#cb8-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-16"><a href="#cb8-16" aria-hidden="true" tabindex="-1"></a>        RegionalRequirements requirements <span class="op">=</span> getRegionalRequirements<span class="op">(</span>regionCode<span class="op">);</span></span>
<span id="cb8-17"><a href="#cb8-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-18"><a href="#cb8-18" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 配置语言支持</span></span>
<span id="cb8-19"><a href="#cb8-19" aria-hidden="true" tabindex="-1"></a>        configureLanguageSupport<span class="op">(</span>requirements<span class="op">.</span>requiredLanguages<span class="op">);</span></span>
<span id="cb8-20"><a href="#cb8-20" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-21"><a href="#cb8-21" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 配置单位系统</span></span>
<span id="cb8-22"><a href="#cb8-22" aria-hidden="true" tabindex="-1"></a>        configureUnits<span class="op">(</span>requirements<span class="op">.</span>unitPreferences<span class="op">);</span></span>
<span id="cb8-23"><a href="#cb8-23" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-24"><a href="#cb8-24" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 配置驾驶方向</span></span>
<span id="cb8-25"><a href="#cb8-25" aria-hidden="true" tabindex="-1"></a>        configureDrivingSide<span class="op">(</span>requirements<span class="op">.</span>requiresRightHandDrive<span class="op">);</span></span>
<span id="cb8-26"><a href="#cb8-26" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-27"><a href="#cb8-27" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 配置强制警告</span></span>
<span id="cb8-28"><a href="#cb8-28" aria-hidden="true" tabindex="-1"></a>        configureMandatoryWarnings<span class="op">(</span>requirements<span class="op">.</span>mandatoryWarnings<span class="op">);</span></span>
<span id="cb8-29"><a href="#cb8-29" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-30"><a href="#cb8-30" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-31"><a href="#cb8-31" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb8-32"><a href="#cb8-32" aria-hidden="true" tabindex="-1"></a>    RegionalRequirements getRegionalRequirements<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> regionCode<span class="op">)</span></span>
<span id="cb8-33"><a href="#cb8-33" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-34"><a href="#cb8-34" aria-hidden="true" tabindex="-1"></a>        RegionalRequirements requirements<span class="op">;</span></span>
<span id="cb8-35"><a href="#cb8-35" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-36"><a href="#cb8-36" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>regionCode <span class="op">==</span> <span class="st">&quot;EU&quot;</span><span class="op">)</span></span>
<span id="cb8-37"><a href="#cb8-37" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb8-38"><a href="#cb8-38" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>region <span class="op">=</span> <span class="st">&quot;European Union&quot;</span><span class="op">;</span></span>
<span id="cb8-39"><a href="#cb8-39" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>requiredLanguages <span class="op">=</span> <span class="op">{</span><span class="st">&quot;en-GB&quot;</span><span class="op">,</span> <span class="st">&quot;de-DE&quot;</span><span class="op">,</span> <span class="st">&quot;fr-FR&quot;</span><span class="op">,</span> <span class="st">&quot;es-ES&quot;</span><span class="op">,</span> <span class="st">&quot;it-IT&quot;</span><span class="op">};</span></span>
<span id="cb8-40"><a href="#cb8-40" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>unitPreferences<span class="op">[</span><span class="st">&quot;speed&quot;</span><span class="op">]</span> <span class="op">=</span> <span class="st">&quot;km/h&quot;</span><span class="op">;</span></span>
<span id="cb8-41"><a href="#cb8-41" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>unitPreferences<span class="op">[</span><span class="st">&quot;temperature&quot;</span><span class="op">]</span> <span class="op">=</span> <span class="st">&quot;celsius&quot;</span><span class="op">;</span></span>
<span id="cb8-42"><a href="#cb8-42" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>requiresRightHandDrive <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb8-43"><a href="#cb8-43" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>mandatoryWarnings <span class="op">=</span> <span class="op">{</span><span class="st">&quot;seatbelt_warning&quot;</span><span class="op">,</span> <span class="st">&quot;speed_limit_warning&quot;</span><span class="op">};</span></span>
<span id="cb8-44"><a href="#cb8-44" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb8-45"><a href="#cb8-45" aria-hidden="true" tabindex="-1"></a>        <span class="cf">else</span> <span class="cf">if</span> <span class="op">(</span>regionCode <span class="op">==</span> <span class="st">&quot;US&quot;</span><span class="op">)</span></span>
<span id="cb8-46"><a href="#cb8-46" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb8-47"><a href="#cb8-47" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>region <span class="op">=</span> <span class="st">&quot;United States&quot;</span><span class="op">;</span></span>
<span id="cb8-48"><a href="#cb8-48" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>requiredLanguages <span class="op">=</span> <span class="op">{</span><span class="st">&quot;en-US&quot;</span><span class="op">,</span> <span class="st">&quot;es-US&quot;</span><span class="op">};</span></span>
<span id="cb8-49"><a href="#cb8-49" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>unitPreferences<span class="op">[</span><span class="st">&quot;speed&quot;</span><span class="op">]</span> <span class="op">=</span> <span class="st">&quot;mph&quot;</span><span class="op">;</span></span>
<span id="cb8-50"><a href="#cb8-50" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>unitPreferences<span class="op">[</span><span class="st">&quot;temperature&quot;</span><span class="op">]</span> <span class="op">=</span> <span class="st">&quot;fahrenheit&quot;</span><span class="op">;</span></span>
<span id="cb8-51"><a href="#cb8-51" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>requiresRightHandDrive <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb8-52"><a href="#cb8-52" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>mandatoryWarnings <span class="op">=</span> <span class="op">{</span><span class="st">&quot;seatbelt_warning&quot;</span><span class="op">};</span></span>
<span id="cb8-53"><a href="#cb8-53" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb8-54"><a href="#cb8-54" aria-hidden="true" tabindex="-1"></a>        <span class="cf">else</span> <span class="cf">if</span> <span class="op">(</span>regionCode <span class="op">==</span> <span class="st">&quot;JP&quot;</span><span class="op">)</span></span>
<span id="cb8-55"><a href="#cb8-55" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb8-56"><a href="#cb8-56" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>region <span class="op">=</span> <span class="st">&quot;Japan&quot;</span><span class="op">;</span></span>
<span id="cb8-57"><a href="#cb8-57" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>requiredLanguages <span class="op">=</span> <span class="op">{</span><span class="st">&quot;ja-JP&quot;</span><span class="op">,</span> <span class="st">&quot;en-US&quot;</span><span class="op">};</span></span>
<span id="cb8-58"><a href="#cb8-58" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>unitPreferences<span class="op">[</span><span class="st">&quot;speed&quot;</span><span class="op">]</span> <span class="op">=</span> <span class="st">&quot;km/h&quot;</span><span class="op">;</span></span>
<span id="cb8-59"><a href="#cb8-59" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>unitPreferences<span class="op">[</span><span class="st">&quot;temperature&quot;</span><span class="op">]</span> <span class="op">=</span> <span class="st">&quot;celsius&quot;</span><span class="op">;</span></span>
<span id="cb8-60"><a href="#cb8-60" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>requiresRightHandDrive <span class="op">=</span> <span class="kw">true</span><span class="op">;</span></span>
<span id="cb8-61"><a href="#cb8-61" aria-hidden="true" tabindex="-1"></a>            requirements<span class="op">.</span>mandatoryWarnings <span class="op">=</span> <span class="op">{</span><span class="st">&quot;seatbelt_warning&quot;</span><span class="op">,</span> <span class="st">&quot;door_open_warning&quot;</span><span class="op">};</span></span>
<span id="cb8-62"><a href="#cb8-62" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb8-63"><a href="#cb8-63" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-64"><a href="#cb8-64" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> requirements<span class="op">;</span></span>
<span id="cb8-65"><a href="#cb8-65" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-66"><a href="#cb8-66" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-67"><a href="#cb8-67" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> configureLanguageSupport<span class="op">(</span><span class="at">const</span> vector<span class="op">&lt;</span>string<span class="op">&gt;&amp;</span> languages<span class="op">)</span></span>
<span id="cb8-68"><a href="#cb8-68" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-69"><a href="#cb8-69" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 配置支持的语言列表</span></span>
<span id="cb8-70"><a href="#cb8-70" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> lang <span class="op">:</span> languages<span class="op">)</span></span>
<span id="cb8-71"><a href="#cb8-71" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb8-72"><a href="#cb8-72" aria-hidden="true" tabindex="-1"></a>            enableLanguage<span class="op">(</span>lang<span class="op">);</span></span>
<span id="cb8-73"><a href="#cb8-73" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb8-74"><a href="#cb8-74" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-75"><a href="#cb8-75" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-76"><a href="#cb8-76" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> configureUnits<span class="op">(</span><span class="at">const</span> map<span class="op">&lt;</span>string<span class="op">,</span> string<span class="op">&gt;&amp;</span> unitPreferences<span class="op">)</span></span>
<span id="cb8-77"><a href="#cb8-77" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-78"><a href="#cb8-78" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 配置单位系统</span></span>
<span id="cb8-79"><a href="#cb8-79" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> <span class="kw">auto</span><span class="op">&amp;</span> pair <span class="op">:</span> unitPreferences<span class="op">)</span></span>
<span id="cb8-80"><a href="#cb8-80" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb8-81"><a href="#cb8-81" aria-hidden="true" tabindex="-1"></a>            setUnitPreference<span class="op">(</span>pair<span class="op">.</span>first<span class="op">,</span> pair<span class="op">.</span>second<span class="op">);</span></span>
<span id="cb8-82"><a href="#cb8-82" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb8-83"><a href="#cb8-83" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-84"><a href="#cb8-84" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-85"><a href="#cb8-85" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> configureDrivingSide<span class="op">(</span><span class="dt">bool</span> rightHandDrive<span class="op">)</span></span>
<span id="cb8-86"><a href="#cb8-86" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-87"><a href="#cb8-87" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 配置驾驶方向相关的UI布局</span></span>
<span id="cb8-88"><a href="#cb8-88" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>rightHandDrive<span class="op">)</span></span>
<span id="cb8-89"><a href="#cb8-89" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb8-90"><a href="#cb8-90" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 调整仪表盘布局为右舵车</span></span>
<span id="cb8-91"><a href="#cb8-91" aria-hidden="true" tabindex="-1"></a>            adjustDashboardForRHD<span class="op">();</span></span>
<span id="cb8-92"><a href="#cb8-92" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb8-93"><a href="#cb8-93" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-94"><a href="#cb8-94" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-95"><a href="#cb8-95" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> configureMandatoryWarnings<span class="op">(</span><span class="at">const</span> vector<span class="op">&lt;</span>string<span class="op">&gt;&amp;</span> warnings<span class="op">)</span></span>
<span id="cb8-96"><a href="#cb8-96" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-97"><a href="#cb8-97" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 配置强制警告消息</span></span>
<span id="cb8-98"><a href="#cb8-98" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> warning <span class="op">:</span> warnings<span class="op">)</span></span>
<span id="cb8-99"><a href="#cb8-99" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb8-100"><a href="#cb8-100" aria-hidden="true" tabindex="-1"></a>            enableMandatoryWarning<span class="op">(</span>warning<span class="op">);</span></span>
<span id="cb8-101"><a href="#cb8-101" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb8-102"><a href="#cb8-102" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-103"><a href="#cb8-103" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-104"><a href="#cb8-104" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> enableLanguage<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span> <span class="op">{</span> <span class="co">/* 实现 */</span> <span class="op">}</span></span>
<span id="cb8-105"><a href="#cb8-105" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> setUnitPreference<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> type<span class="op">,</span> <span class="at">const</span> string<span class="op">&amp;</span> unit<span class="op">)</span> <span class="op">{</span> <span class="co">/* 实现 */</span> <span class="op">}</span></span>
<span id="cb8-106"><a href="#cb8-106" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> adjustDashboardForRHD<span class="op">()</span> <span class="op">{</span> <span class="co">/* 实现 */</span> <span class="op">}</span></span>
<span id="cb8-107"><a href="#cb8-107" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> enableMandatoryWarning<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> warning<span class="op">)</span> <span class="op">{</span> <span class="co">/* 实现 */</span> <span class="op">}</span></span>
<span id="cb8-108"><a href="#cb8-108" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="kanzi-studio-工具">Kanzi Studio 工具</h2>
<h3 id="本地化工作流">本地化工作流</h3>
<div class="sourceCode" id="cb9"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Kanzi Studio 本地化工具集成</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> KanziStudioLocalizationTools</span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 导出本地化字符串</span></span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> exportStringsForTranslation<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> outputPath<span class="op">)</span></span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-8"><a href="#cb9-8" aria-hidden="true" tabindex="-1"></a>        Json<span class="op">::</span>Value exportData<span class="op">;</span></span>
<span id="cb9-9"><a href="#cb9-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-10"><a href="#cb9-10" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 收集所有需要翻译的字符串</span></span>
<span id="cb9-11"><a href="#cb9-11" aria-hidden="true" tabindex="-1"></a>        collectLocalizableStrings<span class="op">(</span>exportData<span class="op">);</span></span>
<span id="cb9-12"><a href="#cb9-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-13"><a href="#cb9-13" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 导出为翻译友好的格式</span></span>
<span id="cb9-14"><a href="#cb9-14" aria-hidden="true" tabindex="-1"></a>        exportToTranslationFormat<span class="op">(</span>exportData<span class="op">,</span> outputPath<span class="op">);</span></span>
<span id="cb9-15"><a href="#cb9-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-16"><a href="#cb9-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-17"><a href="#cb9-17" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 导入翻译结果</span></span>
<span id="cb9-18"><a href="#cb9-18" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> importTranslations<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> translationFile<span class="op">)</span></span>
<span id="cb9-19"><a href="#cb9-19" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-20"><a href="#cb9-20" aria-hidden="true" tabindex="-1"></a>        Json<span class="op">::</span>Value translations<span class="op">;</span></span>
<span id="cb9-21"><a href="#cb9-21" aria-hidden="true" tabindex="-1"></a>        ifstream file<span class="op">(</span>translationFile<span class="op">);</span></span>
<span id="cb9-22"><a href="#cb9-22" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>file<span class="op">.</span>is_open<span class="op">())</span></span>
<span id="cb9-23"><a href="#cb9-23" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb9-24"><a href="#cb9-24" aria-hidden="true" tabindex="-1"></a>            file <span class="op">&gt;&gt;</span> translations<span class="op">;</span></span>
<span id="cb9-25"><a href="#cb9-25" aria-hidden="true" tabindex="-1"></a>            processTranslations<span class="op">(</span>translations<span class="op">);</span></span>
<span id="cb9-26"><a href="#cb9-26" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb9-27"><a href="#cb9-27" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-28"><a href="#cb9-28" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-29"><a href="#cb9-29" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 验证本地化资源</span></span>
<span id="cb9-30"><a href="#cb9-30" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> validateLocalizationResources<span class="op">()</span></span>
<span id="cb9-31"><a href="#cb9-31" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-32"><a href="#cb9-32" aria-hidden="true" tabindex="-1"></a>        vector<span class="op">&lt;</span>string<span class="op">&gt;</span> issues<span class="op">;</span></span>
<span id="cb9-33"><a href="#cb9-33" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-34"><a href="#cb9-34" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 检查缺失的翻译</span></span>
<span id="cb9-35"><a href="#cb9-35" aria-hidden="true" tabindex="-1"></a>        checkMissingTranslations<span class="op">(</span>issues<span class="op">);</span></span>
<span id="cb9-36"><a href="#cb9-36" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-37"><a href="#cb9-37" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 检查资源文件</span></span>
<span id="cb9-38"><a href="#cb9-38" aria-hidden="true" tabindex="-1"></a>        checkMissingResources<span class="op">(</span>issues<span class="op">);</span></span>
<span id="cb9-39"><a href="#cb9-39" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-40"><a href="#cb9-40" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 检查文本长度</span></span>
<span id="cb9-41"><a href="#cb9-41" aria-hidden="true" tabindex="-1"></a>        checkTextLength<span class="op">(</span>issues<span class="op">);</span></span>
<span id="cb9-42"><a href="#cb9-42" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-43"><a href="#cb9-43" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 生成验证报告</span></span>
<span id="cb9-44"><a href="#cb9-44" aria-hidden="true" tabindex="-1"></a>        generateValidationReport<span class="op">(</span>issues<span class="op">);</span></span>
<span id="cb9-45"><a href="#cb9-45" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-46"><a href="#cb9-46" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-47"><a href="#cb9-47" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb9-48"><a href="#cb9-48" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> collectLocalizableStrings<span class="op">(</span>Json<span class="op">::</span>Value<span class="op">&amp;</span> exportData<span class="op">)</span></span>
<span id="cb9-49"><a href="#cb9-49" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-50"><a href="#cb9-50" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 扫描项目文件，收集所有本地化字符串</span></span>
<span id="cb9-51"><a href="#cb9-51" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 包括 .kzb 文件、脚本文件等</span></span>
<span id="cb9-52"><a href="#cb9-52" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-53"><a href="#cb9-53" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-54"><a href="#cb9-54" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> exportToTranslationFormat<span class="op">(</span><span class="at">const</span> Json<span class="op">::</span>Value<span class="op">&amp;</span> data<span class="op">,</span> <span class="at">const</span> string<span class="op">&amp;</span> outputPath<span class="op">)</span></span>
<span id="cb9-55"><a href="#cb9-55" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-56"><a href="#cb9-56" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 导出为 XLIFF、CSV 或其他翻译工具支持的格式</span></span>
<span id="cb9-57"><a href="#cb9-57" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-58"><a href="#cb9-58" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-59"><a href="#cb9-59" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> processTranslations<span class="op">(</span><span class="at">const</span> Json<span class="op">::</span>Value<span class="op">&amp;</span> translations<span class="op">)</span></span>
<span id="cb9-60"><a href="#cb9-60" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-61"><a href="#cb9-61" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 处理翻译结果，更新项目文件</span></span>
<span id="cb9-62"><a href="#cb9-62" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-63"><a href="#cb9-63" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-64"><a href="#cb9-64" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> checkMissingTranslations<span class="op">(</span>vector<span class="op">&lt;</span>string<span class="op">&gt;&amp;</span> issues<span class="op">)</span></span>
<span id="cb9-65"><a href="#cb9-65" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-66"><a href="#cb9-66" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 检查缺失的翻译</span></span>
<span id="cb9-67"><a href="#cb9-67" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-68"><a href="#cb9-68" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-69"><a href="#cb9-69" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> checkMissingResources<span class="op">(</span>vector<span class="op">&lt;</span>string<span class="op">&gt;&amp;</span> issues<span class="op">)</span></span>
<span id="cb9-70"><a href="#cb9-70" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-71"><a href="#cb9-71" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 检查缺失的本地化资源文件</span></span>
<span id="cb9-72"><a href="#cb9-72" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-73"><a href="#cb9-73" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-74"><a href="#cb9-74" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> checkTextLength<span class="op">(</span>vector<span class="op">&lt;</span>string<span class="op">&gt;&amp;</span> issues<span class="op">)</span></span>
<span id="cb9-75"><a href="#cb9-75" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-76"><a href="#cb9-76" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 检查文本长度是否适合UI布局</span></span>
<span id="cb9-77"><a href="#cb9-77" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-78"><a href="#cb9-78" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-79"><a href="#cb9-79" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> generateValidationReport<span class="op">(</span><span class="at">const</span> vector<span class="op">&lt;</span>string<span class="op">&gt;&amp;</span> issues<span class="op">)</span></span>
<span id="cb9-80"><a href="#cb9-80" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-81"><a href="#cb9-81" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 生成验证报告</span></span>
<span id="cb9-82"><a href="#cb9-82" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-83"><a href="#cb9-83" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="最佳实践">最佳实践</h2>
<h3 id="性能优化">1. 性能优化</h3>
<div class="sourceCode" id="cb10"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 本地化资源预加载策略</span></span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> LocalizationPreloader</span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb10-5"><a href="#cb10-5" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> preloadForLanguage<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span></span>
<span id="cb10-6"><a href="#cb10-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-7"><a href="#cb10-7" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 预加载策略：只加载当前屏幕和下一个可能屏幕的资源</span></span>
<span id="cb10-8"><a href="#cb10-8" aria-hidden="true" tabindex="-1"></a>        preloadCurrentScreenResources<span class="op">(</span>languageCode<span class="op">);</span></span>
<span id="cb10-9"><a href="#cb10-9" aria-hidden="true" tabindex="-1"></a>        preloadNextScreenResources<span class="op">(</span>languageCode<span class="op">);</span></span>
<span id="cb10-10"><a href="#cb10-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-11"><a href="#cb10-11" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 异步预加载常用资源</span></span>
<span id="cb10-12"><a href="#cb10-12" aria-hidden="true" tabindex="-1"></a>        asyncPreloadCommonResources<span class="op">(</span>languageCode<span class="op">);</span></span>
<span id="cb10-13"><a href="#cb10-13" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-14"><a href="#cb10-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-15"><a href="#cb10-15" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> optimizeMemoryUsage<span class="op">()</span></span>
<span id="cb10-16"><a href="#cb10-16" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-17"><a href="#cb10-17" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 清理未使用的本地化资源</span></span>
<span id="cb10-18"><a href="#cb10-18" aria-hidden="true" tabindex="-1"></a>        cleanupUnusedResources<span class="op">();</span></span>
<span id="cb10-19"><a href="#cb10-19" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-20"><a href="#cb10-20" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 压缩纹理资源</span></span>
<span id="cb10-21"><a href="#cb10-21" aria-hidden="true" tabindex="-1"></a>        compressTextureResources<span class="op">();</span></span>
<span id="cb10-22"><a href="#cb10-22" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-23"><a href="#cb10-23" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 使用资源池管理</span></span>
<span id="cb10-24"><a href="#cb10-24" aria-hidden="true" tabindex="-1"></a>        setupResourcePools<span class="op">();</span></span>
<span id="cb10-25"><a href="#cb10-25" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-26"><a href="#cb10-26" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-27"><a href="#cb10-27" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb10-28"><a href="#cb10-28" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> preloadCurrentScreenResources<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span></span>
<span id="cb10-29"><a href="#cb10-29" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-30"><a href="#cb10-30" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 预加载当前屏幕的所有本地化资源</span></span>
<span id="cb10-31"><a href="#cb10-31" aria-hidden="true" tabindex="-1"></a>        string currentScreen <span class="op">=</span> getCurrentScreenName<span class="op">();</span></span>
<span id="cb10-32"><a href="#cb10-32" aria-hidden="true" tabindex="-1"></a>        loadScreenResources<span class="op">(</span>currentScreen<span class="op">,</span> languageCode<span class="op">);</span></span>
<span id="cb10-33"><a href="#cb10-33" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-34"><a href="#cb10-34" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-35"><a href="#cb10-35" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> preloadNextScreenResources<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span></span>
<span id="cb10-36"><a href="#cb10-36" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-37"><a href="#cb10-37" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 根据用户行为预测，预加载可能的下一个屏幕</span></span>
<span id="cb10-38"><a href="#cb10-38" aria-hidden="true" tabindex="-1"></a>        vector<span class="op">&lt;</span>string<span class="op">&gt;</span> likelyNextScreens <span class="op">=</span> predictNextScreens<span class="op">();</span></span>
<span id="cb10-39"><a href="#cb10-39" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> screen <span class="op">:</span> likelyNextScreens<span class="op">)</span></span>
<span id="cb10-40"><a href="#cb10-40" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb10-41"><a href="#cb10-41" aria-hidden="true" tabindex="-1"></a>            loadScreenResources<span class="op">(</span>screen<span class="op">,</span> languageCode<span class="op">);</span></span>
<span id="cb10-42"><a href="#cb10-42" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb10-43"><a href="#cb10-43" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-44"><a href="#cb10-44" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-45"><a href="#cb10-45" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> asyncPreloadCommonResources<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> languageCode<span class="op">)</span></span>
<span id="cb10-46"><a href="#cb10-46" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-47"><a href="#cb10-47" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 在后台线程异步加载常用资源</span></span>
<span id="cb10-48"><a href="#cb10-48" aria-hidden="true" tabindex="-1"></a>        thread preloadThread<span class="op">([</span><span class="kw">this</span><span class="op">,</span> languageCode<span class="op">]()</span> <span class="op">{</span></span>
<span id="cb10-49"><a href="#cb10-49" aria-hidden="true" tabindex="-1"></a>            loadCommonIcons<span class="op">(</span>languageCode<span class="op">);</span></span>
<span id="cb10-50"><a href="#cb10-50" aria-hidden="true" tabindex="-1"></a>            loadCommonTexts<span class="op">(</span>languageCode<span class="op">);</span></span>
<span id="cb10-51"><a href="#cb10-51" aria-hidden="true" tabindex="-1"></a>            loadCommonAudio<span class="op">(</span>languageCode<span class="op">);</span></span>
<span id="cb10-52"><a href="#cb10-52" aria-hidden="true" tabindex="-1"></a>        <span class="op">});</span></span>
<span id="cb10-53"><a href="#cb10-53" aria-hidden="true" tabindex="-1"></a>        preloadThread<span class="op">.</span>detach<span class="op">();</span></span>
<span id="cb10-54"><a href="#cb10-54" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-55"><a href="#cb10-55" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-56"><a href="#cb10-56" aria-hidden="true" tabindex="-1"></a>    string getCurrentScreenName<span class="op">()</span> <span class="op">{</span> <span class="cf">return</span> <span class="st">&quot;dashboard&quot;</span><span class="op">;</span> <span class="op">}</span></span>
<span id="cb10-57"><a href="#cb10-57" aria-hidden="true" tabindex="-1"></a>    vector<span class="op">&lt;</span>string<span class="op">&gt;</span> predictNextScreens<span class="op">()</span> <span class="op">{</span> <span class="cf">return</span> <span class="op">{</span><span class="st">&quot;navigation&quot;</span><span class="op">,</span> <span class="st">&quot;media&quot;</span><span class="op">,</span> <span class="st">&quot;settings&quot;</span><span class="op">};</span> <span class="op">}</span></span>
<span id="cb10-58"><a href="#cb10-58" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> loadScreenResources<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> screen<span class="op">,</span> <span class="at">const</span> string<span class="op">&amp;</span> lang<span class="op">)</span> <span class="op">{</span> <span class="co">/* 实现 */</span> <span class="op">}</span></span>
<span id="cb10-59"><a href="#cb10-59" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> cleanupUnusedResources<span class="op">()</span> <span class="op">{</span> <span class="co">/* 实现 */</span> <span class="op">}</span></span>
<span id="cb10-60"><a href="#cb10-60" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> compressTextureResources<span class="op">()</span> <span class="op">{</span> <span class="co">/* 实现 */</span> <span class="op">}</span></span>
<span id="cb10-61"><a href="#cb10-61" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> setupResourcePools<span class="op">()</span> <span class="op">{</span> <span class="co">/* 实现 */</span> <span class="op">}</span></span>
<span id="cb10-62"><a href="#cb10-62" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> loadCommonIcons<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> lang<span class="op">)</span> <span class="op">{</span> <span class="co">/* 实现 */</span> <span class="op">}</span></span>
<span id="cb10-63"><a href="#cb10-63" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> loadCommonTexts<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> lang<span class="op">)</span> <span class="op">{</span> <span class="co">/* 实现 */</span> <span class="op">}</span></span>
<span id="cb10-64"><a href="#cb10-64" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> loadCommonAudio<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> lang<span class="op">)</span> <span class="op">{</span> <span class="co">/* 实现 */</span> <span class="op">}</span></span>
<span id="cb10-65"><a href="#cb10-65" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="测试和验证">2. 测试和验证</h3>
<div class="sourceCode" id="cb11"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 本地化测试框架</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> LocalizationTestFramework</span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a>    <span class="kw">struct</span> TestResult</span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-7"><a href="#cb11-7" aria-hidden="true" tabindex="-1"></a>        string testName<span class="op">;</span></span>
<span id="cb11-8"><a href="#cb11-8" aria-hidden="true" tabindex="-1"></a>        <span class="dt">bool</span> passed<span class="op">;</span></span>
<span id="cb11-9"><a href="#cb11-9" aria-hidden="true" tabindex="-1"></a>        string errorMessage<span class="op">;</span></span>
<span id="cb11-10"><a href="#cb11-10" aria-hidden="true" tabindex="-1"></a>        vector<span class="op">&lt;</span>string<span class="op">&gt;</span> warnings<span class="op">;</span></span>
<span id="cb11-11"><a href="#cb11-11" aria-hidden="true" tabindex="-1"></a>    <span class="op">};</span></span>
<span id="cb11-12"><a href="#cb11-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-13"><a href="#cb11-13" aria-hidden="true" tabindex="-1"></a>    vector<span class="op">&lt;</span>TestResult<span class="op">&gt;</span> runAllTests<span class="op">()</span></span>
<span id="cb11-14"><a href="#cb11-14" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-15"><a href="#cb11-15" aria-hidden="true" tabindex="-1"></a>        vector<span class="op">&lt;</span>TestResult<span class="op">&gt;</span> results<span class="op">;</span></span>
<span id="cb11-16"><a href="#cb11-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-17"><a href="#cb11-17" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 运行各种本地化测试</span></span>
<span id="cb11-18"><a href="#cb11-18" aria-hidden="true" tabindex="-1"></a>        results<span class="op">.</span>push_back<span class="op">(</span>testLanguageSwitching<span class="op">());</span></span>
<span id="cb11-19"><a href="#cb11-19" aria-hidden="true" tabindex="-1"></a>        results<span class="op">.</span>push_back<span class="op">(</span>testTextTruncation<span class="op">());</span></span>
<span id="cb11-20"><a href="#cb11-20" aria-hidden="true" tabindex="-1"></a>        results<span class="op">.</span>push_back<span class="op">(</span>testResourceLoading<span class="op">());</span></span>
<span id="cb11-21"><a href="#cb11-21" aria-hidden="true" tabindex="-1"></a>        results<span class="op">.</span>push_back<span class="op">(</span>testRTLSupport<span class="op">());</span></span>
<span id="cb11-22"><a href="#cb11-22" aria-hidden="true" tabindex="-1"></a>        results<span class="op">.</span>push_back<span class="op">(</span>testNumberFormatting<span class="op">());</span></span>
<span id="cb11-23"><a href="#cb11-23" aria-hidden="true" tabindex="-1"></a>        results<span class="op">.</span>push_back<span class="op">(</span>testDateTimeFormatting<span class="op">());</span></span>
<span id="cb11-24"><a href="#cb11-24" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-25"><a href="#cb11-25" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> results<span class="op">;</span></span>
<span id="cb11-26"><a href="#cb11-26" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-27"><a href="#cb11-27" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-28"><a href="#cb11-28" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb11-29"><a href="#cb11-29" aria-hidden="true" tabindex="-1"></a>    TestResult testLanguageSwitching<span class="op">()</span></span>
<span id="cb11-30"><a href="#cb11-30" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-31"><a href="#cb11-31" aria-hidden="true" tabindex="-1"></a>        TestResult result<span class="op">;</span></span>
<span id="cb11-32"><a href="#cb11-32" aria-hidden="true" tabindex="-1"></a>        result<span class="op">.</span>testName <span class="op">=</span> <span class="st">&quot;Language Switching&quot;</span><span class="op">;</span></span>
<span id="cb11-33"><a href="#cb11-33" aria-hidden="true" tabindex="-1"></a>        result<span class="op">.</span>passed <span class="op">=</span> <span class="kw">true</span><span class="op">;</span></span>
<span id="cb11-34"><a href="#cb11-34" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-35"><a href="#cb11-35" aria-hidden="true" tabindex="-1"></a>        <span class="cf">try</span></span>
<span id="cb11-36"><a href="#cb11-36" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb11-37"><a href="#cb11-37" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 测试语言切换功能</span></span>
<span id="cb11-38"><a href="#cb11-38" aria-hidden="true" tabindex="-1"></a>            vector<span class="op">&lt;</span>string<span class="op">&gt;</span> languages <span class="op">=</span> <span class="op">{</span><span class="st">&quot;en-US&quot;</span><span class="op">,</span> <span class="st">&quot;zh-CN&quot;</span><span class="op">,</span> <span class="st">&quot;ja-JP&quot;</span><span class="op">,</span> <span class="st">&quot;de-DE&quot;</span><span class="op">};</span></span>
<span id="cb11-39"><a href="#cb11-39" aria-hidden="true" tabindex="-1"></a>            <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> lang <span class="op">:</span> languages<span class="op">)</span></span>
<span id="cb11-40"><a href="#cb11-40" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb11-41"><a href="#cb11-41" aria-hidden="true" tabindex="-1"></a>                setLanguage<span class="op">(</span>lang<span class="op">);</span></span>
<span id="cb11-42"><a href="#cb11-42" aria-hidden="true" tabindex="-1"></a>                <span class="cf">if</span> <span class="op">(</span>getCurrentLanguage<span class="op">()</span> <span class="op">!=</span> lang<span class="op">)</span></span>
<span id="cb11-43"><a href="#cb11-43" aria-hidden="true" tabindex="-1"></a>                <span class="op">{</span></span>
<span id="cb11-44"><a href="#cb11-44" aria-hidden="true" tabindex="-1"></a>                    result<span class="op">.</span>passed <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb11-45"><a href="#cb11-45" aria-hidden="true" tabindex="-1"></a>                    result<span class="op">.</span>errorMessage <span class="op">=</span> <span class="st">&quot;Failed to switch to &quot;</span> <span class="op">+</span> lang<span class="op">;</span></span>
<span id="cb11-46"><a href="#cb11-46" aria-hidden="true" tabindex="-1"></a>                    <span class="cf">break</span><span class="op">;</span></span>
<span id="cb11-47"><a href="#cb11-47" aria-hidden="true" tabindex="-1"></a>                <span class="op">}</span></span>
<span id="cb11-48"><a href="#cb11-48" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-49"><a href="#cb11-49" aria-hidden="true" tabindex="-1"></a>                <span class="co">// 验证UI更新</span></span>
<span id="cb11-50"><a href="#cb11-50" aria-hidden="true" tabindex="-1"></a>                <span class="cf">if</span> <span class="op">(!</span>verifyUIUpdated<span class="op">(</span>lang<span class="op">))</span></span>
<span id="cb11-51"><a href="#cb11-51" aria-hidden="true" tabindex="-1"></a>                <span class="op">{</span></span>
<span id="cb11-52"><a href="#cb11-52" aria-hidden="true" tabindex="-1"></a>                    result<span class="op">.</span>passed <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb11-53"><a href="#cb11-53" aria-hidden="true" tabindex="-1"></a>                    result<span class="op">.</span>errorMessage <span class="op">=</span> <span class="st">&quot;UI not updated for &quot;</span> <span class="op">+</span> lang<span class="op">;</span></span>
<span id="cb11-54"><a href="#cb11-54" aria-hidden="true" tabindex="-1"></a>                    <span class="cf">break</span><span class="op">;</span></span>
<span id="cb11-55"><a href="#cb11-55" aria-hidden="true" tabindex="-1"></a>                <span class="op">}</span></span>
<span id="cb11-56"><a href="#cb11-56" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb11-57"><a href="#cb11-57" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb11-58"><a href="#cb11-58" aria-hidden="true" tabindex="-1"></a>        <span class="cf">catch</span> <span class="op">(</span><span class="at">const</span> exception<span class="op">&amp;</span> e<span class="op">)</span></span>
<span id="cb11-59"><a href="#cb11-59" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb11-60"><a href="#cb11-60" aria-hidden="true" tabindex="-1"></a>            result<span class="op">.</span>passed <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb11-61"><a href="#cb11-61" aria-hidden="true" tabindex="-1"></a>            result<span class="op">.</span>errorMessage <span class="op">=</span> e<span class="op">.</span>what<span class="op">();</span></span>
<span id="cb11-62"><a href="#cb11-62" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb11-63"><a href="#cb11-63" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-64"><a href="#cb11-64" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> result<span class="op">;</span></span>
<span id="cb11-65"><a href="#cb11-65" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-66"><a href="#cb11-66" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-67"><a href="#cb11-67" aria-hidden="true" tabindex="-1"></a>    TestResult testTextTruncation<span class="op">()</span></span>
<span id="cb11-68"><a href="#cb11-68" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-69"><a href="#cb11-69" aria-hidden="true" tabindex="-1"></a>        TestResult result<span class="op">;</span></span>
<span id="cb11-70"><a href="#cb11-70" aria-hidden="true" tabindex="-1"></a>        result<span class="op">.</span>testName <span class="op">=</span> <span class="st">&quot;Text Truncation&quot;</span><span class="op">;</span></span>
<span id="cb11-71"><a href="#cb11-71" aria-hidden="true" tabindex="-1"></a>        result<span class="op">.</span>passed <span class="op">=</span> <span class="kw">true</span><span class="op">;</span></span>
<span id="cb11-72"><a href="#cb11-72" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-73"><a href="#cb11-73" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 测试长文本是否正确处理</span></span>
<span id="cb11-74"><a href="#cb11-74" aria-hidden="true" tabindex="-1"></a>        vector<span class="op">&lt;</span>string<span class="op">&gt;</span> longTextKeys <span class="op">=</span> <span class="op">{</span><span class="st">&quot;very_long_warning_message&quot;</span><span class="op">,</span> <span class="st">&quot;detailed_navigation_instruction&quot;</span><span class="op">};</span></span>
<span id="cb11-75"><a href="#cb11-75" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-76"><a href="#cb11-76" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> key <span class="op">:</span> longTextKeys<span class="op">)</span></span>
<span id="cb11-77"><a href="#cb11-77" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb11-78"><a href="#cb11-78" aria-hidden="true" tabindex="-1"></a>            string text <span class="op">=</span> getLocalizedString<span class="op">(</span>key<span class="op">);</span></span>
<span id="cb11-79"><a href="#cb11-79" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> <span class="op">(</span>isTextTruncated<span class="op">(</span>text<span class="op">))</span></span>
<span id="cb11-80"><a href="#cb11-80" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb11-81"><a href="#cb11-81" aria-hidden="true" tabindex="-1"></a>                result<span class="op">.</span>warnings<span class="op">.</span>push_back<span class="op">(</span><span class="st">&quot;Text truncated for key: &quot;</span> <span class="op">+</span> key<span class="op">);</span></span>
<span id="cb11-82"><a href="#cb11-82" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb11-83"><a href="#cb11-83" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb11-84"><a href="#cb11-84" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-85"><a href="#cb11-85" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> result<span class="op">;</span></span>
<span id="cb11-86"><a href="#cb11-86" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-87"><a href="#cb11-87" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-88"><a href="#cb11-88" aria-hidden="true" tabindex="-1"></a>    TestResult testResourceLoading<span class="op">()</span></span>
<span id="cb11-89"><a href="#cb11-89" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-90"><a href="#cb11-90" aria-hidden="true" tabindex="-1"></a>        TestResult result<span class="op">;</span></span>
<span id="cb11-91"><a href="#cb11-91" aria-hidden="true" tabindex="-1"></a>        result<span class="op">.</span>testName <span class="op">=</span> <span class="st">&quot;Resource Loading&quot;</span><span class="op">;</span></span>
<span id="cb11-92"><a href="#cb11-92" aria-hidden="true" tabindex="-1"></a>        result<span class="op">.</span>passed <span class="op">=</span> <span class="kw">true</span><span class="op">;</span></span>
<span id="cb11-93"><a href="#cb11-93" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-94"><a href="#cb11-94" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 测试本地化资源是否正确加载</span></span>
<span id="cb11-95"><a href="#cb11-95" aria-hidden="true" tabindex="-1"></a>        vector<span class="op">&lt;</span>string<span class="op">&gt;</span> languages <span class="op">=</span> getAvailableLanguages<span class="op">();</span></span>
<span id="cb11-96"><a href="#cb11-96" aria-hidden="true" tabindex="-1"></a>        vector<span class="op">&lt;</span>string<span class="op">&gt;</span> resourceKeys <span class="op">=</span> <span class="op">{</span><span class="st">&quot;home_icon&quot;</span><span class="op">,</span> <span class="st">&quot;settings_icon&quot;</span><span class="op">,</span> <span class="st">&quot;warning_sound&quot;</span><span class="op">};</span></span>
<span id="cb11-97"><a href="#cb11-97" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-98"><a href="#cb11-98" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> lang <span class="op">:</span> languages<span class="op">)</span></span>
<span id="cb11-99"><a href="#cb11-99" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb11-100"><a href="#cb11-100" aria-hidden="true" tabindex="-1"></a>            <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> key <span class="op">:</span> resourceKeys<span class="op">)</span></span>
<span id="cb11-101"><a href="#cb11-101" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb11-102"><a href="#cb11-102" aria-hidden="true" tabindex="-1"></a>                <span class="cf">if</span> <span class="op">(!</span>isResourceAvailable<span class="op">(</span>key<span class="op">,</span> lang<span class="op">))</span></span>
<span id="cb11-103"><a href="#cb11-103" aria-hidden="true" tabindex="-1"></a>                <span class="op">{</span></span>
<span id="cb11-104"><a href="#cb11-104" aria-hidden="true" tabindex="-1"></a>                    result<span class="op">.</span>warnings<span class="op">.</span>push_back<span class="op">(</span><span class="st">&quot;Missing resource: &quot;</span> <span class="op">+</span> key <span class="op">+</span> <span class="st">&quot; for &quot;</span> <span class="op">+</span> lang<span class="op">);</span></span>
<span id="cb11-105"><a href="#cb11-105" aria-hidden="true" tabindex="-1"></a>                <span class="op">}</span></span>
<span id="cb11-106"><a href="#cb11-106" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb11-107"><a href="#cb11-107" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb11-108"><a href="#cb11-108" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-109"><a href="#cb11-109" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> result<span class="op">;</span></span>
<span id="cb11-110"><a href="#cb11-110" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-111"><a href="#cb11-111" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-112"><a href="#cb11-112" aria-hidden="true" tabindex="-1"></a>    TestResult testRTLSupport<span class="op">()</span></span>
<span id="cb11-113"><a href="#cb11-113" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-114"><a href="#cb11-114" aria-hidden="true" tabindex="-1"></a>        TestResult result<span class="op">;</span></span>
<span id="cb11-115"><a href="#cb11-115" aria-hidden="true" tabindex="-1"></a>        result<span class="op">.</span>testName <span class="op">=</span> <span class="st">&quot;RTL Support&quot;</span><span class="op">;</span></span>
<span id="cb11-116"><a href="#cb11-116" aria-hidden="true" tabindex="-1"></a>        result<span class="op">.</span>passed <span class="op">=</span> <span class="kw">true</span><span class="op">;</span></span>
<span id="cb11-117"><a href="#cb11-117" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-118"><a href="#cb11-118" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 测试从右到左语言支持</span></span>
<span id="cb11-119"><a href="#cb11-119" aria-hidden="true" tabindex="-1"></a>        vector<span class="op">&lt;</span>string<span class="op">&gt;</span> rtlLanguages <span class="op">=</span> <span class="op">{</span><span class="st">&quot;ar-SA&quot;</span><span class="op">,</span> <span class="st">&quot;he-IL&quot;</span><span class="op">};</span></span>
<span id="cb11-120"><a href="#cb11-120" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-121"><a href="#cb11-121" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> lang <span class="op">:</span> rtlLanguages<span class="op">)</span></span>
<span id="cb11-122"><a href="#cb11-122" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb11-123"><a href="#cb11-123" aria-hidden="true" tabindex="-1"></a>            setLanguage<span class="op">(</span>lang<span class="op">);</span></span>
<span id="cb11-124"><a href="#cb11-124" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> <span class="op">(!</span>isRTLLayoutActive<span class="op">())</span></span>
<span id="cb11-125"><a href="#cb11-125" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb11-126"><a href="#cb11-126" aria-hidden="true" tabindex="-1"></a>                result<span class="op">.</span>passed <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb11-127"><a href="#cb11-127" aria-hidden="true" tabindex="-1"></a>                result<span class="op">.</span>errorMessage <span class="op">=</span> <span class="st">&quot;RTL layout not activated for &quot;</span> <span class="op">+</span> lang<span class="op">;</span></span>
<span id="cb11-128"><a href="#cb11-128" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb11-129"><a href="#cb11-129" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb11-130"><a href="#cb11-130" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb11-131"><a href="#cb11-131" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-132"><a href="#cb11-132" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> result<span class="op">;</span></span>
<span id="cb11-133"><a href="#cb11-133" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-134"><a href="#cb11-134" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-135"><a href="#cb11-135" aria-hidden="true" tabindex="-1"></a>    TestResult testNumberFormatting<span class="op">()</span> <span class="op">{</span> <span class="co">/* 实现数字格式化测试 */</span> <span class="cf">return</span> TestResult<span class="op">();</span> <span class="op">}</span></span>
<span id="cb11-136"><a href="#cb11-136" aria-hidden="true" tabindex="-1"></a>    TestResult testDateTimeFormatting<span class="op">()</span> <span class="op">{</span> <span class="co">/* 实现日期时间格式化测试 */</span> <span class="cf">return</span> TestResult<span class="op">();</span> <span class="op">}</span></span>
<span id="cb11-137"><a href="#cb11-137" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-138"><a href="#cb11-138" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 辅助方法</span></span>
<span id="cb11-139"><a href="#cb11-139" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> setLanguage<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> lang<span class="op">)</span> <span class="op">{</span> <span class="co">/* 实现 */</span> <span class="op">}</span></span>
<span id="cb11-140"><a href="#cb11-140" aria-hidden="true" tabindex="-1"></a>    string getCurrentLanguage<span class="op">()</span> <span class="op">{</span> <span class="cf">return</span> <span class="st">&quot;en-US&quot;</span><span class="op">;</span> <span class="op">}</span></span>
<span id="cb11-141"><a href="#cb11-141" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> verifyUIUpdated<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> lang<span class="op">)</span> <span class="op">{</span> <span class="cf">return</span> <span class="kw">true</span><span class="op">;</span> <span class="op">}</span></span>
<span id="cb11-142"><a href="#cb11-142" aria-hidden="true" tabindex="-1"></a>    string getLocalizedString<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> key<span class="op">)</span> <span class="op">{</span> <span class="cf">return</span> <span class="st">&quot;&quot;</span><span class="op">;</span> <span class="op">}</span></span>
<span id="cb11-143"><a href="#cb11-143" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> isTextTruncated<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> text<span class="op">)</span> <span class="op">{</span> <span class="cf">return</span> <span class="kw">false</span><span class="op">;</span> <span class="op">}</span></span>
<span id="cb11-144"><a href="#cb11-144" aria-hidden="true" tabindex="-1"></a>    vector<span class="op">&lt;</span>string<span class="op">&gt;</span> getAvailableLanguages<span class="op">()</span> <span class="op">{</span> <span class="cf">return</span> <span class="op">{</span><span class="st">&quot;en-US&quot;</span><span class="op">,</span> <span class="st">&quot;zh-CN&quot;</span><span class="op">};</span> <span class="op">}</span></span>
<span id="cb11-145"><a href="#cb11-145" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> isResourceAvailable<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> key<span class="op">,</span> <span class="at">const</span> string<span class="op">&amp;</span> lang<span class="op">)</span> <span class="op">{</span> <span class="cf">return</span> <span class="kw">true</span><span class="op">;</span> <span class="op">}</span></span>
<span id="cb11-146"><a href="#cb11-146" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> isRTLLayoutActive<span class="op">()</span> <span class="op">{</span> <span class="cf">return</span> <span class="kw">true</span><span class="op">;</span> <span class="op">}</span></span>
<span id="cb11-147"><a href="#cb11-147" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<hr />
<p><em>Kanzi 的国际化系统专为汽车 HMI
设计，提供了符合汽车行业标准的完整本地化解决方案。更多详细信息请参考 <a
href="https://docs.kanzi.com/">Kanzi 官方文档</a></em></p>
<pre><code></code></pre>
</div><div lang="en" style="display: none;"><h1>[English translation pending]</h1><p>The content for this page has not been translated yet. Please check back later.</p></div>
                </main>
            </div>
        </section>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">Qt 3D 开发指南</h4>
                    <p data-i18n="footer-subtitle">专业的跨平台 3D 应用开发解决方案</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.</p>
            </div>
        </div>
    </footer>

    <script src="../../assets/js/prism.js"></script>
    <script src="../../assets/js/docs.js"></script>
</body>
</html> 