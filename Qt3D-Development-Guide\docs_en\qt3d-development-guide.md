# Qt 3D Engine Development Guide

## Table of Contents
1. [Overview](#overview)
2. [Technical Features](#technical-features)
3. [Development Environment Setup](#development-environment-setup)
4. [Core Architecture](#core-architecture)
5. [Development Process](#development-process)
6. [Best Practices](#best-practices)
7. [Performance Optimization](#performance-optimization)
8. [Debugging and Testing](#debugging-and-testing)

## Overview

Qt 3D is part of the Qt framework, providing rich 3D graphics rendering and scene management capabilities. Based on modern graphics APIs (OpenGL, Vulkan, DirectX), it offers developers advanced 3D application development capabilities.

### Main Advantages
- **Cross-platform Support**: Supports multiple platforms including Windows, Linux, macOS, Android, iOS
- **Modern Graphics APIs**: Supports modern graphics APIs like OpenGL, Vulkan, DirectX 12
- **Declarative Programming**: Supports QML declarative programming, simplifying 3D scene construction
- **High-performance Rendering**: Based on multi-threaded rendering architecture, providing efficient graphics rendering performance
- **Rich Material System**: Built-in various materials and shaders, supporting custom materials
- **Complete Ecosystem**: Perfect integration with Qt ecosystem, reusing existing Qt skills

## Technical Features

### Rendering Engine
- **Multi-threaded Rendering Architecture**: Separates rendering thread from main thread, ensuring UI responsiveness
- **Modern Graphics API Support**:
  - OpenGL 3.2+ / OpenGL ES 3.0+
  - Vulkan 1.0+
  - DirectX 12 (Windows)
  - Metal (macOS/iOS)
- **Deferred Rendering Pipeline**: Supports deferred rendering and forward rendering
- **HDR Rendering**: Supports high dynamic range rendering
- **Multi-sample Anti-aliasing**: MSAA, FXAA and other anti-aliasing technologies

### Scene Management
- **Entity-Component-System (ECS) Architecture**: Flexible scene object management
- **Hierarchical Scene Graph**: Supports complex scene hierarchy structures
- **Space Partitioning**: Octree, BSP tree and other spatial optimization algorithms
- **Frustum Culling**: Automatic frustum culling optimization
- **LOD System**: Multi-level detail model support

### Materials and Shaders
- **Physically Based Rendering (PBR)**: Supports modern PBR material workflow
- **Custom Shaders**: Supports GLSL, HLSL shader programming
- **Material Editor**: Visual material editing tools
- **Texture Management**: Efficient texture loading and management system
- **Dynamic Materials**: Runtime material parameter adjustment

### Animation System
- **Skeletal Animation**: Complete skeletal animation system
- **Keyframe Animation**: Supports position, rotation, scale animation
- **Animation Blending**: Multi-animation blending and transitions
- **Animation State Machine**: Complex animation logic management
- **Physics Animation**: Animation integrated with physics engine

### 2D Graphics Support
- **Qt Quick**: QML-based 2D graphics framework
- **Canvas API**: HTML5 Canvas-style 2D drawing
- **SVG Support**: Complete vector graphics support
- **Image Processing**: Rich image filters and effects
- **2D Animation**: Property animation, path animation, particle systems

### 2D/3D Fusion Development
- **Hybrid Rendering**: Simultaneously render 2D and 3D content in the same scene
- **UI Overlay**: 2D UI interface on 3D scene
- **Texture Mapping**: Use 2D content as 3D object textures
- **Coordinate Transformation**: Conversion between 2D screen coordinates and 3D world coordinates
- **Interaction Integration**: Interaction between 2D UI controls and 3D scene

## Development Environment Setup

### System Requirements
- **Operating System**: Windows 10+, Ubuntu 18.04+, macOS 10.14+
- **Qt Version**: Qt 5.15+ or Qt 6.2+
- **Compiler**:
  - Windows: MSVC 2019+ or MinGW 8.1+
  - Linux: GCC 7+ or Clang 8+
  - macOS: Xcode 11+
- **Graphics Driver**: Graphics driver supporting OpenGL 3.2+

### Installation Steps

#### 1. Install Qt
```bash
# Use Qt online installer
./qt-unified-linux-x64-online.run

# Or use package manager (Ubuntu)
sudo apt-get install qt6-base-dev qt6-3d-dev
```

#### 2. Configure Development Environment
```bash
# Set environment variables
export QT_DIR=/opt/Qt/6.5.0/gcc_64
export PATH=$QT_DIR/bin:$PATH
export LD_LIBRARY_PATH=$QT_DIR/lib:$LD_LIBRARY_PATH
```

#### 3. Verify Installation
```cpp
// test_qt3d.cpp
#include <Qt3DCore/QEntity>
#include <Qt3DRender/QCamera>
#include <QApplication>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Create basic 3D scene
    Qt3DCore::QEntity *rootEntity = new Qt3DCore::QEntity;
    
    qDebug() << "Qt 3D installation successful!";
    return 0;
}
```

## Core Architecture

### ECS Architecture Pattern

Qt 3D adopts Entity-Component-System (ECS) architecture:

```cpp
// Entity - objects in the scene
Qt3DCore::QEntity *cubeEntity = new Qt3DCore::QEntity(rootEntity);

// Component - properties and behaviors of objects
Qt3DCore::QTransform *cubeTransform = new Qt3DCore::QTransform();
Qt3DRender::QMesh *cubeMesh = new Qt3DRender::QMesh();
Qt3DExtras::QPhongMaterial *cubeMaterial = new Qt3DExtras::QPhongMaterial();

// Add components to entity
cubeEntity->addComponent(cubeTransform);
cubeEntity->addComponent(cubeMesh);
cubeEntity->addComponent(cubeMaterial);
```

### Rendering Pipeline

```cpp
// Create render settings
Qt3DRender::QRenderSettings *renderSettings = new Qt3DRender::QRenderSettings();

// Configure rendering pipeline
Qt3DRender::QRenderSurfaceSelector *surfaceSelector = new Qt3DRender::QRenderSurfaceSelector();
Qt3DRender::QViewport *viewport = new Qt3DRender::QViewport(surfaceSelector);
Qt3DRender::QCameraSelector *cameraSelector = new Qt3DRender::QCameraSelector(viewport);
Qt3DRender::QClearBuffers *clearBuffers = new Qt3DRender::QClearBuffers(cameraSelector);

// Set clear color
clearBuffers->setBuffers(Qt3DRender::QClearBuffers::ColorDepthBuffer);
clearBuffers->setClearColor(QColor(64, 64, 64));
```

## Development Process

### 1. Project Initialization

```cpp
// main.cpp
#include <QApplication>
#include <Qt3DExtras/Qt3DWindow>
#include <Qt3DExtras/QFirstPersonCameraController>
#include <Qt3DCore/QEntity>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Create 3D window
    Qt3DExtras::Qt3DWindow *view = new Qt3DExtras::Qt3DWindow();
    view->defaultFrameGraph()->setClearColor(QColor(QRgb(0x4d4d4f)));
    
    // Create root entity
    Qt3DCore::QEntity *rootEntity = new Qt3DCore::QEntity();
    
    // Set scene
    view->setRootEntity(rootEntity);
    view->show();
    
    return app.exec();
}
```

### 2. Scene Construction

```cpp
// Create camera
Qt3DRender::QCamera *cameraEntity = view->camera();
cameraEntity->lens()->setPerspectiveProjection(45.0f, 16.0f/9.0f, 0.1f, 1000.0f);
cameraEntity->setPosition(QVector3D(0, 0, 20.0f));
cameraEntity->setUpVector(QVector3D(0, 1, 0));
cameraEntity->setViewCenter(QVector3D(0, 0, 0));

// Add camera controller
Qt3DExtras::QFirstPersonCameraController *camController = 
    new Qt3DExtras::QFirstPersonCameraController(rootEntity);
camController->setCamera(cameraEntity);
```

### 3. Geometry Creation

```cpp
// Create cube
Qt3DCore::QEntity *cubeEntity = new Qt3DCore::QEntity(rootEntity);

// Geometry component
Qt3DExtras::QCuboidMesh *cubeMesh = new Qt3DExtras::QCuboidMesh();
cubeMesh->setXExtent(2.0f);
cubeMesh->setYExtent(2.0f);
cubeMesh->setZExtent(2.0f);

// Transform component
Qt3DCore::QTransform *cubeTransform = new Qt3DCore::QTransform();
cubeTransform->setTranslation(QVector3D(0.0f, 0.0f, 0.0f));

// Material component
Qt3DExtras::QPhongMaterial *cubeMaterial = new Qt3DExtras::QPhongMaterial();
cubeMaterial->setDiffuse(QColor(QRgb(0xa69929)));

// Add components to entity
cubeEntity->addComponent(cubeMesh);
cubeEntity->addComponent(cubeTransform);
cubeEntity->addComponent(cubeMaterial);
```

### 4. Animation Implementation

```cpp
// Create animation
QPropertyAnimation *cubeRotateTransformAnimation = new QPropertyAnimation(cubeTransform);
cubeRotateTransformAnimation->setTargetObject(cubeTransform);
cubeRotateTransformAnimation->setPropertyName("rotation");
cubeRotateTransformAnimation->setStartValue(QVariant::fromValue(QQuaternion::fromAxisAndAngle(QVector3D(1, 0, 0), 0)));
cubeRotateTransformAnimation->setEndValue(QVariant::fromValue(QQuaternion::fromAxisAndAngle(QVector3D(1, 0, 0), 360)));
cubeRotateTransformAnimation->setDuration(5000);
cubeRotateTransformAnimation->setLoopCount(-1);
cubeRotateTransformAnimation->start();
```

## 2D/3D Fusion Development

### 1. 2D/3D Mixed Scene in QML

```qml
import QtQuick 2.15
import QtQuick.Scene3D 2.15
import Qt3D.Core 2.15
import Qt3D.Render 2.15
import Qt3D.Extras 2.15

ApplicationWindow {
    width: 1200
    height: 800

    // 2D background
    Rectangle {
        anchors.fill: parent
        gradient: Gradient {
            GradientStop { position: 0.0; color: "#87CEEB" }
            GradientStop { position: 1.0; color: "#98FB98" }
        }
    }

    // 3D scene
    Scene3D {
        id: scene3d
        anchors.fill: parent
        aspects: ["input", "logic"]
        cameraAspectRatioMode: Scene3D.AutomaticAspectRatio

        Entity {
            id: sceneRoot

            Camera {
                id: camera
                projectionType: CameraLens.PerspectiveProjection
                fieldOfView: 45
                aspectRatio: 16/9
                nearPlane: 0.1
                farPlane: 1000.0
                position: Qt.vector3d(0.0, 0.0, 20.0)
                upVector: Qt.vector3d(0.0, 1.0, 0.0)
                viewCenter: Qt.vector3d(0.0, 0.0, 0.0)
            }

            components: [
                RenderSettings {
                    activeFrameGraph: ForwardRenderer {
                        clearColor: Qt.rgba(0, 0, 0, 0) // Transparent background
                        camera: camera
                    }
                },
                InputSettings { }
            ]

            // 3D cube
            Entity {
                id: cubeEntity

                components: [
                    CuboidMesh {
                        xExtent: 4
                        yExtent: 4
                        zExtent: 4
                    },
                    PhongMaterial {
                        diffuse: "lightblue"
                        ambient: "blue"
                        specular: "white"
                        shininess: 150.0
                    },
                    Transform {
                        id: cubeTransform
                        translation: Qt.vector3d(0, 0, 0)

                        PropertyAnimation on rotationY {
                            loops: Animation.Infinite
                            from: 0
                            to: 360
                            duration: 5000
                        }
                    }
                ]
            }
        }
    }

    // 2D UI overlay
    Rectangle {
        id: uiPanel
        width: 300
        height: 200
        x: 20
        y: 20
        color: Qt.rgba(0, 0, 0, 0.7)
        radius: 10

        Column {
            anchors.centerIn: parent
            spacing: 10

            Text {
                text: "3D Scene Control"
                color: "white"
                font.pixelSize: 18
                font.bold: true
            }

            Slider {
                id: rotationSpeedSlider
                from: 0
                to: 10
                value: 5
                onValueChanged: {
                    // Control 3D animation speed
                    cubeTransform.rotationY = value * 36
                }
            }

            Text {
                text: "Rotation Speed: " + rotationSpeedSlider.value.toFixed(1)
                color: "white"
                font.pixelSize: 14
            }
        }
    }

    // 2D information display
    Text {
        anchors.bottom: parent.bottom
        anchors.right: parent.right
        anchors.margins: 20
        text: "2D/3D Fusion Demo"
        color: "darkblue"
        font.pixelSize: 16
        font.bold: true
    }
}
```

### 2. 2D Content as 3D Texture

```cpp
// Create 2D content as 3D texture
class Dynamic2DTexture : public Qt3DRender::QPaintedTextureImage
{
    Q_OBJECT

public:
    Dynamic2DTexture(QNode *parent = nullptr)
        : Qt3DRender::QPaintedTextureImage(parent)
    {
        setSize(QSize(512, 512));
    }

protected:
    void paint(QPainter *painter) override
    {
        // Draw 2D content
        painter->fillRect(rect(), Qt::white);

        // Draw chart
        painter->setPen(QPen(Qt::blue, 3));
        painter->drawLine(50, 400, 450, 100);

        // Draw text
        painter->setPen(Qt::black);
        painter->setFont(QFont("Arial", 24));
        painter->drawText(rect(), Qt::AlignCenter, "Dynamic 2D Content");

        // Draw real-time data
        painter->setPen(QPen(Qt::red, 2));
        for (int i = 0; i < 10; ++i) {
            int x = 50 + i * 40;
            int y = 300 - qrand() % 200;
            painter->drawEllipse(x-5, y-5, 10, 10);
        }
    }
};

// Use in 3D scene
Qt3DCore::QEntity *planeEntity = new Qt3DCore::QEntity(rootEntity);

// Plane geometry
Qt3DExtras::QPlaneMesh *planeMesh = new Qt3DExtras::QPlaneMesh();
planeMesh->setWidth(10.0f);
planeMesh->setHeight(10.0f);

// Material using dynamic 2D texture
Qt3DExtras::QTextureMaterial *planeMaterial = new Qt3DExtras::QTextureMaterial();
Qt3DRender::QTexture2D *texture = new Qt3DRender::QTexture2D();
texture->addTextureImage(new Dynamic2DTexture());
planeMaterial->setTexture(texture);

// Transform
Qt3DCore::QTransform *planeTransform = new Qt3DCore::QTransform();
planeTransform->setRotationX(90);

planeEntity->addComponent(planeMesh);
planeEntity->addComponent(planeMaterial);
planeEntity->addComponent(planeTransform);
```

### 3. Coordinate System Conversion

```cpp
// 3D world coordinates to 2D screen coordinates
class CoordinateConverter : public QObject
{
    Q_OBJECT

public:
    QPointF worldToScreen(const QVector3D& worldPos,
                         Qt3DRender::QCamera* camera,
                         const QSize& viewportSize)
    {
        // Get view matrix and projection matrix
        QMatrix4x4 viewMatrix = camera->viewMatrix();
        QMatrix4x4 projMatrix = camera->projectionMatrix();

        // World coordinates to clip coordinates
        QVector4D clipPos = projMatrix * viewMatrix * QVector4D(worldPos, 1.0f);

        // Perspective division
        if (clipPos.w() != 0.0f) {
            clipPos /= clipPos.w();
        }

        // NDC coordinates to screen coordinates
        float screenX = (clipPos.x() + 1.0f) * 0.5f * viewportSize.width();
        float screenY = (1.0f - clipPos.y()) * 0.5f * viewportSize.height();

        return QPointF(screenX, screenY);
    }

    QVector3D screenToWorld(const QPointF& screenPos,
                           float depth,
                           Qt3DRender::QCamera* camera,
                           const QSize& viewportSize)
    {
        // Screen coordinates to NDC
        float ndcX = (2.0f * screenPos.x()) / viewportSize.width() - 1.0f;
        float ndcY = 1.0f - (2.0f * screenPos.y()) / viewportSize.height();

        // NDC to world coordinates
        QMatrix4x4 viewMatrix = camera->viewMatrix();
        QMatrix4x4 projMatrix = camera->projectionMatrix();
        QMatrix4x4 invMatrix = (projMatrix * viewMatrix).inverted();

        QVector4D worldPos = invMatrix * QVector4D(ndcX, ndcY, depth, 1.0f);

        if (worldPos.w() != 0.0f) {
            worldPos /= worldPos.w();
        }

        return worldPos.toVector3D();
    }
};
```

### 4. 2D UI Interaction with 3D Scene

```cpp
// Mouse picking of 3D objects
class ObjectPicker : public QObject
{
    Q_OBJECT

public slots:
    void handleMouseClick(const QPointF& position)
    {
        // Create ray
        QVector3D rayOrigin = camera->position();
        QVector3D rayDirection = screenToWorldDirection(position);

        // Detect intersection with 3D objects
        for (auto* entity : scene3DEntities) {
            if (rayIntersectsEntity(rayOrigin, rayDirection, entity)) {
                // Show 2D information panel
                showInfoPanel(entity, position);
                break;
            }
        }
    }

private:
    void showInfoPanel(Qt3DCore::QEntity* entity, const QPointF& screenPos)
    {
        // Create 2D information panel
        QQuickItem* infoPanel = qmlEngine->create("InfoPanel.qml");
        infoPanel->setProperty("targetEntity", QVariant::fromValue(entity));
        infoPanel->setProperty("x", screenPos.x());
        infoPanel->setProperty("y", screenPos.y());
        infoPanel->setParent(rootItem);
    }

    bool rayIntersectsEntity(const QVector3D& rayOrigin,
                           const QVector3D& rayDirection,
                           Qt3DCore::QEntity* entity)
    {
        // Simplified bounding box detection
        // Actual applications need more precise collision detection
        return true; // Placeholder
    }
};
```

## Best Practices

### 1. Performance Optimization Principles
- **Batch Rendering**: Merge similar geometries to reduce draw calls
- **Texture Optimization**: Use texture atlases and compressed texture formats
- **LOD Management**: Dynamically adjust model details based on distance
- **Frustum Culling**: Only render visible objects
- **Memory Management**: Release unused resources promptly

### 2. Code Organization
```cpp
// Recommended project structure
src/
├── main.cpp
├── scene/
│   ├── SceneManager.h/cpp
│   ├── Entity.h/cpp
│   └── Component.h/cpp
├── render/
│   ├── Renderer.h/cpp
│   ├── Material.h/cpp
│   └── Shader.h/cpp
├── assets/
│   ├── models/
│   ├── textures/
│   └── shaders/
└── utils/
    ├── MathUtils.h/cpp
    └── ResourceManager.h/cpp
```

### 3. Resource Management
```cpp
class ResourceManager {
public:
    static ResourceManager& instance() {
        static ResourceManager instance;
        return instance;
    }

    Qt3DRender::QMesh* loadMesh(const QString& path) {
        if (m_meshCache.contains(path)) {
            return m_meshCache[path];
        }

        Qt3DRender::QMesh* mesh = new Qt3DRender::QMesh();
        mesh->setSource(QUrl::fromLocalFile(path));
        m_meshCache[path] = mesh;
        return mesh;
    }

private:
    QHash<QString, Qt3DRender::QMesh*> m_meshCache;
};
```

### 4. Error Handling
```cpp
// Check OpenGL errors
void checkGLError(const QString& operation) {
    GLenum error = glGetError();
    if (error != GL_NO_ERROR) {
        qWarning() << "OpenGL error in" << operation << ":" << error;
    }
}

// Resource loading error handling
Qt3DRender::QMesh* loadMeshSafely(const QString& path) {
    QFileInfo fileInfo(path);
    if (!fileInfo.exists()) {
        qWarning() << "Mesh file not found:" << path;
        return nullptr;
    }

    Qt3DRender::QMesh* mesh = new Qt3DRender::QMesh();
    mesh->setSource(QUrl::fromLocalFile(path));
    return mesh;
}
```

## Performance Optimization

### 1. Rendering Optimization
- **Instanced Rendering**: Use instanced rendering for identical geometries
- **Texture Compression**: Use compressed formats like DXT, ETC, ASTC
- **Mipmap Generation**: Automatically generate multi-level textures to improve distant rendering quality
- **Depth Pre-pass**: Use depth pre-pass to reduce overdraw

### 2. Memory Optimization
- **Object Pooling**: Reuse frequently created and destroyed objects
- **Smart Pointers**: Use Qt's smart pointers for memory management
- **Resource Streaming**: Load large resources on demand

### 3. CPU Optimization
- **Multi-threading**: Utilize Qt's multi-threading capabilities for parallel computation
- **Space Partitioning**: Use data structures like octrees to optimize collision detection
- **Cache-friendly**: Optimize data structures to improve cache hit rates

## Debugging and Testing

### 1. Debugging Tools
```cpp
// Enable Qt 3D debug output
QLoggingCategory::setFilterRules("Qt3D.*.debug=true");

// Performance analysis
Qt3DRender::QRenderSettings *renderSettings = new Qt3DRender::QRenderSettings();
renderSettings->setRenderPolicy(Qt3DRender::QRenderSettings::OnDemand);
```

### 2. Unit Testing
```cpp
// Test geometry creation
class GeometryTest : public QObject {
    Q_OBJECT

private slots:
    void testCubeCreation() {
        Qt3DExtras::QCuboidMesh cube;
        cube.setXExtent(2.0f);
        QCOMPARE(cube.xExtent(), 2.0f);
    }

    void testTransform() {
        Qt3DCore::QTransform transform;
        transform.setTranslation(QVector3D(1, 2, 3));
        QCOMPARE(transform.translation(), QVector3D(1, 2, 3));
    }
};
```

### 3. Performance Testing
```cpp
// FPS calculation
class FPSCounter : public QObject {
    Q_OBJECT

public slots:
    void update() {
        static int frameCount = 0;
        static QTime lastTime = QTime::currentTime();

        frameCount++;
        QTime currentTime = QTime::currentTime();
        int elapsed = lastTime.msecsTo(currentTime);

        if (elapsed >= 1000) {
            float fps = frameCount * 1000.0f / elapsed;
            qDebug() << "FPS:" << fps;
            frameCount = 0;
            lastTime = currentTime;
        }
    }
};
```

---

*This document is continuously updated. For more detailed information, please refer to the [Qt 3D Official Documentation](https://doc.qt.io/qt-6/qt3d-index.html)* 