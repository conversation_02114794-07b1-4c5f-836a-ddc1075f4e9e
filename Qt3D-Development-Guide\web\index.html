<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="pageTitle">3D 引擎开发指南 - Qt 3D | Unity | Unreal Engine | Kanzi</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text" data-i18n="nav-logo-text">3D 引擎指南</span>
            </div>
            <div class="nav-menu">
                <a href="#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero">
        <div class="hero-container">
            <h1 class="hero-title" data-i18n="hero-title">3D 引擎技术对比参考</h1>
            <p class="hero-subtitle" data-i18n="hero-subtitle">Qt 3D、Unity、Unreal Engine、Kanzi、Cocos、Godot 技术特性对比</p>
            <p class="hero-description" data-i18n="hero-description">
                客观分析六大主流 3D 引擎的技术特性、开发特点和适用场景，
                为技术选型提供参考信息。
            </p>
            <div class="hero-buttons">
                <a href="#engines" class="btn btn-primary" data-i18n="hero-btn-primary">开始对比</a>
                <a href="#guides" class="btn btn-secondary" data-i18n="hero-btn-secondary">查看指南</a>
            </div>
        </div>
    </section>

    <!-- 六大引擎展示 -->
    <section id="engines" class="engines-showcase">
        <div class="container">
            <h2 class="section-title" data-i18n="showcase-title">主流 3D 引擎技术特性</h2>
            <div class="engines-grid">
                <!-- Qt 3D -->
                <div class="engine-showcase qt3d">
                    <div class="engine-header">
                        <div class="engine-logo">
                            <img src="assets/images/qt-logo.svg" alt="Qt 3D Logo">
                        </div>
                        <h3 data-i18n="qt3d-title">Qt 3D</h3>
                        <p class="engine-tagline" data-i18n="qt3d-tagline">企业级跨平台 3D 框架</p>
                    </div>
                    <div class="engine-highlights">
                        <ul>
                            <li data-i18n="qt3d-highlight-1">深度集成 Qt 生态系统</li>
                            <li data-i18n="qt3d-highlight-2">2D/3D 无缝融合</li>
                            <li data-i18n="qt3d-highlight-3">企业级支持</li>
                            <li data-i18n="qt3d-highlight-4">轻量级运行时</li>
                        </ul>
                    </div>
                    <div class="engine-demo">
                        <h4 data-i18n="qt3d-demo-title">典型应用</h4>
                        <p data-i18n="qt3d-demo-text">汽车仪表盘、工业控制界面、医疗设备显示</p>
                    </div>
                    <div class="engine-actions">
                        <a href="https://www.qt.io/product/qt6/3d-graphics" target="_blank" class="btn btn-primary" data-i18n="btn-official-website">官方网站</a>
                        <a href="docs/zh/qt3d-development-guide.html" target="_blank" class="btn btn-secondary" data-i18n="btn-dev-guide">开发指南</a>
                    </div>
                </div>

                <!-- Unity -->
                <div class="engine-showcase unity">
                    <div class="engine-header">
                        <div class="engine-logo unity-logo">
                            <span>Unity</span>
                        </div>
                        <h3 data-i18n="unity-title">Unity</h3>
                        <p class="engine-tagline" data-i18n="unity-tagline">跨平台游戏开发引擎</p>
                    </div>
                    <div class="engine-highlights">
                        <ul>
                            <li data-i18n="unity-highlight-1">易于学习的编辑器</li>
                            <li data-i18n="unity-highlight-2">丰富的资源商店</li>
                            <li data-i18n="unity-highlight-3">强大的社区支持</li>
                            <li data-i18n="unity-highlight-4">多平台一键发布</li>
                        </ul>
                    </div>
                    <div class="engine-demo">
                        <h4 data-i18n="unity-demo-title">热门Demo</h4>
                        <p data-i18n="unity-demo-text">URP 3D Sample、Fantasy Kingdom、Behavior Package Demo</p>
                    </div>
                    <div class="engine-actions">
                        <a href="https://unity.com/" target="_blank" class="btn btn-primary" data-i18n="btn-official-website">官方网站</a>
                        <a href="docs/zh/unity-development-guide.html" target="_blank" class="btn btn-secondary" data-i18n="btn-dev-guide">开发指南</a>
                    </div>
                </div>

                <!-- Unreal Engine -->
                <div class="engine-showcase unreal">
                    <div class="engine-header">
                        <div class="engine-logo unreal-logo">
                            <span>UE</span>
                        </div>
                        <h3 data-i18n="unreal-title">Unreal Engine</h3>
                        <p class="engine-tagline" data-i18n="unreal-tagline">AAA 级游戏开发引擎</p>
                    </div>
                    <div class="engine-highlights">
                        <ul>
                            <li data-i18n="unreal-highlight-1">顶级渲染质量</li>
                            <li data-i18n="unreal-highlight-2">Blueprint 可视化编程</li>
                            <li data-i18n="unreal-highlight-3">完整的开发工具链</li>
                            <li data-i18n="unreal-highlight-4">电影级实时渲染</li>
                        </ul>
                    </div>
                    <div class="engine-demo">
                        <h4 data-i18n="unreal-demo-title">技术Demo</h4>
                        <p data-i18n="unreal-demo-text">Lumen in the Land of Nanite、Lyra Starter Game、MetaHuman Creator</p>
                    </div>
                    <div class="engine-actions">
                        <a href="https://www.unrealengine.com/" target="_blank" class="btn btn-primary" data-i18n="btn-official-website">官方网站</a>
                        <a href="docs/zh/unreal-development-guide.html" target="_blank" class="btn btn-secondary" data-i18n="btn-dev-guide">开发指南</a>
                    </div>
                </div>

                <!-- Kanzi -->
                <div class="engine-showcase kanzi">
                    <div class="engine-header">
                        <div class="engine-logo kanzi-logo">
                            <span>Kanzi</span>
                        </div>
                        <h3 data-i18n="kanzi-title">Kanzi</h3>
                        <p class="engine-tagline" data-i18n="kanzi-tagline">嵌入式 HMI 专业平台</p>
                    </div>
                    <div class="engine-highlights">
                        <ul>
                            <li data-i18n="kanzi-highlight-1">嵌入式系统优化</li>
                            <li data-i18n="kanzi-highlight-2">极低资源占用</li>
                            <li data-i18n="kanzi-highlight-3">汽车行业标准</li>
                            <li data-i18n="kanzi-highlight-4">专业 HMI 工具</li>
                        </ul>
                    </div>
                    <div class="engine-demo">
                        <h4 data-i18n="kanzi-demo-title">行业案例</h4>
                        <p data-i18n="kanzi-demo-text">奔驰MBUX、宝马iDrive、奥迪MMI、沃尔沃Sensus</p>
                    </div>
                    <div class="engine-actions">
                        <a href="https://www.rightware.com/kanzi/" target="_blank" class="btn btn-primary" data-i18n="btn-official-website">官方网站</a>
                        <a href="docs/zh/kanzi-development-guide.html" target="_blank" class="btn btn-secondary" data-i18n="btn-dev-guide">开发指南</a>
                    </div>
                </div>

                <!-- Cocos Creator -->
                <div class="engine-showcase cocos">
                    <div class="engine-header">
                        <div class="engine-logo cocos-logo">
                            <span>Cocos</span>
                        </div>
                        <h3 data-i18n="cocos-title">Cocos Creator</h3>
                        <p class="engine-tagline" data-i18n="cocos-tagline">跨平台2D/3D游戏引擎</p>
                    </div>
                    <div class="engine-highlights">
                        <ul>
                            <li data-i18n="cocos-highlight-1">轻量级高性能</li>
                            <li data-i18n="cocos-highlight-2">强大的2D能力</li>
                            <li data-i18n="cocos-highlight-3">亚洲市场应用广泛</li>
                            <li data-i18n="cocos-highlight-4">完整开发工具链</li>
                        </ul>
                    </div>
                    <div class="engine-demo">
                        <h4 data-i18n="cocos-demo-title">应用案例</h4>
                        <p data-i18n="cocos-demo-text">多款知名移动游戏采用，在亚洲市场应用广泛</p>
                    </div>
                    <div class="engine-actions">
                        <a href="https://www.cocos.com/" target="_blank" class="btn btn-primary" data-i18n="btn-official-website">官方网站</a>
                        <a href="docs/zh/cocos-development-guide.html" target="_blank" class="btn btn-secondary" data-i18n="btn-dev-guide">开发指南</a>
                    </div>
                </div>

                <!-- Godot Engine -->
                <div class="engine-showcase godot">
                    <div class="engine-header">
                        <div class="engine-logo godot-logo">
                            <span>Godot</span>
                        </div>
                        <h3 data-i18n="godot-title">Godot Engine</h3>
                        <p class="engine-tagline" data-i18n="godot-tagline">开源免费游戏引擎</p>
                    </div>
                    <div class="engine-highlights">
                        <ul>
                            <li data-i18n="godot-highlight-1">完全免费开源</li>
                            <li data-i18n="godot-highlight-2">节点系统设计</li>
                            <li data-i18n="godot-highlight-3">多语言支持</li>
                            <li data-i18n="godot-highlight-4">轻量级架构</li>
                        </ul>
                    </div>
                    <div class="engine-demo">
                        <h4 data-i18n="godot-demo-title">社区项目</h4>
                        <p data-i18n="godot-demo-text">Brotato、Dome Keeper、Cassette Beasts等独立游戏</p>
                    </div>
                    <div class="engine-actions">
                        <a href="https://godotengine.org/" target="_blank" class="btn btn-primary" data-i18n="btn-official-website">官方网站</a>
                        <a href="docs/zh/godot-development-guide.html" target="_blank" class="btn btn-secondary" data-i18n="btn-dev-guide">开发指南</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 技术特性对比 -->
    <section id="features" class="features-comparison">
        <div class="container">
            <h2 class="section-title" data-i18n="features-comparison-title">技术特性深度对比</h2>
            <div class="features-tabs">
                <button class="tab-button active" data-tab="rendering" data-i18n="tab-rendering">渲染技术</button>
                <button class="tab-button" data-tab="development" data-i18n="tab-development">开发体验</button>
                <button class="tab-button" data-tab="efficiency" data-i18n="tab-efficiency">开发效率</button>
                <button class="tab-button" data-tab="platform" data-i18n="tab-platform">平台支持</button>
                <button class="tab-button" data-tab="performance" data-i18n="tab-performance">性能表现</button>
            </div>

            <div class="tab-content active" id="rendering">
                <div class="comparison-grid">
                    <div class="feature-comparison">
                        <h3 data-i18n="qt3d-title">Qt 3D</h3>
                        <ul>
                            <li data-i18n="rendering-qt3d-api"><span class="feature-name">渲染API:</span> OpenGL, Vulkan, Metal, D3D11</li>
                            <li data-i18n="rendering-qt3d-lighting"><span class="feature-name">光照:</span> 前向渲染，延迟渲染</li>
                            <li data-i18n="rendering-qt3d-material"><span class="feature-name">材质:</span> 基于节点的材质编辑器</li>
                            <li data-i18n="rendering-qt3d-posteffect"><span class="feature-name">后处理:</span> 基础后处理效果</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="unity-title">Unity</h3>
                        <ul>
                            <li data-i18n="rendering-unity-api"><span class="feature-name">渲染API:</span> URP, HDRP, Built-in RP</li>
                            <li data-i18n="rendering-unity-lighting"><span class="feature-name">光照:</span> 实时GI，烘焙光照，混合光照</li>
                            <li data-i18n="rendering-unity-material"><span class="feature-name">材质:</span> Shader Graph 可视化编辑</li>
                            <li data-i18n="rendering-unity-posteffect"><span class="feature-name">后处理:</span> 丰富的后处理栈</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="unreal-title">Unreal Engine</h3>
                        <ul>
                            <li data-i18n="rendering-unreal-api"><span class="feature-name">渲染API:</span> Lumen, Nanite, TSR</li>
                            <li data-i18n="rendering-unreal-lighting"><span class="feature-name">光照:</span> 动态全局光照 Lumen</li>
                            <li data-i18n="rendering-unreal-material"><span class="feature-name">材质:</span> 强大的材质编辑器</li>
                            <li data-i18n="rendering-unreal-posteffect"><span class="feature-name">后处理:</span> 电影级后处理</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="kanzi-title">Kanzi</h3>
                        <ul>
                            <li data-i18n="rendering-kanzi-api"><span class="feature-name">渲染API:</span> OpenGL ES, Vulkan</li>
                            <li data-i18n="rendering-kanzi-lighting"><span class="feature-name">光照:</span> 优化的实时光照</li>
                            <li data-i18n="rendering-kanzi-material"><span class="feature-name">材质:</span> 嵌入式优化材质</li>
                            <li data-i18n="rendering-kanzi-posteffect"><span class="feature-name">后处理:</span> 轻量级效果</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="cocos-title">Cocos Creator</h3>
                        <ul>
                            <li data-i18n="rendering-cocos-api"><span class="feature-name">渲染API:</span> OpenGL ES, Metal, WebGL</li>
                            <li data-i18n="rendering-cocos-lighting"><span class="feature-name">光照:</span> 2D优化，3D基础光照</li>
                            <li data-i18n="rendering-cocos-material"><span class="feature-name">材质:</span> 移动端优化材质</li>
                            <li data-i18n="rendering-cocos-posteffect"><span class="feature-name">后处理:</span> 轻量级2D/3D效果</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="godot-title">Godot Engine</h3>
                        <ul>
                            <li data-i18n="rendering-godot-api"><span class="feature-name">渲染API:</span> OpenGL ES, Vulkan, WebGL</li>
                            <li data-i18n="rendering-godot-lighting"><span class="feature-name">光照:</span> 实时光照，烘焙光照</li>
                            <li data-i18n="rendering-godot-material"><span class="feature-name">材质:</span> Shader 编辑器，Visual Shader</li>
                            <li data-i18n="rendering-godot-posteffect"><span class="feature-name">后处理:</span> 基础后处理效果</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="development">
                <div class="comparison-grid">
                    <div class="feature-comparison">
                        <h3 data-i18n="qt3d-title">Qt 3D</h3>
                        <ul>
                            <li data-i18n="dev-qt3d-lang"><span class="feature-name">编程语言:</span> C++, QML</li>
                            <li data-i18n="dev-qt3d-ide"><span class="feature-name">IDE:</span> Qt Creator</li>
                            <li data-i18n="dev-qt3d-debug"><span class="feature-name">调试:</span> Qt 调试工具</li>
                            <li data-i18n="dev-qt3d-curve"><span class="feature-name">学习曲线:</span> 中等（需要Qt基础）</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="unity-title">Unity</h3>
                        <ul>
                            <li data-i18n="dev-unity-lang"><span class="feature-name">编程语言:</span> C#, Visual Scripting</li>
                            <li data-i18n="dev-unity-ide"><span class="feature-name">IDE:</span> Unity Editor</li>
                            <li data-i18n="dev-unity-debug"><span class="feature-name">调试:</span> 内置调试器，Profiler</li>
                            <li data-i18n="dev-unity-curve"><span class="feature-name">学习曲线:</span> 简单（可视化编辑器）</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="unreal-title">Unreal Engine</h3>
                        <ul>
                            <li data-i18n="dev-unreal-lang"><span class="feature-name">编程语言:</span> C++, Blueprint</li>
                            <li data-i18n="dev-unreal-ide"><span class="feature-name">IDE:</span> Unreal Editor</li>
                            <li data-i18n="dev-unreal-debug"><span class="feature-name">调试:</span> 强大的调试和分析工具</li>
                            <li data-i18n="dev-unreal-curve"><span class="feature-name">学习曲线:</span> 陡峭（功能复杂）</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="kanzi-title">Kanzi</h3>
                        <ul>
                            <li data-i18n="dev-kanzi-lang"><span class="feature-name">编程语言:</span> C++</li>
                            <li data-i18n="dev-kanzi-ide"><span class="feature-name">IDE:</span> Kanzi Studio</li>
                            <li data-i18n="dev-kanzi-debug"><span class="feature-name">调试:</span> 专业HMI调试工具</li>
                            <li data-i18n="dev-kanzi-curve"><span class="feature-name">学习曲线:</span> 陡峭（专业工具）</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="cocos-title">Cocos Creator</h3>
                        <ul>
                            <li data-i18n="dev-cocos-lang"><span class="feature-name">编程语言:</span> TypeScript, JavaScript</li>
                            <li data-i18n="dev-cocos-ide"><span class="feature-name">IDE:</span> Cocos Creator Editor</li>
                            <li data-i18n="dev-cocos-debug"><span class="feature-name">调试:</span> 内置调试器，Chrome DevTools</li>
                            <li data-i18n="dev-cocos-curve"><span class="feature-name">学习曲线:</span> 中等（Web技术栈）</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="godot-title">Godot Engine</h3>
                        <ul>
                            <li data-i18n="dev-godot-lang"><span class="feature-name">编程语言:</span> GDScript, C#, VisualScript</li>
                            <li data-i18n="dev-godot-ide"><span class="feature-name">IDE:</span> Godot Editor</li>
                            <li data-i18n="dev-godot-debug"><span class="feature-name">调试:</span> 内置调试器，Profiler</li>
                            <li data-i18n="dev-godot-curve"><span class="feature-name">学习曲线:</span> 简单（节点系统）</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="efficiency">
                <div class="comparison-grid">
                    <div class="feature-comparison">
                        <h3 data-i18n="qt3d-title">Qt 3D</h3>
                        <ul>
                            <li data-i18n="eff-qt3d-curve"><span class="feature-name">学习曲线:</span> 中等（需要Qt基础）</li>
                            <li data-i18n="eff-qt3d-speed"><span class="feature-name">开发速度:</span> 中等（企业应用快）</li>
                            <li data-i18n="eff-qt3d-debug"><span class="feature-name">调试效率:</span> 高（专业工具）</li>
                            <li data-i18n="eff-qt3d-team"><span class="feature-name">团队协作:</span> 良好（版本控制友好）</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="unity-title">Unity</h3>
                        <ul>
                            <li data-i18n="eff-unity-curve"><span class="feature-name">学习曲线:</span> 简单（可视化编辑器）</li>
                            <li data-i18n="eff-unity-speed"><span class="feature-name">开发速度:</span> 快（丰富资源商店）</li>
                            <li data-i18n="eff-unity-debug"><span class="feature-name">调试效率:</span> 高（内置工具）</li>
                            <li data-i18n="eff-unity-team"><span class="feature-name">团队协作:</span> 良好（协作工具）</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="unreal-title">Unreal Engine</h3>
                        <ul>
                            <li data-i18n="eff-unreal-curve"><span class="feature-name">学习曲线:</span> 陡峭（功能复杂）</li>
                            <li data-i18n="eff-unreal-speed"><span class="feature-name">开发速度:</span> 中等（Blueprint加速）</li>
                            <li data-i18n="eff-unreal-debug"><span class="feature-name">调试效率:</span> 高（强大工具链）</li>
                            <li data-i18n="eff-unreal-team"><span class="feature-name">团队协作:</span> 优秀（企业级工具）</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="kanzi-title">Kanzi</h3>
                        <ul>
                            <li data-i18n="eff-kanzi-curve"><span class="feature-name">学习曲线:</span> 陡峭（专业工具）</li>
                            <li data-i18n="eff-kanzi-speed"><span class="feature-name">开发速度:</span> 中等（HMI专用）</li>
                            <li data-i18n="eff-kanzi-debug"><span class="feature-name">调试效率:</span> 高（专业调试）</li>
                            <li data-i18n="eff-kanzi-team"><span class="feature-name">团队协作:</span> 良好（工业标准）</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="cocos-title">Cocos Creator</h3>
                        <ul>
                            <li data-i18n="eff-cocos-curve"><span class="feature-name">学习曲线:</span> 中等（Web技术栈）</li>
                            <li data-i18n="eff-cocos-speed"><span class="feature-name">开发速度:</span> 快（2D游戏）</li>
                            <li data-i18n="eff-cocos-debug"><span class="feature-name">调试效率:</span> 中等（Chrome DevTools）</li>
                            <li data-i18n="eff-cocos-team"><span class="feature-name">团队协作:</span> 良好（版本控制）</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="godot-title">Godot Engine</h3>
                        <ul>
                            <li data-i18n="eff-godot-curve"><span class="feature-name">学习曲线:</span> 简单（节点系统）</li>
                            <li data-i18n="eff-godot-speed"><span class="feature-name">开发速度:</span> 快（轻量级）</li>
                            <li data-i18n="eff-godot-debug"><span class="feature-name">调试效率:</span> 中等（内置调试器）</li>
                            <li data-i18n="eff-godot-team"><span class="feature-name">团队协作:</span> 良好（开源友好）</li>
                        </ul>
                    </div>
                </div>
                <div class="efficiency-details">
                    <h3 data-i18n="eff-details-title">开发效率详细分析</h3>
                    <div class="efficiency-metrics">
                        <div class="metric-card">
                            <h4 data-i18n="eff-startup-time">项目启动时间</h4>
                            <div class="metric-comparison">
                                <div class="metric-item">
                                    <span class="engine-name">Godot</span>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: 95%"></div>
                                    </div>
                                    <span class="metric-value" data-i18n="eff-fastest">最快</span>
                                </div>
                                <div class="metric-item">
                                    <span class="engine-name">Unity</span>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: 85%"></div>
                                    </div>
                                    <span class="metric-value" data-i18n="eff-fast">快</span>
                                </div>
                                <div class="metric-item">
                                    <span class="engine-name">Cocos</span>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: 80%"></div>
                                    </div>
                                    <span class="metric-value" data-i18n="eff-fast">快</span>
                                </div>
                                <div class="metric-item">
                                    <span class="engine-name">Qt 3D</span>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: 70%"></div>
                                    </div>
                                    <span class="metric-value" data-i18n="eff-medium">中等</span>
                                </div>
                                <div class="metric-item">
                                    <span class="engine-name">Kanzi</span>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: 60%"></div>
                                    </div>
                                    <span class="metric-value" data-i18n="eff-medium">中等</span>
                                </div>
                                <div class="metric-item">
                                    <span class="engine-name">Unreal</span>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: 45%"></div>
                                    </div>
                                    <span class="metric-value" data-i18n="eff-slow">慢</span>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <h4 data-i18n="eff-learning-cost">学习成本</h4>
                            <div class="metric-comparison">
                                <div class="metric-item">
                                    <span class="engine-name">Godot</span>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: 90%"></div>
                                    </div>
                                    <span class="metric-value" data-i18n="eff-lowest">最低</span>
                                </div>
                                <div class="metric-item">
                                    <span class="engine-name">Unity</span>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: 85%"></div>
                                    </div>
                                    <span class="metric-value" data-i18n="eff-low">低</span>
                                </div>
                                <div class="metric-item">
                                    <span class="engine-name">Cocos</span>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: 70%"></div>
                                    </div>
                                    <span class="metric-value" data-i18n="eff-medium">中等</span>
                                </div>
                                <div class="metric-item">
                                    <span class="engine-name">Qt 3D</span>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: 60%"></div>
                                    </div>
                                    <span class="metric-value" data-i18n="eff-medium">中等</span>
                                </div>
                                <div class="metric-item">
                                    <span class="engine-name">Unreal</span>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: 40%"></div>
                                    </div>
                                    <span class="metric-value" data-i18n="eff-high">高</span>
                                </div>
                                <div class="metric-item">
                                    <span class="engine-name">Kanzi</span>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: 35%"></div>
                                    </div>
                                    <span class="metric-value" data-i18n="eff-high">高</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="platform">
                <div class="comparison-grid">
                    <div class="feature-comparison">
                        <h3 data-i18n="qt3d-title">Qt 3D</h3>
                        <ul>
                            <li data-i18n="platform-qt3d-desktop"><span class="feature-name">桌面:</span> Windows, macOS, Linux</li>
                            <li data-i18n="platform-qt3d-mobile"><span class="feature-name">移动:</span> Android, iOS</li>
                            <li data-i18n="platform-qt3d-embedded"><span class="feature-name">嵌入式:</span> 广泛支持</li>
                            <li data-i18n="platform-qt3d-web"><span class="feature-name">Web:</span> WebAssembly</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="unity-title">Unity</h3>
                        <ul>
                            <li data-i18n="platform-unity-desktop"><span class="feature-name">桌面:</span> Windows, macOS, Linux</li>
                            <li data-i18n="platform-unity-mobile"><span class="feature-name">移动:</span> Android, iOS</li>
                            <li data-i18n="platform-unity-console"><span class="feature-name">游戏机:</span> PlayStation, Xbox, Switch</li>
                            <li data-i18n="platform-unity-web"><span class="feature-name">Web:</span> WebGL</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="unreal-title">Unreal Engine</h3>
                        <ul>
                            <li data-i18n="platform-unreal-desktop"><span class="feature-name">桌面:</span> Windows, macOS, Linux</li>
                            <li data-i18n="platform-unreal-mobile"><span class="feature-name">移动:</span> Android, iOS</li>
                            <li data-i18n="platform-unreal-console"><span class="feature-name">游戏机:</span> PlayStation, Xbox</li>
                            <li data-i18n="platform-unreal-vr"><span class="feature-name">VR/AR:</span> 全面支持</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="kanzi-title">Kanzi</h3>
                        <ul>
                            <li data-i18n="platform-kanzi-auto"><span class="feature-name">汽车:</span> QNX, Linux, Android Auto</li>
                            <li data-i18n="platform-kanzi-industrial"><span class="feature-name">工业:</span> VxWorks, Linux RT</li>
                            <li data-i18n="platform-kanzi-embedded"><span class="feature-name">嵌入式:</span> ARM, x86</li>
                            <li data-i18n="platform-kanzi-realtime"><span class="feature-name">实时:</span> 硬实时系统</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="cocos-title">Cocos Creator</h3>
                        <ul>
                            <li data-i18n="platform-cocos-mobile"><span class="feature-name">移动:</span> Android, iOS (优化)</li>
                            <li data-i18n="platform-cocos-web"><span class="feature-name">Web:</span> H5, 微信小游戏</li>
                            <li data-i18n="platform-cocos-desktop"><span class="feature-name">桌面:</span> Windows, macOS</li>
                            <li data-i18n="platform-cocos-new"><span class="feature-name">新平台:</span> 华为快游戏, OPPO小游戏</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="godot-title">Godot Engine</h3>
                        <ul>
                            <li data-i18n="platform-godot-desktop"><span class="feature-name">桌面:</span> Windows, macOS, Linux</li>
                            <li data-i18n="platform-godot-mobile"><span class="feature-name">移动:</span> Android, iOS</li>
                            <li data-i18n="platform-godot-web"><span class="feature-name">Web:</span> HTML5</li>
                            <li data-i18n="platform-godot-console"><span class="feature-name">游戏机:</span> 社区支持</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="performance">
                <div class="comparison-grid">
                    <div class="feature-comparison">
                        <h3 data-i18n="qt3d-title">Qt 3D</h3>
                        <ul>
                            <li data-i18n="perf-qt3d-memory"><span class="feature-name">内存占用:</span> 低（20-50MB）*</li>
                            <li data-i18n="perf-qt3d-startup"><span class="feature-name">启动时间:</span> 快（1-3秒）*</li>
                            <li data-i18n="perf-qt3d-render"><span class="feature-name">渲染性能:</span> 中等</li>
                            <li data-i18n="perf-qt3d-scene"><span class="feature-name">适用场景:</span> 企业应用，嵌入式</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="unity-title">Unity</h3>
                        <ul>
                            <li data-i18n="perf-unity-memory"><span class="feature-name">内存占用:</span> 中等（100-300MB）**</li>
                            <li data-i18n="perf-unity-startup"><span class="feature-name">启动时间:</span> 中等（3-8秒）**</li>
                            <li data-i18n="perf-unity-render"><span class="feature-name">渲染性能:</span> 良好**</li>
                            <li data-i18n="perf-unity-scene"><span class="feature-name">适用场景:</span> 跨平台游戏开发</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="unreal-title">Unreal Engine</h3>
                        <ul>
                            <li data-i18n="perf-unreal-memory"><span class="feature-name">内存占用:</span> 高（500MB+）**</li>
                            <li data-i18n="perf-unreal-startup"><span class="feature-name">启动时间:</span> 慢（10-30秒）**</li>
                            <li data-i18n="perf-unreal-render"><span class="feature-name">渲染性能:</span> 优秀**</li>
                            <li data-i18n="perf-unreal-scene"><span class="feature-name">适用场景:</span> 高端3D游戏开发</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="kanzi-title">Kanzi</h3>
                        <ul>
                            <li data-i18n="perf-kanzi-memory"><span class="feature-name">内存占用:</span> 极低（5-20MB）*</li>
                            <li data-i18n="perf-kanzi-startup"><span class="feature-name">启动时间:</span> 极快（<1秒）*</li>
                            <li data-i18n="perf-kanzi-render"><span class="feature-name">渲染性能:</span> 嵌入式优化</li>
                            <li data-i18n="perf-kanzi-scene"><span class="feature-name">适用场景:</span> 汽车HMI，工业界面</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="cocos-title">Cocos Creator</h3>
                        <ul>
                            <li data-i18n="perf-cocos-memory"><span class="feature-name">内存占用:</span> 低（30-80MB）***</li>
                            <li data-i18n="perf-cocos-startup"><span class="feature-name">启动时间:</span> 快（1-3秒）***</li>
                            <li data-i18n="perf-cocos-render"><span class="feature-name">渲染性能:</span> 移动端优化</li>
                            <li data-i18n="perf-cocos-scene"><span class="feature-name">适用场景:</span> 移动游戏开发</li>
                        </ul>
                    </div>
                    <div class="feature-comparison">
                        <h3 data-i18n="godot-title">Godot Engine</h3>
                        <ul>
                            <li data-i18n="perf-godot-memory"><span class="feature-name">内存占用:</span> 低（20-60MB）****</li>
                            <li data-i18n="perf-godot-startup"><span class="feature-name">启动时间:</span> 快（1-2秒）****</li>
                            <li data-i18n="perf-godot-render"><span class="feature-name">渲染性能:</span> 中等</li>
                            <li data-i18n="perf-godot-scene"><span class="feature-name">适用场景:</span> 独立游戏开发</li>
                        </ul>
                    </div>
                </div>
                <div class="data-sources">
                    <h4 data-i18n="perf-source-title">数据来源说明</h4>
                    <p data-i18n="perf-source-qt">* 基于Qt官方文档和技术规格</p>
                    <p data-i18n="perf-source-unity-unreal">** 参考<a href="https://pinglestudio.com/blog/unity-vs-unreal-a-detailed-performance-showdown" target="_blank">Pingle Studio技术对比报告</a>等行业分析</p>
                    <p data-i18n="perf-source-cocos">*** 基于Cocos官方技术文档</p>
                    <p data-i18n="perf-source-godot">**** 基于Godot官方文档和社区反馈</p>
                    <p class="disclaimer" data-i18n="perf-source-disclaimer">注：性能数据仅供参考，实际表现因项目复杂度和硬件配置而异</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 国际化支持对比 -->
    <section id="i18n" class="internationalization-showcase">
        <div class="container">
            <h2 class="section-title" data-i18n="i18n-showcase-title">国际化与本地化技术对比</h2>
            <div class="i18n-overview">
                <p data-i18n="i18n-overview">以下对比基于各引擎的官方文档和技术特性，从文本管理、资源本地化、工具支持、技术实现四个维度进行客观分析。</p>
            </div>
            <div class="i18n-grid">
                <div class="i18n-engine">
                    <h3 data-i18n="qt3d-title">Qt 3D</h3>
                    <div class="i18n-features">
                        <div class="i18n-feature">
                            <span class="feature-icon">📝</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-qt-text">文本管理</h4>
                                <p data-i18n="i18n-qt-text-desc">QTranslator系统，.ts/.qm文件格式</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🎨</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-qt-resource">资源本地化</h4>
                                <p data-i18n="i18n-qt-resource-desc">资源系统支持多语言资源变体</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🛠️</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-qt-tools">工具支持</h4>
                                <p data-i18n="i18n-qt-tools-desc">Qt Linguist专业翻译工具</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">⚙️</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-qt-impl">技术实现</h4>
                                <p data-i18n="i18n-qt-impl-desc">完整RTL支持，自动布局镜像</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="i18n-engine">
                    <h3 data-i18n="unity-title">Unity</h3>
                    <div class="i18n-features">
                        <div class="i18n-feature">
                            <span class="feature-icon">📝</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-unity-text">文本管理</h4>
                                <p data-i18n="i18n-unity-text-desc">Localization Package，字符串表系统</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🎨</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-unity-resource">资源本地化</h4>
                                <p data-i18n="i18n-unity-resource-desc">Addressables系统支持资源变体</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🛠️</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-unity-tools">工具支持</h4>
                                <p data-i18n="i18n-unity-tools-desc">CSV工作流，翻译团队协作</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">⚙️</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-unity-impl">技术实现</h4>
                                <p data-i18n="i18n-unity-impl-desc">实时语言切换，伪本地化测试</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="i18n-engine">
                    <h3 data-i18n="unreal-title">Unreal Engine</h3>
                    <div class="i18n-features">
                        <div class="i18n-feature">
                            <span class="feature-icon">📝</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-unreal-text">文本管理</h4>
                                <p data-i18n="i18n-unreal-text-desc">FText系统，支持复数和格式化</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🎨</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-unreal-resource">资源本地化</h4>
                                <p data-i18n="i18n-unreal-resource-desc">资产本地化系统，多语言资源管理</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🛠️</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-unreal-tools">工具支持</h4>
                                <p data-i18n="i18n-unreal-tools-desc">本地化仪表板，翻译编辑器</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">⚙️</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-unreal-impl">技术实现</h4>
                                <p data-i18n="i18n-unreal-impl-desc">自动文本收集，XLIFF标准支持</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="i18n-engine">
                    <h3 data-i18n="kanzi-title">Kanzi</h3>
                    <div class="i18n-features">
                        <div class="i18n-feature">
                            <span class="feature-icon">📝</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-kanzi-text">文本管理</h4>
                                <p data-i18n="i18n-kanzi-text-desc">字符串管理系统，多语言文本支持</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🎨</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-kanzi-resource">资源本地化</h4>
                                <p data-i18n="i18n-kanzi-resource-desc">嵌入式优化的资源管理系统</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🛠️</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-kanzi-tools">工具支持</h4>
                                <p data-i18n="i18n-kanzi-tools-desc">Kanzi Studio集成本地化工具</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">⚙️</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-kanzi-impl">技术实现</h4>
                                <p data-i18n="i18n-kanzi-impl-desc">实时语言切换，汽车行业标准</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="i18n-engine">
                    <h3 data-i18n="cocos-title">Cocos Creator</h3>
                    <div class="i18n-features">
                        <div class="i18n-feature">
                            <span class="feature-icon">📝</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-cocos-text">文本管理</h4>
                                <p data-i18n="i18n-cocos-text-desc">多语言管理器，JSON/CSV格式</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🎨</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-cocos-resource">资源本地化</h4>
                                <p data-i18n="i18n-cocos-resource-desc">资源管理系统，多语言资源变体</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🛠️</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-cocos-tools">工具支持</h4>
                                <p data-i18n="i18n-cocos-tools-desc">JSON/CSV工作流，编辑器集成</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">⚙️</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-cocos-impl">技术实现</h4>
                                <p data-i18n="i18n-cocos-impl-desc">热更新支持，移动端优化</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="i18n-engine">
                    <h3 data-i18n="godot-title">Godot Engine</h3>
                    <div class="i18n-features">
                        <div class="i18n-feature">
                            <span class="feature-icon">📝</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-godot-text">文本管理</h4>
                                <p data-i18n="i18n-godot-text-desc">CSV翻译系统，tr()函数支持</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🎨</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-godot-resource">资源本地化</h4>
                                <p data-i18n="i18n-godot-resource-desc">资源重映射，多语言资源管理</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🛠️</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-godot-tools">工具支持</h4>
                                <p data-i18n="i18n-godot-tools-desc">内置CSV编辑器，社区工具</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">⚙️</span>
                            <div class="feature-content">
                                <h4 data-i18n="i18n-godot-impl">技术实现</h4>
                                <p data-i18n="i18n-godot-impl-desc">运行时语言切换，开源扩展</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="i18n-comparison-table">
                <h3 data-i18n="i18n-table-title">国际化技术特性对比</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th data-i18n="i18n-table-feature">技术特性</th>
                                <th data-i18n="qt3d-title">Qt 3D</th>
                                <th data-i18n="unity-title">Unity</th>
                                <th data-i18n="unreal-title">Unreal Engine</th>
                                <th data-i18n="kanzi-title">Kanzi</th>
                                <th data-i18n="cocos-title">Cocos Creator</th>
                                <th data-i18n="godot-title">Godot Engine</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td data-i18n="i18n-table-text-management">文本管理系统</td>
                                <td data-i18n="i18n-table-text-management-qt">QTranslator (.ts/.qm)</td>
                                <td data-i18n="i18n-table-text-management-unity">Localization Package</td>
                                <td data-i18n="i18n-table-text-management-unreal">FText System</td>
                                <td data-i18n="i18n-table-text-management-kanzi">字符串管理</td>
                                <td data-i18n="i18n-table-text-management-cocos">多语言管理器</td>
                                <td data-i18n="i18n-table-text-management-godot">CSV翻译系统</td>
                            </tr>
                            <tr>
                                <td data-i18n="i18n-table-resource">资源本地化</td>
                                <td data-i18n="i18n-table-resource-qt">资源系统变体</td>
                                <td data-i18n="i18n-table-resource-unity">Addressables</td>
                                <td data-i18n="i18n-table-resource-unreal">Asset Localization</td>
                                <td data-i18n="i18n-table-resource-kanzi">嵌入式资源管理</td>
                                <td data-i18n="i18n-table-resource-cocos">资源管理系统</td>
                                <td data-i18n="i18n-table-resource-godot">资源重映射</td>
                            </tr>
                            <tr>
                                <td data-i18n="i18n-table-rtl">RTL语言支持</td>
                                <td data-i18n="i18n-table-rtl-qt">完整支持</td>
                                <td data-i18n="i18n-table-rtl-unity">需要插件</td>
                                <td data-i18n="i18n-table-rtl-unreal">内置支持</td>
                                <td data-i18n="i18n-table-rtl-kanzi">基础支持</td>
                                <td data-i18n="i18n-table-rtl-cocos">基础支持</td>
                                <td data-i18n="i18n-table-rtl-godot">社区扩展</td>
                            </tr>
                            <tr>
                                <td data-i18n="i18n-table-plural">复数和格式化</td>
                                <td data-i18n="i18n-table-plural-qt">QLocale支持</td>
                                <td data-i18n="i18n-table-plural-unity">Smart Format</td>
                                <td data-i18n="i18n-table-plural-unreal">高级格式化</td>
                                <td data-i18n="i18n-table-plural-kanzi">基础支持</td>
                                <td data-i18n="i18n-table-plural-cocos">Intl API</td>
                                <td data-i18n="i18n-table-plural-godot">基础支持</td>
                            </tr>
                            <tr>
                                <td data-i18n="i18n-table-realtime">实时语言切换</td>
                                <td data-i18n="i18n-table-realtime-qt">支持</td>
                                <td data-i18n="i18n-table-realtime-unity">支持</td>
                                <td data-i18n="i18n-table-realtime-unreal">支持</td>
                                <td data-i18n="i18n-table-realtime-kanzi">优化性能</td>
                                <td data-i18n="i18n-table-realtime-cocos">热更新</td>
                                <td data-i18n="i18n-table-realtime-godot">运行时切换</td>
                            </tr>
                            <tr>
                                <td data-i18n="i18n-table-tools">开发工具</td>
                                <td data-i18n="i18n-table-tools-qt">Qt Linguist</td>
                                <td data-i18n="i18n-table-tools-unity">CSV工作流</td>
                                <td data-i18n="i18n-table-tools-unreal">Translation Editor</td>
                                <td data-i18n="i18n-table-tools-kanzi">Kanzi Studio</td>
                                <td data-i18n="i18n-table-tools-cocos">JSON/CSV</td>
                                <td data-i18n="i18n-table-tools-godot">内置CSV编辑器</td>
                            </tr>
                            <tr>
                                <td data-i18n="i18n-table-difficulty">学习难度</td>
                                <td data-i18n="i18n-table-difficulty-qt">中等</td>
                                <td data-i18n="i18n-table-difficulty-unity">简单</td>
                                <td data-i18n="i18n-table-difficulty-unreal">复杂</td>
                                <td data-i18n="i18n-table-difficulty-kanzi">复杂</td>
                                <td data-i18n="i18n-table-difficulty-cocos">简单</td>
                                <td data-i18n="i18n-table-difficulty-godot">简单</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="comparison-note">
                    <p data-i18n="i18n-table-note"><strong>说明：</strong>以上对比基于各引擎官方文档，实际功能可能因版本更新而变化。建议根据具体项目需求选择合适的技术方案。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 引擎对比 -->
    <section id="comparison" class="comparison">
        <div class="container">
            <h2 class="section-title" data-i18n="comparison-title">引擎对比分析</h2>
            <div class="comparison-intro">
                <p data-i18n="comparison-intro">详细对比 Qt 3D 与其他主流 3D 引擎的特性、优缺点和适用场景</p>
            </div>
            <div class="comparison-table-container">
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th data-i18n="comparison-table-feature">特性</th>
                            <th data-i18n="qt3d-title">Qt 3D</th>
                            <th data-i18n="unity-title">Unity</th>
                            <th data-i18n="unreal-title">Unreal Engine</th>
                            <th data-i18n="kanzi-title">Kanzi</th>
                            <th data-i18n="cocos-title">Cocos Creator</th>
                            <th data-i18n="godot-title">Godot Engine</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td data-i18n="comparison-table-dev">开发商</td>
                            <td data-i18n="dev-qt3d">Qt Company</td>
                            <td data-i18n="dev-unity">Unity Technologies</td>
                            <td data-i18n="dev-unreal">Epic Games</td>
                            <td data-i18n="dev-kanzi">Rightware</td>
                            <td data-i18n="dev-cocos">Xiamen Yaji Software</td>
                            <td data-i18n="dev-godot">Godot Community</td>
                        </tr>
                        <tr>
                            <td data-i18n="comparison-table-lang">主要语言</td>
                            <td data-i18n="lang-qt3d">C++/QML</td>
                            <td data-i18n="lang-unity">C#/JavaScript</td>
                            <td data-i18n="lang-unreal">C++/Blueprint</td>
                            <td data-i18n="lang-kanzi">C++</td>
                            <td data-i18n="lang-cocos">TypeScript/JavaScript</td>
                            <td data-i18n="lang-godot">GDScript/C#/C++</td>
                        </tr>
                        <tr>
                            <td data-i18n="comparison-table-license">许可证</td>
                            <td data-i18n="license-qt">商业/开源</td>
                            <td data-i18n="license-unity">免费/商业</td>
                            <td data-i18n="license-unreal">免费/商业</td>
                            <td data-i18n="license-kanzi">商业</td>
                            <td data-i18n="license-cocos">免费/开源</td>
                            <td data-i18n="license-godot">MIT开源</td>
                        </tr>
                        <tr>
                            <td data-i18n="comparison-table-platform">跨平台支持</td>
                            <td data-i18n="comparison-table-platform-qt3d">桌面、移动、嵌入式多平台</td>
                            <td data-i18n="comparison-table-platform-unity">桌面、移动、主流游戏机、Web</td>
                            <td data-i18n="comparison-table-platform-unreal">桌面、移动、主流游戏机、VR/AR</td>
                            <td data-i18n="comparison-table-platform-kanzi">嵌入式、汽车、工业平台</td>
                            <td data-i18n="comparison-table-platform-cocos">移动、Web、桌面</td>
                            <td data-i18n="comparison-table-platform-godot">桌面、移动、Web、部分主机（社区支持）</td>
                        </tr>
                        <tr>
                            <td data-i18n="comparison-table-difficulty">学习难度</td>
                            <td data-i18n="comparison-table-difficulty-qt3d">适合有Qt基础的开发者</td>
                            <td data-i18n="comparison-table-difficulty-unity">适合初学者和专业开发者</td>
                            <td data-i18n="comparison-table-difficulty-unreal">适合有一定开发经验的用户</td>
                            <td data-i18n="comparison-table-difficulty-kanzi">适合嵌入式和HMI专业开发者</td>
                            <td data-i18n="comparison-table-difficulty-cocos">适合Web和移动开发者</td>
                            <td data-i18n="comparison-table-difficulty-godot">适合独立开发者和教育用途</td>
                        </tr>
                        <tr>
                            <td data-i18n="comparison-table-render">渲染特性</td>
                            <td data-i18n="comparison-table-render-qt3d">支持OpenGL/Vulkan/Metal等多种API，2D/3D融合</td>
                            <td data-i18n="comparison-table-render-unity">支持URP/HDRP，丰富后处理和实时渲染</td>
                            <td data-i18n="comparison-table-render-unreal">支持Lumen、Nanite等高端渲染技术</td>
                            <td data-i18n="comparison-table-render-kanzi">针对嵌入式优化的实时渲染</td>
                            <td data-i18n="comparison-table-render-cocos">高效2D/3D渲染，移动端优化</td>
                            <td data-i18n="comparison-table-render-godot">支持OpenGL ES/Vulkan/WebGL，轻量级架构</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="comparison-cards">
                <div class="engine-card qt3d">
                    <h3 data-i18n="qt3d-title">Qt 3D</h3>
                    <div class="engine-pros">
                        <h4 data-i18n="pros-title">亮点</h4>
                        <ul>
                            <li data-i18n="qt3d-pros-1">深度集成 Qt 生态系统</li>
                            <li data-i18n="qt3d-pros-2">跨平台一致性</li>
                            <li data-i18n="qt3d-pros-3">企业级支持</li>
                            <li data-i18n="qt3d-pros-4">轻量级运行时</li>
                        </ul>
                    </div>
                    <div class="engine-use-cases">
                        <h4 data-i18n="use-cases-title">适合项目类型</h4>
                        <ul>
                            <li data-i18n="qt3d-use-1">企业应用</li>
                            <li data-i18n="qt3d-use-2">工业软件</li>
                            <li data-i18n="qt3d-use-3">嵌入式系统</li>
                            <li data-i18n="qt3d-use-4">跨平台应用</li>
                        </ul>
                    </div>
                </div>
                <div class="engine-card unity">
                    <h3 data-i18n="unity-title">Unity</h3>
                    <div class="engine-pros">
                        <h4 data-i18n="pros-title">亮点</h4>
                        <ul>
                            <li data-i18n="unity-pros-1">易于学习和上手</li>
                            <li data-i18n="unity-pros-2">丰富的资源和插件</li>
                            <li data-i18n="unity-pros-3">活跃的开发者社区</li>
                            <li data-i18n="unity-pros-4">多平台发布能力</li>
                        </ul>
                    </div>
                    <div class="engine-use-cases">
                        <h4 data-i18n="use-cases-title">适合项目类型</h4>
                        <ul>
                            <li data-i18n="unity-use-1">独立游戏</li>
                            <li data-i18n="unity-use-2">移动游戏</li>
                            <li data-i18n="unity-use-3">VR/AR 应用</li>
                            <li data-i18n="unity-use-4">教育培训</li>
                        </ul>
                    </div>
                </div>
                <div class="engine-card unreal">
                    <h3 data-i18n="unreal-title">Unreal Engine</h3>
                    <div class="engine-pros">
                        <h4 data-i18n="pros-title">亮点</h4>
                        <ul>
                            <li data-i18n="unreal-pros-1">高质量实时渲染</li>
                            <li data-i18n="unreal-pros-2">Blueprint 可视化编程</li>
                            <li data-i18n="unreal-pros-3">完整开发工具链</li>
                            <li data-i18n="unreal-pros-4">丰富的行业应用</li>
                        </ul>
                    </div>
                    <div class="engine-use-cases">
                        <h4 data-i18n="use-cases-title">适合项目类型</h4>
                        <ul>
                            <li data-i18n="unreal-use-1">大型3D游戏</li>
                            <li data-i18n="unreal-use-2">影视制作</li>
                            <li data-i18n="unreal-use-3">建筑可视化</li>
                            <li data-i18n="unreal-use-4">VR/AR体验</li>
                        </ul>
                    </div>
                </div>
                <div class="engine-card kanzi">
                    <h3 data-i18n="kanzi-title">Kanzi</h3>
                    <div class="engine-pros">
                        <h4 data-i18n="pros-title">亮点</h4>
                        <ul>
                            <li data-i18n="kanzi-pros-1">嵌入式系统优化</li>
                            <li data-i18n="kanzi-pros-2">低资源占用</li>
                            <li data-i18n="kanzi-pros-3">实时性能</li>
                            <li data-i18n="kanzi-pros-4">汽车行业标准</li>
                        </ul>
                    </div>
                    <div class="engine-use-cases">
                        <h4 data-i18n="use-cases-title">适合项目类型</h4>
                        <ul>
                            <li data-i18n="kanzi-use-1">汽车仪表盘</li>
                            <li data-i18n="kanzi-use-2">工业控制</li>
                            <li data-i18n="kanzi-use-3">医疗设备</li>
                            <li data-i18n="kanzi-use-4">消费电子</li>
                        </ul>
                    </div>
                </div>
                <div class="engine-card cocos">
                    <h3 data-i18n="cocos-title">Cocos Creator</h3>
                    <div class="engine-pros">
                        <h4 data-i18n="pros-title">亮点</h4>
                        <ul>
                            <li data-i18n="cocos-pros-1">轻量高效</li>
                            <li data-i18n="cocos-pros-2">强大2D能力</li>
                            <li data-i18n="cocos-pros-3">亚洲市场广泛应用</li>
                            <li data-i18n="cocos-pros-4">完整工具链</li>
                        </ul>
                    </div>
                    <div class="engine-use-cases">
                        <h4 data-i18n="use-cases-title">适合项目类型</h4>
                        <ul>
                            <li data-i18n="cocos-use-1">移动游戏</li>
                            <li data-i18n="cocos-use-2">轻量3D项目</li>
                            <li data-i18n="cocos-use-3">H5小游戏</li>
                            <li data-i18n="cocos-use-4">教育应用</li>
                        </ul>
                    </div>
                </div>
                <div class="engine-card godot">
                    <h3 data-i18n="godot-title">Godot Engine</h3>
                    <div class="engine-pros">
                        <h4 data-i18n="pros-title">亮点</h4>
                        <ul>
                            <li data-i18n="godot-pros-1">完全免费开源</li>
                            <li data-i18n="godot-pros-2">节点系统易用</li>
                            <li data-i18n="godot-pros-3">多语言支持</li>
                            <li data-i18n="godot-pros-4">轻量级架构</li>
                        </ul>
                    </div>
                    <div class="engine-use-cases">
                        <h4 data-i18n="use-cases-title">适合项目类型</h4>
                        <ul>
                            <li data-i18n="godot-use-1">独立游戏</li>
                            <li data-i18n="godot-use-2">教育项目</li>
                            <li data-i18n="godot-use-3">快速原型</li>
                            <li data-i18n="godot-use-4">多平台发布</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 国际化支持 -->
    <section id="i18n-demo-section" class="internationalization">
        <div class="container">
            <h2 class="section-title" data-i18n="i18n-support-title">国际化支持</h2>
            <div class="i18n-content">
                <div class="i18n-text">
                    <h3 data-i18n="i18n-global-dev">全球化应用开发</h3>
                    <p data-i18n="i18n-global-desc">Qt 3D 提供完整的国际化和本地化支持，让您的 3D 应用能够轻松适应全球市场。</p>
                    <ul class="i18n-features">
                        <li data-i18n="i18n-feature-multi-lang">多语言文本支持</li>
                        <li data-i18n="i18n-feature-unicode">Unicode 字符渲染</li>
                        <li data-i18n="i18n-feature-l10n-res">本地化资源管理</li>
                        <li data-i18n="i18n-feature-rtl">文本方向适配</li>
                        <li data-i18n="i18n-feature-dynamic-switch">动态语言切换</li>
                    </ul>
                    <a href="#docs" class="btn btn-primary" data-i18n="btn-learn-more">了解更多</a>
                </div>
                <div class="i18n-demo">
                    <div class="language-samples">
                        <div class="language-sample active" data-lang="zh">
                            <h4 data-i18n="lang-sample-zh-title">中文</h4>
                            <p data-i18n="lang-sample-zh-text">欢迎使用 3D 引擎</p>
                        </div>
                        <div class="language-sample" data-lang="en">
                            <h4 data-i18n="lang-sample-en-title">English</h4>
                            <p data-i18n="lang-sample-en-text">Welcome to 3D Engine</p>
                        </div>
                        <div class="language-sample" data-lang="ja">
                            <h4 data-i18n="lang-sample-ja-title">日本語</h4>
                            <p data-i18n="lang-sample-ja-text">3D エンジンへようこそ</p>
                        </div>
                        <div class="language-sample" data-lang="ar">
                            <h4 data-i18n="lang-sample-ar-title">العربية</h4>
                            <p data-i18n="lang-sample-ar-text">مرحباً بك في محرك    3D</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 文档链接 -->
    <section id="docs" class="documentation">
        <div class="container">
            <h2 class="section-title" data-i18n="docs-title">开发文档</h2>
            <div class="docs-grid">
                <div class="doc-card">
                    <div class="doc-icon">📚</div>
                    <h3 data-i18n="docs-guide-title">开发指南</h3>
                    <p data-i18n="docs-guide-desc">完整的 Qt 3D 开发指南，包括环境搭建、核心概念和最佳实践。</p>
                    <a href="docs/qt3d-development-guide.html" class="doc-link" data-i18n="docs-guide-link">查看文档</a>
                </div>
                <!-- <div class="doc-card">
                    <div class="doc-icon">⚖️</div>
                    <h3 data-i18n="docs-comparison-title">引擎对比</h3>
                    <p data-i18n="docs-comparison-desc">详细对比 Qt 3D 与其他主流 3D 引擎的特性和适用场景。</p>
                    <a href="docs/engine-comparison.html" class="doc-link" data-i18n="docs-comparison-link">查看对比</a>
                </div> -->
                <!-- <div class="doc-card">
                    <div class="doc-icon">🌍</div>
                    <h3 data-i18n="docs-i18n-title">国际化指南</h3>
                    <p data-i18n="docs-i18n-desc">Qt 3D 应用的国际化和本地化开发指南。</p>
                    <a href="docs/internationalization.html" class="doc-link" data-i18n="docs-i18n-link">查看指南</a>
                </div> -->
                <div class="doc-card">
                    <div class="doc-icon">💡</div>
                    <h3 data-i18n="docs-case-title">国际化案例分享</h3>
                    <p data-i18n="docs-case-desc">精选实际国际化项目案例，展示多语言和本地化最佳实践。</p>
                    <a href="docs/case-studies.html" class="doc-link" data-i18n="docs-case-link">查看案例</a>
                </div>
                <div class="doc-card">
                    <div class="doc-icon">🤖</div>
                    <h3>AndroidFwk能力</h3>
                    <p>AndroidFwk集成与开发实践，助力高效移动端3D应用开发。</p>
                    <a href="docs/androidfwk.html" class="doc-link">查看文档</a>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">Qt 3D 开发指南</h4>
                    <p data-i18n="footer-subtitle">专业的跨平台 3D 应用开发解决方案</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="docs/qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="docs/engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="docs/internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/prism.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
