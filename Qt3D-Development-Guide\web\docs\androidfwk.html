<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="androidfwk-page-title">AndroidFwk集成能力 - 3D引擎移动端开发</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text" data-i18n="nav-logo-text">3D 引擎技术对比</span>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="../index.html#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="../index.html#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="../index.html#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <section class="hero" style="padding: 88px 0 48px; min-height: 40vh;">
        <div class="hero-container">
            <h1 class="hero-title" data-i18n="androidfwk-hero-title">AndroidFwk集成能力</h1>
            <p class="hero-subtitle" data-i18n="androidfwk-hero-subtitle">移动端3D应用开发的专业集成方案</p>
            <p class="hero-description" data-i18n="androidfwk-hero-description" style="max-width: 700px; margin: 0 auto 32px;">
                AndroidFwk模块为移动端3D应用开发提供了高效的集成方案，支持多种3D渲染功能与Android系统能力，
                助力开发者快速构建高性能的跨平台3D移动应用。
            </p>
        </div>
    </section>

    <!-- 主要功能特性 -->
    <section class="features-comparison" style="padding: 0 0 64px;">
        <div class="container">
            <h2 class="section-title" data-i18n="features-title">核心功能特性</h2>
            <div class="comparison-grid" style="grid-template-columns: 1fr 1fr; gap: 48px; max-width: 1000px; margin: 0 auto;">
                <div class="feature-comparison">
                    <h3 data-i18n="capabilities-title">主要能力</h3>
                    <ul>
                        <li data-i18n="capability-1">Android原生系统深度集成</li>
                        <li data-i18n="capability-2">高效的3D渲染与资源管理</li>
                        <li data-i18n="capability-3">多媒体与传感器支持</li>
                        <li data-i18n="capability-4">跨平台兼容性保障</li>
                        <li data-i18n="capability-5">便捷的调试与部署工具</li>
                    </ul>
                </div>
                <div class="feature-comparison">
                    <h3 data-i18n="workflow-title">集成流程</h3>
                    <ol>
                        <li data-i18n="workflow-1">集成AndroidFwk模块到3D项目</li>
                        <li data-i18n="workflow-2">配置AndroidManifest及相关权限</li>
                        <li data-i18n="workflow-3">调用API实现3D渲染与系统交互</li>
                        <li data-i18n="workflow-4">打包并部署到Android设备</li>
                    </ol>
                </div>
            </div>

            <!-- 技术优势 -->
            <div class="i18n-grid" style="margin-top: 64px;">
                <div class="i18n-engine">
                    <h3 data-i18n="advantages-title">技术优势</h3>
                    <div class="i18n-features">
                        <div class="i18n-feature">
                            <span class="feature-icon">🚀</span>
                            <div class="feature-content">
                                <h4 data-i18n="advantage-performance">高性能渲染</h4>
                                <p data-i18n="advantage-performance-desc">优化的3D渲染管线，支持硬件加速和GPU优化</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🔧</span>
                            <div class="feature-content">
                                <h4 data-i18n="advantage-integration">系统集成</h4>
                                <p data-i18n="advantage-integration-desc">深度集成Android系统API和原生功能</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">📱</span>
                            <div class="feature-content">
                                <h4 data-i18n="advantage-compatibility">兼容性</h4>
                                <p data-i18n="advantage-compatibility-desc">支持多种Android版本和设备类型</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">⚡</span>
                            <div class="feature-content">
                                <h4 data-i18n="advantage-efficiency">开发效率</h4>
                                <p data-i18n="advantage-efficiency-desc">简化的API设计，快速集成和部署</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>

    <!-- 车载行业应用案例 -->
    <section class="comparison-section" style="padding: 64px 0;">
        <div class="container">
            <h2 class="section-title" data-i18n="automotive-cases-title">车载行业应用案例</h2>
            <div class="comparison-intro">
                <p data-i18n="automotive-cases-intro">AndroidFwk在车载行业的实际应用展示了其在智能座舱、仪表盘和车载娱乐系统中的强大能力</p>
            </div>

            <div class="engines-grid" style="grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 32px; margin-top: 48px;">
                <!-- 案例1：豪华电动车品牌 -->
                <div class="engine-card">
                    <div class="engine-header">
                        <div class="engine-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">🚗</div>
                        <div class="engine-info">
                            <h3 data-i18n="case1-title">豪华电动车品牌智能座舱</h3>
                            <p data-i18n="case1-subtitle">多屏融合HMI系统</p>
                        </div>
                    </div>
                    <div class="engine-highlights">
                        <h4 data-i18n="case1-challenge">项目挑战</h4>
                        <ul>
                            <li data-i18n="case1-challenge-1">15.6英寸中控屏与12.3英寸仪表盘联动</li>
                            <li data-i18n="case1-challenge-2">实时3D导航与车辆状态可视化</li>
                            <li data-i18n="case1-challenge-3">多媒体内容与车载系统深度集成</li>
                        </ul>
                    </div>
                    <div class="engine-demo">
                        <h4 data-i18n="case1-solution">解决方案</h4>
                        <p data-i18n="case1-solution-desc">基于AndroidFwk构建统一的HMI框架，实现跨屏数据同步和3D渲染优化，支持60fps流畅交互体验。</p>
                    </div>
                    <div class="engine-demo">
                        <h4 data-i18n="case1-result">项目成果</h4>
                        <p data-i18n="case1-result-desc">系统启动时间缩短40%，内存占用降低30%，获得用户体验满意度95%以上评价。</p>
                    </div>
                </div>

                <!-- 案例2：商用车队管理 -->
                <div class="engine-card">
                    <div class="engine-header">
                        <div class="engine-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">🚛</div>
                        <div class="engine-info">
                            <h3 data-i18n="case2-title">商用车队管理系统</h3>
                            <p data-i18n="case2-subtitle">智能仪表与监控平台</p>
                        </div>
                    </div>
                    <div class="engine-highlights">
                        <h4 data-i18n="case2-challenge">项目挑战</h4>
                        <ul>
                            <li data-i18n="case2-challenge-1">实时监控车辆运行状态和驾驶行为</li>
                            <li data-i18n="case2-challenge-2">集成GPS导航与路线优化算法</li>
                            <li data-i18n="case2-challenge-3">支持离线模式和数据同步</li>
                        </ul>
                    </div>
                    <div class="engine-demo">
                        <h4 data-i18n="case2-solution">解决方案</h4>
                        <p data-i18n="case2-solution-desc">利用AndroidFwk的传感器集成能力，开发智能仪表盘系统，实现车辆数据可视化和远程监控功能。</p>
                    </div>
                    <div class="engine-demo">
                        <h4 data-i18n="case2-result">项目成果</h4>
                        <p data-i18n="case2-result-desc">车队运营效率提升25%，燃油消耗降低15%，事故率下降35%，获得行业认可。</p>
                    </div>
                </div>

                <!-- 案例3：新能源出行平台 -->
                <div class="engine-card">
                    <div class="engine-header">
                        <div class="engine-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">⚡</div>
                        <div class="engine-info">
                            <h3 data-i18n="case3-title">新能源出行平台</h3>
                            <p data-i18n="case3-subtitle">充电桩互联与能耗管理</p>
                        </div>
                    </div>
                    <div class="engine-highlights">
                        <h4 data-i18n="case3-challenge">项目挑战</h4>
                        <ul>
                            <li data-i18n="case3-challenge-1">实时显示电池状态和充电站信息</li>
                            <li data-i18n="case3-challenge-2">智能路径规划考虑续航里程</li>
                            <li data-i18n="case3-challenge-3">与第三方充电网络API集成</li>
                        </ul>
                    </div>
                    <div class="engine-demo">
                        <h4 data-i18n="case3-solution">解决方案</h4>
                        <p data-i18n="case3-solution-desc">基于AndroidFwk开发能耗管理HMI，集成3D地图显示和实时数据分析，提供智能充电建议。</p>
                    </div>
                    <div class="engine-demo">
                        <h4 data-i18n="case3-result">项目成果</h4>
                        <p data-i18n="case3-result-desc">用户充电效率提升50%，里程焦虑降低60%，平台日活跃用户增长200%。</p>
                    </div>
                </div>
            </div>

            <!-- 工作流程 -->
            <div class="i18n-content" style="margin-top: 64px;">
                <div class="i18n-text">
                    <h3 data-i18n="workflow-process-title">AndroidFwk车载开发工作流程</h3>
                    <p data-i18n="workflow-process-desc">基于实际项目经验总结的标准化开发流程，确保项目高效交付和质量保障。</p>
                </div>

                <div class="comparison-grid" style="grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 24px; margin-top: 32px;">
                    <div class="feature-comparison">
                        <div class="i18n-feature">
                            <span class="feature-icon">📋</span>
                            <div class="feature-content">
                                <h4 data-i18n="workflow-step1">需求分析与架构设计</h4>
                                <p data-i18n="workflow-step1-desc">分析车载场景需求，设计HMI架构和数据流，制定技术方案和开发计划。</p>
                            </div>
                        </div>
                    </div>

                    <div class="feature-comparison">
                        <div class="i18n-feature">
                            <span class="feature-icon">⚙️</span>
                            <div class="feature-content">
                                <h4 data-i18n="workflow-step2">环境搭建与集成</h4>
                                <p data-i18n="workflow-step2-desc">配置AndroidFwk开发环境，集成车载硬件驱动和传感器接口。</p>
                            </div>
                        </div>
                    </div>

                    <div class="feature-comparison">
                        <div class="i18n-feature">
                            <span class="feature-icon">🎨</span>
                            <div class="feature-content">
                                <h4 data-i18n="workflow-step3">UI/UX开发与优化</h4>
                                <p data-i18n="workflow-step3-desc">开发3D界面组件，优化渲染性能，确保车载环境下的可用性。</p>
                            </div>
                        </div>
                    </div>

                    <div class="feature-comparison">
                        <div class="i18n-feature">
                            <span class="feature-icon">🔧</span>
                            <div class="feature-content">
                                <h4 data-i18n="workflow-step4">系统集成与测试</h4>
                                <p data-i18n="workflow-step4-desc">集成车载ECU，进行功能测试、性能测试和安全测试验证。</p>
                            </div>
                        </div>
                    </div>

                    <div class="feature-comparison">
                        <div class="i18n-feature">
                            <span class="feature-icon">🚀</span>
                            <div class="feature-content">
                                <h4 data-i18n="workflow-step5">部署与维护</h4>
                                <p data-i18n="workflow-step5-desc">批量部署到车载设备，提供OTA更新和远程维护支持。</p>
                            </div>
                        </div>
                    </div>

                    <div class="feature-comparison">
                        <div class="i18n-feature">
                            <span class="feature-icon">📊</span>
                            <div class="feature-content">
                                <h4 data-i18n="workflow-step6">数据分析与优化</h4>
                                <p data-i18n="workflow-step6-desc">收集用户使用数据，分析系统性能，持续优化用户体验。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 48px;">
                <a href="../index.html#docs" class="btn btn-primary" data-i18n="btn-back-home">返回技术文档</a>
                <a href="../index.html" class="btn btn-secondary" data-i18n="btn-back-main">返回主页</a>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">3D 引擎技术对比</h4>
                    <p data-i18n="footer-subtitle">专业的 3D 引擎技术分析与选型参考平台</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 3D 引擎技术对比平台. 为开发者提供专业技术参考.</p>
            </div>
        </div>
    </footer>

    <script src="../assets/js/prism.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html> 