<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AndroidFwk能力</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text">3D 引擎指南</span>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link">引擎对比</a>
                <a href="../index.html#features" class="nav-link">特性分析</a>
                <a href="../index.html#i18n" class="nav-link">国际化</a>
                <a href="../index.html#docs" class="nav-link">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh">中文</option>
                        <option value="en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <section class="hero" style="padding: 88px 0 48px; min-height: 40vh;">
        <div class="hero-container">
            <h1 class="hero-title">AndroidFwk能力</h1>
            <p class="hero-description" style="max-width: 700px; margin: 0 auto 32px;">AndroidFwk模块为Qt 3D移动端开发提供了高效的集成方案，支持多种3D功能与系统能力，助力开发者快速构建高性能的3D应用。</p>
        </div>
    </section>

    <section class="engines-showcase" style="padding: 0 0 64px;">
        <div class="container">
            <div class="engines-grid" style="display: flex; justify-content: center;">
                <div class="engine-card" style="max-width: 480px;">
                    <h3>主要能力</h3>
                    <ul>
                        <li>与Android原生系统深度集成</li>
                        <li>高效的3D渲染与资源管理</li>
                        <li>多媒体与传感器支持</li>
                        <li>跨平台兼容性</li>
                        <li>便捷的调试与部署工具</li>
                    </ul>
                    <h4 style="margin-top:32px;">简单使用流程</h4>
                    <ol>
                        <li>集成AndroidFwk模块到Qt 3D项目</li>
                        <li>配置AndroidManifest及相关权限</li>
                        <li>调用API实现3D渲染与系统交互</li>
                        <li>打包并部署到Android设备</li>
                    </ol>
                    <a href="../index.html#docs" class="btn btn-primary" style="margin-top:32px;">返回文档首页</a>
                </div>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Qt 3D 开发指南</h4>
                    <p>专业的跨平台 3D 应用开发解决方案</p>
                </div>
                <div class="footer-section">
                    <h4>文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html">开发指南</a></li>
                        <li><a href="engine-comparison.html">引擎对比</a></li>
                        <li><a href="internationalization.html">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.</p>
            </div>
        </div>
    </footer>

    <script src="../../assets/js/prism.js"></script>
    <script src="../../assets/js/docs.js"></script>
</body>
</html> 