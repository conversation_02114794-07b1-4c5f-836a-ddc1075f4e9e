<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="androidfwk-page-title">AndroidFwk集成能力 - 3D引擎移动端开发</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text" data-i18n="nav-logo-text">3D 引擎技术对比</span>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="../index.html#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="../index.html#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="../index.html#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <section class="hero" style="padding: 88px 0 48px; min-height: 40vh;">
        <div class="hero-container">
            <h1 class="hero-title" data-i18n="androidfwk-hero-title">AndroidFwk集成能力</h1>
            <p class="hero-subtitle" data-i18n="androidfwk-hero-subtitle">移动端3D应用开发的专业集成方案</p>
            <p class="hero-description" data-i18n="androidfwk-hero-description" style="max-width: 700px; margin: 0 auto 32px;">
                AndroidFwk模块为移动端3D应用开发提供了高效的集成方案，支持多种3D渲染功能与Android系统能力，
                助力开发者快速构建高性能的跨平台3D移动应用。
            </p>
        </div>
    </section>

    <!-- 主要功能特性 -->
    <section class="features-comparison" style="padding: 0 0 64px;">
        <div class="container">
            <h2 class="section-title" data-i18n="features-title">核心功能特性</h2>
            <div class="comparison-grid" style="grid-template-columns: 1fr 1fr; gap: 48px; max-width: 1000px; margin: 0 auto;">
                <div class="feature-comparison">
                    <h3 data-i18n="capabilities-title">主要能力</h3>
                    <ul>
                        <li data-i18n="capability-1">Android原生系统深度集成</li>
                        <li data-i18n="capability-2">高效的3D渲染与资源管理</li>
                        <li data-i18n="capability-3">多媒体与传感器支持</li>
                        <li data-i18n="capability-4">跨平台兼容性保障</li>
                        <li data-i18n="capability-5">便捷的调试与部署工具</li>
                    </ul>
                </div>
                <div class="feature-comparison">
                    <h3 data-i18n="workflow-title">集成流程</h3>
                    <ol>
                        <li data-i18n="workflow-1">集成AndroidFwk模块到3D项目</li>
                        <li data-i18n="workflow-2">配置AndroidManifest及相关权限</li>
                        <li data-i18n="workflow-3">调用API实现3D渲染与系统交互</li>
                        <li data-i18n="workflow-4">打包并部署到Android设备</li>
                    </ol>
                </div>
            </div>

            <!-- 技术优势 -->
            <div class="i18n-grid" style="margin-top: 64px;">
                <div class="i18n-engine">
                    <h3 data-i18n="advantages-title">技术优势</h3>
                    <div class="i18n-features">
                        <div class="i18n-feature">
                            <span class="feature-icon">🚀</span>
                            <div class="feature-content">
                                <h4 data-i18n="advantage-performance">高性能渲染</h4>
                                <p data-i18n="advantage-performance-desc">优化的3D渲染管线，支持硬件加速和GPU优化</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">🔧</span>
                            <div class="feature-content">
                                <h4 data-i18n="advantage-integration">系统集成</h4>
                                <p data-i18n="advantage-integration-desc">深度集成Android系统API和原生功能</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">📱</span>
                            <div class="feature-content">
                                <h4 data-i18n="advantage-compatibility">兼容性</h4>
                                <p data-i18n="advantage-compatibility-desc">支持多种Android版本和设备类型</p>
                            </div>
                        </div>
                        <div class="i18n-feature">
                            <span class="feature-icon">⚡</span>
                            <div class="feature-content">
                                <h4 data-i18n="advantage-efficiency">开发效率</h4>
                                <p data-i18n="advantage-efficiency-desc">简化的API设计，快速集成和部署</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 48px;">
                <a href="../index.html#docs" class="btn btn-primary" data-i18n="btn-back-home">返回技术文档</a>
                <a href="../index.html" class="btn btn-secondary" data-i18n="btn-back-main">返回主页</a>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">3D 引擎技术对比</h4>
                    <p data-i18n="footer-subtitle">专业的 3D 引擎技术分析与选型参考平台</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 3D 引擎技术对比平台. 为开发者提供专业技术参考.</p>
            </div>
        </div>
    </footer>

    <script src="../assets/js/prism.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html> 