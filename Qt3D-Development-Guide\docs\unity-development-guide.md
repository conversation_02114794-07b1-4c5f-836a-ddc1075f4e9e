# Unity 引擎开发指南

## 目录
1. [概述](#概述)
2. [技术特性](#技术特性)
3. [开发环境搭建](#开发环境搭建)
4. [核心概念](#核心概念)
5. [2D/3D 开发](#2d3d-开发)
6. [脚本编程](#脚本编程)
7. [最佳实践](#最佳实践)
8. [性能优化](#性能优化)

## 概述

Unity 是全球领先的实时 3D 开发平台，为游戏、汽车、建筑、影视等行业提供强大的创作工具。Unity 以其易用性、强大的跨平台能力和丰富的生态系统而闻名。

### 主要优势
- **易于学习**：直观的可视化编辑器，适合初学者
- **跨平台发布**：支持 25+ 个平台的一键发布
- **丰富生态**：Asset Store 提供大量现成资源
- **强大社区**：庞大的开发者社区和学习资源
- **2D/3D 兼容**：统一的开发环境支持 2D 和 3D 项目
- **可视化编程**：Visual Scripting 支持无代码开发

## 技术特性

### 渲染系统
- **通用渲染管线 (URP)**：高性能、可扩展的渲染管线
- **高清渲染管线 (HDRP)**：AAA 级高质量渲染
- **内置渲染管线**：传统的前向/延迟渲染
- **Shader Graph**：可视化着色器编辑器
- **光照系统**：实时光照、烘焙光照、混合光照
- **后处理效果**：丰富的后处理效果栈

### 物理系统
- **3D 物理**：基于 PhysX 的 3D 物理引擎
- **2D 物理**：基于 Box2D 的 2D 物理引擎
- **关节系统**：各种物理关节和约束
- **碰撞检测**：高效的碰撞检测系统
- **布料模拟**：实时布料物理模拟

### 动画系统
- **Animator Controller**：状态机驱动的动画系统
- **Timeline**：电影级的序列编辑器
- **Cinemachine**：智能相机系统
- **2D Animation**：专业的 2D 骨骼动画工具
- **动画重定向**：人形角色动画重定向

## 开发环境搭建

### 系统要求
- **操作系统**：Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **内存**：最少 8GB RAM，推荐 16GB+
- **存储**：至少 5GB 可用空间
- **显卡**：支持 DirectX 11 或 OpenGL 3.3+

### 安装步骤

#### 1. 下载 Unity Hub
```bash
# 访问官网下载
https://unity3d.com/get-unity/download

# 或使用包管理器 (macOS)
brew install --cask unity-hub
```

#### 2. 安装 Unity 编辑器
1. 打开 Unity Hub
2. 选择 "Installs" 标签
3. 点击 "Install Editor"
4. 选择推荐的 LTS 版本
5. 选择所需的模块（Android、iOS、WebGL 等）

#### 3. 创建第一个项目
```csharp
// 创建新项目
// 1. 在 Unity Hub 中点击 "New Project"
// 2. 选择模板（3D、2D、VR 等）
// 3. 设置项目名称和位置
// 4. 点击 "Create project"
```

## 核心概念

### GameObject 和 Component
```csharp
// GameObject 是场景中所有对象的基类
GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);

// Component 为 GameObject 添加功能
Rigidbody rb = cube.AddComponent<Rigidbody>();
rb.mass = 2.0f;

// 获取组件
Transform transform = cube.GetComponent<Transform>();
transform.position = new Vector3(0, 5, 0);
```

### 场景管理
```csharp
using UnityEngine.SceneManagement;

// 加载场景
SceneManager.LoadScene("MainMenu");

// 异步加载场景
StartCoroutine(LoadSceneAsync("GameLevel"));

IEnumerator LoadSceneAsync(string sceneName)
{
    AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(sceneName);
    
    while (!asyncLoad.isDone)
    {
        // 显示加载进度
        float progress = Mathf.Clamp01(asyncLoad.progress / 0.9f);
        Debug.Log("Loading progress: " + (progress * 100) + "%");
        yield return null;
    }
}
```

### 预制体 (Prefabs)
```csharp
// 创建预制体实例
public GameObject enemyPrefab;

void SpawnEnemy()
{
    GameObject enemy = Instantiate(enemyPrefab);
    enemy.transform.position = spawnPoint.position;
}

// 销毁对象
Destroy(enemy, 3.0f); // 3秒后销毁
```

## 2D/3D 开发

### 2D 游戏开发
```csharp
// 2D 精灵渲染
public class SpriteController : MonoBehaviour
{
    private SpriteRenderer spriteRenderer;
    
    void Start()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
        spriteRenderer.sprite = Resources.Load<Sprite>("PlayerSprite");
    }
    
    void Update()
    {
        // 2D 移动
        float horizontal = Input.GetAxis("Horizontal");
        transform.Translate(Vector2.right * horizontal * Time.deltaTime * 5f);
    }
}

// 2D 物理
public class Player2D : MonoBehaviour
{
    private Rigidbody2D rb2d;
    public float jumpForce = 10f;
    
    void Start()
    {
        rb2d = GetComponent<Rigidbody2D>();
    }
    
    void Update()
    {
        if (Input.GetKeyDown(KeyCode.Space))
        {
            rb2d.AddForce(Vector2.up * jumpForce, ForceMode2D.Impulse);
        }
    }
}
```

### 3D 游戏开发
```csharp
// 3D 角色控制器
public class PlayerController : MonoBehaviour
{
    public float speed = 5f;
    public float jumpHeight = 2f;
    private CharacterController controller;
    private Vector3 velocity;
    private bool isGrounded;
    
    void Start()
    {
        controller = GetComponent<CharacterController>();
    }
    
    void Update()
    {
        // 地面检测
        isGrounded = controller.isGrounded;
        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f;
        }
        
        // 移动输入
        float x = Input.GetAxis("Horizontal");
        float z = Input.GetAxis("Vertical");
        Vector3 move = transform.right * x + transform.forward * z;
        controller.Move(move * speed * Time.deltaTime);
        
        // 跳跃
        if (Input.GetButtonDown("Jump") && isGrounded)
        {
            velocity.y = Mathf.Sqrt(jumpHeight * -2f * Physics.gravity.y);
        }
        
        // 重力
        velocity.y += Physics.gravity.y * Time.deltaTime;
        controller.Move(velocity * Time.deltaTime);
    }
}
```

### UI 系统
```csharp
// Canvas 和 UI 元素
public class UIManager : MonoBehaviour
{
    public Text scoreText;
    public Button startButton;
    public Slider healthSlider;
    
    void Start()
    {
        startButton.onClick.AddListener(StartGame);
    }
    
    public void UpdateScore(int score)
    {
        scoreText.text = "Score: " + score;
    }
    
    public void UpdateHealth(float health)
    {
        healthSlider.value = health / 100f;
    }
    
    void StartGame()
    {
        SceneManager.LoadScene("GameScene");
    }
}
```

## 脚本编程

### MonoBehaviour 生命周期
```csharp
public class LifecycleExample : MonoBehaviour
{
    void Awake()
    {
        // 对象创建时调用，早于 Start
        Debug.Log("Awake called");
    }
    
    void Start()
    {
        // 第一帧更新前调用
        Debug.Log("Start called");
    }
    
    void Update()
    {
        // 每帧调用
        // 用于游戏逻辑、输入处理等
    }
    
    void FixedUpdate()
    {
        // 固定时间间隔调用
        // 用于物理计算
    }
    
    void LateUpdate()
    {
        // 在所有 Update 之后调用
        // 用于相机跟随等
    }
    
    void OnDestroy()
    {
        // 对象销毁时调用
        Debug.Log("OnDestroy called");
    }
}
```

### 协程 (Coroutines)
```csharp
public class CoroutineExample : MonoBehaviour
{
    void Start()
    {
        StartCoroutine(CountdownCoroutine());
    }
    
    IEnumerator CountdownCoroutine()
    {
        for (int i = 10; i > 0; i--)
        {
            Debug.Log("Countdown: " + i);
            yield return new WaitForSeconds(1f);
        }
        Debug.Log("Go!");
    }
    
    IEnumerator FadeOut(SpriteRenderer sprite)
    {
        Color color = sprite.color;
        while (color.a > 0)
        {
            color.a -= Time.deltaTime;
            sprite.color = color;
            yield return null;
        }
    }
}
```

### 事件系统
```csharp
// 使用 UnityEvent
[System.Serializable]
public class GameEvent : UnityEvent<int> { }

public class EventManager : MonoBehaviour
{
    public GameEvent onScoreChanged;
    
    public void AddScore(int points)
    {
        onScoreChanged.Invoke(points);
    }
}

// 使用 C# 事件
public class Player : MonoBehaviour
{
    public static event System.Action<float> OnHealthChanged;
    
    private float health = 100f;
    
    public void TakeDamage(float damage)
    {
        health -= damage;
        OnHealthChanged?.Invoke(health);
    }
}
```

## 最佳实践

### 1. 项目组织
```
Assets/
├── Scripts/
│   ├── Player/
│   ├── Enemies/
│   ├── UI/
│   └── Managers/
├── Prefabs/
├── Materials/
├── Textures/
├── Audio/
├── Scenes/
└── Resources/
```

### 2. 性能优化
```csharp
// 对象池模式
public class ObjectPool : MonoBehaviour
{
    public GameObject prefab;
    public int poolSize = 10;
    private Queue<GameObject> pool = new Queue<GameObject>();
    
    void Start()
    {
        for (int i = 0; i < poolSize; i++)
        {
            GameObject obj = Instantiate(prefab);
            obj.SetActive(false);
            pool.Enqueue(obj);
        }
    }
    
    public GameObject GetObject()
    {
        if (pool.Count > 0)
        {
            GameObject obj = pool.Dequeue();
            obj.SetActive(true);
            return obj;
        }
        return Instantiate(prefab);
    }
    
    public void ReturnObject(GameObject obj)
    {
        obj.SetActive(false);
        pool.Enqueue(obj);
    }
}
```

### 3. 代码规范
```csharp
// 良好的命名规范
public class PlayerController : MonoBehaviour
{
    [SerializeField] private float moveSpeed = 5f;
    [SerializeField] private float jumpForce = 10f;
    
    private Rigidbody playerRigidbody;
    private bool isGrounded;
    
    // 使用属性而不是公共字段
    public float Health { get; private set; } = 100f;
    
    void Start()
    {
        playerRigidbody = GetComponent<Rigidbody>();
    }
    
    // 清晰的方法命名
    public void TakeDamage(float damageAmount)
    {
        Health = Mathf.Max(0, Health - damageAmount);
        
        if (Health <= 0)
        {
            HandlePlayerDeath();
        }
    }
    
    private void HandlePlayerDeath()
    {
        // 处理玩家死亡逻辑
    }
}
```

## 性能优化

### 1. 渲染优化
- **批处理**：使用 Static Batching 和 Dynamic Batching
- **LOD 系统**：根据距离使用不同细节级别的模型
- **遮挡剔除**：使用 Occlusion Culling 减少不可见对象的渲染
- **纹理压缩**：使用适当的纹理格式和压缩

### 2. 脚本优化
```csharp
// 缓存组件引用
private Transform cachedTransform;
void Start()
{
    cachedTransform = transform; // 避免每次调用 transform 属性
}

// 使用对象池
// 避免频繁的 Instantiate 和 Destroy

// 优化 Update 调用
void Update()
{
    // 避免在 Update 中进行复杂计算
    // 考虑使用协程或定时器
}
```

### 3. 内存管理
- **及时释放资源**：使用 Resources.UnloadUnusedAssets()
- **避免内存泄漏**：正确管理事件订阅和取消订阅
- **使用 Addressables**：更好的资源管理系统

---

*Unity 提供了强大而灵活的开发环境，适合从独立游戏到商业项目的各种需求。更多详细信息请参考 [Unity 官方文档](https://docs.unity3d.com/)*
