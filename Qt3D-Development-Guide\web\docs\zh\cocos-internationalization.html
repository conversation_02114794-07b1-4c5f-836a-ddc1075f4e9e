<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cocos Creator 国际化与本地化指南</title> <!-- The title will be replaced for each page -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/docs.css">
    <link rel="stylesheet" href="../../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html" class="logo-text" data-i18n="nav-logo-text">3D 引擎指南</a>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="../index.html#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="../index.html#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="../index.html#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 文档内容区域 -->
    <div class="doc-layout container">
        <!-- 左侧目录 -->
        <aside class="doc-sidebar card">
            <div class="toc-container">
                <!-- 目录内容将通过JavaScript动态生成 -->
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <section class="doc-section card">
            <div class="doc-container">
                <main class="doc-content">
                    <div lang="zh"><h1 id="cocos-creator-国际化与本地化指南">Cocos Creator
国际化与本地化指南</h1>
<h2 id="目录">目录</h2>
<ol type="1">
<li><a href="#概述">概述</a></li>
<li><a href="#cocos-国际化架构">Cocos 国际化架构</a></li>
<li><a href="#多语言文本管理">多语言文本管理</a></li>
<li><a href="#资源本地化">资源本地化</a></li>
<li><a href="#移动端国际化">移动端国际化</a></li>
<li><a href="#亚洲市场特殊需求">亚洲市场特殊需求</a></li>
<li><a href="#发布平台适配">发布平台适配</a></li>
<li><a href="#最佳实践">最佳实践</a></li>
</ol>
<h2 id="概述">概述</h2>
<p>Cocos Creator
作为全球领先的移动游戏引擎，在国际化方面有着丰富的经验和强大的支持。凭借在中国手游市场40%的占有率和全球20%的市场份额，Cocos
深度理解不同地区和文化的游戏本地化需求，特别是在亚洲市场有着独特的优势。</p>
<h3 id="主要特性">主要特性</h3>
<ul>
<li><strong>多语言支持</strong>：完整的多语言文本管理系统</li>
<li><strong>资源本地化</strong>：图片、音频、字体的本地化管理</li>
<li><strong>亚洲语言优化</strong>：对中文、日文、韩文的特殊优化</li>
<li><strong>移动端优化</strong>：针对移动设备的国际化性能优化</li>
<li><strong>发布平台集成</strong>：与各大发布平台的本地化工具集成</li>
<li><strong>热更新支持</strong>：本地化内容的热更新能力</li>
</ul>
<h2 id="cocos-国际化架构">Cocos 国际化架构</h2>
<h3 id="多语言管理系统">多语言管理系统</h3>
<div class="sourceCode" id="cb1"><pre
class="sourceCode typescript"><code class="sourceCode typescript"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 语言管理器</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> { sys } <span class="im">from</span> <span class="st">&#39;cc&#39;</span><span class="op">;</span></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">enum</span> LanguageType {</span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a>    CHINESE <span class="op">=</span> <span class="st">&#39;zh&#39;</span><span class="op">,</span></span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a>    ENGLISH <span class="op">=</span> <span class="st">&#39;en&#39;</span><span class="op">,</span></span>
<span id="cb1-7"><a href="#cb1-7" aria-hidden="true" tabindex="-1"></a>    JAPANESE <span class="op">=</span> <span class="st">&#39;ja&#39;</span><span class="op">,</span></span>
<span id="cb1-8"><a href="#cb1-8" aria-hidden="true" tabindex="-1"></a>    KOREAN <span class="op">=</span> <span class="st">&#39;ko&#39;</span><span class="op">,</span></span>
<span id="cb1-9"><a href="#cb1-9" aria-hidden="true" tabindex="-1"></a>    SPANISH <span class="op">=</span> <span class="st">&#39;es&#39;</span><span class="op">,</span></span>
<span id="cb1-10"><a href="#cb1-10" aria-hidden="true" tabindex="-1"></a>    FRENCH <span class="op">=</span> <span class="st">&#39;fr&#39;</span><span class="op">,</span></span>
<span id="cb1-11"><a href="#cb1-11" aria-hidden="true" tabindex="-1"></a>    GERMAN <span class="op">=</span> <span class="st">&#39;de&#39;</span><span class="op">,</span></span>
<span id="cb1-12"><a href="#cb1-12" aria-hidden="true" tabindex="-1"></a>    RUSSIAN <span class="op">=</span> <span class="st">&#39;ru&#39;</span><span class="op">,</span></span>
<span id="cb1-13"><a href="#cb1-13" aria-hidden="true" tabindex="-1"></a>    PORTUGUESE <span class="op">=</span> <span class="st">&#39;pt&#39;</span><span class="op">,</span></span>
<span id="cb1-14"><a href="#cb1-14" aria-hidden="true" tabindex="-1"></a>    THAI <span class="op">=</span> <span class="st">&#39;th&#39;</span><span class="op">,</span></span>
<span id="cb1-15"><a href="#cb1-15" aria-hidden="true" tabindex="-1"></a>    VIETNAMESE <span class="op">=</span> <span class="st">&#39;vi&#39;</span><span class="op">,</span></span>
<span id="cb1-16"><a href="#cb1-16" aria-hidden="true" tabindex="-1"></a>    INDONESIAN <span class="op">=</span> <span class="st">&#39;id&#39;</span></span>
<span id="cb1-17"><a href="#cb1-17" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb1-18"><a href="#cb1-18" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-19"><a href="#cb1-19" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> LanguageManager {</span>
<span id="cb1-20"><a href="#cb1-20" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="kw">static</span> instance<span class="op">:</span> LanguageManager<span class="op">;</span></span>
<span id="cb1-21"><a href="#cb1-21" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> currentLanguage<span class="op">:</span> LanguageType <span class="op">=</span> LanguageType<span class="op">.</span><span class="at">ENGLISH</span><span class="op">;</span></span>
<span id="cb1-22"><a href="#cb1-22" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> languageData<span class="op">:</span> <span class="bu">Map</span><span class="op">&lt;</span>LanguageType<span class="op">,</span> <span class="dt">any</span><span class="op">&gt;</span> <span class="op">=</span> <span class="kw">new</span> <span class="bu">Map</span>()<span class="op">;</span></span>
<span id="cb1-23"><a href="#cb1-23" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> fallbackLanguage<span class="op">:</span> LanguageType <span class="op">=</span> LanguageType<span class="op">.</span><span class="at">ENGLISH</span><span class="op">;</span></span>
<span id="cb1-24"><a href="#cb1-24" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-25"><a href="#cb1-25" aria-hidden="true" tabindex="-1"></a>    <span class="kw">static</span> <span class="fu">getInstance</span>()<span class="op">:</span> LanguageManager {</span>
<span id="cb1-26"><a href="#cb1-26" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="op">!</span>LanguageManager<span class="op">.</span><span class="at">instance</span>) {</span>
<span id="cb1-27"><a href="#cb1-27" aria-hidden="true" tabindex="-1"></a>            LanguageManager<span class="op">.</span><span class="at">instance</span> <span class="op">=</span> <span class="kw">new</span> <span class="fu">LanguageManager</span>()<span class="op">;</span></span>
<span id="cb1-28"><a href="#cb1-28" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb1-29"><a href="#cb1-29" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> LanguageManager<span class="op">.</span><span class="at">instance</span><span class="op">;</span></span>
<span id="cb1-30"><a href="#cb1-30" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-31"><a href="#cb1-31" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-32"><a href="#cb1-32" aria-hidden="true" tabindex="-1"></a>    <span class="kw">async</span> <span class="fu">initialize</span>() {</span>
<span id="cb1-33"><a href="#cb1-33" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 检测系统语言</span></span>
<span id="cb1-34"><a href="#cb1-34" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="fu">detectSystemLanguage</span>()<span class="op">;</span></span>
<span id="cb1-35"><a href="#cb1-35" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-36"><a href="#cb1-36" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载语言数据</span></span>
<span id="cb1-37"><a href="#cb1-37" aria-hidden="true" tabindex="-1"></a>        <span class="cf">await</span> <span class="kw">this</span><span class="op">.</span><span class="fu">loadLanguageData</span>()<span class="op">;</span></span>
<span id="cb1-38"><a href="#cb1-38" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-39"><a href="#cb1-39" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 应用语言设置</span></span>
<span id="cb1-40"><a href="#cb1-40" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="fu">applyLanguage</span>()<span class="op">;</span></span>
<span id="cb1-41"><a href="#cb1-41" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-42"><a href="#cb1-42" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-43"><a href="#cb1-43" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">detectSystemLanguage</span>() {</span>
<span id="cb1-44"><a href="#cb1-44" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> systemLang <span class="op">=</span> sys<span class="op">.</span><span class="at">languageCode</span><span class="op">;</span></span>
<span id="cb1-45"><a href="#cb1-45" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-46"><a href="#cb1-46" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 映射系统语言代码到支持的语言</span></span>
<span id="cb1-47"><a href="#cb1-47" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> languageMap<span class="op">:</span> { [key<span class="op">:</span> <span class="dt">string</span>]<span class="op">:</span> LanguageType } <span class="op">=</span> {</span>
<span id="cb1-48"><a href="#cb1-48" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;zh&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">CHINESE</span><span class="op">,</span></span>
<span id="cb1-49"><a href="#cb1-49" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;zh-CN&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">CHINESE</span><span class="op">,</span></span>
<span id="cb1-50"><a href="#cb1-50" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;zh-TW&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">CHINESE</span><span class="op">,</span></span>
<span id="cb1-51"><a href="#cb1-51" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;en&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">ENGLISH</span><span class="op">,</span></span>
<span id="cb1-52"><a href="#cb1-52" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;ja&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">JAPANESE</span><span class="op">,</span></span>
<span id="cb1-53"><a href="#cb1-53" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;ko&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">KOREAN</span><span class="op">,</span></span>
<span id="cb1-54"><a href="#cb1-54" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;es&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">SPANISH</span><span class="op">,</span></span>
<span id="cb1-55"><a href="#cb1-55" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;fr&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">FRENCH</span><span class="op">,</span></span>
<span id="cb1-56"><a href="#cb1-56" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;de&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">GERMAN</span><span class="op">,</span></span>
<span id="cb1-57"><a href="#cb1-57" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;ru&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">RUSSIAN</span><span class="op">,</span></span>
<span id="cb1-58"><a href="#cb1-58" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;pt&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">PORTUGUESE</span><span class="op">,</span></span>
<span id="cb1-59"><a href="#cb1-59" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;th&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">THAI</span><span class="op">,</span></span>
<span id="cb1-60"><a href="#cb1-60" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;vi&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">VIETNAMESE</span><span class="op">,</span></span>
<span id="cb1-61"><a href="#cb1-61" aria-hidden="true" tabindex="-1"></a>            <span class="st">&#39;id&#39;</span><span class="op">:</span> LanguageType<span class="op">.</span><span class="at">INDONESIAN</span></span>
<span id="cb1-62"><a href="#cb1-62" aria-hidden="true" tabindex="-1"></a>        }<span class="op">;</span></span>
<span id="cb1-63"><a href="#cb1-63" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-64"><a href="#cb1-64" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">currentLanguage</span> <span class="op">=</span> languageMap[systemLang] <span class="op">||</span> LanguageType<span class="op">.</span><span class="at">ENGLISH</span><span class="op">;</span></span>
<span id="cb1-65"><a href="#cb1-65" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-66"><a href="#cb1-66" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 从本地存储读取用户设置</span></span>
<span id="cb1-67"><a href="#cb1-67" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> savedLang <span class="op">=</span> sys<span class="op">.</span><span class="at">localStorage</span><span class="op">.</span><span class="fu">getItem</span>(<span class="st">&#39;game_language&#39;</span>)<span class="op">;</span></span>
<span id="cb1-68"><a href="#cb1-68" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (savedLang <span class="op">&amp;&amp;</span> <span class="bu">Object</span><span class="op">.</span><span class="fu">values</span>(LanguageType)<span class="op">.</span><span class="fu">includes</span>(savedLang <span class="im">as</span> LanguageType)) {</span>
<span id="cb1-69"><a href="#cb1-69" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="at">currentLanguage</span> <span class="op">=</span> savedLang <span class="im">as</span> LanguageType<span class="op">;</span></span>
<span id="cb1-70"><a href="#cb1-70" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb1-71"><a href="#cb1-71" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-72"><a href="#cb1-72" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-73"><a href="#cb1-73" aria-hidden="true" tabindex="-1"></a>    <span class="kw">async</span> <span class="fu">loadLanguageData</span>() {</span>
<span id="cb1-74"><a href="#cb1-74" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载所有支持的语言数据</span></span>
<span id="cb1-75"><a href="#cb1-75" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> languages <span class="op">=</span> <span class="bu">Object</span><span class="op">.</span><span class="fu">values</span>(LanguageType)<span class="op">;</span></span>
<span id="cb1-76"><a href="#cb1-76" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-77"><a href="#cb1-77" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> (<span class="kw">const</span> lang <span class="kw">of</span> languages) {</span>
<span id="cb1-78"><a href="#cb1-78" aria-hidden="true" tabindex="-1"></a>            <span class="cf">try</span> {</span>
<span id="cb1-79"><a href="#cb1-79" aria-hidden="true" tabindex="-1"></a>                <span class="kw">const</span> data <span class="op">=</span> <span class="cf">await</span> <span class="kw">this</span><span class="op">.</span><span class="fu">loadLanguageFile</span>(lang)<span class="op">;</span></span>
<span id="cb1-80"><a href="#cb1-80" aria-hidden="true" tabindex="-1"></a>                <span class="kw">this</span><span class="op">.</span><span class="at">languageData</span><span class="op">.</span><span class="fu">set</span>(lang<span class="op">,</span> data)<span class="op">;</span></span>
<span id="cb1-81"><a href="#cb1-81" aria-hidden="true" tabindex="-1"></a>            } <span class="cf">catch</span> (error) {</span>
<span id="cb1-82"><a href="#cb1-82" aria-hidden="true" tabindex="-1"></a>                <span class="bu">console</span><span class="op">.</span><span class="fu">warn</span>(<span class="vs">`Failed to load language data for </span><span class="sc">${</span>lang<span class="sc">}</span><span class="vs">:`</span><span class="op">,</span> error)<span class="op">;</span></span>
<span id="cb1-83"><a href="#cb1-83" aria-hidden="true" tabindex="-1"></a>            }</span>
<span id="cb1-84"><a href="#cb1-84" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb1-85"><a href="#cb1-85" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-86"><a href="#cb1-86" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-87"><a href="#cb1-87" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="kw">async</span> <span class="fu">loadLanguageFile</span>(language<span class="op">:</span> LanguageType)<span class="op">:</span> <span class="bu">Promise</span><span class="op">&lt;</span><span class="dt">any</span><span class="op">&gt;</span> {</span>
<span id="cb1-88"><a href="#cb1-88" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">new</span> <span class="bu">Promise</span>((resolve<span class="op">,</span> reject) <span class="kw">=&gt;</span> {</span>
<span id="cb1-89"><a href="#cb1-89" aria-hidden="true" tabindex="-1"></a>            <span class="kw">const</span> path <span class="op">=</span> <span class="vs">`i18n/</span><span class="sc">${</span>language<span class="sc">}</span><span class="vs">`</span><span class="op">;</span></span>
<span id="cb1-90"><a href="#cb1-90" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-91"><a href="#cb1-91" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 使用 Cocos 资源管理系统加载</span></span>
<span id="cb1-92"><a href="#cb1-92" aria-hidden="true" tabindex="-1"></a>            resources<span class="op">.</span><span class="fu">load</span>(path<span class="op">,</span> (err<span class="op">,</span> data) <span class="kw">=&gt;</span> {</span>
<span id="cb1-93"><a href="#cb1-93" aria-hidden="true" tabindex="-1"></a>                <span class="cf">if</span> (err) {</span>
<span id="cb1-94"><a href="#cb1-94" aria-hidden="true" tabindex="-1"></a>                    <span class="fu">reject</span>(err)<span class="op">;</span></span>
<span id="cb1-95"><a href="#cb1-95" aria-hidden="true" tabindex="-1"></a>                } <span class="cf">else</span> {</span>
<span id="cb1-96"><a href="#cb1-96" aria-hidden="true" tabindex="-1"></a>                    <span class="fu">resolve</span>(data<span class="op">.</span><span class="at">json</span>)<span class="op">;</span></span>
<span id="cb1-97"><a href="#cb1-97" aria-hidden="true" tabindex="-1"></a>                }</span>
<span id="cb1-98"><a href="#cb1-98" aria-hidden="true" tabindex="-1"></a>            })<span class="op">;</span></span>
<span id="cb1-99"><a href="#cb1-99" aria-hidden="true" tabindex="-1"></a>        })<span class="op">;</span></span>
<span id="cb1-100"><a href="#cb1-100" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-101"><a href="#cb1-101" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-102"><a href="#cb1-102" aria-hidden="true" tabindex="-1"></a>    <span class="fu">setLanguage</span>(language<span class="op">:</span> LanguageType) {</span>
<span id="cb1-103"><a href="#cb1-103" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="kw">this</span><span class="op">.</span><span class="at">languageData</span><span class="op">.</span><span class="fu">has</span>(language)) {</span>
<span id="cb1-104"><a href="#cb1-104" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="at">currentLanguage</span> <span class="op">=</span> language<span class="op">;</span></span>
<span id="cb1-105"><a href="#cb1-105" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-106"><a href="#cb1-106" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 保存到本地存储</span></span>
<span id="cb1-107"><a href="#cb1-107" aria-hidden="true" tabindex="-1"></a>            sys<span class="op">.</span><span class="at">localStorage</span><span class="op">.</span><span class="fu">setItem</span>(<span class="st">&#39;game_language&#39;</span><span class="op">,</span> language)<span class="op">;</span></span>
<span id="cb1-108"><a href="#cb1-108" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-109"><a href="#cb1-109" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 应用新语言</span></span>
<span id="cb1-110"><a href="#cb1-110" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="fu">applyLanguage</span>()<span class="op">;</span></span>
<span id="cb1-111"><a href="#cb1-111" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-112"><a href="#cb1-112" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 发送语言变化事件</span></span>
<span id="cb1-113"><a href="#cb1-113" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="fu">notifyLanguageChanged</span>()<span class="op">;</span></span>
<span id="cb1-114"><a href="#cb1-114" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb1-115"><a href="#cb1-115" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-116"><a href="#cb1-116" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-117"><a href="#cb1-117" aria-hidden="true" tabindex="-1"></a>    <span class="fu">getCurrentLanguage</span>()<span class="op">:</span> LanguageType {</span>
<span id="cb1-118"><a href="#cb1-118" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">this</span><span class="op">.</span><span class="at">currentLanguage</span><span class="op">;</span></span>
<span id="cb1-119"><a href="#cb1-119" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-120"><a href="#cb1-120" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-121"><a href="#cb1-121" aria-hidden="true" tabindex="-1"></a>    <span class="fu">getText</span>(key<span class="op">:</span> <span class="dt">string</span><span class="op">,</span> params<span class="op">?:</span> { [key<span class="op">:</span> <span class="dt">string</span>]<span class="op">:</span> <span class="dt">string</span> })<span class="op">:</span> <span class="dt">string</span> {</span>
<span id="cb1-122"><a href="#cb1-122" aria-hidden="true" tabindex="-1"></a>        <span class="kw">let</span> text <span class="op">=</span> <span class="kw">this</span><span class="op">.</span><span class="fu">getTextFromLanguage</span>(<span class="kw">this</span><span class="op">.</span><span class="at">currentLanguage</span><span class="op">,</span> key)<span class="op">;</span></span>
<span id="cb1-123"><a href="#cb1-123" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-124"><a href="#cb1-124" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 如果当前语言没有找到，使用回退语言</span></span>
<span id="cb1-125"><a href="#cb1-125" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="op">!</span>text <span class="op">&amp;&amp;</span> <span class="kw">this</span><span class="op">.</span><span class="at">currentLanguage</span> <span class="op">!==</span> <span class="kw">this</span><span class="op">.</span><span class="at">fallbackLanguage</span>) {</span>
<span id="cb1-126"><a href="#cb1-126" aria-hidden="true" tabindex="-1"></a>            text <span class="op">=</span> <span class="kw">this</span><span class="op">.</span><span class="fu">getTextFromLanguage</span>(<span class="kw">this</span><span class="op">.</span><span class="at">fallbackLanguage</span><span class="op">,</span> key)<span class="op">;</span></span>
<span id="cb1-127"><a href="#cb1-127" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb1-128"><a href="#cb1-128" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-129"><a href="#cb1-129" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 如果还是没有找到，返回键名</span></span>
<span id="cb1-130"><a href="#cb1-130" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="op">!</span>text) {</span>
<span id="cb1-131"><a href="#cb1-131" aria-hidden="true" tabindex="-1"></a>            <span class="bu">console</span><span class="op">.</span><span class="fu">warn</span>(<span class="vs">`Missing translation for key: </span><span class="sc">${</span>key<span class="sc">}</span><span class="vs">`</span>)<span class="op">;</span></span>
<span id="cb1-132"><a href="#cb1-132" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> key<span class="op">;</span></span>
<span id="cb1-133"><a href="#cb1-133" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb1-134"><a href="#cb1-134" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-135"><a href="#cb1-135" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 参数替换</span></span>
<span id="cb1-136"><a href="#cb1-136" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (params) {</span>
<span id="cb1-137"><a href="#cb1-137" aria-hidden="true" tabindex="-1"></a>            <span class="bu">Object</span><span class="op">.</span><span class="fu">keys</span>(params)<span class="op">.</span><span class="fu">forEach</span>(param <span class="kw">=&gt;</span> {</span>
<span id="cb1-138"><a href="#cb1-138" aria-hidden="true" tabindex="-1"></a>                text <span class="op">=</span> text<span class="op">.</span><span class="fu">replace</span>(<span class="kw">new</span> <span class="bu">RegExp</span>(<span class="vs">`{</span><span class="sc">${</span>param<span class="sc">}</span><span class="vs">}`</span><span class="op">,</span> <span class="st">&#39;g&#39;</span>)<span class="op">,</span> params[param])<span class="op">;</span></span>
<span id="cb1-139"><a href="#cb1-139" aria-hidden="true" tabindex="-1"></a>            })<span class="op">;</span></span>
<span id="cb1-140"><a href="#cb1-140" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb1-141"><a href="#cb1-141" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-142"><a href="#cb1-142" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> text<span class="op">;</span></span>
<span id="cb1-143"><a href="#cb1-143" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-144"><a href="#cb1-144" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-145"><a href="#cb1-145" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">getTextFromLanguage</span>(language<span class="op">:</span> LanguageType<span class="op">,</span> key<span class="op">:</span> <span class="dt">string</span>)<span class="op">:</span> <span class="dt">string</span> {</span>
<span id="cb1-146"><a href="#cb1-146" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> data <span class="op">=</span> <span class="kw">this</span><span class="op">.</span><span class="at">languageData</span><span class="op">.</span><span class="fu">get</span>(language)<span class="op">;</span></span>
<span id="cb1-147"><a href="#cb1-147" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="op">!</span>data) <span class="cf">return</span> <span class="st">&#39;&#39;</span><span class="op">;</span></span>
<span id="cb1-148"><a href="#cb1-148" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-149"><a href="#cb1-149" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 支持嵌套键名，如 &quot;ui.menu.start&quot;</span></span>
<span id="cb1-150"><a href="#cb1-150" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> keys <span class="op">=</span> key<span class="op">.</span><span class="fu">split</span>(<span class="st">&#39;.&#39;</span>)<span class="op">;</span></span>
<span id="cb1-151"><a href="#cb1-151" aria-hidden="true" tabindex="-1"></a>        <span class="kw">let</span> value <span class="op">=</span> data<span class="op">;</span></span>
<span id="cb1-152"><a href="#cb1-152" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-153"><a href="#cb1-153" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> (<span class="kw">const</span> k <span class="kw">of</span> keys) {</span>
<span id="cb1-154"><a href="#cb1-154" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> (value <span class="op">&amp;&amp;</span> <span class="kw">typeof</span> value <span class="op">===</span> <span class="st">&#39;object&#39;</span> <span class="op">&amp;&amp;</span> k <span class="kw">in</span> value) {</span>
<span id="cb1-155"><a href="#cb1-155" aria-hidden="true" tabindex="-1"></a>                value <span class="op">=</span> value[k]<span class="op">;</span></span>
<span id="cb1-156"><a href="#cb1-156" aria-hidden="true" tabindex="-1"></a>            } <span class="cf">else</span> {</span>
<span id="cb1-157"><a href="#cb1-157" aria-hidden="true" tabindex="-1"></a>                <span class="cf">return</span> <span class="st">&#39;&#39;</span><span class="op">;</span></span>
<span id="cb1-158"><a href="#cb1-158" aria-hidden="true" tabindex="-1"></a>            }</span>
<span id="cb1-159"><a href="#cb1-159" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb1-160"><a href="#cb1-160" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-161"><a href="#cb1-161" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">typeof</span> value <span class="op">===</span> <span class="st">&#39;string&#39;</span> <span class="op">?</span> value <span class="op">:</span> <span class="st">&#39;&#39;</span><span class="op">;</span></span>
<span id="cb1-162"><a href="#cb1-162" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-163"><a href="#cb1-163" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-164"><a href="#cb1-164" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">applyLanguage</span>() {</span>
<span id="cb1-165"><a href="#cb1-165" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 通知所有本地化组件更新</span></span>
<span id="cb1-166"><a href="#cb1-166" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="fu">notifyLanguageChanged</span>()<span class="op">;</span></span>
<span id="cb1-167"><a href="#cb1-167" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-168"><a href="#cb1-168" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 更新字体设置</span></span>
<span id="cb1-169"><a href="#cb1-169" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="fu">updateFontSettings</span>()<span class="op">;</span></span>
<span id="cb1-170"><a href="#cb1-170" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-171"><a href="#cb1-171" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-172"><a href="#cb1-172" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">updateFontSettings</span>() {</span>
<span id="cb1-173"><a href="#cb1-173" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 根据语言设置合适的字体</span></span>
<span id="cb1-174"><a href="#cb1-174" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> fontSettings <span class="op">=</span> <span class="kw">this</span><span class="op">.</span><span class="fu">getFontSettingsForLanguage</span>(<span class="kw">this</span><span class="op">.</span><span class="at">currentLanguage</span>)<span class="op">;</span></span>
<span id="cb1-175"><a href="#cb1-175" aria-hidden="true" tabindex="-1"></a>        FontManager<span class="op">.</span><span class="fu">getInstance</span>()<span class="op">.</span><span class="fu">setDefaultFont</span>(fontSettings)<span class="op">;</span></span>
<span id="cb1-176"><a href="#cb1-176" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-177"><a href="#cb1-177" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-178"><a href="#cb1-178" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">getFontSettingsForLanguage</span>(language<span class="op">:</span> LanguageType) {</span>
<span id="cb1-179"><a href="#cb1-179" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> fontMap <span class="op">=</span> {</span>
<span id="cb1-180"><a href="#cb1-180" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">CHINESE</span>]<span class="op">:</span> <span class="st">&#39;fonts/NotoSansCJK&#39;</span><span class="op">,</span></span>
<span id="cb1-181"><a href="#cb1-181" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">JAPANESE</span>]<span class="op">:</span> <span class="st">&#39;fonts/NotoSansJP&#39;</span><span class="op">,</span></span>
<span id="cb1-182"><a href="#cb1-182" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">KOREAN</span>]<span class="op">:</span> <span class="st">&#39;fonts/NotoSansKR&#39;</span><span class="op">,</span></span>
<span id="cb1-183"><a href="#cb1-183" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">THAI</span>]<span class="op">:</span> <span class="st">&#39;fonts/NotoSansThai&#39;</span><span class="op">,</span></span>
<span id="cb1-184"><a href="#cb1-184" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">VIETNAMESE</span>]<span class="op">:</span> <span class="st">&#39;fonts/NotoSansVietnamese&#39;</span></span>
<span id="cb1-185"><a href="#cb1-185" aria-hidden="true" tabindex="-1"></a>        }<span class="op">;</span></span>
<span id="cb1-186"><a href="#cb1-186" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-187"><a href="#cb1-187" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> fontMap[language] <span class="op">||</span> <span class="st">&#39;fonts/NotoSans&#39;</span><span class="op">;</span></span>
<span id="cb1-188"><a href="#cb1-188" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-189"><a href="#cb1-189" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-190"><a href="#cb1-190" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">notifyLanguageChanged</span>() {</span>
<span id="cb1-191"><a href="#cb1-191" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 发送全局事件</span></span>
<span id="cb1-192"><a href="#cb1-192" aria-hidden="true" tabindex="-1"></a>        GameEventManager<span class="op">.</span><span class="fu">getInstance</span>()<span class="op">.</span><span class="fu">emit</span>(<span class="st">&#39;language-changed&#39;</span><span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">currentLanguage</span>)<span class="op">;</span></span>
<span id="cb1-193"><a href="#cb1-193" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-194"><a href="#cb1-194" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-195"><a href="#cb1-195" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取当前语言的文本方向</span></span>
<span id="cb1-196"><a href="#cb1-196" aria-hidden="true" tabindex="-1"></a>    <span class="fu">isRTL</span>()<span class="op">:</span> <span class="dt">boolean</span> {</span>
<span id="cb1-197"><a href="#cb1-197" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 阿拉伯语、希伯来语等从右到左的语言</span></span>
<span id="cb1-198"><a href="#cb1-198" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> rtlLanguages <span class="op">=</span> [<span class="st">&#39;ar&#39;</span><span class="op">,</span> <span class="st">&#39;he&#39;</span><span class="op">,</span> <span class="st">&#39;fa&#39;</span><span class="op">,</span> <span class="st">&#39;ur&#39;</span>]<span class="op">;</span></span>
<span id="cb1-199"><a href="#cb1-199" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> rtlLanguages<span class="op">.</span><span class="fu">includes</span>(<span class="kw">this</span><span class="op">.</span><span class="at">currentLanguage</span>)<span class="op">;</span></span>
<span id="cb1-200"><a href="#cb1-200" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-201"><a href="#cb1-201" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-202"><a href="#cb1-202" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取当前语言的数字格式</span></span>
<span id="cb1-203"><a href="#cb1-203" aria-hidden="true" tabindex="-1"></a>    <span class="fu">formatNumber</span>(num<span class="op">:</span> <span class="dt">number</span>)<span class="op">:</span> <span class="dt">string</span> {</span>
<span id="cb1-204"><a href="#cb1-204" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> locale <span class="op">=</span> <span class="kw">this</span><span class="op">.</span><span class="fu">getLocaleString</span>()<span class="op">;</span></span>
<span id="cb1-205"><a href="#cb1-205" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">new</span> <span class="bu">Intl</span><span class="op">.</span><span class="fu">NumberFormat</span>(locale)<span class="op">.</span><span class="fu">format</span>(num)<span class="op">;</span></span>
<span id="cb1-206"><a href="#cb1-206" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-207"><a href="#cb1-207" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-208"><a href="#cb1-208" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取当前语言的货币格式</span></span>
<span id="cb1-209"><a href="#cb1-209" aria-hidden="true" tabindex="-1"></a>    <span class="fu">formatCurrency</span>(amount<span class="op">:</span> <span class="dt">number</span><span class="op">,</span> currency<span class="op">:</span> <span class="dt">string</span> <span class="op">=</span> <span class="st">&#39;USD&#39;</span>)<span class="op">:</span> <span class="dt">string</span> {</span>
<span id="cb1-210"><a href="#cb1-210" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> locale <span class="op">=</span> <span class="kw">this</span><span class="op">.</span><span class="fu">getLocaleString</span>()<span class="op">;</span></span>
<span id="cb1-211"><a href="#cb1-211" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">new</span> <span class="bu">Intl</span><span class="op">.</span><span class="fu">NumberFormat</span>(locale<span class="op">,</span> {</span>
<span id="cb1-212"><a href="#cb1-212" aria-hidden="true" tabindex="-1"></a>            style<span class="op">:</span> <span class="st">&#39;currency&#39;</span><span class="op">,</span></span>
<span id="cb1-213"><a href="#cb1-213" aria-hidden="true" tabindex="-1"></a>            currency<span class="op">:</span> currency</span>
<span id="cb1-214"><a href="#cb1-214" aria-hidden="true" tabindex="-1"></a>        })<span class="op">.</span><span class="fu">format</span>(amount)<span class="op">;</span></span>
<span id="cb1-215"><a href="#cb1-215" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-216"><a href="#cb1-216" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-217"><a href="#cb1-217" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">getLocaleString</span>()<span class="op">:</span> <span class="dt">string</span> {</span>
<span id="cb1-218"><a href="#cb1-218" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> localeMap <span class="op">=</span> {</span>
<span id="cb1-219"><a href="#cb1-219" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">CHINESE</span>]<span class="op">:</span> <span class="st">&#39;zh-CN&#39;</span><span class="op">,</span></span>
<span id="cb1-220"><a href="#cb1-220" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">ENGLISH</span>]<span class="op">:</span> <span class="st">&#39;en-US&#39;</span><span class="op">,</span></span>
<span id="cb1-221"><a href="#cb1-221" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">JAPANESE</span>]<span class="op">:</span> <span class="st">&#39;ja-JP&#39;</span><span class="op">,</span></span>
<span id="cb1-222"><a href="#cb1-222" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">KOREAN</span>]<span class="op">:</span> <span class="st">&#39;ko-KR&#39;</span><span class="op">,</span></span>
<span id="cb1-223"><a href="#cb1-223" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">SPANISH</span>]<span class="op">:</span> <span class="st">&#39;es-ES&#39;</span><span class="op">,</span></span>
<span id="cb1-224"><a href="#cb1-224" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">FRENCH</span>]<span class="op">:</span> <span class="st">&#39;fr-FR&#39;</span><span class="op">,</span></span>
<span id="cb1-225"><a href="#cb1-225" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">GERMAN</span>]<span class="op">:</span> <span class="st">&#39;de-DE&#39;</span><span class="op">,</span></span>
<span id="cb1-226"><a href="#cb1-226" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">RUSSIAN</span>]<span class="op">:</span> <span class="st">&#39;ru-RU&#39;</span><span class="op">,</span></span>
<span id="cb1-227"><a href="#cb1-227" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">PORTUGUESE</span>]<span class="op">:</span> <span class="st">&#39;pt-BR&#39;</span><span class="op">,</span></span>
<span id="cb1-228"><a href="#cb1-228" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">THAI</span>]<span class="op">:</span> <span class="st">&#39;th-TH&#39;</span><span class="op">,</span></span>
<span id="cb1-229"><a href="#cb1-229" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">VIETNAMESE</span>]<span class="op">:</span> <span class="st">&#39;vi-VN&#39;</span><span class="op">,</span></span>
<span id="cb1-230"><a href="#cb1-230" aria-hidden="true" tabindex="-1"></a>            [LanguageType<span class="op">.</span><span class="at">INDONESIAN</span>]<span class="op">:</span> <span class="st">&#39;id-ID&#39;</span></span>
<span id="cb1-231"><a href="#cb1-231" aria-hidden="true" tabindex="-1"></a>        }<span class="op">;</span></span>
<span id="cb1-232"><a href="#cb1-232" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-233"><a href="#cb1-233" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> localeMap[<span class="kw">this</span><span class="op">.</span><span class="at">currentLanguage</span>] <span class="op">||</span> <span class="st">&#39;en-US&#39;</span><span class="op">;</span></span>
<span id="cb1-234"><a href="#cb1-234" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-235"><a href="#cb1-235" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
</div><div lang="en" style="display: none;"><h1>[English translation pending]</h1><p>The content for this page has not been translated yet. Please check back later.</p></div>
                </main>
            </div>
        </section>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">Qt 3D 开发指南</h4>
                    <p data-i18n="footer-subtitle">专业的跨平台 3D 应用开发解决方案</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.</p>
            </div>
        </div>
    </footer>

    <script src="../../assets/js/prism.js"></script>
    <script src="../../assets/js/docs.js"></script>
</body>
</html> 