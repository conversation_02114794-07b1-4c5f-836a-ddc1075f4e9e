# Qt 3D 引擎开发指南

## 目录
1. [概述](#概述)
2. [技术特性](#技术特性)
3. [开发环境搭建](#开发环境搭建)
4. [核心架构](#核心架构)
5. [开发流程](#开发流程)
6. [最佳实践](#最佳实践)
7. [性能优化](#性能优化)
8. [调试与测试](#调试与测试)

## 概述

Qt 3D 是 Qt 框架的一部分，提供了功能丰富的 3D 图形渲染和场景管理功能。它基于现代图形 API（OpenGL、Vulkan、DirectX），为开发者提供了高级的 3D 应用程序开发能力。

### 主要优势
- **跨平台支持**：支持 Windows、Linux、macOS、Android、iOS 等多个平台
- **现代图形 API**：支持 OpenGL、Vulkan、DirectX 12 等现代图形 API
- **声明式编程**：支持 QML 声明式编程，简化 3D 场景构建
- **高性能渲染**：基于多线程渲染架构，提供高效的图形渲染性能
- **丰富的材质系统**：内置多种材质和着色器，支持自定义材质
- **完整的生态系统**：与 Qt 生态系统完美集成，可复用现有 Qt 技能

## 技术特性

### 渲染引擎
- **多线程渲染架构**：渲染线程与主线程分离，确保 UI 响应性
- **现代图形 API 支持**：
  - OpenGL 3.2+ / OpenGL ES 3.0+
  - Vulkan 1.0+
  - DirectX 12（Windows）
  - Metal（macOS/iOS）
- **延迟渲染管线**：支持延迟渲染和前向渲染
- **HDR 渲染**：支持高动态范围渲染
- **多重采样抗锯齿**：MSAA、FXAA 等抗锯齿技术

### 场景管理
- **实体-组件-系统（ECS）架构**：灵活的场景对象管理
- **层次化场景图**：支持复杂的场景层次结构
- **空间分割**：八叉树、BSP 树等空间优化算法
- **视锥剔除**：自动进行视锥剔除优化
- **LOD 系统**：多级细节模型支持

### 材质与着色器
- **基于物理的渲染（PBR）**：支持现代 PBR 材质工作流
- **自定义着色器**：支持 GLSL、HLSL 着色器编程
- **材质编辑器**：可视化材质编辑工具
- **纹理管理**：高效的纹理加载和管理系统
- **动态材质**：运行时材质参数调整

### 动画系统
- **骨骼动画**：完整的骨骼动画系统
- **关键帧动画**：支持位置、旋转、缩放动画
- **动画混合**：多动画混合和过渡
- **动画状态机**：复杂动画逻辑管理
- **物理动画**：与物理引擎集成的动画

### 2D 图形支持
- **Qt Quick**：基于 QML 的 2D 图形框架
- **Canvas API**：HTML5 Canvas 风格的 2D 绘图
- **SVG 支持**：矢量图形的完整支持
- **图像处理**：丰富的图像滤镜和效果
- **2D 动画**：属性动画、路径动画、粒子系统

### 2D/3D 融合开发
- **混合渲染**：在同一场景中同时渲染 2D 和 3D 内容
- **UI 叠加**：3D 场景上的 2D UI 界面
- **纹理映射**：将 2D 内容作为 3D 对象的纹理
- **坐标转换**：2D 屏幕坐标与 3D 世界坐标的转换
- **交互集成**：2D UI 控件与 3D 场景的交互

## 开发环境搭建

### 系统要求
- **操作系统**：Windows 10+, Ubuntu 18.04+, macOS 10.14+
- **Qt 版本**：Qt 5.15+ 或 Qt 6.2+
- **编译器**：
  - Windows: MSVC 2019+ 或 MinGW 8.1+
  - Linux: GCC 7+ 或 Clang 8+
  - macOS: Xcode 11+
- **图形驱动**：支持 OpenGL 3.2+ 的显卡驱动

### 安装步骤

#### 1. 安装 Qt
```bash
# 使用 Qt 在线安装器
./qt-unified-linux-x64-online.run

# 或使用包管理器（Ubuntu）
sudo apt-get install qt6-base-dev qt6-3d-dev
```

#### 2. 配置开发环境
```bash
# 设置环境变量
export QT_DIR=/opt/Qt/6.5.0/gcc_64
export PATH=$QT_DIR/bin:$PATH
export LD_LIBRARY_PATH=$QT_DIR/lib:$LD_LIBRARY_PATH
```

#### 3. 验证安装
```cpp
// test_qt3d.cpp
#include <Qt3DCore/QEntity>
#include <Qt3DRender/QCamera>
#include <QApplication>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 创建基本的 3D 场景
    Qt3DCore::QEntity *rootEntity = new Qt3DCore::QEntity;
    
    qDebug() << "Qt 3D 安装成功！";
    return 0;
}
```

## 核心架构

### ECS 架构模式

Qt 3D 采用实体-组件-系统（Entity-Component-System）架构：

```cpp
// 实体（Entity）- 场景中的对象
Qt3DCore::QEntity *cubeEntity = new Qt3DCore::QEntity(rootEntity);

// 组件（Component）- 对象的属性和行为
Qt3DCore::QTransform *cubeTransform = new Qt3DCore::QTransform();
Qt3DRender::QMesh *cubeMesh = new Qt3DRender::QMesh();
Qt3DExtras::QPhongMaterial *cubeMaterial = new Qt3DExtras::QPhongMaterial();

// 将组件添加到实体
cubeEntity->addComponent(cubeTransform);
cubeEntity->addComponent(cubeMesh);
cubeEntity->addComponent(cubeMaterial);
```

### 渲染管线

```cpp
// 创建渲染设置
Qt3DRender::QRenderSettings *renderSettings = new Qt3DRender::QRenderSettings();

// 配置渲染管线
Qt3DRender::QRenderSurfaceSelector *surfaceSelector = new Qt3DRender::QRenderSurfaceSelector();
Qt3DRender::QViewport *viewport = new Qt3DRender::QViewport(surfaceSelector);
Qt3DRender::QCameraSelector *cameraSelector = new Qt3DRender::QCameraSelector(viewport);
Qt3DRender::QClearBuffers *clearBuffers = new Qt3DRender::QClearBuffers(cameraSelector);

// 设置清除颜色
clearBuffers->setBuffers(Qt3DRender::QClearBuffers::ColorDepthBuffer);
clearBuffers->setClearColor(QColor(64, 64, 64));
```

## 开发流程

### 1. 项目初始化

```cpp
// main.cpp
#include <QApplication>
#include <Qt3DExtras/Qt3DWindow>
#include <Qt3DExtras/QFirstPersonCameraController>
#include <Qt3DCore/QEntity>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 创建 3D 窗口
    Qt3DExtras::Qt3DWindow *view = new Qt3DExtras::Qt3DWindow();
    view->defaultFrameGraph()->setClearColor(QColor(QRgb(0x4d4d4f)));
    
    // 创建根实体
    Qt3DCore::QEntity *rootEntity = new Qt3DCore::QEntity();
    
    // 设置场景
    view->setRootEntity(rootEntity);
    view->show();
    
    return app.exec();
}
```

### 2. 场景构建

```cpp
// 创建相机
Qt3DRender::QCamera *cameraEntity = view->camera();
cameraEntity->lens()->setPerspectiveProjection(45.0f, 16.0f/9.0f, 0.1f, 1000.0f);
cameraEntity->setPosition(QVector3D(0, 0, 20.0f));
cameraEntity->setUpVector(QVector3D(0, 1, 0));
cameraEntity->setViewCenter(QVector3D(0, 0, 0));

// 添加相机控制器
Qt3DExtras::QFirstPersonCameraController *camController = 
    new Qt3DExtras::QFirstPersonCameraController(rootEntity);
camController->setCamera(cameraEntity);
```

### 3. 几何体创建

```cpp
// 创建立方体
Qt3DCore::QEntity *cubeEntity = new Qt3DCore::QEntity(rootEntity);

// 几何体组件
Qt3DExtras::QCuboidMesh *cubeMesh = new Qt3DExtras::QCuboidMesh();
cubeMesh->setXExtent(2.0f);
cubeMesh->setYExtent(2.0f);
cubeMesh->setZExtent(2.0f);

// 变换组件
Qt3DCore::QTransform *cubeTransform = new Qt3DCore::QTransform();
cubeTransform->setTranslation(QVector3D(0.0f, 0.0f, 0.0f));

// 材质组件
Qt3DExtras::QPhongMaterial *cubeMaterial = new Qt3DExtras::QPhongMaterial();
cubeMaterial->setDiffuse(QColor(QRgb(0xa69929)));

// 添加组件到实体
cubeEntity->addComponent(cubeMesh);
cubeEntity->addComponent(cubeTransform);
cubeEntity->addComponent(cubeMaterial);
```

### 4. 动画实现

```cpp
// 创建动画
QPropertyAnimation *cubeRotateTransformAnimation = new QPropertyAnimation(cubeTransform);
cubeRotateTransformAnimation->setTargetObject(cubeTransform);
cubeRotateTransformAnimation->setPropertyName("rotation");
cubeRotateTransformAnimation->setStartValue(QVariant::fromValue(QQuaternion::fromAxisAndAngle(QVector3D(1, 0, 0), 0)));
cubeRotateTransformAnimation->setEndValue(QVariant::fromValue(QQuaternion::fromAxisAndAngle(QVector3D(1, 0, 0), 360)));
cubeRotateTransformAnimation->setDuration(5000);
cubeRotateTransformAnimation->setLoopCount(-1);
cubeRotateTransformAnimation->start();
```

## 2D/3D 融合开发

### 1. QML 中的 2D/3D 混合场景

```qml
import QtQuick 2.15
import QtQuick.Scene3D 2.15
import Qt3D.Core 2.15
import Qt3D.Render 2.15
import Qt3D.Extras 2.15

ApplicationWindow {
    width: 1200
    height: 800

    // 2D 背景
    Rectangle {
        anchors.fill: parent
        gradient: Gradient {
            GradientStop { position: 0.0; color: "#87CEEB" }
            GradientStop { position: 1.0; color: "#98FB98" }
        }
    }

    // 3D 场景
    Scene3D {
        id: scene3d
        anchors.fill: parent
        aspects: ["input", "logic"]
        cameraAspectRatioMode: Scene3D.AutomaticAspectRatio

        Entity {
            id: sceneRoot

            Camera {
                id: camera
                projectionType: CameraLens.PerspectiveProjection
                fieldOfView: 45
                aspectRatio: 16/9
                nearPlane: 0.1
                farPlane: 1000.0
                position: Qt.vector3d(0.0, 0.0, 20.0)
                upVector: Qt.vector3d(0.0, 1.0, 0.0)
                viewCenter: Qt.vector3d(0.0, 0.0, 0.0)
            }

            components: [
                RenderSettings {
                    activeFrameGraph: ForwardRenderer {
                        clearColor: Qt.rgba(0, 0, 0, 0) // 透明背景
                        camera: camera
                    }
                },
                InputSettings { }
            ]

            // 3D 立方体
            Entity {
                id: cubeEntity

                components: [
                    CuboidMesh {
                        xExtent: 4
                        yExtent: 4
                        zExtent: 4
                    },
                    PhongMaterial {
                        diffuse: "lightblue"
                        ambient: "blue"
                        specular: "white"
                        shininess: 150.0
                    },
                    Transform {
                        id: cubeTransform
                        translation: Qt.vector3d(0, 0, 0)

                        PropertyAnimation on rotationY {
                            loops: Animation.Infinite
                            from: 0
                            to: 360
                            duration: 5000
                        }
                    }
                ]
            }
        }
    }

    // 2D UI 叠加层
    Rectangle {
        id: uiPanel
        width: 300
        height: 200
        x: 20
        y: 20
        color: Qt.rgba(0, 0, 0, 0.7)
        radius: 10

        Column {
            anchors.centerIn: parent
            spacing: 10

            Text {
                text: "3D 场景控制"
                color: "white"
                font.pixelSize: 18
                font.bold: true
            }

            Slider {
                id: rotationSpeedSlider
                from: 0
                to: 10
                value: 5
                onValueChanged: {
                    // 控制 3D 动画速度
                    cubeTransform.rotationY = value * 36
                }
            }

            Text {
                text: "旋转速度: " + rotationSpeedSlider.value.toFixed(1)
                color: "white"
                font.pixelSize: 14
            }
        }
    }

    // 2D 信息显示
    Text {
        anchors.bottom: parent.bottom
        anchors.right: parent.right
        anchors.margins: 20
        text: "2D/3D 融合演示"
        color: "darkblue"
        font.pixelSize: 16
        font.bold: true
    }
}
```

### 2. 3D 纹理中的 2D 内容

```cpp
// 创建 2D 内容作为 3D 纹理
class Dynamic2DTexture : public Qt3DRender::QPaintedTextureImage
{
    Q_OBJECT

public:
    Dynamic2DTexture(QNode *parent = nullptr)
        : Qt3DRender::QPaintedTextureImage(parent)
    {
        setSize(QSize(512, 512));
    }

protected:
    void paint(QPainter *painter) override
    {
        // 绘制 2D 内容
        painter->fillRect(rect(), Qt::white);

        // 绘制图表
        painter->setPen(QPen(Qt::blue, 3));
        painter->drawLine(50, 400, 450, 100);

        // 绘制文字
        painter->setPen(Qt::black);
        painter->setFont(QFont("Arial", 24));
        painter->drawText(rect(), Qt::AlignCenter, "动态 2D 内容");

        // 绘制实时数据
        painter->setPen(QPen(Qt::red, 2));
        for (int i = 0; i < 10; ++i) {
            int x = 50 + i * 40;
            int y = 300 - qrand() % 200;
            painter->drawEllipse(x-5, y-5, 10, 10);
        }
    }
};

// 在 3D 场景中使用
Qt3DCore::QEntity *planeEntity = new Qt3DCore::QEntity(rootEntity);

// 平面几何体
Qt3DExtras::QPlaneMesh *planeMesh = new Qt3DExtras::QPlaneMesh();
planeMesh->setWidth(10.0f);
planeMesh->setHeight(10.0f);

// 使用动态 2D 纹理的材质
Qt3DExtras::QTextureMaterial *planeMaterial = new Qt3DExtras::QTextureMaterial();
Qt3DRender::QTexture2D *texture = new Qt3DRender::QTexture2D();
texture->addTextureImage(new Dynamic2DTexture());
planeMaterial->setTexture(texture);

// 变换
Qt3DCore::QTransform *planeTransform = new Qt3DCore::QTransform();
planeTransform->setRotationX(90);

planeEntity->addComponent(planeMesh);
planeEntity->addComponent(planeMaterial);
planeEntity->addComponent(planeTransform);
```

### 3. 坐标系转换

```cpp
// 3D 世界坐标到 2D 屏幕坐标
class CoordinateConverter : public QObject
{
    Q_OBJECT

public:
    QPointF worldToScreen(const QVector3D& worldPos,
                         Qt3DRender::QCamera* camera,
                         const QSize& viewportSize)
    {
        // 获取视图矩阵和投影矩阵
        QMatrix4x4 viewMatrix = camera->viewMatrix();
        QMatrix4x4 projMatrix = camera->projectionMatrix();

        // 世界坐标转换到裁剪坐标
        QVector4D clipPos = projMatrix * viewMatrix * QVector4D(worldPos, 1.0f);

        // 透视除法
        if (clipPos.w() != 0.0f) {
            clipPos /= clipPos.w();
        }

        // NDC 坐标转换到屏幕坐标
        float screenX = (clipPos.x() + 1.0f) * 0.5f * viewportSize.width();
        float screenY = (1.0f - clipPos.y()) * 0.5f * viewportSize.height();

        return QPointF(screenX, screenY);
    }

    QVector3D screenToWorld(const QPointF& screenPos,
                           float depth,
                           Qt3DRender::QCamera* camera,
                           const QSize& viewportSize)
    {
        // 屏幕坐标转换到 NDC
        float ndcX = (2.0f * screenPos.x()) / viewportSize.width() - 1.0f;
        float ndcY = 1.0f - (2.0f * screenPos.y()) / viewportSize.height();

        // NDC 转换到世界坐标
        QMatrix4x4 viewMatrix = camera->viewMatrix();
        QMatrix4x4 projMatrix = camera->projectionMatrix();
        QMatrix4x4 invMatrix = (projMatrix * viewMatrix).inverted();

        QVector4D worldPos = invMatrix * QVector4D(ndcX, ndcY, depth, 1.0f);

        if (worldPos.w() != 0.0f) {
            worldPos /= worldPos.w();
        }

        return worldPos.toVector3D();
    }
};
```

### 4. 2D UI 与 3D 场景交互

```cpp
// 鼠标拾取 3D 对象
class ObjectPicker : public QObject
{
    Q_OBJECT

public slots:
    void handleMouseClick(const QPointF& position)
    {
        // 创建射线
        QVector3D rayOrigin = camera->position();
        QVector3D rayDirection = screenToWorldDirection(position);

        // 检测与 3D 对象的交集
        for (auto* entity : scene3DEntities) {
            if (rayIntersectsEntity(rayOrigin, rayDirection, entity)) {
                // 显示 2D 信息面板
                showInfoPanel(entity, position);
                break;
            }
        }
    }

private:
    void showInfoPanel(Qt3DCore::QEntity* entity, const QPointF& screenPos)
    {
        // 创建 2D 信息面板
        QQuickItem* infoPanel = qmlEngine->create("InfoPanel.qml");
        infoPanel->setProperty("targetEntity", QVariant::fromValue(entity));
        infoPanel->setProperty("x", screenPos.x());
        infoPanel->setProperty("y", screenPos.y());
        infoPanel->setParent(rootItem);
    }

    bool rayIntersectsEntity(const QVector3D& rayOrigin,
                           const QVector3D& rayDirection,
                           Qt3DCore::QEntity* entity)
    {
        // 简化的包围盒检测
        // 实际应用中需要更精确的碰撞检测
        return true; // 占位符
    }
};
```

## 最佳实践

### 1. 性能优化原则
- **批量渲染**：合并相似的几何体以减少绘制调用
- **纹理优化**：使用纹理图集和压缩纹理格式
- **LOD 管理**：根据距离动态调整模型细节
- **视锥剔除**：只渲染可见的对象
- **内存管理**：及时释放不需要的资源

### 2. 代码组织
```cpp
// 推荐的项目结构
src/
├── main.cpp
├── scene/
│   ├── SceneManager.h/cpp
│   ├── Entity.h/cpp
│   └── Component.h/cpp
├── render/
│   ├── Renderer.h/cpp
│   ├── Material.h/cpp
│   └── Shader.h/cpp
├── assets/
│   ├── models/
│   ├── textures/
│   └── shaders/
└── utils/
    ├── MathUtils.h/cpp
    └── ResourceManager.h/cpp
```

### 3. 资源管理
```cpp
class ResourceManager {
public:
    static ResourceManager& instance() {
        static ResourceManager instance;
        return instance;
    }

    Qt3DRender::QMesh* loadMesh(const QString& path) {
        if (m_meshCache.contains(path)) {
            return m_meshCache[path];
        }

        Qt3DRender::QMesh* mesh = new Qt3DRender::QMesh();
        mesh->setSource(QUrl::fromLocalFile(path));
        m_meshCache[path] = mesh;
        return mesh;
    }

private:
    QHash<QString, Qt3DRender::QMesh*> m_meshCache;
};
```

### 4. 错误处理
```cpp
// 检查 OpenGL 错误
void checkGLError(const QString& operation) {
    GLenum error = glGetError();
    if (error != GL_NO_ERROR) {
        qWarning() << "OpenGL error in" << operation << ":" << error;
    }
}

// 资源加载错误处理
Qt3DRender::QMesh* loadMeshSafely(const QString& path) {
    QFileInfo fileInfo(path);
    if (!fileInfo.exists()) {
        qWarning() << "Mesh file not found:" << path;
        return nullptr;
    }

    Qt3DRender::QMesh* mesh = new Qt3DRender::QMesh();
    mesh->setSource(QUrl::fromLocalFile(path));
    return mesh;
}
```

## 性能优化

### 1. 渲染优化
- **实例化渲染**：对相同几何体使用实例化渲染
- **纹理压缩**：使用 DXT、ETC、ASTC 等压缩格式
- **Mipmap 生成**：自动生成多级纹理以提高远距离渲染质量
- **深度预通道**：使用深度预通道减少过度绘制

### 2. 内存优化
- **对象池**：重用频繁创建销毁的对象
- **智能指针**：使用 Qt 的智能指针管理内存
- **资源流式加载**：按需加载大型资源

### 3. CPU 优化
- **多线程**：利用 Qt 的多线程能力进行并行计算
- **空间分割**：使用八叉树等数据结构优化碰撞检测
- **缓存友好**：优化数据结构以提高缓存命中率

## 调试与测试

### 1. 调试工具
```cpp
// 启用 Qt 3D 调试输出
QLoggingCategory::setFilterRules("Qt3D.*.debug=true");

// 性能分析
Qt3DRender::QRenderSettings *renderSettings = new Qt3DRender::QRenderSettings();
renderSettings->setRenderPolicy(Qt3DRender::QRenderSettings::OnDemand);
```

### 2. 单元测试
```cpp
// 测试几何体创建
class GeometryTest : public QObject {
    Q_OBJECT

private slots:
    void testCubeCreation() {
        Qt3DExtras::QCuboidMesh cube;
        cube.setXExtent(2.0f);
        QCOMPARE(cube.xExtent(), 2.0f);
    }

    void testTransform() {
        Qt3DCore::QTransform transform;
        transform.setTranslation(QVector3D(1, 2, 3));
        QCOMPARE(transform.translation(), QVector3D(1, 2, 3));
    }
};
```

### 3. 性能测试
```cpp
// FPS 计算
class FPSCounter : public QObject {
    Q_OBJECT

public slots:
    void update() {
        static int frameCount = 0;
        static QTime lastTime = QTime::currentTime();

        frameCount++;
        QTime currentTime = QTime::currentTime();
        int elapsed = lastTime.msecsTo(currentTime);

        if (elapsed >= 1000) {
            float fps = frameCount * 1000.0f / elapsed;
            qDebug() << "FPS:" << fps;
            frameCount = 0;
            lastTime = currentTime;
        }
    }
};
```

---

*本文档持续更新中，更多详细信息请参考 [Qt 3D 官方文档](https://doc.qt.io/qt-6/qt3d-index.html)*
