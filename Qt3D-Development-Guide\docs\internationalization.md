# Qt 3D 国际化与本地化指南

## 目录
1. [概述](#概述)
2. [Qt 国际化框架](#qt-国际化框架)
3. [3D 应用中的国际化挑战](#3d-应用中的国际化挑战)
4. [文本国际化](#文本国际化)
5. [资源本地化](#资源本地化)
6. [用户界面适配](#用户界面适配)
7. [最佳实践](#最佳实践)
8. [工具与工作流](#工具与工作流)

## 概述

Qt 3D 应用的国际化（i18n）和本地化（l10n）是将应用程序适配到不同语言、地区和文化的过程。Qt 框架提供了完整的国际化支持，使 3D 应用能够轻松支持多语言和多地区。

### 国际化的重要性
- **全球市场**：扩大应用的目标用户群体
- **用户体验**：提供本地化的用户体验
- **法规遵循**：满足不同地区的法规要求
- **竞争优势**：在本地市场获得竞争优势
- **文化适应**：尊重不同文化的使用习惯

## Qt 国际化框架

### 核心组件

#### 1. QTranslator
```cpp
// 创建翻译器
QTranslator translator;
if (translator.load("myapp_zh_CN.qm", ":/translations")) {
    QApplication::installTranslator(&translator);
}
```

#### 2. tr() 函数
```cpp
// 在 C++ 中标记可翻译字符串
QString message = tr("Welcome to 3D World");
QString info = tr("Loading model: %1").arg(modelName);
```

#### 3. QML 中的国际化
```qml
// QML 中的翻译
Text {
    text: qsTr("3D Scene Controls")
}

Text {
    text: qsTr("Rotation: %1°").arg(rotationAngle)
}
```

### 语言环境支持

#### 1. QLocale 类
```cpp
// 获取系统语言环境
QLocale systemLocale = QLocale::system();
QString language = systemLocale.languageToString(systemLocale.language());
QString country = systemLocale.countryToString(systemLocale.country());

// 设置特定语言环境
QLocale::setDefault(QLocale(QLocale::Chinese, QLocale::China));
```

#### 2. 动态语言切换
```cpp
class LanguageManager : public QObject {
    Q_OBJECT
    
public:
    void switchLanguage(const QString& languageCode) {
        // 移除当前翻译器
        if (m_currentTranslator) {
            QApplication::removeTranslator(m_currentTranslator);
            delete m_currentTranslator;
        }
        
        // 加载新的翻译文件
        m_currentTranslator = new QTranslator(this);
        if (m_currentTranslator->load(QString("app_%1.qm").arg(languageCode), ":/translations")) {
            QApplication::installTranslator(m_currentTranslator);
            emit languageChanged();
        }
    }
    
signals:
    void languageChanged();
    
private:
    QTranslator* m_currentTranslator = nullptr;
};
```

## 3D 应用中的国际化挑战

### 1. 3D 文本渲染
```cpp
// 3D 场景中的文本实体
Qt3DExtras::QText3DEntity *text3D = new Qt3DExtras::QText3DEntity();
text3D->setText(tr("3D Text in Scene"));
text3D->setFont(QFont("Arial", 24));

// 支持 Unicode 字符
text3D->setText(tr("你好世界")); // 中文
text3D->setText(tr("こんにちは")); // 日文
text3D->setText(tr("مرحبا")); // 阿拉伯文
```

### 2. 字体支持
```cpp
// 加载支持多语言的字体
QFontDatabase fontDb;
fontDb.addApplicationFont(":/fonts/NotoSansCJK-Regular.ttc");

// 为不同语言设置合适的字体
QFont getLocalizedFont(const QLocale& locale) {
    switch (locale.language()) {
        case QLocale::Chinese:
            return QFont("Noto Sans CJK SC", 12);
        case QLocale::Japanese:
            return QFont("Noto Sans CJK JP", 12);
        case QLocale::Korean:
            return QFont("Noto Sans CJK KR", 12);
        case QLocale::Arabic:
            return QFont("Noto Sans Arabic", 12);
        default:
            return QFont("Noto Sans", 12);
    }
}
```

### 3. 文本方向支持
```cpp
// 支持从右到左的文本（如阿拉伯语、希伯来语）
void setupTextDirection(Qt3DExtras::QText3DEntity* textEntity, const QLocale& locale) {
    if (locale.textDirection() == Qt::RightToLeft) {
        // 调整文本对齐和布局
        textEntity->setProperty("textDirection", Qt::RightToLeft);
    }
}
```

## 文本国际化

### 1. 字符串提取
```cpp
// 使用 tr() 标记所有用户可见的字符串
class Scene3DController : public QObject {
    Q_OBJECT
    
public:
    void showLoadingMessage() {
        emit statusChanged(tr("Loading 3D model..."));
    }
    
    void showError(const QString& error) {
        emit statusChanged(tr("Error: %1").arg(error));
    }
    
    void showModelInfo(int vertices, int faces) {
        emit statusChanged(tr("Model loaded: %n vertices, %n faces", "", vertices));
    }
    
signals:
    void statusChanged(const QString& message);
};
```

### 2. 复数形式处理
```cpp
// 处理复数形式
QString getObjectCountText(int count) {
    return tr("%n object(s) selected", "", count);
}

// 在翻译文件中：
// <message numerus="yes">
//     <source>%n object(s) selected</source>
//     <translation>
//         <numerusform>选中了 %n 个对象</numerusform>
//     </translation>
// </message>
```

### 3. 上下文相关翻译
```cpp
// 使用上下文区分相同的英文单词
class MaterialEditor : public QObject {
    Q_OBJECT
    
public:
    QString getMaterialProperty() {
        return tr("Color", "material property"); // 材质属性中的颜色
    }
};

class LightEditor : public QObject {
    Q_OBJECT
    
public:
    QString getLightProperty() {
        return tr("Color", "light property"); // 光源属性中的颜色
    }
};
```

## 资源本地化

### 1. 纹理和图像
```cpp
// 根据语言环境加载不同的纹理
QString getLocalizedTexture(const QString& baseName, const QLocale& locale) {
    QString localizedPath = QString(":/textures/%1_%2.png")
                           .arg(baseName)
                           .arg(locale.name());
    
    if (QFile::exists(localizedPath)) {
        return localizedPath;
    }
    
    // 回退到默认纹理
    return QString(":/textures/%1.png").arg(baseName);
}

// 使用本地化纹理
Qt3DRender::QTexture2D* texture = new Qt3DRender::QTexture2D();
texture->setSource(QUrl(getLocalizedTexture("ui_button", QLocale::system())));
```

### 2. 3D 模型本地化
```cpp
// 加载本地化的 3D 模型（如包含文字的模型）
class LocalizedModelLoader {
public:
    Qt3DRender::QMesh* loadLocalizedModel(const QString& modelName, const QLocale& locale) {
        QString localizedPath = QString(":/models/%1_%2.obj")
                               .arg(modelName)
                               .arg(locale.name());
        
        if (QFile::exists(localizedPath)) {
            Qt3DRender::QMesh* mesh = new Qt3DRender::QMesh();
            mesh->setSource(QUrl(localizedPath));
            return mesh;
        }
        
        // 回退到默认模型
        Qt3DRender::QMesh* mesh = new Qt3DRender::QMesh();
        mesh->setSource(QUrl(QString(":/models/%1.obj").arg(modelName)));
        return mesh;
    }
};
```

### 3. 音频本地化
```cpp
// 本地化音频资源
class AudioManager {
public:
    void playLocalizedSound(const QString& soundName) {
        QString localizedPath = QString(":/audio/%1_%2.wav")
                               .arg(soundName)
                               .arg(QLocale::system().name());
        
        if (QFile::exists(localizedPath)) {
            // 播放本地化音频
            playSound(localizedPath);
        } else {
            // 播放默认音频
            playSound(QString(":/audio/%1.wav").arg(soundName));
        }
    }
    
private:
    void playSound(const QString& path) {
        // 音频播放实现
    }
};
```

## 用户界面适配

### 1. 布局适配
```qml
// QML 中的自适应布局
Rectangle {
    width: parent.width
    height: 50
    
    Text {
        id: labelText
        text: qsTr("Camera Position:")
        anchors.left: parent.left
        anchors.verticalCenter: parent.verticalCenter
        
        // 根据文本长度调整布局
        onTextChanged: {
            parent.adjustLayout()
        }
    }
    
    TextField {
        anchors.left: labelText.right
        anchors.leftMargin: 10
        anchors.right: parent.right
        anchors.verticalCenter: parent.verticalCenter
    }
    
    function adjustLayout() {
        // 根据标签文本长度调整布局
        var textWidth = labelText.contentWidth
        if (textWidth > parent.width * 0.4) {
            // 切换到垂直布局
            labelText.anchors.left = parent.left
            labelText.anchors.top = parent.top
            // ... 调整其他控件位置
        }
    }
}
```

### 2. 文本大小适配
```cpp
// 根据语言调整文本大小
class TextSizeManager {
public:
    static int getOptimalFontSize(const QLocale& locale, int baseFontSize) {
        switch (locale.language()) {
            case QLocale::Chinese:
            case QLocale::Japanese:
            case QLocale::Korean:
                // CJK 字符通常需要稍大的字体
                return baseFontSize + 2;
            case QLocale::Arabic:
            case QLocale::Hebrew:
                // 阿拉伯语和希伯来语可能需要调整
                return baseFontSize + 1;
            default:
                return baseFontSize;
        }
    }
};
```

## 最佳实践

### 1. 开发阶段
- **早期规划**：在项目开始时就考虑国际化需求
- **字符串外部化**：所有用户可见的字符串都使用 tr() 函数
- **避免硬编码**：不要在代码中硬编码文本、图像路径等
- **Unicode 支持**：确保应用完全支持 Unicode
- **测试驱动**：为每种目标语言创建测试用例

### 2. 设计原则
```cpp
// 好的做法
QString message = tr("Welcome, %1!").arg(userName);
QString info = tr("File size: %1 MB").arg(fileSize);

// 避免的做法
QString message = "Welcome, " + userName + "!"; // 硬编码
QString info = QString::number(fileSize) + " MB"; // 无法翻译
```

### 3. 资源组织
```
resources/
├── translations/
│   ├── app_zh_CN.ts
│   ├── app_ja_JP.ts
│   ├── app_ko_KR.ts
│   └── app_ar_SA.ts
├── fonts/
│   ├── NotoSansCJK-Regular.ttc
│   └── NotoSansArabic-Regular.ttf
├── textures/
│   ├── ui_button.png
│   ├── ui_button_zh_CN.png
│   └── ui_button_ar_SA.png
└── models/
    ├── sign.obj
    ├── sign_zh_CN.obj
    └── sign_ar_SA.obj
```

## 工具与工作流

### 1. Qt Linguist 工具链
```bash
# 提取可翻译字符串
lupdate myapp.pro

# 编译翻译文件
lrelease myapp.pro

# 使用 Qt Linguist 进行翻译
linguist translations/myapp_zh_CN.ts
```

### 2. 自动化工作流
```cmake
# CMake 中的国际化支持
find_package(Qt6 REQUIRED COMPONENTS LinguistTools)

set(TS_FILES
    translations/myapp_zh_CN.ts
    translations/myapp_ja_JP.ts
    translations/myapp_ko_KR.ts
)

qt6_add_translations(myapp TS_FILES ${TS_FILES})
```

### 3. 持续集成
```yaml
# GitHub Actions 示例
- name: Update translations
  run: |
    lupdate src/ -ts translations/*.ts
    lrelease translations/*.ts
    
- name: Test translations
  run: |
    python scripts/check_translations.py
```

---

*Qt 3D 的国际化支持让您的 3D 应用能够轻松适应全球市场，提供优秀的本地化用户体验*
