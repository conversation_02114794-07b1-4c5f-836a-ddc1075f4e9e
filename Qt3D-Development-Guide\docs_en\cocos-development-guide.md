# Cocos Creator Engine Development Guide

## Table of Contents
1. [Overview](#overview)
2. [Technical Features](#technical-features)
3. [Development Environment Setup](#development-environment-setup)
4. [Core Concepts](#core-concepts)
5. [2D/3D Development](#2d3d-development)
6. [Script Programming](#script-programming)
7. [Mobile Optimization](#mobile-optimization)
8. [Best Practices](#best-practices)

## Overview

Cocos Creator is a globally leading lightweight, high-efficiency, cross-platform digital content development platform, focusing on 2D and 3D game development. As one of China's most successful game engines, Cocos has 1.7 million registered developers worldwide, covering 203 countries and regions, with a 40% market share in China's mobile game market and a 20% global mobile game market share.

### Main Advantages
- **Lightweight and Efficient**: Small engine size, high runtime efficiency, especially suitable for mobile platforms
- **Cross-platform Support**: Develop once, deploy to multiple platforms
- **Complete Toolchain**: Complete development environment from editor to publishing
- **Powerful 2D Support**: Industry-leading 2D game development capabilities
- **Enhanced 3D Capabilities**: Cocos Creator 3.x provides powerful 3D development capabilities
- **Chinese-friendly**: Comprehensive Chinese documentation and community support

## Technical Features

### Rendering System
- **Custom Rendering Pipeline**: Flexible rendering pipeline customization
- **Multi-platform Rendering**: OpenGL ES, Metal, Vulkan, WebGL support
- **2D Rendering Optimization**: Specially optimized 2D rendering system
- **3D Rendering Capabilities**: PBR materials, shadows, post-processing effects
- **UI System**: Powerful UI components and layout system

### Animation System
- **Skeletal Animation**: Spine, DragonBones integration
- **Tween Animation**: Rich tween function library
- **Timeline Animation**: Visual animation editor
- **Particle System**: High-performance 2D/3D particle effects
- **Physics Animation**: Animation integrated with physics engine

### Script System
- **TypeScript**: Primary development language, type-safe
- **JavaScript**: Traditional script support
- **Component-based Architecture**: Component-based development model
- **Hot Update**: Support for code and resource hot updates
- **Native Plugins**: Support for native code extensions

## Development Environment Setup

### System Requirements
- **Operating System**: Windows 7+, macOS 10.14+, Ubuntu 18.04+
- **Memory**: Minimum 4GB RAM, recommended 8GB+
- **Storage**: At least 2GB available space
- **Graphics**: Support for OpenGL 3.3+ or DirectX 11+

### Installation Steps

#### 1. Download Cocos Creator
```bash
# Visit official website to download
https://www.cocos.com/creator-download

# Choose appropriate version
# - Cocos Creator 3.x: Latest version, supports 3D
# - Cocos Creator 2.x: Stable version, mainly for 2D
```

#### 2. Install Development Environment
```bash
# Install Node.js (recommended LTS version)
https://nodejs.org/

# Install TypeScript (optional)
npm install -g typescript

# Install native development environment
# Android: Android Studio + SDK
# iOS: Xcode (macOS only)
```

#### 3. Create First Project
```typescript
// 1. Open Cocos Creator
// 2. Click "New Project"
// 3. Select template (Hello World, 2D Game, 3D Game, etc.)
// 4. Set project name and path
// 5. Click "Create and Open"
```

## Core Concepts

### Node and Component System
```typescript
import { _decorator, Component, Node, Sprite, Label } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GameController')
export class GameController extends Component {
    @property(Node)
    playerNode: Node = null!;
    
    @property(Label)
    scoreLabel: Label = null!;
    
    private score: number = 0;
    
    start() {
        // Initialize game
        this.updateScore();
    }
    
    update(deltaTime: number) {
        // Update every frame
        if (this.playerNode) {
            // Update player position
            this.updatePlayer(deltaTime);
        }
    }
    
    private updatePlayer(dt: number) {
        // Player logic update
        const pos = this.playerNode.position;
        // Example: simple movement
        this.playerNode.setPosition(pos.x + 100 * dt, pos.y);
    }
    
    public addScore(points: number) {
        this.score += points;
        this.updateScore();
    }
    
    private updateScore() {
        if (this.scoreLabel) {
            this.scoreLabel.string = `Score: ${this.score}`;
        }
    }
}
```

### Scene Management
```typescript
import { director, Scene } from 'cc';

export class SceneManager {
    // Load scene
    static loadScene(sceneName: string, onLaunched?: () => void) {
        director.loadScene(sceneName, onLaunched);
    }
    
    // Preload scene
    static preloadScene(sceneName: string, onProgress?: (progress: number) => void) {
        director.preloadScene(sceneName, (completedCount, totalCount) => {
            const progress = completedCount / totalCount;
            onProgress?.(progress);
        }, (error) => {
            if (error) {
                console.error('Scene preload failed:', error);
            } else {
                console.log('Scene preload completed');
            }
        });
    }
    
    // Get current scene
    static getCurrentScene(): Scene | null {
        return director.getScene();
    }
}
```

### Resource Management
```typescript
import { resources, Texture2D, SpriteFrame, AudioClip, Prefab, instantiate } from 'cc';

export class ResourceManager {
    // Load texture
    static loadTexture(path: string): Promise<Texture2D> {
        return new Promise((resolve, reject) => {
            resources.load(path, Texture2D, (err, texture) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(texture);
                }
            });
        });
    }
    
    // Load sprite frame
    static loadSpriteFrame(path: string): Promise<SpriteFrame> {
        return new Promise((resolve, reject) => {
            resources.load(path, SpriteFrame, (err, spriteFrame) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(spriteFrame);
                }
            });
        });
    }
    
    // Load audio
    static loadAudio(path: string): Promise<AudioClip> {
        return new Promise((resolve, reject) => {
            resources.load(path, AudioClip, (err, audio) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(audio);
                }
            });
        });
    }
    
    // Load prefab and instantiate
    static async loadAndInstantiatePrefab(path: string): Promise<Node> {
        return new Promise((resolve, reject) => {
            resources.load(path, Prefab, (err, prefab) => {
                if (err) {
                    reject(err);
                } else {
                    const instance = instantiate(prefab);
                    resolve(instance);
                }
            });
        });
    }
}
```

## 2D/3D Development

### 2D Game Development
```typescript
import { _decorator, Component, Node, Vec3, input, Input, EventKeyboard, KeyCode } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Player2D')
export class Player2D extends Component {
    @property
    moveSpeed: number = 200;
    
    @property
    jumpForce: number = 500;
    
    private velocity: Vec3 = new Vec3();
    private isGrounded: boolean = true;
    
    start() {
        // Register input events
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        input.on(Input.EventType.KEY_UP, this.onKeyUp, this);
    }
    
    onDestroy() {
        // Clean up event listeners
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        input.off(Input.EventType.KEY_UP, this.onKeyUp, this);
    }
    
    update(deltaTime: number) {
        // Apply gravity
        if (!this.isGrounded) {
            this.velocity.y -= 980 * deltaTime; // Gravity acceleration
        }
        
        // Update position
        const pos = this.node.position;
        pos.x += this.velocity.x * deltaTime;
        pos.y += this.velocity.y * deltaTime;
        
        // Ground detection (simplified)
        if (pos.y <= 0) {
            pos.y = 0;
            this.velocity.y = 0;
            this.isGrounded = true;
        }
        
        this.node.setPosition(pos);
    }
    
    private onKeyDown(event: EventKeyboard) {
        switch (event.keyCode) {
            case KeyCode.ARROW_LEFT:
            case KeyCode.KEY_A:
                this.velocity.x = -this.moveSpeed;
                break;
            case KeyCode.ARROW_RIGHT:
            case KeyCode.KEY_D:
                this.velocity.x = this.moveSpeed;
                break;
            case KeyCode.SPACE:
                if (this.isGrounded) {
                    this.velocity.y = this.jumpForce;
                    this.isGrounded = false;
                }
                break;
        }
    }
    
    private onKeyUp(event: EventKeyboard) {
        switch (event.keyCode) {
            case KeyCode.ARROW_LEFT:
            case KeyCode.KEY_A:
            case KeyCode.ARROW_RIGHT:
            case KeyCode.KEY_D:
                this.velocity.x = 0;
                break;
        }
    }
}
```

### 3D Game Development
```typescript
import { _decorator, Component, Node, Vec3, Camera, geometry, PhysicsSystem } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Player3D')
export class Player3D extends Component {
    @property(Camera)
    camera: Camera = null!;
    
    @property
    moveSpeed: number = 5;
    
    @property
    rotateSpeed: number = 90;
    
    private inputVector: Vec3 = new Vec3();
    
    start() {
        // Initialize 3D controller
        this.setupInputHandlers();
    }
    
    update(deltaTime: number) {
        this.handleMovement(deltaTime);
        this.handleRotation(deltaTime);
    }
    
    private setupInputHandlers() {
        input.on(Input.EventType.KEY_DOWN, (event: EventKeyboard) => {
            switch (event.keyCode) {
                case KeyCode.KEY_W:
                    this.inputVector.z = 1;
                    break;
                case KeyCode.KEY_S:
                    this.inputVector.z = -1;
                    break;
                case KeyCode.KEY_A:
                    this.inputVector.x = -1;
                    break;
                case KeyCode.KEY_D:
                    this.inputVector.x = 1;
                    break;
            }
        }, this);
        
        input.on(Input.EventType.KEY_UP, (event: EventKeyboard) => {
            switch (event.keyCode) {
                case KeyCode.KEY_W:
                case KeyCode.KEY_S:
                    this.inputVector.z = 0;
                    break;
                case KeyCode.KEY_A:
                case KeyCode.KEY_D:
                    this.inputVector.x = 0;
                    break;
            }
        }, this);
    }
    
    private handleMovement(dt: number) {
        if (this.inputVector.lengthSqr() > 0) {
            // Normalize input vector
            this.inputVector.normalize();
            
            // Calculate movement vector
            const forward = new Vec3();
            this.node.getForward(forward);
            const right = new Vec3();
            this.node.getRight(right);
            
            const moveVector = new Vec3();
            Vec3.scaleAndAdd(moveVector, moveVector, forward, this.inputVector.z);
            Vec3.scaleAndAdd(moveVector, moveVector, right, this.inputVector.x);
            
            // Apply movement
            Vec3.scaleAndAdd(moveVector, this.node.position, moveVector, this.moveSpeed * dt);
            this.node.setPosition(moveVector);
        }
    }
    
    private handleRotation(dt: number) {
        // Mouse control rotation (simplified implementation)
        // In actual projects, mouse input events need to be handled
    }
}
```

## Script Programming

### Component Lifecycle
```typescript
import { _decorator, Component } from 'cc';
const { ccclass } = _decorator;

@ccclass('LifecycleExample')
export class LifecycleExample extends Component {
    // Called when component is created
    onLoad() {
        console.log('onLoad: Component initialization');
        // Initialize component data
    }
    
    // Called when component is first activated
    start() {
        console.log('start: Component starts running');
        // Start game logic
    }
    
    // Called every frame
    update(deltaTime: number) {
        // Game logic update
        // deltaTime: Time interval since last frame
    }
    
    // Called at fixed time intervals (for physics calculations)
    lateUpdate(deltaTime: number) {
        // Called after all updates
        // Usually used for camera following and other logic
    }
    
    // Called when component is activated
    onEnable() {
        console.log('onEnable: Component activated');
    }
    
    // Called when component is disabled
    onDisable() {
        console.log('onDisable: Component disabled');
    }
    
    // Called when component is destroyed
    onDestroy() {
        console.log('onDestroy: Component destroyed');
        // Clean up resources, remove event listeners
    }
}
```

### Event System
```typescript
import { _decorator, Component, EventTarget } from 'cc';
const { ccclass } = _decorator;

// Custom event manager
export class GameEventManager {
    private static instance: GameEventManager;
    private eventTarget: EventTarget;
    
    private constructor() {
        this.eventTarget = new EventTarget();
    }
    
    static getInstance(): GameEventManager {
        if (!GameEventManager.instance) {
            GameEventManager.instance = new GameEventManager();
        }
        return GameEventManager.instance;
    }
    
    // Emit event
    emit(eventName: string, ...args: any[]) {
        this.eventTarget.emit(eventName, ...args);
    }
    
    // Listen to event
    on(eventName: string, callback: Function, target?: any) {
        this.eventTarget.on(eventName, callback, target);
    }
    
    // Remove event listener
    off(eventName: string, callback?: Function, target?: any) {
        this.eventTarget.off(eventName, callback, target);
    }
    
    // Listen to event once
    once(eventName: string, callback: Function, target?: any) {
        this.eventTarget.once(eventName, callback, target);
    }
}

// Usage example
@ccclass('EventExample')
export class EventExample extends Component {
    start() {
        const eventManager = GameEventManager.getInstance();
        
        // Listen to events
        eventManager.on('player-score', this.onPlayerScore, this);
        eventManager.on('game-over', this.onGameOver, this);
    }
    
    onDestroy() {
        const eventManager = GameEventManager.getInstance();
        
        // Remove event listeners
        eventManager.off('player-score', this.onPlayerScore, this);
        eventManager.off('game-over', this.onGameOver, this);
    }
    
    private onPlayerScore(score: number) {
        console.log('Player score:', score);
    }
    
    private onGameOver() {
        console.log('Game over');
    }
    
    // Trigger event
    public triggerScore() {
        const eventManager = GameEventManager.getInstance();
        eventManager.emit('player-score', 100);
    }
}
```

## Mobile Optimization

### Performance Optimization
```typescript
// Object pool management
export class ObjectPool<T> {
    private pool: T[] = [];
    private createFunc: () => T;
    private resetFunc?: (obj: T) => void;
    
    constructor(createFunc: () => T, resetFunc?: (obj: T) => void, initialSize: number = 10) {
        this.createFunc = createFunc;
        this.resetFunc = resetFunc;
        
        // Pre-create objects
        for (let i = 0; i < initialSize; i++) {
            this.pool.push(this.createFunc());
        }
    }
    
    get(): T {
        if (this.pool.length > 0) {
            return this.pool.pop()!;
        } else {
            return this.createFunc();
        }
    }
    
    put(obj: T) {
        if (this.resetFunc) {
            this.resetFunc(obj);
        }
        this.pool.push(obj);
    }
    
    clear() {
        this.pool.length = 0;
    }
}

// Usage example
@ccclass('BulletManager')
export class BulletManager extends Component {
    private bulletPool: ObjectPool<Node>;
    
    start() {
        // Create bullet object pool
        this.bulletPool = new ObjectPool<Node>(
            () => {
                // Create bullet node
                const bullet = new Node('Bullet');
                // Add necessary components
                return bullet;
            },
            (bullet) => {
                // Reset bullet state
                bullet.active = false;
                bullet.setPosition(0, 0, 0);
            },
            20 // Initial pool size
        );
    }
    
    createBullet(position: Vec3): Node {
        const bullet = this.bulletPool.get();
        bullet.setPosition(position);
        bullet.active = true;
        return bullet;
    }
    
    destroyBullet(bullet: Node) {
        this.bulletPool.put(bullet);
    }
}
```

## Best Practices

### 1. Project Structure
```
assets/
├── scenes/              # Scene files
├── scripts/             # Script files
│   ├── components/      # Component scripts
│   ├── managers/        # Manager scripts
│   ├── utils/          # Utility scripts
│   └── data/           # Data scripts
├── resources/          # Dynamically loaded resources
├── textures/           # Texture resources
├── audio/              # Audio resources
├── prefabs/            # Prefabs
└── animations/         # Animation resources
```

### 2. Code Standards
```typescript
// Use TypeScript strict mode
// Component names use PascalCase
// Properties and methods use camelCase
// Constants use UPPER_SNAKE_CASE

@ccclass('PlayerController')
export class PlayerController extends Component {
    // Public properties use @property decorator
    @property
    public moveSpeed: number = 100;
    
    // Private properties use private
    private currentHealth: number = 100;
    
    // Constants
    private static readonly MAX_HEALTH: number = 100;
    
    // Clear method naming
    public takeDamage(damage: number): void {
        this.currentHealth = Math.max(0, this.currentHealth - damage);
        
        if (this.currentHealth <= 0) {
            this.handleDeath();
        }
    }
    
    private handleDeath(): void {
        // Handle death logic
    }
}
```

### 3. Memory Management
```typescript
// Release resources promptly
export class ResourceCleaner {
    private loadedTextures: Texture2D[] = [];
    
    async loadTexture(path: string): Promise<Texture2D> {
        const texture = await ResourceManager.loadTexture(path);
        this.loadedTextures.push(texture);
        return texture;
    }
    
    cleanup() {
        // Release all loaded textures
        this.loadedTextures.forEach(texture => {
            if (texture && texture.isValid) {
                texture.destroy();
            }
        });
        this.loadedTextures.length = 0;
    }
}
```

---

*Cocos Creator occupies an important position in mobile game development with its lightweight efficiency and powerful 2D capabilities. For more detailed information, please refer to the [Cocos Creator Official Documentation](https://docs.cocos.com/creator/manual/)* 