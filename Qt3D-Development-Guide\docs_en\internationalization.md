# Qt 3D Internationalization and Localization Guide

## Table of Contents
1. [Overview](#overview)
2. [Qt Internationalization Framework](#qt-internationalization-framework)
3. [Internationalization Challenges in 3D Applications](#internationalization-challenges-in-3d-applications)
4. [Text Internationalization](#text-internationalization)
5. [Resource Localization](#resource-localization)
6. [User Interface Adaptation](#user-interface-adaptation)
7. [Best Practices](#best-practices)
8. [Tools and Workflow](#tools-and-workflow)

## Overview

Internationalization (i18n) and localization (l10n) of Qt 3D applications is the process of adapting applications to different languages, regions, and cultures. The Qt framework provides complete internationalization support, enabling 3D applications to easily support multiple languages and regions.

### Importance of Internationalization
- **Global Market**: Expand the target user base of applications
- **User Experience**: Provide localized user experience
- **Regulatory Compliance**: Meet regulatory requirements in different regions
- **Competitive Advantage**: Gain competitive advantage in local markets
- **Cultural Adaptation**: Respect usage habits of different cultures

## Qt Internationalization Framework

### Core Components

#### 1. QTranslator
```cpp
// Create translator
QTranslator translator;
if (translator.load("myapp_zh_CN.qm", ":/translations")) {
    QApplication::installTranslator(&translator);
}
```

#### 2. tr() Function
```cpp
// Mark translatable strings in C++
QString message = tr("Welcome to 3D World");
QString info = tr("Loading model: %1").arg(modelName);
```

#### 3. Internationalization in QML
```qml
// Translation in QML
Text {
    text: qsTr("3D Scene Controls")
}

Text {
    text: qsTr("Rotation: %1°").arg(rotationAngle)
}
```

### Locale Support

#### 1. QLocale Class
```cpp
// Get system locale
QLocale systemLocale = QLocale::system();
QString language = systemLocale.languageToString(systemLocale.language());
QString country = systemLocale.countryToString(systemLocale.country());

// Set specific locale
QLocale::setDefault(QLocale(QLocale::Chinese, QLocale::China));
```

#### 2. Dynamic Language Switching
```cpp
class LanguageManager : public QObject {
    Q_OBJECT
    
public:
    void switchLanguage(const QString& languageCode) {
        // Remove current translator
        if (m_currentTranslator) {
            QApplication::removeTranslator(m_currentTranslator);
            delete m_currentTranslator;
        }
        
        // Load new translation file
        m_currentTranslator = new QTranslator(this);
        if (m_currentTranslator->load(QString("app_%1.qm").arg(languageCode), ":/translations")) {
            QApplication::installTranslator(m_currentTranslator);
            emit languageChanged();
        }
    }
    
signals:
    void languageChanged();
    
private:
    QTranslator* m_currentTranslator = nullptr;
};
```

## Internationalization Challenges in 3D Applications

### 1. 3D Text Rendering
```cpp
// Text entity in 3D scene
Qt3DExtras::QText3DEntity *text3D = new Qt3DExtras::QText3DEntity();
text3D->setText(tr("3D Text in Scene"));
text3D->setFont(QFont("Arial", 24));

// Support Unicode characters
text3D->setText(tr("Hello World")); // English
text3D->setText(tr("こんにちは")); // Japanese
text3D->setText(tr("مرحبا")); // Arabic
```

### 2. Font Support
```cpp
// Load fonts supporting multiple languages
QFontDatabase fontDb;
fontDb.addApplicationFont(":/fonts/NotoSansCJK-Regular.ttc");

// Set appropriate fonts for different languages
QFont getLocalizedFont(const QLocale& locale) {
    switch (locale.language()) {
        case QLocale::Chinese:
            return QFont("Noto Sans CJK SC", 12);
        case QLocale::Japanese:
            return QFont("Noto Sans CJK JP", 12);
        case QLocale::Korean:
            return QFont("Noto Sans CJK KR", 12);
        case QLocale::Arabic:
            return QFont("Noto Sans Arabic", 12);
        default:
            return QFont("Noto Sans", 12);
    }
}
```

### 3. Text Direction Support
```cpp
// Support right-to-left text (such as Arabic, Hebrew)
void setupTextDirection(Qt3DExtras::QText3DEntity* textEntity, const QLocale& locale) {
    if (locale.textDirection() == Qt::RightToLeft) {
        // Adjust text alignment and layout
        textEntity->setProperty("textDirection", Qt::RightToLeft);
    }
}
```

## Text Internationalization

### 1. String Extraction
```cpp
// Use tr() to mark all user-visible strings
class Scene3DController : public QObject {
    Q_OBJECT
    
public:
    void showLoadingMessage() {
        emit statusChanged(tr("Loading 3D model..."));
    }
    
    void showError(const QString& error) {
        emit statusChanged(tr("Error: %1").arg(error));
    }
    
    void showModelInfo(int vertices, int faces) {
        emit statusChanged(tr("Model loaded: %n vertices, %n faces", "", vertices));
    }
    
signals:
    void statusChanged(const QString& message);
};
```

### 2. Plural Form Handling
```cpp
// Handle plural forms
QString getObjectCountText(int count) {
    return tr("%n object(s) selected", "", count);
}

// In translation files:
// <message numerus="yes">
//     <source>%n object(s) selected</source>
//     <translation>
//         <numerusform>%n objects selected</numerusform>
//     </translation>
// </message>
```

### 3. Context-dependent Translation
```cpp
// Use context to distinguish identical English words
class MaterialEditor : public QObject {
    Q_OBJECT
    
public:
    QString getMaterialProperty() {
        return tr("Color", "material property"); // Color in material properties
    }
};

class LightEditor : public QObject {
    Q_OBJECT
    
public:
    QString getLightProperty() {
        return tr("Color", "light property"); // Color in light properties
    }
};
```

## Resource Localization

### 1. Textures and Images
```cpp
// Load different textures based on locale
QString getLocalizedTexture(const QString& baseName, const QLocale& locale) {
    QString localizedPath = QString(":/textures/%1_%2.png")
                           .arg(baseName)
                           .arg(locale.name());
    
    if (QFile::exists(localizedPath)) {
        return localizedPath;
    }
    
    // Fallback to default texture
    return QString(":/textures/%1.png").arg(baseName);
}

// Use localized texture
Qt3DRender::QTexture2D* texture = new Qt3DRender::QTexture2D();
texture->setSource(QUrl(getLocalizedTexture("ui_button", QLocale::system())));
```

### 2. 3D Model Localization
```cpp
// Load localized 3D models (such as models containing text)
class LocalizedModelLoader {
public:
    Qt3DRender::QMesh* loadLocalizedModel(const QString& modelName, const QLocale& locale) {
        QString localizedPath = QString(":/models/%1_%2.obj")
                               .arg(modelName)
                               .arg(locale.name());
        
        if (QFile::exists(localizedPath)) {
            Qt3DRender::QMesh* mesh = new Qt3DRender::QMesh();
            mesh->setSource(QUrl(localizedPath));
            return mesh;
        }
        
        // Fallback to default model
        Qt3DRender::QMesh* mesh = new Qt3DRender::QMesh();
        mesh->setSource(QUrl(QString(":/models/%1.obj").arg(modelName)));
        return mesh;
    }
};
```

### 3. Audio Localization
```cpp
// Localize audio resources
class AudioManager {
public:
    void playLocalizedSound(const QString& soundName) {
        QString localizedPath = QString(":/audio/%1_%2.wav")
                               .arg(soundName)
                               .arg(QLocale::system().name());
        
        if (QFile::exists(localizedPath)) {
            // Play localized audio
            playSound(localizedPath);
        } else {
            // Play default audio
            playSound(QString(":/audio/%1.wav").arg(soundName));
        }
    }
    
private:
    void playSound(const QString& path) {
        // Audio playback implementation
    }
};
```

## User Interface Adaptation

### 1. Layout Adaptation
```qml
// Adaptive layout in QML
Rectangle {
    width: parent.width
    height: 50
    
    Text {
        id: labelText
        text: qsTr("Camera Position:")
        anchors.left: parent.left
        anchors.verticalCenter: parent.verticalCenter
        
        // Adjust layout based on text length
        onTextChanged: {
            parent.adjustLayout()
        }
    }
    
    TextField {
        anchors.left: labelText.right
        anchors.leftMargin: 10
        anchors.right: parent.right
        anchors.verticalCenter: parent.verticalCenter
    }
    
    function adjustLayout() {
        // Adjust layout based on label text length
        var textWidth = labelText.contentWidth
        if (textWidth > parent.width * 0.4) {
            // Switch to vertical layout
            labelText.anchors.left = parent.left
            labelText.anchors.top = parent.top
            // ... adjust other control positions
        }
    }
}
```

### 2. Text Size Adaptation
```cpp
// Adjust text size based on language
class TextSizeManager {
public:
    static int getOptimalFontSize(const QLocale& locale, int baseFontSize) {
        switch (locale.language()) {
            case QLocale::Chinese:
            case QLocale::Japanese:
            case QLocale::Korean:
                // CJK characters usually need slightly larger fonts
                return baseFontSize + 2;
            case QLocale::Arabic:
            case QLocale::Hebrew:
                // Arabic and Hebrew may need adjustment
                return baseFontSize + 1;
            default:
                return baseFontSize;
        }
    }
};
```

## Best Practices

### 1. Development Phase
- **Early Planning**: Consider internationalization requirements at the start of the project
- **String Externalization**: Use tr() function for all user-visible strings
- **Avoid Hardcoding**: Don't hardcode text, image paths, etc. in code
- **Unicode Support**: Ensure application fully supports Unicode
- **Test-driven**: Create test cases for each target language

### 2. Design Principles
```cpp
// Good practices
QString message = tr("Welcome, %1!").arg(userName);
QString info = tr("File size: %1 MB").arg(fileSize);

// Avoid these practices
QString message = "Welcome, " + userName + "!"; // Hardcoded
QString info = QString::number(fileSize) + " MB"; // Cannot be translated
```

### 3. Resource Organization
```
resources/
├── translations/
│   ├── app_zh_CN.ts
│   ├── app_ja_JP.ts
│   ├── app_ko_KR.ts
│   └── app_ar_SA.ts
├── fonts/
│   ├── NotoSansCJK-Regular.ttc
│   └── NotoSansArabic-Regular.ttf
├── textures/
│   ├── ui_button.png
│   ├── ui_button_zh_CN.png
│   └── ui_button_ar_SA.png
└── models/
    ├── sign.obj
    ├── sign_zh_CN.obj
    └── sign_ar_SA.obj
```

## Tools and Workflow

### 1. Qt Linguist Toolchain
```bash
# Extract translatable strings
lupdate myapp.pro

# Compile translation files
lrelease myapp.pro

# Use Qt Linguist for translation
linguist translations/myapp_zh_CN.ts
```

### 2. Automated Workflow
```cmake
# Internationalization support in CMake
find_package(Qt6 REQUIRED COMPONENTS LinguistTools)

set(TS_FILES
    translations/myapp_zh_CN.ts
    translations/myapp_ja_JP.ts
    translations/myapp_ko_KR.ts
)

qt6_add_translations(myapp TS_FILES ${TS_FILES})
```

### 3. Continuous Integration
```yaml
# GitHub Actions example
- name: Update translations
  run: |
    lupdate src/ -ts translations/*.ts
    lrelease translations/*.ts
    
- name: Test translations
  run: |
    python scripts/check_translations.py
```

---

*Qt 3D's internationalization support allows your 3D applications to easily adapt to global markets, providing excellent localized user experience*