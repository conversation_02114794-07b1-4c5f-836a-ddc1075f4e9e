# Qt 3D 引擎开发指南 - 使用说明

## 快速开始

### 1. 查看网页版本（推荐）
最简单的方式是直接打开网页版本：

1. 导航到 `web/` 目录
2. 双击 `index.html` 文件
3. 在浏览器中查看完整的交互式指南

**推荐使用现代浏览器**：Chrome、Firefox、Safari、Edge

### 2. 使用本地服务器（最佳体验）
为了获得最佳体验，建议使用本地服务器：

#### 使用 Python（推荐）
```bash
cd "3D Engine/Qt3D-Development-Guide/web"
python -m http.server 8000
```
然后在浏览器中访问：`http://localhost:8000`

#### 使用 Node.js
```bash
cd "3D Engine/Qt3D-Development-Guide/web"
npx serve .
```

#### 使用 PHP
```bash
cd "3D Engine/Qt3D-Development-Guide/web"
php -S localhost:8000
```

### 3. 查看 Markdown 文档
如果您更喜欢阅读纯文本文档：

- **开发指南**：`docs/qt3d-development-guide.md`
- **引擎对比**：`docs/engine-comparison.md`
- **国际化指南**：`docs/internationalization.md`

## 功能导航

### 🏠 主页功能
- **导航栏**：快速跳转到不同章节
- **英雄区域**：项目概述和主要特性
- **3D 立方体**：展示 3D 效果的动画立方体
- **语言切换**：支持中英文界面切换

### 📊 引擎对比
- **对比表格**：详细的功能对比表
- **引擎卡片**：每个引擎的优势和适用场景
- **评分系统**：直观的星级评分显示

### 🌍 国际化演示
- **多语言展示**：中文、英文、日文、阿拉伯文示例
- **交互式切换**：点击不同语言查看效果
- **本地化说明**：详细的国际化实现指南

### 📚 文档链接
- **开发指南**：完整的 Qt 3D 开发教程
- **对比分析**：引擎选择决策支持
- **国际化指南**：多语言应用开发指南

## 交互功能

### 🖱️ 鼠标交互
- **悬停效果**：卡片和按钮的悬停动画
- **点击导航**：平滑滚动到目标章节
- **立方体控制**：鼠标悬停暂停/恢复动画

### ⌨️ 键盘导航
- **Tab 键**：在可交互元素间切换
- **Enter/Space**：激活按钮和链接
- **ESC 键**：关闭移动端菜单

### 📱 移动端支持
- **响应式设计**：自动适配手机和平板
- **触摸友好**：大按钮和易点击的链接
- **移动菜单**：折叠式导航菜单

## 自定义配置

### 🎨 主题定制
编辑 `web/assets/css/style.css` 文件中的 CSS 变量：

```css
:root {
    --primary-color: #41cd52;    /* 主色调 */
    --secondary-color: #2c5aa0;  /* 次要色调 */
    --accent-color: #f39c12;     /* 强调色 */
    --text-color: #333;          /* 文字颜色 */
    --bg-color: #ffffff;         /* 背景色 */
}
```

### 🌐 语言扩展
在 `web/assets/js/main.js` 中添加新语言：

```javascript
const translations = {
    'zh': { /* 中文翻译 */ },
    'en': { /* 英文翻译 */ },
    'fr': { /* 法文翻译 - 新增 */ }
};
```

### 📝 内容更新
- **修改文档**：编辑 `docs/` 目录下的 Markdown 文件
- **更新网页**：修改 `web/index.html` 中的内容
- **添加资源**：在 `web/assets/images/` 中添加图片

## 故障排除

### 🚫 常见问题

#### 网页显示异常
**问题**：样式丢失或布局错乱
**解决**：
1. 确保使用现代浏览器
2. 检查是否通过本地服务器访问
3. 清除浏览器缓存

#### 动画不流畅
**问题**：3D 立方体动画卡顿
**解决**：
1. 关闭其他占用 GPU 的应用
2. 更新显卡驱动
3. 在 CSS 中调整动画时长

#### 移动端显示问题
**问题**：在手机上显示不正常
**解决**：
1. 确保使用支持的浏览器
2. 检查网络连接
3. 尝试横屏查看

### 🔧 技术支持

#### 浏览器兼容性
- ✅ **推荐**：Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- ⚠️ **有限支持**：较旧版本的现代浏览器
- ❌ **不支持**：Internet Explorer

#### 性能要求
- **最低配置**：双核 CPU，4GB RAM，集成显卡
- **推荐配置**：四核 CPU，8GB RAM，独立显卡
- **网络要求**：无（本地运行）

## 高级用法

### 📊 数据分析
使用浏览器开发者工具查看：
- **性能指标**：页面加载时间、渲染性能
- **网络活动**：资源加载情况
- **控制台输出**：调试信息和错误日志

### 🔍 SEO 优化
如果需要部署到网站：
1. 添加适当的 meta 标签
2. 优化图片大小和格式
3. 添加结构化数据标记
4. 生成 sitemap.xml

### 📈 扩展功能
可以添加的功能：
- **搜索功能**：文档内容搜索
- **评论系统**：用户反馈和讨论
- **下载功能**：PDF 版本下载
- **分享功能**：社交媒体分享

## 部署指南

### 🌐 静态网站部署
可以部署到以下平台：
- **GitHub Pages**：免费静态网站托管
- **Netlify**：现代化的静态网站平台
- **Vercel**：快速的静态网站部署
- **传统服务器**：Apache、Nginx 等

### 📦 打包分发
创建分发包：
1. 压缩整个 `Qt3D-Development-Guide` 目录
2. 包含所有文档和网页文件
3. 添加使用说明和许可证信息

## 反馈与支持

### 💬 获取帮助
- **文档问题**：检查 README.md 和本使用说明
- **技术问题**：查看浏览器控制台错误信息
- **功能建议**：记录在项目反馈中

### 🤝 贡献代码
欢迎贡献：
1. **文档改进**：修正错误、添加示例
2. **功能增强**：新功能、用户体验改进
3. **设计优化**：视觉设计、可访问性提升
4. **性能优化**：加载速度、动画流畅度

---

**享受使用 Qt 3D 引擎开发指南！** 🚀

如有任何问题或建议，请随时反馈。
