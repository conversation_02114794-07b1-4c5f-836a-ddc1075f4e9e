import os
import shutil
from pathlib import Path

def create_language_versions(src_dir, dest_dir_zh, dest_dir_en):
    """
    将源目录中的HTML文件分别复制到中文和英文目录，并进行必要的路径调整
    """
    # 确保目标目录存在
    Path(dest_dir_zh).mkdir(parents=True, exist_ok=True)
    Path(dest_dir_en).mkdir(parents=True, exist_ok=True)

    # 处理所有HTML文件
    for html_file in Path(src_dir).glob('*.html'):
        if html_file.name == '_template.html':
            continue

        # 读取原始文件内容
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 创建中文版本
        zh_file = Path(dest_dir_zh) / html_file.name
        with open(zh_file, 'w', encoding='utf-8') as f:
            # 调整资源路径
            modified_content = content.replace('../assets', '../../assets')
            f.write(modified_content)

        # 创建英文版本
        en_file = Path(dest_dir_en) / html_file.name
        with open(en_file, 'w', encoding='utf-8') as f:
            # 调整资源路径
            modified_content = content.replace('../assets', '../../assets')
            # 设置语言为英文
            modified_content = modified_content.replace('lang="zh-CN"', 'lang="en"')
            f.write(modified_content)

def main():
    # 项目根目录
    project_root = Path(__file__).parent
    
    # 源文件和目标目录
    src_dir = project_root / 'web' / 'docs'
    dest_dir_zh = project_root / 'web' / 'docs' / 'zh'
    dest_dir_en = project_root / 'web' / 'docs' / 'en'

    # 处理文件
    create_language_versions(src_dir, dest_dir_zh, dest_dir_en)

    print("文档处理完成！")
    print("下一步：")
    print("1. 检查 zh 目录中的中文文档")
    print("2. 翻译 en 目录中的文档为英文")
    print("3. 更新文档中的导航链接")

if __name__ == '__main__':
    main() 