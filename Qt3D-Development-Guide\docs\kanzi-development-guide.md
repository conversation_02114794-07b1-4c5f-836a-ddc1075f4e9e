# Kanzi 引擎开发指南

## 目录
1. [概述](#概述)
2. [技术特性](#技术特性)
3. [开发环境搭建](#开发环境搭建)
4. [核心概念](#核心概念)
5. [HMI 开发](#hmi-开发)
6. [2D/3D 界面设计](#2d3d-界面设计)
7. [数据绑定与交互](#数据绑定与交互)
8. [最佳实践](#最佳实践)

## 概述

Kanzi 是 Rightware 开发的专业 HMI（人机界面）开发平台，专门针对嵌入式系统和汽车行业设计。Kanzi 以其极低的资源占用、实时性能和符合汽车安全标准的特性，成为汽车仪表盘、工业控制和高端消费电子产品的首选 HMI 解决方案。

### 主要优势
- **嵌入式优化**：专为资源受限的嵌入式系统设计
- **极低资源占用**：最小化内存和 CPU 使用
- **实时性能**：保证实时响应和流畅动画
- **汽车标准**：符合 ISO 26262 等汽车安全标准
- **专业工具链**：完整的 HMI 设计和开发工具
- **跨平台支持**：支持多种嵌入式平台和操作系统

## 技术特性

### 渲染引擎
- **GPU 加速渲染**：充分利用硬件 GPU 加速
- **矢量图形**：高质量的 2D 矢量图形渲染
- **3D 渲染**：轻量级 3D 渲染能力
- **多层合成**：高效的图层合成系统
- **抗锯齿**：高质量的抗锯齿技术
- **HDR 支持**：高动态范围显示支持

### HMI 框架
- **组件化架构**：模块化的 UI 组件系统
- **状态管理**：强大的状态机和状态管理
- **动画系统**：流畅的 2D/3D 动画引擎
- **触摸交互**：多点触控和手势识别
- **数据绑定**：实时数据绑定和更新
- **主题系统**：灵活的主题和样式管理

### 平台支持
- **汽车平台**：QNX、Linux、Android Automotive
- **工业平台**：VxWorks、Linux RT、Windows IoT
- **移动平台**：Android、iOS（有限支持）
- **硬件平台**：ARM、x86、PowerPC 等

## 开发环境搭建

### 系统要求
- **开发主机**：Windows 10+, Ubuntu 18.04+, macOS 10.14+
- **内存**：最少 8GB RAM，推荐 16GB+
- **存储**：至少 10GB 可用空间
- **显卡**：支持 OpenGL ES 2.0+ 或 DirectX 11+

### 安装步骤

#### 1. 获取 Kanzi 许可证
```bash
# 联系 Rightware 获取评估许可证
# https://www.rightware.com/kanzi/

# 商业许可证需要与销售团队联系
# 教育许可证可通过学术合作伙伴获取
```

#### 2. 安装 Kanzi Studio
```bash
# 下载 Kanzi Studio 安装包
# 运行安装程序
# 输入许可证密钥
# 选择安装组件：
# - Kanzi Studio (设计工具)
# - Kanzi Engine (运行时)
# - Platform SDKs (目标平台 SDK)
```

#### 3. 配置开发环境
```bash
# 设置环境变量
export KANZI_HOME=/opt/kanzi
export PATH=$KANZI_HOME/bin:$PATH

# 配置目标平台工具链
# 例如：ARM 交叉编译工具链
export CROSS_COMPILE=arm-linux-gnueabihf-
export CC=${CROSS_COMPILE}gcc
export CXX=${CROSS_COMPILE}g++
```

## 核心概念

### Node 层次结构
```cpp
// Kanzi 中的基本节点类型
#include <kanzi/kanzi.hpp>

using namespace kanzi;

// 创建场景图
class MyApplication : public Application
{
public:
    void onConfigure(ApplicationProperties& configuration) override
    {
        configuration.binaryName = "MyKanziApp";
        configuration.defaultWindowProperties.width = 1280;
        configuration.defaultWindowProperties.height = 720;
    }
    
    void onProjectLoaded() override
    {
        // 获取根节点
        Node2DSharedPtr rootNode = getScreen();
        
        // 创建子节点
        Node2DSharedPtr panel = Node2D::create(getDomain(), "MainPanel");
        rootNode->addChild(panel);
        
        // 设置节点属性
        panel->setWidth(400);
        panel->setHeight(300);
        panel->setTranslation(Vector2(100, 100));
    }
};

// 应用程序入口点
Application* createApplication()
{
    return new MyApplication;
}
```

### 资源管理
```cpp
// 资源加载和管理
class ResourceManager
{
public:
    void loadResources()
    {
        // 加载纹理
        TextureSharedPtr texture = Texture::createFromFile(getDomain(), "assets/button.png");
        
        // 加载字体
        FontSharedPtr font = Font::createFromFile(getDomain(), "assets/arial.ttf");
        
        // 加载 3D 模型
        MeshSharedPtr mesh = Mesh::createFromFile(getDomain(), "assets/car.fbx");
        
        // 缓存资源
        m_textureCache["button"] = texture;
        m_fontCache["default"] = font;
        m_meshCache["car"] = mesh;
    }
    
private:
    map<string, TextureSharedPtr> m_textureCache;
    map<string, FontSharedPtr> m_fontCache;
    map<string, MeshSharedPtr> m_meshCache;
};
```

### 组件系统
```cpp
// 自定义组件
class SpeedometerComponent : public NodeComponent
{
    KZ_COMPONENT(SpeedometerComponent)
    
public:
    static PropertyType<float> SpeedProperty;
    static PropertyType<float> MaxSpeedProperty;
    
    explicit SpeedometerComponent(Domain* domain, string_view name)
        : NodeComponent(domain, name)
    {
    }
    
    void initialize() override
    {
        NodeComponent::initialize();
        
        // 监听属性变化
        addPropertyNotificationHandler(SpeedProperty, 
            bind(&SpeedometerComponent::onSpeedChanged, this, placeholders::_1));
    }
    
private:
    void onSpeedChanged(PropertyObject& object)
    {
        float speed = getProperty(SpeedProperty);
        float maxSpeed = getProperty(MaxSpeedProperty);
        float angle = (speed / maxSpeed) * 270.0f - 135.0f; // -135° to +135°
        
        // 更新指针旋转
        Node2DSharedPtr needle = findChild<Node2D>("Needle");
        if (needle)
        {
            needle->setRotation(angle);
        }
    }
};

// 注册属性
PropertyType<float> SpeedometerComponent::SpeedProperty(
    kzMakeFixedString("SpeedometerComponent.Speed"), 0.0f);
PropertyType<float> SpeedometerComponent::MaxSpeedProperty(
    kzMakeFixedString("SpeedometerComponent.MaxSpeed"), 200.0f);
```

## HMI 开发

### 仪表盘界面
```cpp
// 汽车仪表盘实现
class CarDashboard : public Node2D
{
public:
    static Node2DSharedPtr create(Domain* domain, string_view name)
    {
        return Node2DSharedPtr(new CarDashboard(domain, name));
    }
    
protected:
    explicit CarDashboard(Domain* domain, string_view name)
        : Node2D(domain, name)
    {
        initialize();
    }
    
    void initialize()
    {
        // 创建速度表
        m_speedometer = createSpeedometer();
        addChild(m_speedometer);
        
        // 创建转速表
        m_tachometer = createTachometer();
        addChild(m_tachometer);
        
        // 创建燃油表
        m_fuelGauge = createFuelGauge();
        addChild(m_fuelGauge);
        
        // 创建警告灯
        m_warningLights = createWarningLights();
        addChild(m_warningLights);
        
        // 创建信息显示区
        m_infoDisplay = createInfoDisplay();
        addChild(m_infoDisplay);
    }
    
private:
    Node2DSharedPtr createSpeedometer()
    {
        Node2DSharedPtr speedometer = Node2D::create(getDomain(), "Speedometer");
        
        // 背景圆盘
        Node2DSharedPtr background = Image2D::create(getDomain(), "SpeedBackground");
        background->setTexture(getResourceManager()->acquireResource<Texture>("speedometer_bg"));
        speedometer->addChild(background);
        
        // 刻度
        for (int i = 0; i <= 200; i += 20)
        {
            Node2DSharedPtr tick = createSpeedTick(i);
            speedometer->addChild(tick);
        }
        
        // 指针
        Node2DSharedPtr needle = Image2D::create(getDomain(), "SpeedNeedle");
        needle->setTexture(getResourceManager()->acquireResource<Texture>("needle"));
        needle->setOrigin(Vector2(0.5f, 0.9f)); // 设置旋转中心
        speedometer->addChild(needle);
        
        // 数字显示
        Text2DSharedPtr speedText = Text2D::create(getDomain(), "SpeedText");
        speedText->setFont(getResourceManager()->acquireResource<Font>("digital_font"));
        speedText->setText("0");
        speedometer->addChild(speedText);
        
        return speedometer;
    }
    
    Node2DSharedPtr createSpeedTick(int speed)
    {
        Node2DSharedPtr tick = Node2D::create(getDomain(), "Tick_" + to_string(speed));
        
        // 计算刻度位置
        float angle = (speed / 200.0f) * 270.0f - 135.0f;
        float radius = 150.0f;
        float x = cos(degreesToRadians(angle)) * radius;
        float y = sin(degreesToRadians(angle)) * radius;
        
        tick->setTranslation(Vector2(x, y));
        tick->setRotation(angle + 90.0f);
        
        // 主刻度和次刻度
        if (speed % 40 == 0)
        {
            // 主刻度 - 较长较粗
            Rectangle2DSharedPtr line = Rectangle2D::create(getDomain(), "MajorTick");
            line->setWidth(3);
            line->setHeight(20);
            line->setBrush(ColorBrush::create(getDomain(), Color::createRGB(1.0f, 1.0f, 1.0f)));
            tick->addChild(line);
            
            // 数字标签
            Text2DSharedPtr label = Text2D::create(getDomain(), "TickLabel");
            label->setText(to_string(speed));
            label->setTranslation(Vector2(0, -30));
            tick->addChild(label);
        }
        else
        {
            // 次刻度 - 较短较细
            Rectangle2DSharedPtr line = Rectangle2D::create(getDomain(), "MinorTick");
            line->setWidth(1);
            line->setHeight(10);
            line->setBrush(ColorBrush::create(getDomain(), Color::createRGB(0.8f, 0.8f, 0.8f)));
            tick->addChild(line);
        }
        
        return tick;
    }
    
    void updateSpeed(float speed)
    {
        // 更新速度表指针
        Node2DSharedPtr needle = m_speedometer->findChild<Node2D>("SpeedNeedle");
        if (needle)
        {
            float angle = (speed / 200.0f) * 270.0f - 135.0f;
            
            // 平滑动画
            PropertyAnimationSharedPtr animation = PropertyAnimation::create(getDomain(), "SpeedAnimation");
            animation->setTargetObject(needle);
            animation->setTargetProperty(Node2D::RotationProperty);
            animation->setDuration(chrono::milliseconds(500));
            animation->setStartValue(needle->getRotation());
            animation->setTargetValue(angle);
            animation->setEasingFunction(EasingFunction::createEaseInOut());
            animation->start();
        }
        
        // 更新数字显示
        Text2DSharedPtr speedText = m_speedometer->findChild<Text2D>("SpeedText");
        if (speedText)
        {
            speedText->setText(to_string(static_cast<int>(speed)));
        }
    }
    
private:
    Node2DSharedPtr m_speedometer;
    Node2DSharedPtr m_tachometer;
    Node2DSharedPtr m_fuelGauge;
    Node2DSharedPtr m_warningLights;
    Node2DSharedPtr m_infoDisplay;
};
```

## 2D/3D 界面设计

### 混合 2D/3D 界面
```cpp
// 3D 场景中的 2D UI 叠加
class Mixed2D3DInterface : public Node3D
{
public:
    static Node3DSharedPtr create(Domain* domain, string_view name)
    {
        return Node3DSharedPtr(new Mixed2D3DInterface(domain, name));
    }
    
protected:
    explicit Mixed2D3DInterface(Domain* domain, string_view name)
        : Node3D(domain, name)
    {
        initialize();
    }
    
    void initialize()
    {
        // 创建 3D 场景
        create3DScene();
        
        // 创建 2D UI 叠加层
        create2DOverlay();
        
        // 设置相机
        setupCamera();
    }
    
private:
    void create3DScene()
    {
        // 加载 3D 汽车模型
        Node3DSharedPtr carModel = Model3D::create(getDomain(), "CarModel");
        carModel->setMesh(getResourceManager()->acquireResource<Mesh>("car_model.fbx"));
        carModel->setMaterial(getResourceManager()->acquireResource<Material>("car_material"));
        addChild(carModel);
        
        // 环境光照
        LightSharedPtr ambientLight = AmbientLight::create(getDomain(), "AmbientLight");
        ambientLight->setColor(Color::createRGB(0.3f, 0.3f, 0.3f));
        addChild(ambientLight);
        
        // 方向光
        LightSharedPtr directionalLight = DirectionalLight::create(getDomain(), "DirectionalLight");
        directionalLight->setDirection(Vector3(-1, -1, -1));
        directionalLight->setColor(Color::createRGB(0.8f, 0.8f, 0.8f));
        addChild(directionalLight);
    }
    
    void create2DOverlay()
    {
        // 创建 2D 叠加层
        Node2DSharedPtr overlay = Node2D::create(getDomain(), "UIOverlay");
        
        // HUD 元素
        createSpeedDisplay(overlay);
        createNavigationInfo(overlay);
        createControlButtons(overlay);
        
        // 将 2D 叠加层添加到 3D 场景
        addChild(overlay);
    }
    
    void createSpeedDisplay(Node2DSharedPtr parent)
    {
        Node2DSharedPtr speedPanel = Rectangle2D::create(getDomain(), "SpeedPanel");
        speedPanel->setWidth(200);
        speedPanel->setHeight(100);
        speedPanel->setTranslation(Vector2(50, 50));
        speedPanel->setBrush(ColorBrush::create(getDomain(), Color::createRGBA(0, 0, 0, 0.7f)));
        
        Text2DSharedPtr speedLabel = Text2D::create(getDomain(), "SpeedLabel");
        speedLabel->setText("Speed");
        speedLabel->setTranslation(Vector2(10, 10));
        speedPanel->addChild(speedLabel);
        
        Text2DSharedPtr speedValue = Text2D::create(getDomain(), "SpeedValue");
        speedValue->setText("0 km/h");
        speedValue->setTranslation(Vector2(10, 40));
        speedValue->setFontSize(24);
        speedPanel->addChild(speedValue);
        
        parent->addChild(speedPanel);
    }
    
    void setupCamera()
    {
        CameraSharedPtr camera = Camera::create(getDomain(), "MainCamera");
        camera->setTranslation(Vector3(0, 2, 5));
        camera->setRotation(Vector3(-15, 0, 0));
        camera->setFieldOfView(45.0f);
        camera->setNearPlane(0.1f);
        camera->setFarPlane(1000.0f);
        addChild(camera);
    }
};
```

## 数据绑定与交互

### 数据绑定系统
```cpp
// 数据源定义
class VehicleDataSource : public DataObject
{
    KZ_METACLASS_BEGIN(VehicleDataSource, DataObject, "VehicleDataSource")
        KZ_METACLASS_PROPERTY_TYPE(SpeedProperty)
        KZ_METACLASS_PROPERTY_TYPE(RPMProperty)
        KZ_METACLASS_PROPERTY_TYPE(FuelLevelProperty)
    KZ_METACLASS_END()
    
public:
    static PropertyType<float> SpeedProperty;
    static PropertyType<float> RPMProperty;
    static PropertyType<float> FuelLevelProperty;
    
    explicit VehicleDataSource(Domain* domain, string_view name)
        : DataObject(domain, name)
    {
        // 模拟数据更新
        startDataSimulation();
    }
    
    void updateSpeed(float speed)
    {
        setProperty(SpeedProperty, speed);
    }
    
    void updateRPM(float rpm)
    {
        setProperty(RPMProperty, rpm);
    }
    
    void updateFuelLevel(float level)
    {
        setProperty(FuelLevelProperty, level);
    }
    
private:
    void startDataSimulation()
    {
        // 创建定时器模拟数据变化
        TimerSharedPtr timer = Timer::create(getDomain());
        timer->setInterval(chrono::milliseconds(100));
        timer->setTimeout([this]() {
            simulateDataUpdate();
        });
        timer->start();
    }
    
    void simulateDataUpdate()
    {
        static float time = 0.0f;
        time += 0.1f;
        
        // 模拟速度变化
        float speed = 60.0f + 30.0f * sin(time * 0.5f);
        updateSpeed(speed);
        
        // 模拟转速变化
        float rpm = 2000.0f + 1000.0f * sin(time * 0.7f);
        updateRPM(rpm);
        
        // 模拟燃油消耗
        float fuel = 100.0f - time * 0.1f;
        updateFuelLevel(max(0.0f, fuel));
    }
};

// 属性定义
PropertyType<float> VehicleDataSource::SpeedProperty(
    kzMakeFixedString("VehicleDataSource.Speed"), 0.0f);
PropertyType<float> VehicleDataSource::RPMProperty(
    kzMakeFixedString("VehicleDataSource.RPM"), 0.0f);
PropertyType<float> VehicleDataSource::FuelLevelProperty(
    kzMakeFixedString("VehicleDataSource.FuelLevel"), 100.0f);
```

### 触摸交互
```cpp
// 触摸处理
class TouchInteractionHandler : public InputManipulator
{
public:
    explicit TouchInteractionHandler(Domain* domain)
        : InputManipulator(domain)
    {
    }
    
    void attachTo(Node& node) override
    {
        InputManipulator::attachTo(node);
        
        // 注册触摸事件
        node.addMessageHandler(InputTouchBeginMessage::getStaticMessageType(),
            bind(&TouchInteractionHandler::onTouchBegin, this, placeholders::_1));
        node.addMessageHandler(InputTouchMoveMessage::getStaticMessageType(),
            bind(&TouchInteractionHandler::onTouchMove, this, placeholders::_1));
        node.addMessageHandler(InputTouchEndMessage::getStaticMessageType(),
            bind(&TouchInteractionHandler::onTouchEnd, this, placeholders::_1));
    }
    
private:
    void onTouchBegin(InputTouchBeginMessageArguments& messageArguments)
    {
        Vector2 touchPosition = messageArguments.getTouchPoint().position;
        m_lastTouchPosition = touchPosition;
        m_isTouching = true;
        
        // 处理触摸开始
        handleTouchStart(touchPosition);
    }
    
    void onTouchMove(InputTouchMoveMessageArguments& messageArguments)
    {
        if (!m_isTouching) return;
        
        Vector2 touchPosition = messageArguments.getTouchPoint().position;
        Vector2 delta = touchPosition - m_lastTouchPosition;
        
        // 处理拖拽
        handleTouchDrag(delta);
        
        m_lastTouchPosition = touchPosition;
    }
    
    void onTouchEnd(InputTouchEndMessageArguments& messageArguments)
    {
        m_isTouching = false;
        
        // 处理触摸结束
        handleTouchEnd();
    }
    
    void handleTouchStart(const Vector2& position)
    {
        // 实现触摸开始逻辑
        Node* attachedNode = getAttachedNode();
        if (attachedNode)
        {
            // 例如：高亮显示
            attachedNode->setProperty(Node::OpacityProperty, 0.8f);
        }
    }
    
    void handleTouchDrag(const Vector2& delta)
    {
        // 实现拖拽逻辑
        Node* attachedNode = getAttachedNode();
        if (attachedNode)
        {
            Vector2 currentPos = attachedNode->getTranslation();
            attachedNode->setTranslation(currentPos + delta);
        }
    }
    
    void handleTouchEnd()
    {
        // 实现触摸结束逻辑
        Node* attachedNode = getAttachedNode();
        if (attachedNode)
        {
            // 恢复正常状态
            attachedNode->setProperty(Node::OpacityProperty, 1.0f);
        }
    }
    
private:
    Vector2 m_lastTouchPosition;
    bool m_isTouching = false;
};
```

## 最佳实践

### 1. 性能优化
```cpp
// 内存池管理
class MemoryPoolManager
{
public:
    template<typename T>
    T* allocate()
    {
        return static_cast<T*>(m_pool.allocate(sizeof(T)));
    }
    
    template<typename T>
    void deallocate(T* ptr)
    {
        m_pool.deallocate(ptr, sizeof(T));
    }
    
private:
    FixedSizePool m_pool;
};

// 对象复用
class UIElementPool
{
public:
    Node2DSharedPtr getButton()
    {
        if (!m_buttonPool.empty())
        {
            Node2DSharedPtr button = m_buttonPool.back();
            m_buttonPool.pop_back();
            return button;
        }
        
        return createNewButton();
    }
    
    void returnButton(Node2DSharedPtr button)
    {
        // 重置状态
        button->setVisible(false);
        button->removeFromParent();
        
        m_buttonPool.push_back(button);
    }
    
private:
    vector<Node2DSharedPtr> m_buttonPool;
    
    Node2DSharedPtr createNewButton()
    {
        // 创建新按钮的实现
        return Button2D::create(getDomain(), "PooledButton");
    }
};
```

### 2. 资源管理
```cpp
// 异步资源加载
class AsyncResourceLoader
{
public:
    void loadResourceAsync(const string& path, function<void(ResourceSharedPtr)> callback)
    {
        // 在后台线程加载资源
        thread loadThread([this, path, callback]() {
            ResourceSharedPtr resource = loadResourceFromFile(path);
            
            // 在主线程中调用回调
            m_mainThreadQueue.push([callback, resource]() {
                callback(resource);
            });
        });
        
        loadThread.detach();
    }
    
    void processMainThreadQueue()
    {
        while (!m_mainThreadQueue.empty())
        {
            auto task = m_mainThreadQueue.front();
            m_mainThreadQueue.pop();
            task();
        }
    }
    
private:
    queue<function<void()>> m_mainThreadQueue;
    
    ResourceSharedPtr loadResourceFromFile(const string& path)
    {
        // 实际的文件加载逻辑
        return nullptr;
    }
};
```

### 3. 状态管理
```cpp
// 状态机实现
class UIStateMachine
{
public:
    enum class State
    {
        Idle,
        Navigation,
        MediaPlayer,
        Settings,
        Emergency
    };
    
    void setState(State newState)
    {
        if (m_currentState == newState) return;
        
        // 退出当前状态
        exitState(m_currentState);
        
        State previousState = m_currentState;
        m_currentState = newState;
        
        // 进入新状态
        enterState(m_currentState, previousState);
    }
    
private:
    void enterState(State state, State previousState)
    {
        switch (state)
        {
            case State::Navigation:
                showNavigationUI();
                break;
            case State::MediaPlayer:
                showMediaPlayerUI();
                break;
            case State::Settings:
                showSettingsUI();
                break;
            case State::Emergency:
                showEmergencyUI();
                break;
        }
    }
    
    void exitState(State state)
    {
        switch (state)
        {
            case State::Navigation:
                hideNavigationUI();
                break;
            case State::MediaPlayer:
                hideMediaPlayerUI();
                break;
            case State::Settings:
                hideSettingsUI();
                break;
        }
    }
    
    State m_currentState = State::Idle;
};
```

---

*Kanzi 专为嵌入式 HMI 开发而设计，在汽车、工业和消费电子领域具有独特优势。更多详细信息请参考 [Kanzi 官方文档](https://www.rightware.com/kanzi/documentation/)*
