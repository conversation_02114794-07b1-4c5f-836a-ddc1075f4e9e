# Cocos Creator 引擎开发指南

## 目录
1. [概述](#概述)
2. [技术特性](#技术特性)
3. [开发环境搭建](#开发环境搭建)
4. [核心概念](#核心概念)
5. [2D/3D 开发](#2d3d-开发)
6. [脚本编程](#脚本编程)
7. [移动端优化](#移动端优化)
8. [最佳实践](#最佳实践)

## 概述

Cocos Creator 是全球领先的轻量级、高效率、跨平台的数字内容开发平台，专注于2D和3D游戏开发。作为中国最成功的游戏引擎之一，Cocos在全球拥有170万注册开发者，覆盖203个国家和地区，在中国手游市场占有率达40%，全球手游市场占有率达20%。

### 主要优势
- **轻量高效**：引擎体积小，运行效率高，特别适合移动端
- **跨平台支持**：一次开发，多平台发布
- **完整工具链**：从编辑器到发布的完整开发环境
- **强大的2D支持**：业界领先的2D游戏开发能力
- **3D 能力增强**：Cocos Creator 3.x 提供强大的3D开发能力
- **中文友好**：完善的中文文档和社区支持

## 技术特性

### 渲染系统
- **自定义渲染管线**：灵活的渲染管线定制
- **多平台渲染**：OpenGL ES、Metal、Vulkan、WebGL支持
- **2D 渲染优化**：专门优化的2D渲染系统
- **3D 渲染能力**：PBR材质、阴影、后处理效果
- **UI 系统**：强大的UI组件和布局系统

### 动画系统
- **骨骼动画**：Spine、DragonBones 集成
- **缓动动画**：丰富的缓动函数库
- **时间轴动画**：可视化的动画编辑器
- **粒子系统**：高性能的2D/3D粒子效果
- **物理动画**：与物理引擎集成的动画

### 脚本系统
- **TypeScript**：主要开发语言，类型安全
- **JavaScript**：传统的脚本支持
- **组件化架构**：基于组件的开发模式
- **热更新**：支持代码和资源的热更新
- **原生插件**：支持原生代码扩展

## 开发环境搭建

### 系统要求
- **操作系统**：Windows 7+, macOS 10.14+, Ubuntu 18.04+
- **内存**：最少 4GB RAM，推荐 8GB+
- **存储**：至少 2GB 可用空间
- **显卡**：支持 OpenGL 3.3+ 或 DirectX 11+

### 安装步骤

#### 1. 下载 Cocos Creator
```bash
# 访问官网下载
https://www.cocos.com/creator-download

# 选择适合的版本
# - Cocos Creator 3.x: 最新版本，支持3D
# - Cocos Creator 2.x: 稳定版本，主要用于2D
```

#### 2. 安装开发环境
```bash
# 安装 Node.js (推荐 LTS 版本)
https://nodejs.org/

# 安装 TypeScript (可选)
npm install -g typescript

# 安装原生开发环境
# Android: Android Studio + SDK
# iOS: Xcode (仅 macOS)
```

#### 3. 创建第一个项目
```typescript
// 1. 打开 Cocos Creator
// 2. 点击 "新建项目"
// 3. 选择模板（Hello World、2D游戏、3D游戏等）
// 4. 设置项目名称和路径
// 5. 点击 "创建并打开"
```

## 核心概念

### 节点和组件系统
```typescript
import { _decorator, Component, Node, Sprite, Label } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GameController')
export class GameController extends Component {
    @property(Node)
    playerNode: Node = null!;
    
    @property(Label)
    scoreLabel: Label = null!;
    
    private score: number = 0;
    
    start() {
        // 初始化游戏
        this.updateScore();
    }
    
    update(deltaTime: number) {
        // 每帧更新
        if (this.playerNode) {
            // 更新玩家位置
            this.updatePlayer(deltaTime);
        }
    }
    
    private updatePlayer(dt: number) {
        // 玩家逻辑更新
        const pos = this.playerNode.position;
        // 示例：简单移动
        this.playerNode.setPosition(pos.x + 100 * dt, pos.y);
    }
    
    public addScore(points: number) {
        this.score += points;
        this.updateScore();
    }
    
    private updateScore() {
        if (this.scoreLabel) {
            this.scoreLabel.string = `Score: ${this.score}`;
        }
    }
}
```

### 场景管理
```typescript
import { director, Scene } from 'cc';

export class SceneManager {
    // 加载场景
    static loadScene(sceneName: string, onLaunched?: () => void) {
        director.loadScene(sceneName, onLaunched);
    }
    
    // 预加载场景
    static preloadScene(sceneName: string, onProgress?: (progress: number) => void) {
        director.preloadScene(sceneName, (completedCount, totalCount) => {
            const progress = completedCount / totalCount;
            onProgress?.(progress);
        }, (error) => {
            if (error) {
                console.error('预加载场景失败:', error);
            } else {
                console.log('场景预加载完成');
            }
        });
    }
    
    // 获取当前场景
    static getCurrentScene(): Scene | null {
        return director.getScene();
    }
}
```

### 资源管理
```typescript
import { resources, Texture2D, SpriteFrame, AudioClip, Prefab, instantiate } from 'cc';

export class ResourceManager {
    // 加载纹理
    static loadTexture(path: string): Promise<Texture2D> {
        return new Promise((resolve, reject) => {
            resources.load(path, Texture2D, (err, texture) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(texture);
                }
            });
        });
    }
    
    // 加载精灵帧
    static loadSpriteFrame(path: string): Promise<SpriteFrame> {
        return new Promise((resolve, reject) => {
            resources.load(path, SpriteFrame, (err, spriteFrame) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(spriteFrame);
                }
            });
        });
    }
    
    // 加载音频
    static loadAudio(path: string): Promise<AudioClip> {
        return new Promise((resolve, reject) => {
            resources.load(path, AudioClip, (err, audio) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(audio);
                }
            });
        });
    }
    
    // 加载预制体并实例化
    static async loadAndInstantiatePrefab(path: string): Promise<Node> {
        return new Promise((resolve, reject) => {
            resources.load(path, Prefab, (err, prefab) => {
                if (err) {
                    reject(err);
                } else {
                    const instance = instantiate(prefab);
                    resolve(instance);
                }
            });
        });
    }
}
```

## 2D/3D 开发

### 2D 游戏开发
```typescript
import { _decorator, Component, Node, Vec3, input, Input, EventKeyboard, KeyCode } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Player2D')
export class Player2D extends Component {
    @property
    moveSpeed: number = 200;
    
    @property
    jumpForce: number = 500;
    
    private velocity: Vec3 = new Vec3();
    private isGrounded: boolean = true;
    
    start() {
        // 注册输入事件
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        input.on(Input.EventType.KEY_UP, this.onKeyUp, this);
    }
    
    onDestroy() {
        // 清理事件监听
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        input.off(Input.EventType.KEY_UP, this.onKeyUp, this);
    }
    
    update(deltaTime: number) {
        // 应用重力
        if (!this.isGrounded) {
            this.velocity.y -= 980 * deltaTime; // 重力加速度
        }
        
        // 更新位置
        const pos = this.node.position;
        pos.x += this.velocity.x * deltaTime;
        pos.y += this.velocity.y * deltaTime;
        
        // 地面检测（简化）
        if (pos.y <= 0) {
            pos.y = 0;
            this.velocity.y = 0;
            this.isGrounded = true;
        }
        
        this.node.setPosition(pos);
    }
    
    private onKeyDown(event: EventKeyboard) {
        switch (event.keyCode) {
            case KeyCode.ARROW_LEFT:
            case KeyCode.KEY_A:
                this.velocity.x = -this.moveSpeed;
                break;
            case KeyCode.ARROW_RIGHT:
            case KeyCode.KEY_D:
                this.velocity.x = this.moveSpeed;
                break;
            case KeyCode.SPACE:
                if (this.isGrounded) {
                    this.velocity.y = this.jumpForce;
                    this.isGrounded = false;
                }
                break;
        }
    }
    
    private onKeyUp(event: EventKeyboard) {
        switch (event.keyCode) {
            case KeyCode.ARROW_LEFT:
            case KeyCode.KEY_A:
            case KeyCode.ARROW_RIGHT:
            case KeyCode.KEY_D:
                this.velocity.x = 0;
                break;
        }
    }
}
```

### 3D 游戏开发
```typescript
import { _decorator, Component, Node, Vec3, Camera, geometry, PhysicsSystem } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Player3D')
export class Player3D extends Component {
    @property(Camera)
    camera: Camera = null!;
    
    @property
    moveSpeed: number = 5;
    
    @property
    rotateSpeed: number = 90;
    
    private inputVector: Vec3 = new Vec3();
    
    start() {
        // 初始化3D控制器
        this.setupInputHandlers();
    }
    
    update(deltaTime: number) {
        this.handleMovement(deltaTime);
        this.handleRotation(deltaTime);
    }
    
    private setupInputHandlers() {
        input.on(Input.EventType.KEY_DOWN, (event: EventKeyboard) => {
            switch (event.keyCode) {
                case KeyCode.KEY_W:
                    this.inputVector.z = 1;
                    break;
                case KeyCode.KEY_S:
                    this.inputVector.z = -1;
                    break;
                case KeyCode.KEY_A:
                    this.inputVector.x = -1;
                    break;
                case KeyCode.KEY_D:
                    this.inputVector.x = 1;
                    break;
            }
        }, this);
        
        input.on(Input.EventType.KEY_UP, (event: EventKeyboard) => {
            switch (event.keyCode) {
                case KeyCode.KEY_W:
                case KeyCode.KEY_S:
                    this.inputVector.z = 0;
                    break;
                case KeyCode.KEY_A:
                case KeyCode.KEY_D:
                    this.inputVector.x = 0;
                    break;
            }
        }, this);
    }
    
    private handleMovement(dt: number) {
        if (this.inputVector.lengthSqr() > 0) {
            // 标准化输入向量
            this.inputVector.normalize();
            
            // 计算移动向量
            const forward = new Vec3();
            this.node.getForward(forward);
            const right = new Vec3();
            this.node.getRight(right);
            
            const moveVector = new Vec3();
            Vec3.scaleAndAdd(moveVector, moveVector, forward, this.inputVector.z);
            Vec3.scaleAndAdd(moveVector, moveVector, right, this.inputVector.x);
            
            // 应用移动
            Vec3.scaleAndAdd(moveVector, this.node.position, moveVector, this.moveSpeed * dt);
            this.node.setPosition(moveVector);
        }
    }
    
    private handleRotation(dt: number) {
        // 鼠标控制旋转（简化实现）
        // 实际项目中需要处理鼠标输入事件
    }
}
```

## 脚本编程

### 组件生命周期
```typescript
import { _decorator, Component } from 'cc';
const { ccclass } = _decorator;

@ccclass('LifecycleExample')
export class LifecycleExample extends Component {
    // 组件被创建时调用
    onLoad() {
        console.log('onLoad: 组件初始化');
        // 初始化组件数据
    }
    
    // 组件第一次激活时调用
    start() {
        console.log('start: 组件开始运行');
        // 开始游戏逻辑
    }
    
    // 每帧调用
    update(deltaTime: number) {
        // 游戏逻辑更新
        // deltaTime: 距离上一帧的时间间隔
    }
    
    // 固定时间间隔调用（用于物理计算）
    lateUpdate(deltaTime: number) {
        // 在所有 update 之后调用
        // 通常用于相机跟随等逻辑
    }
    
    // 组件被激活时调用
    onEnable() {
        console.log('onEnable: 组件被激活');
    }
    
    // 组件被禁用时调用
    onDisable() {
        console.log('onDisable: 组件被禁用');
    }
    
    // 组件被销毁时调用
    onDestroy() {
        console.log('onDestroy: 组件被销毁');
        // 清理资源，移除事件监听
    }
}
```

### 事件系统
```typescript
import { _decorator, Component, EventTarget } from 'cc';
const { ccclass } = _decorator;

// 自定义事件管理器
export class GameEventManager {
    private static instance: GameEventManager;
    private eventTarget: EventTarget;
    
    private constructor() {
        this.eventTarget = new EventTarget();
    }
    
    static getInstance(): GameEventManager {
        if (!GameEventManager.instance) {
            GameEventManager.instance = new GameEventManager();
        }
        return GameEventManager.instance;
    }
    
    // 发送事件
    emit(eventName: string, ...args: any[]) {
        this.eventTarget.emit(eventName, ...args);
    }
    
    // 监听事件
    on(eventName: string, callback: Function, target?: any) {
        this.eventTarget.on(eventName, callback, target);
    }
    
    // 移除事件监听
    off(eventName: string, callback?: Function, target?: any) {
        this.eventTarget.off(eventName, callback, target);
    }
    
    // 监听一次事件
    once(eventName: string, callback: Function, target?: any) {
        this.eventTarget.once(eventName, callback, target);
    }
}

// 使用示例
@ccclass('EventExample')
export class EventExample extends Component {
    start() {
        const eventManager = GameEventManager.getInstance();
        
        // 监听事件
        eventManager.on('player-score', this.onPlayerScore, this);
        eventManager.on('game-over', this.onGameOver, this);
    }
    
    onDestroy() {
        const eventManager = GameEventManager.getInstance();
        
        // 移除事件监听
        eventManager.off('player-score', this.onPlayerScore, this);
        eventManager.off('game-over', this.onGameOver, this);
    }
    
    private onPlayerScore(score: number) {
        console.log('玩家得分:', score);
    }
    
    private onGameOver() {
        console.log('游戏结束');
    }
    
    // 触发事件
    public triggerScore() {
        const eventManager = GameEventManager.getInstance();
        eventManager.emit('player-score', 100);
    }
}
```

## 移动端优化

### 性能优化
```typescript
// 对象池管理
export class ObjectPool<T> {
    private pool: T[] = [];
    private createFunc: () => T;
    private resetFunc?: (obj: T) => void;
    
    constructor(createFunc: () => T, resetFunc?: (obj: T) => void, initialSize: number = 10) {
        this.createFunc = createFunc;
        this.resetFunc = resetFunc;
        
        // 预创建对象
        for (let i = 0; i < initialSize; i++) {
            this.pool.push(this.createFunc());
        }
    }
    
    get(): T {
        if (this.pool.length > 0) {
            return this.pool.pop()!;
        } else {
            return this.createFunc();
        }
    }
    
    put(obj: T) {
        if (this.resetFunc) {
            this.resetFunc(obj);
        }
        this.pool.push(obj);
    }
    
    clear() {
        this.pool.length = 0;
    }
}

// 使用示例
@ccclass('BulletManager')
export class BulletManager extends Component {
    private bulletPool: ObjectPool<Node>;
    
    start() {
        // 创建子弹对象池
        this.bulletPool = new ObjectPool<Node>(
            () => {
                // 创建子弹节点
                const bullet = new Node('Bullet');
                // 添加必要的组件
                return bullet;
            },
            (bullet) => {
                // 重置子弹状态
                bullet.active = false;
                bullet.setPosition(0, 0, 0);
            },
            20 // 初始池大小
        );
    }
    
    createBullet(position: Vec3): Node {
        const bullet = this.bulletPool.get();
        bullet.setPosition(position);
        bullet.active = true;
        return bullet;
    }
    
    destroyBullet(bullet: Node) {
        this.bulletPool.put(bullet);
    }
}
```

## 最佳实践

### 1. 项目结构
```
assets/
├── scenes/              # 场景文件
├── scripts/             # 脚本文件
│   ├── components/      # 组件脚本
│   ├── managers/        # 管理器脚本
│   ├── utils/          # 工具脚本
│   └── data/           # 数据脚本
├── resources/          # 动态加载资源
├── textures/           # 纹理资源
├── audio/              # 音频资源
├── prefabs/            # 预制体
└── animations/         # 动画资源
```

### 2. 代码规范
```typescript
// 使用 TypeScript 严格模式
// 组件命名使用 PascalCase
// 属性和方法使用 camelCase
// 常量使用 UPPER_SNAKE_CASE

@ccclass('PlayerController')
export class PlayerController extends Component {
    // 公共属性使用 @property 装饰器
    @property
    public moveSpeed: number = 100;
    
    // 私有属性使用 private
    private currentHealth: number = 100;
    
    // 常量
    private static readonly MAX_HEALTH: number = 100;
    
    // 方法命名清晰
    public takeDamage(damage: number): void {
        this.currentHealth = Math.max(0, this.currentHealth - damage);
        
        if (this.currentHealth <= 0) {
            this.handleDeath();
        }
    }
    
    private handleDeath(): void {
        // 处理死亡逻辑
    }
}
```

### 3. 内存管理
```typescript
// 及时释放资源
export class ResourceCleaner {
    private loadedTextures: Texture2D[] = [];
    
    async loadTexture(path: string): Promise<Texture2D> {
        const texture = await ResourceManager.loadTexture(path);
        this.loadedTextures.push(texture);
        return texture;
    }
    
    cleanup() {
        // 释放所有加载的纹理
        this.loadedTextures.forEach(texture => {
            if (texture && texture.isValid) {
                texture.destroy();
            }
        });
        this.loadedTextures.length = 0;
    }
}
```

---

*Cocos Creator 以其轻量高效和强大的2D能力在移动游戏开发领域占据重要地位。更多详细信息请参考 [Cocos Creator 官方文档](https://docs.cocos.com/creator/manual/)*
