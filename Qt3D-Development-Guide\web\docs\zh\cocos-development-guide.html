<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cocos Creator 引擎开发指南</title> <!-- The title will be replaced for each page -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/docs.css">
    <link rel="stylesheet" href="../../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html" class="logo-text" data-i18n="nav-logo-text">3D 引擎指南</a>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="../index.html#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="../index.html#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="../index.html#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 文档内容区域 -->
    <div class="doc-layout container">
        <!-- 左侧目录 -->
        <aside class="doc-sidebar card">
            <div class="toc-container">
                <!-- 目录内容将通过JavaScript动态生成 -->
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <section class="doc-section card">
            <div class="doc-container">
                <main class="doc-content">
                    <div lang="zh"><h1 id="cocos-creator-引擎开发指南">Cocos Creator 引擎开发指南</h1>
<h2 id="目录">目录</h2>
<ol type="1">
<li><a href="#概述">概述</a></li>
<li><a href="#技术特性">技术特性</a></li>
<li><a href="#开发环境搭建">开发环境搭建</a></li>
<li><a href="#核心概念">核心概念</a></li>
<li><a href="#2d3d-开发">2D/3D 开发</a></li>
<li><a href="#脚本编程">脚本编程</a></li>
<li><a href="#移动端优化">移动端优化</a></li>
<li><a href="#最佳实践">最佳实践</a></li>
</ol>
<h2 id="概述">概述</h2>
<p>Cocos Creator
是全球领先的轻量级、高效率、跨平台的数字内容开发平台，专注于2D和3D游戏开发。作为中国最成功的游戏引擎之一，Cocos在全球拥有170万注册开发者，覆盖203个国家和地区，在中国手游市场占有率达40%，全球手游市场占有率达20%。</p>
<h3 id="主要优势">主要优势</h3>
<ul>
<li><strong>轻量高效</strong>：引擎体积小，运行效率高，特别适合移动端</li>
<li><strong>跨平台支持</strong>：一次开发，多平台发布</li>
<li><strong>完整工具链</strong>：从编辑器到发布的完整开发环境</li>
<li><strong>强大的2D支持</strong>：业界领先的2D游戏开发能力</li>
<li><strong>3D 能力增强</strong>：Cocos Creator 3.x
提供强大的3D开发能力</li>
<li><strong>中文友好</strong>：完善的中文文档和社区支持</li>
</ul>
<h2 id="技术特性">技术特性</h2>
<h3 id="渲染系统">渲染系统</h3>
<ul>
<li><strong>自定义渲染管线</strong>：灵活的渲染管线定制</li>
<li><strong>多平台渲染</strong>：OpenGL
ES、Metal、Vulkan、WebGL支持</li>
<li><strong>2D 渲染优化</strong>：专门优化的2D渲染系统</li>
<li><strong>3D 渲染能力</strong>：PBR材质、阴影、后处理效果</li>
<li><strong>UI 系统</strong>：强大的UI组件和布局系统</li>
</ul>
<h3 id="动画系统">动画系统</h3>
<ul>
<li><strong>骨骼动画</strong>：Spine、DragonBones 集成</li>
<li><strong>缓动动画</strong>：丰富的缓动函数库</li>
<li><strong>时间轴动画</strong>：可视化的动画编辑器</li>
<li><strong>粒子系统</strong>：高性能的2D/3D粒子效果</li>
<li><strong>物理动画</strong>：与物理引擎集成的动画</li>
</ul>
<h3 id="脚本系统">脚本系统</h3>
<ul>
<li><strong>TypeScript</strong>：主要开发语言，类型安全</li>
<li><strong>JavaScript</strong>：传统的脚本支持</li>
<li><strong>组件化架构</strong>：基于组件的开发模式</li>
<li><strong>热更新</strong>：支持代码和资源的热更新</li>
<li><strong>原生插件</strong>：支持原生代码扩展</li>
</ul>
<h2 id="开发环境搭建">开发环境搭建</h2>
<h3 id="系统要求">系统要求</h3>
<ul>
<li><strong>操作系统</strong>：Windows 7+, macOS 10.14+, Ubuntu
18.04+</li>
<li><strong>内存</strong>：最少 4GB RAM，推荐 8GB+</li>
<li><strong>存储</strong>：至少 2GB 可用空间</li>
<li><strong>显卡</strong>：支持 OpenGL 3.3+ 或 DirectX 11+</li>
</ul>
<h3 id="安装步骤">安装步骤</h3>
<h4 id="下载-cocos-creator">1. 下载 Cocos Creator</h4>
<div class="sourceCode" id="cb1"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 访问官网下载</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="ex">https://www.cocos.com/creator-download</span></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a><span class="co"># 选择适合的版本</span></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a><span class="co"># - Cocos Creator 3.x: 最新版本，支持3D</span></span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a><span class="co"># - Cocos Creator 2.x: 稳定版本，主要用于2D</span></span></code></pre></div>
<h4 id="安装开发环境">2. 安装开发环境</h4>
<div class="sourceCode" id="cb2"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 安装 Node.js (推荐 LTS 版本)</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a><span class="ex">https://nodejs.org/</span></span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a><span class="co"># 安装 TypeScript (可选)</span></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a><span class="ex">npm</span> install <span class="at">-g</span> typescript</span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-7"><a href="#cb2-7" aria-hidden="true" tabindex="-1"></a><span class="co"># 安装原生开发环境</span></span>
<span id="cb2-8"><a href="#cb2-8" aria-hidden="true" tabindex="-1"></a><span class="co"># Android: Android Studio + SDK</span></span>
<span id="cb2-9"><a href="#cb2-9" aria-hidden="true" tabindex="-1"></a><span class="co"># iOS: Xcode (仅 macOS)</span></span></code></pre></div>
<h4 id="创建第一个项目">3. 创建第一个项目</h4>
<div class="sourceCode" id="cb3"><pre
class="sourceCode typescript"><code class="sourceCode typescript"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 1. 打开 Cocos Creator</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a><span class="co">// 2. 点击 &quot;新建项目&quot;</span></span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a><span class="co">// 3. 选择模板（Hello World、2D游戏、3D游戏等）</span></span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a><span class="co">// 4. 设置项目名称和路径</span></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a><span class="co">// 5. 点击 &quot;创建并打开&quot;</span></span></code></pre></div>
<h2 id="核心概念">核心概念</h2>
<h3 id="节点和组件系统">节点和组件系统</h3>
<div class="sourceCode" id="cb4"><pre
class="sourceCode typescript"><code class="sourceCode typescript"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> { _decorator<span class="op">,</span> Component<span class="op">,</span> <span class="bu">Node</span><span class="op">,</span> Sprite<span class="op">,</span> Label } <span class="im">from</span> <span class="st">&#39;cc&#39;</span><span class="op">;</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a><span class="kw">const</span> { ccclass<span class="op">,</span> property } <span class="op">=</span> _decorator<span class="op">;</span></span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a>@<span class="fu">ccclass</span>(<span class="st">&#39;GameController&#39;</span>)</span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> GameController <span class="kw">extends</span> Component {</span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a>    @<span class="fu">property</span>(<span class="bu">Node</span>)</span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a>    playerNode<span class="op">:</span> <span class="bu">Node</span> <span class="op">=</span> <span class="kw">null</span><span class="op">!;</span></span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a>    @<span class="fu">property</span>(Label)</span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a>    scoreLabel<span class="op">:</span> Label <span class="op">=</span> <span class="kw">null</span><span class="op">!;</span></span>
<span id="cb4-11"><a href="#cb4-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-12"><a href="#cb4-12" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> score<span class="op">:</span> <span class="dt">number</span> <span class="op">=</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb4-13"><a href="#cb4-13" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-14"><a href="#cb4-14" aria-hidden="true" tabindex="-1"></a>    <span class="fu">start</span>() {</span>
<span id="cb4-15"><a href="#cb4-15" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 初始化游戏</span></span>
<span id="cb4-16"><a href="#cb4-16" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="fu">updateScore</span>()<span class="op">;</span></span>
<span id="cb4-17"><a href="#cb4-17" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb4-18"><a href="#cb4-18" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-19"><a href="#cb4-19" aria-hidden="true" tabindex="-1"></a>    <span class="fu">update</span>(deltaTime<span class="op">:</span> <span class="dt">number</span>) {</span>
<span id="cb4-20"><a href="#cb4-20" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 每帧更新</span></span>
<span id="cb4-21"><a href="#cb4-21" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="kw">this</span><span class="op">.</span><span class="at">playerNode</span>) {</span>
<span id="cb4-22"><a href="#cb4-22" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 更新玩家位置</span></span>
<span id="cb4-23"><a href="#cb4-23" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="fu">updatePlayer</span>(deltaTime)<span class="op">;</span></span>
<span id="cb4-24"><a href="#cb4-24" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb4-25"><a href="#cb4-25" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb4-26"><a href="#cb4-26" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-27"><a href="#cb4-27" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">updatePlayer</span>(dt<span class="op">:</span> <span class="dt">number</span>) {</span>
<span id="cb4-28"><a href="#cb4-28" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 玩家逻辑更新</span></span>
<span id="cb4-29"><a href="#cb4-29" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> pos <span class="op">=</span> <span class="kw">this</span><span class="op">.</span><span class="at">playerNode</span><span class="op">.</span><span class="at">position</span><span class="op">;</span></span>
<span id="cb4-30"><a href="#cb4-30" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 示例：简单移动</span></span>
<span id="cb4-31"><a href="#cb4-31" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">playerNode</span><span class="op">.</span><span class="fu">setPosition</span>(pos<span class="op">.</span><span class="at">x</span> <span class="op">+</span> <span class="dv">100</span> <span class="op">*</span> dt<span class="op">,</span> pos<span class="op">.</span><span class="at">y</span>)<span class="op">;</span></span>
<span id="cb4-32"><a href="#cb4-32" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb4-33"><a href="#cb4-33" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-34"><a href="#cb4-34" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="fu">addScore</span>(points<span class="op">:</span> <span class="dt">number</span>) {</span>
<span id="cb4-35"><a href="#cb4-35" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">score</span> <span class="op">+=</span> points<span class="op">;</span></span>
<span id="cb4-36"><a href="#cb4-36" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="fu">updateScore</span>()<span class="op">;</span></span>
<span id="cb4-37"><a href="#cb4-37" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb4-38"><a href="#cb4-38" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-39"><a href="#cb4-39" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">updateScore</span>() {</span>
<span id="cb4-40"><a href="#cb4-40" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="kw">this</span><span class="op">.</span><span class="at">scoreLabel</span>) {</span>
<span id="cb4-41"><a href="#cb4-41" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="at">scoreLabel</span><span class="op">.</span><span class="at">string</span> <span class="op">=</span> <span class="vs">`Score: </span><span class="sc">${</span><span class="kw">this</span><span class="op">.</span><span class="at">score</span><span class="sc">}</span><span class="vs">`</span><span class="op">;</span></span>
<span id="cb4-42"><a href="#cb4-42" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb4-43"><a href="#cb4-43" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb4-44"><a href="#cb4-44" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<h3 id="场景管理">场景管理</h3>
<div class="sourceCode" id="cb5"><pre
class="sourceCode typescript"><code class="sourceCode typescript"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> { director<span class="op">,</span> Scene } <span class="im">from</span> <span class="st">&#39;cc&#39;</span><span class="op">;</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> SceneManager {</span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 加载场景</span></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a>    <span class="kw">static</span> <span class="fu">loadScene</span>(sceneName<span class="op">:</span> <span class="dt">string</span><span class="op">,</span> onLaunched<span class="op">?:</span> () <span class="kw">=&gt;</span> <span class="dt">void</span>) {</span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a>        director<span class="op">.</span><span class="fu">loadScene</span>(sceneName<span class="op">,</span> onLaunched)<span class="op">;</span></span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 预加载场景</span></span>
<span id="cb5-10"><a href="#cb5-10" aria-hidden="true" tabindex="-1"></a>    <span class="kw">static</span> <span class="fu">preloadScene</span>(sceneName<span class="op">:</span> <span class="dt">string</span><span class="op">,</span> onProgress<span class="op">?:</span> (progress<span class="op">:</span> <span class="dt">number</span>) <span class="kw">=&gt;</span> <span class="dt">void</span>) {</span>
<span id="cb5-11"><a href="#cb5-11" aria-hidden="true" tabindex="-1"></a>        director<span class="op">.</span><span class="fu">preloadScene</span>(sceneName<span class="op">,</span> (completedCount<span class="op">,</span> totalCount) <span class="kw">=&gt;</span> {</span>
<span id="cb5-12"><a href="#cb5-12" aria-hidden="true" tabindex="-1"></a>            <span class="kw">const</span> progress <span class="op">=</span> completedCount <span class="op">/</span> totalCount<span class="op">;</span></span>
<span id="cb5-13"><a href="#cb5-13" aria-hidden="true" tabindex="-1"></a>            onProgress<span class="op">?.</span>(progress)<span class="op">;</span></span>
<span id="cb5-14"><a href="#cb5-14" aria-hidden="true" tabindex="-1"></a>        }<span class="op">,</span> (error) <span class="kw">=&gt;</span> {</span>
<span id="cb5-15"><a href="#cb5-15" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> (error) {</span>
<span id="cb5-16"><a href="#cb5-16" aria-hidden="true" tabindex="-1"></a>                <span class="bu">console</span><span class="op">.</span><span class="fu">error</span>(<span class="st">&#39;预加载场景失败:&#39;</span><span class="op">,</span> error)<span class="op">;</span></span>
<span id="cb5-17"><a href="#cb5-17" aria-hidden="true" tabindex="-1"></a>            } <span class="cf">else</span> {</span>
<span id="cb5-18"><a href="#cb5-18" aria-hidden="true" tabindex="-1"></a>                <span class="bu">console</span><span class="op">.</span><span class="fu">log</span>(<span class="st">&#39;场景预加载完成&#39;</span>)<span class="op">;</span></span>
<span id="cb5-19"><a href="#cb5-19" aria-hidden="true" tabindex="-1"></a>            }</span>
<span id="cb5-20"><a href="#cb5-20" aria-hidden="true" tabindex="-1"></a>        })<span class="op">;</span></span>
<span id="cb5-21"><a href="#cb5-21" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb5-22"><a href="#cb5-22" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-23"><a href="#cb5-23" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 获取当前场景</span></span>
<span id="cb5-24"><a href="#cb5-24" aria-hidden="true" tabindex="-1"></a>    <span class="kw">static</span> <span class="fu">getCurrentScene</span>()<span class="op">:</span> Scene <span class="op">|</span> <span class="dt">null</span> {</span>
<span id="cb5-25"><a href="#cb5-25" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> director<span class="op">.</span><span class="fu">getScene</span>()<span class="op">;</span></span>
<span id="cb5-26"><a href="#cb5-26" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb5-27"><a href="#cb5-27" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<h3 id="资源管理">资源管理</h3>
<div class="sourceCode" id="cb6"><pre
class="sourceCode typescript"><code class="sourceCode typescript"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> { resources<span class="op">,</span> Texture2D<span class="op">,</span> SpriteFrame<span class="op">,</span> AudioClip<span class="op">,</span> Prefab<span class="op">,</span> instantiate } <span class="im">from</span> <span class="st">&#39;cc&#39;</span><span class="op">;</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> ResourceManager {</span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 加载纹理</span></span>
<span id="cb6-5"><a href="#cb6-5" aria-hidden="true" tabindex="-1"></a>    <span class="kw">static</span> <span class="fu">loadTexture</span>(path<span class="op">:</span> <span class="dt">string</span>)<span class="op">:</span> <span class="bu">Promise</span><span class="op">&lt;</span>Texture2D<span class="op">&gt;</span> {</span>
<span id="cb6-6"><a href="#cb6-6" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">new</span> <span class="bu">Promise</span>((resolve<span class="op">,</span> reject) <span class="kw">=&gt;</span> {</span>
<span id="cb6-7"><a href="#cb6-7" aria-hidden="true" tabindex="-1"></a>            resources<span class="op">.</span><span class="fu">load</span>(path<span class="op">,</span> Texture2D<span class="op">,</span> (err<span class="op">,</span> texture) <span class="kw">=&gt;</span> {</span>
<span id="cb6-8"><a href="#cb6-8" aria-hidden="true" tabindex="-1"></a>                <span class="cf">if</span> (err) {</span>
<span id="cb6-9"><a href="#cb6-9" aria-hidden="true" tabindex="-1"></a>                    <span class="fu">reject</span>(err)<span class="op">;</span></span>
<span id="cb6-10"><a href="#cb6-10" aria-hidden="true" tabindex="-1"></a>                } <span class="cf">else</span> {</span>
<span id="cb6-11"><a href="#cb6-11" aria-hidden="true" tabindex="-1"></a>                    <span class="fu">resolve</span>(texture)<span class="op">;</span></span>
<span id="cb6-12"><a href="#cb6-12" aria-hidden="true" tabindex="-1"></a>                }</span>
<span id="cb6-13"><a href="#cb6-13" aria-hidden="true" tabindex="-1"></a>            })<span class="op">;</span></span>
<span id="cb6-14"><a href="#cb6-14" aria-hidden="true" tabindex="-1"></a>        })<span class="op">;</span></span>
<span id="cb6-15"><a href="#cb6-15" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb6-16"><a href="#cb6-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-17"><a href="#cb6-17" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 加载精灵帧</span></span>
<span id="cb6-18"><a href="#cb6-18" aria-hidden="true" tabindex="-1"></a>    <span class="kw">static</span> <span class="fu">loadSpriteFrame</span>(path<span class="op">:</span> <span class="dt">string</span>)<span class="op">:</span> <span class="bu">Promise</span><span class="op">&lt;</span>SpriteFrame<span class="op">&gt;</span> {</span>
<span id="cb6-19"><a href="#cb6-19" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">new</span> <span class="bu">Promise</span>((resolve<span class="op">,</span> reject) <span class="kw">=&gt;</span> {</span>
<span id="cb6-20"><a href="#cb6-20" aria-hidden="true" tabindex="-1"></a>            resources<span class="op">.</span><span class="fu">load</span>(path<span class="op">,</span> SpriteFrame<span class="op">,</span> (err<span class="op">,</span> spriteFrame) <span class="kw">=&gt;</span> {</span>
<span id="cb6-21"><a href="#cb6-21" aria-hidden="true" tabindex="-1"></a>                <span class="cf">if</span> (err) {</span>
<span id="cb6-22"><a href="#cb6-22" aria-hidden="true" tabindex="-1"></a>                    <span class="fu">reject</span>(err)<span class="op">;</span></span>
<span id="cb6-23"><a href="#cb6-23" aria-hidden="true" tabindex="-1"></a>                } <span class="cf">else</span> {</span>
<span id="cb6-24"><a href="#cb6-24" aria-hidden="true" tabindex="-1"></a>                    <span class="fu">resolve</span>(spriteFrame)<span class="op">;</span></span>
<span id="cb6-25"><a href="#cb6-25" aria-hidden="true" tabindex="-1"></a>                }</span>
<span id="cb6-26"><a href="#cb6-26" aria-hidden="true" tabindex="-1"></a>            })<span class="op">;</span></span>
<span id="cb6-27"><a href="#cb6-27" aria-hidden="true" tabindex="-1"></a>        })<span class="op">;</span></span>
<span id="cb6-28"><a href="#cb6-28" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb6-29"><a href="#cb6-29" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-30"><a href="#cb6-30" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 加载音频</span></span>
<span id="cb6-31"><a href="#cb6-31" aria-hidden="true" tabindex="-1"></a>    <span class="kw">static</span> <span class="fu">loadAudio</span>(path<span class="op">:</span> <span class="dt">string</span>)<span class="op">:</span> <span class="bu">Promise</span><span class="op">&lt;</span>AudioClip<span class="op">&gt;</span> {</span>
<span id="cb6-32"><a href="#cb6-32" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">new</span> <span class="bu">Promise</span>((resolve<span class="op">,</span> reject) <span class="kw">=&gt;</span> {</span>
<span id="cb6-33"><a href="#cb6-33" aria-hidden="true" tabindex="-1"></a>            resources<span class="op">.</span><span class="fu">load</span>(path<span class="op">,</span> AudioClip<span class="op">,</span> (err<span class="op">,</span> audio) <span class="kw">=&gt;</span> {</span>
<span id="cb6-34"><a href="#cb6-34" aria-hidden="true" tabindex="-1"></a>                <span class="cf">if</span> (err) {</span>
<span id="cb6-35"><a href="#cb6-35" aria-hidden="true" tabindex="-1"></a>                    <span class="fu">reject</span>(err)<span class="op">;</span></span>
<span id="cb6-36"><a href="#cb6-36" aria-hidden="true" tabindex="-1"></a>                } <span class="cf">else</span> {</span>
<span id="cb6-37"><a href="#cb6-37" aria-hidden="true" tabindex="-1"></a>                    <span class="fu">resolve</span>(audio)<span class="op">;</span></span>
<span id="cb6-38"><a href="#cb6-38" aria-hidden="true" tabindex="-1"></a>                }</span>
<span id="cb6-39"><a href="#cb6-39" aria-hidden="true" tabindex="-1"></a>            })<span class="op">;</span></span>
<span id="cb6-40"><a href="#cb6-40" aria-hidden="true" tabindex="-1"></a>        })<span class="op">;</span></span>
<span id="cb6-41"><a href="#cb6-41" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb6-42"><a href="#cb6-42" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-43"><a href="#cb6-43" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 加载预制体并实例化</span></span>
<span id="cb6-44"><a href="#cb6-44" aria-hidden="true" tabindex="-1"></a>    <span class="kw">static</span> <span class="kw">async</span> <span class="fu">loadAndInstantiatePrefab</span>(path<span class="op">:</span> <span class="dt">string</span>)<span class="op">:</span> <span class="bu">Promise</span><span class="op">&lt;</span><span class="bu">Node</span><span class="op">&gt;</span> {</span>
<span id="cb6-45"><a href="#cb6-45" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">new</span> <span class="bu">Promise</span>((resolve<span class="op">,</span> reject) <span class="kw">=&gt;</span> {</span>
<span id="cb6-46"><a href="#cb6-46" aria-hidden="true" tabindex="-1"></a>            resources<span class="op">.</span><span class="fu">load</span>(path<span class="op">,</span> Prefab<span class="op">,</span> (err<span class="op">,</span> prefab) <span class="kw">=&gt;</span> {</span>
<span id="cb6-47"><a href="#cb6-47" aria-hidden="true" tabindex="-1"></a>                <span class="cf">if</span> (err) {</span>
<span id="cb6-48"><a href="#cb6-48" aria-hidden="true" tabindex="-1"></a>                    <span class="fu">reject</span>(err)<span class="op">;</span></span>
<span id="cb6-49"><a href="#cb6-49" aria-hidden="true" tabindex="-1"></a>                } <span class="cf">else</span> {</span>
<span id="cb6-50"><a href="#cb6-50" aria-hidden="true" tabindex="-1"></a>                    <span class="kw">const</span> instance <span class="op">=</span> <span class="fu">instantiate</span>(prefab)<span class="op">;</span></span>
<span id="cb6-51"><a href="#cb6-51" aria-hidden="true" tabindex="-1"></a>                    <span class="fu">resolve</span>(instance)<span class="op">;</span></span>
<span id="cb6-52"><a href="#cb6-52" aria-hidden="true" tabindex="-1"></a>                }</span>
<span id="cb6-53"><a href="#cb6-53" aria-hidden="true" tabindex="-1"></a>            })<span class="op">;</span></span>
<span id="cb6-54"><a href="#cb6-54" aria-hidden="true" tabindex="-1"></a>        })<span class="op">;</span></span>
<span id="cb6-55"><a href="#cb6-55" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb6-56"><a href="#cb6-56" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<h2 id="d3d-开发">2D/3D 开发</h2>
<h3 id="d-游戏开发">2D 游戏开发</h3>
<div class="sourceCode" id="cb7"><pre
class="sourceCode typescript"><code class="sourceCode typescript"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> { _decorator<span class="op">,</span> Component<span class="op">,</span> <span class="bu">Node</span><span class="op">,</span> Vec3<span class="op">,</span> input<span class="op">,</span> Input<span class="op">,</span> EventKeyboard<span class="op">,</span> KeyCode } <span class="im">from</span> <span class="st">&#39;cc&#39;</span><span class="op">;</span></span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a><span class="kw">const</span> { ccclass<span class="op">,</span> property } <span class="op">=</span> _decorator<span class="op">;</span></span>
<span id="cb7-3"><a href="#cb7-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-4"><a href="#cb7-4" aria-hidden="true" tabindex="-1"></a>@<span class="fu">ccclass</span>(<span class="st">&#39;Player2D&#39;</span>)</span>
<span id="cb7-5"><a href="#cb7-5" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> Player2D <span class="kw">extends</span> Component {</span>
<span id="cb7-6"><a href="#cb7-6" aria-hidden="true" tabindex="-1"></a>    @property</span>
<span id="cb7-7"><a href="#cb7-7" aria-hidden="true" tabindex="-1"></a>    moveSpeed<span class="op">:</span> <span class="dt">number</span> <span class="op">=</span> <span class="dv">200</span><span class="op">;</span></span>
<span id="cb7-8"><a href="#cb7-8" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-9"><a href="#cb7-9" aria-hidden="true" tabindex="-1"></a>    @property</span>
<span id="cb7-10"><a href="#cb7-10" aria-hidden="true" tabindex="-1"></a>    jumpForce<span class="op">:</span> <span class="dt">number</span> <span class="op">=</span> <span class="dv">500</span><span class="op">;</span></span>
<span id="cb7-11"><a href="#cb7-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-12"><a href="#cb7-12" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> velocity<span class="op">:</span> Vec3 <span class="op">=</span> <span class="kw">new</span> <span class="fu">Vec3</span>()<span class="op">;</span></span>
<span id="cb7-13"><a href="#cb7-13" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> isGrounded<span class="op">:</span> <span class="dt">boolean</span> <span class="op">=</span> <span class="kw">true</span><span class="op">;</span></span>
<span id="cb7-14"><a href="#cb7-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-15"><a href="#cb7-15" aria-hidden="true" tabindex="-1"></a>    <span class="fu">start</span>() {</span>
<span id="cb7-16"><a href="#cb7-16" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 注册输入事件</span></span>
<span id="cb7-17"><a href="#cb7-17" aria-hidden="true" tabindex="-1"></a>        input<span class="op">.</span><span class="fu">on</span>(Input<span class="op">.</span><span class="at">EventType</span><span class="op">.</span><span class="at">KEY_DOWN</span><span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">onKeyDown</span><span class="op">,</span> <span class="kw">this</span>)<span class="op">;</span></span>
<span id="cb7-18"><a href="#cb7-18" aria-hidden="true" tabindex="-1"></a>        input<span class="op">.</span><span class="fu">on</span>(Input<span class="op">.</span><span class="at">EventType</span><span class="op">.</span><span class="at">KEY_UP</span><span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">onKeyUp</span><span class="op">,</span> <span class="kw">this</span>)<span class="op">;</span></span>
<span id="cb7-19"><a href="#cb7-19" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb7-20"><a href="#cb7-20" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-21"><a href="#cb7-21" aria-hidden="true" tabindex="-1"></a>    <span class="fu">onDestroy</span>() {</span>
<span id="cb7-22"><a href="#cb7-22" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 清理事件监听</span></span>
<span id="cb7-23"><a href="#cb7-23" aria-hidden="true" tabindex="-1"></a>        input<span class="op">.</span><span class="fu">off</span>(Input<span class="op">.</span><span class="at">EventType</span><span class="op">.</span><span class="at">KEY_DOWN</span><span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">onKeyDown</span><span class="op">,</span> <span class="kw">this</span>)<span class="op">;</span></span>
<span id="cb7-24"><a href="#cb7-24" aria-hidden="true" tabindex="-1"></a>        input<span class="op">.</span><span class="fu">off</span>(Input<span class="op">.</span><span class="at">EventType</span><span class="op">.</span><span class="at">KEY_UP</span><span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">onKeyUp</span><span class="op">,</span> <span class="kw">this</span>)<span class="op">;</span></span>
<span id="cb7-25"><a href="#cb7-25" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb7-26"><a href="#cb7-26" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-27"><a href="#cb7-27" aria-hidden="true" tabindex="-1"></a>    <span class="fu">update</span>(deltaTime<span class="op">:</span> <span class="dt">number</span>) {</span>
<span id="cb7-28"><a href="#cb7-28" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 应用重力</span></span>
<span id="cb7-29"><a href="#cb7-29" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="op">!</span><span class="kw">this</span><span class="op">.</span><span class="at">isGrounded</span>) {</span>
<span id="cb7-30"><a href="#cb7-30" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="at">velocity</span><span class="op">.</span><span class="at">y</span> <span class="op">-=</span> <span class="dv">980</span> <span class="op">*</span> deltaTime<span class="op">;</span> <span class="co">// 重力加速度</span></span>
<span id="cb7-31"><a href="#cb7-31" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb7-32"><a href="#cb7-32" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-33"><a href="#cb7-33" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 更新位置</span></span>
<span id="cb7-34"><a href="#cb7-34" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> pos <span class="op">=</span> <span class="kw">this</span><span class="op">.</span><span class="at">node</span><span class="op">.</span><span class="at">position</span><span class="op">;</span></span>
<span id="cb7-35"><a href="#cb7-35" aria-hidden="true" tabindex="-1"></a>        pos<span class="op">.</span><span class="at">x</span> <span class="op">+=</span> <span class="kw">this</span><span class="op">.</span><span class="at">velocity</span><span class="op">.</span><span class="at">x</span> <span class="op">*</span> deltaTime<span class="op">;</span></span>
<span id="cb7-36"><a href="#cb7-36" aria-hidden="true" tabindex="-1"></a>        pos<span class="op">.</span><span class="at">y</span> <span class="op">+=</span> <span class="kw">this</span><span class="op">.</span><span class="at">velocity</span><span class="op">.</span><span class="at">y</span> <span class="op">*</span> deltaTime<span class="op">;</span></span>
<span id="cb7-37"><a href="#cb7-37" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-38"><a href="#cb7-38" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 地面检测（简化）</span></span>
<span id="cb7-39"><a href="#cb7-39" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (pos<span class="op">.</span><span class="at">y</span> <span class="op">&lt;=</span> <span class="dv">0</span>) {</span>
<span id="cb7-40"><a href="#cb7-40" aria-hidden="true" tabindex="-1"></a>            pos<span class="op">.</span><span class="at">y</span> <span class="op">=</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb7-41"><a href="#cb7-41" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="at">velocity</span><span class="op">.</span><span class="at">y</span> <span class="op">=</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb7-42"><a href="#cb7-42" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="at">isGrounded</span> <span class="op">=</span> <span class="kw">true</span><span class="op">;</span></span>
<span id="cb7-43"><a href="#cb7-43" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb7-44"><a href="#cb7-44" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-45"><a href="#cb7-45" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">node</span><span class="op">.</span><span class="fu">setPosition</span>(pos)<span class="op">;</span></span>
<span id="cb7-46"><a href="#cb7-46" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb7-47"><a href="#cb7-47" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-48"><a href="#cb7-48" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">onKeyDown</span>(event<span class="op">:</span> EventKeyboard) {</span>
<span id="cb7-49"><a href="#cb7-49" aria-hidden="true" tabindex="-1"></a>        <span class="cf">switch</span> (<span class="bu">event</span><span class="op">.</span><span class="at">keyCode</span>) {</span>
<span id="cb7-50"><a href="#cb7-50" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">ARROW_LEFT</span><span class="op">:</span></span>
<span id="cb7-51"><a href="#cb7-51" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">KEY_A</span><span class="op">:</span></span>
<span id="cb7-52"><a href="#cb7-52" aria-hidden="true" tabindex="-1"></a>                <span class="kw">this</span><span class="op">.</span><span class="at">velocity</span><span class="op">.</span><span class="at">x</span> <span class="op">=</span> <span class="op">-</span><span class="kw">this</span><span class="op">.</span><span class="at">moveSpeed</span><span class="op">;</span></span>
<span id="cb7-53"><a href="#cb7-53" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb7-54"><a href="#cb7-54" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">ARROW_RIGHT</span><span class="op">:</span></span>
<span id="cb7-55"><a href="#cb7-55" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">KEY_D</span><span class="op">:</span></span>
<span id="cb7-56"><a href="#cb7-56" aria-hidden="true" tabindex="-1"></a>                <span class="kw">this</span><span class="op">.</span><span class="at">velocity</span><span class="op">.</span><span class="at">x</span> <span class="op">=</span> <span class="kw">this</span><span class="op">.</span><span class="at">moveSpeed</span><span class="op">;</span></span>
<span id="cb7-57"><a href="#cb7-57" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb7-58"><a href="#cb7-58" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">SPACE</span><span class="op">:</span></span>
<span id="cb7-59"><a href="#cb7-59" aria-hidden="true" tabindex="-1"></a>                <span class="cf">if</span> (<span class="kw">this</span><span class="op">.</span><span class="at">isGrounded</span>) {</span>
<span id="cb7-60"><a href="#cb7-60" aria-hidden="true" tabindex="-1"></a>                    <span class="kw">this</span><span class="op">.</span><span class="at">velocity</span><span class="op">.</span><span class="at">y</span> <span class="op">=</span> <span class="kw">this</span><span class="op">.</span><span class="at">jumpForce</span><span class="op">;</span></span>
<span id="cb7-61"><a href="#cb7-61" aria-hidden="true" tabindex="-1"></a>                    <span class="kw">this</span><span class="op">.</span><span class="at">isGrounded</span> <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb7-62"><a href="#cb7-62" aria-hidden="true" tabindex="-1"></a>                }</span>
<span id="cb7-63"><a href="#cb7-63" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb7-64"><a href="#cb7-64" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb7-65"><a href="#cb7-65" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb7-66"><a href="#cb7-66" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-67"><a href="#cb7-67" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">onKeyUp</span>(event<span class="op">:</span> EventKeyboard) {</span>
<span id="cb7-68"><a href="#cb7-68" aria-hidden="true" tabindex="-1"></a>        <span class="cf">switch</span> (<span class="bu">event</span><span class="op">.</span><span class="at">keyCode</span>) {</span>
<span id="cb7-69"><a href="#cb7-69" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">ARROW_LEFT</span><span class="op">:</span></span>
<span id="cb7-70"><a href="#cb7-70" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">KEY_A</span><span class="op">:</span></span>
<span id="cb7-71"><a href="#cb7-71" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">ARROW_RIGHT</span><span class="op">:</span></span>
<span id="cb7-72"><a href="#cb7-72" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">KEY_D</span><span class="op">:</span></span>
<span id="cb7-73"><a href="#cb7-73" aria-hidden="true" tabindex="-1"></a>                <span class="kw">this</span><span class="op">.</span><span class="at">velocity</span><span class="op">.</span><span class="at">x</span> <span class="op">=</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb7-74"><a href="#cb7-74" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb7-75"><a href="#cb7-75" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb7-76"><a href="#cb7-76" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb7-77"><a href="#cb7-77" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<h3 id="d-游戏开发-1">3D 游戏开发</h3>
<div class="sourceCode" id="cb8"><pre
class="sourceCode typescript"><code class="sourceCode typescript"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> { _decorator<span class="op">,</span> Component<span class="op">,</span> <span class="bu">Node</span><span class="op">,</span> Vec3<span class="op">,</span> Camera<span class="op">,</span> geometry<span class="op">,</span> PhysicsSystem } <span class="im">from</span> <span class="st">&#39;cc&#39;</span><span class="op">;</span></span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a><span class="kw">const</span> { ccclass<span class="op">,</span> property } <span class="op">=</span> _decorator<span class="op">;</span></span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-4"><a href="#cb8-4" aria-hidden="true" tabindex="-1"></a>@<span class="fu">ccclass</span>(<span class="st">&#39;Player3D&#39;</span>)</span>
<span id="cb8-5"><a href="#cb8-5" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> Player3D <span class="kw">extends</span> Component {</span>
<span id="cb8-6"><a href="#cb8-6" aria-hidden="true" tabindex="-1"></a>    @<span class="fu">property</span>(Camera)</span>
<span id="cb8-7"><a href="#cb8-7" aria-hidden="true" tabindex="-1"></a>    camera<span class="op">:</span> Camera <span class="op">=</span> <span class="kw">null</span><span class="op">!;</span></span>
<span id="cb8-8"><a href="#cb8-8" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-9"><a href="#cb8-9" aria-hidden="true" tabindex="-1"></a>    @property</span>
<span id="cb8-10"><a href="#cb8-10" aria-hidden="true" tabindex="-1"></a>    moveSpeed<span class="op">:</span> <span class="dt">number</span> <span class="op">=</span> <span class="dv">5</span><span class="op">;</span></span>
<span id="cb8-11"><a href="#cb8-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-12"><a href="#cb8-12" aria-hidden="true" tabindex="-1"></a>    @property</span>
<span id="cb8-13"><a href="#cb8-13" aria-hidden="true" tabindex="-1"></a>    rotateSpeed<span class="op">:</span> <span class="dt">number</span> <span class="op">=</span> <span class="dv">90</span><span class="op">;</span></span>
<span id="cb8-14"><a href="#cb8-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-15"><a href="#cb8-15" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> inputVector<span class="op">:</span> Vec3 <span class="op">=</span> <span class="kw">new</span> <span class="fu">Vec3</span>()<span class="op">;</span></span>
<span id="cb8-16"><a href="#cb8-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-17"><a href="#cb8-17" aria-hidden="true" tabindex="-1"></a>    <span class="fu">start</span>() {</span>
<span id="cb8-18"><a href="#cb8-18" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 初始化3D控制器</span></span>
<span id="cb8-19"><a href="#cb8-19" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="fu">setupInputHandlers</span>()<span class="op">;</span></span>
<span id="cb8-20"><a href="#cb8-20" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb8-21"><a href="#cb8-21" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-22"><a href="#cb8-22" aria-hidden="true" tabindex="-1"></a>    <span class="fu">update</span>(deltaTime<span class="op">:</span> <span class="dt">number</span>) {</span>
<span id="cb8-23"><a href="#cb8-23" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="fu">handleMovement</span>(deltaTime)<span class="op">;</span></span>
<span id="cb8-24"><a href="#cb8-24" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="fu">handleRotation</span>(deltaTime)<span class="op">;</span></span>
<span id="cb8-25"><a href="#cb8-25" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb8-26"><a href="#cb8-26" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-27"><a href="#cb8-27" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">setupInputHandlers</span>() {</span>
<span id="cb8-28"><a href="#cb8-28" aria-hidden="true" tabindex="-1"></a>        input<span class="op">.</span><span class="fu">on</span>(Input<span class="op">.</span><span class="at">EventType</span><span class="op">.</span><span class="at">KEY_DOWN</span><span class="op">,</span> (event<span class="op">:</span> EventKeyboard) <span class="kw">=&gt;</span> {</span>
<span id="cb8-29"><a href="#cb8-29" aria-hidden="true" tabindex="-1"></a>            <span class="cf">switch</span> (<span class="bu">event</span><span class="op">.</span><span class="at">keyCode</span>) {</span>
<span id="cb8-30"><a href="#cb8-30" aria-hidden="true" tabindex="-1"></a>                <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">KEY_W</span><span class="op">:</span></span>
<span id="cb8-31"><a href="#cb8-31" aria-hidden="true" tabindex="-1"></a>                    <span class="kw">this</span><span class="op">.</span><span class="at">inputVector</span><span class="op">.</span><span class="at">z</span> <span class="op">=</span> <span class="dv">1</span><span class="op">;</span></span>
<span id="cb8-32"><a href="#cb8-32" aria-hidden="true" tabindex="-1"></a>                    <span class="cf">break</span><span class="op">;</span></span>
<span id="cb8-33"><a href="#cb8-33" aria-hidden="true" tabindex="-1"></a>                <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">KEY_S</span><span class="op">:</span></span>
<span id="cb8-34"><a href="#cb8-34" aria-hidden="true" tabindex="-1"></a>                    <span class="kw">this</span><span class="op">.</span><span class="at">inputVector</span><span class="op">.</span><span class="at">z</span> <span class="op">=</span> <span class="op">-</span><span class="dv">1</span><span class="op">;</span></span>
<span id="cb8-35"><a href="#cb8-35" aria-hidden="true" tabindex="-1"></a>                    <span class="cf">break</span><span class="op">;</span></span>
<span id="cb8-36"><a href="#cb8-36" aria-hidden="true" tabindex="-1"></a>                <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">KEY_A</span><span class="op">:</span></span>
<span id="cb8-37"><a href="#cb8-37" aria-hidden="true" tabindex="-1"></a>                    <span class="kw">this</span><span class="op">.</span><span class="at">inputVector</span><span class="op">.</span><span class="at">x</span> <span class="op">=</span> <span class="op">-</span><span class="dv">1</span><span class="op">;</span></span>
<span id="cb8-38"><a href="#cb8-38" aria-hidden="true" tabindex="-1"></a>                    <span class="cf">break</span><span class="op">;</span></span>
<span id="cb8-39"><a href="#cb8-39" aria-hidden="true" tabindex="-1"></a>                <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">KEY_D</span><span class="op">:</span></span>
<span id="cb8-40"><a href="#cb8-40" aria-hidden="true" tabindex="-1"></a>                    <span class="kw">this</span><span class="op">.</span><span class="at">inputVector</span><span class="op">.</span><span class="at">x</span> <span class="op">=</span> <span class="dv">1</span><span class="op">;</span></span>
<span id="cb8-41"><a href="#cb8-41" aria-hidden="true" tabindex="-1"></a>                    <span class="cf">break</span><span class="op">;</span></span>
<span id="cb8-42"><a href="#cb8-42" aria-hidden="true" tabindex="-1"></a>            }</span>
<span id="cb8-43"><a href="#cb8-43" aria-hidden="true" tabindex="-1"></a>        }<span class="op">,</span> <span class="kw">this</span>)<span class="op">;</span></span>
<span id="cb8-44"><a href="#cb8-44" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb8-45"><a href="#cb8-45" aria-hidden="true" tabindex="-1"></a>        input<span class="op">.</span><span class="fu">on</span>(Input<span class="op">.</span><span class="at">EventType</span><span class="op">.</span><span class="at">KEY_UP</span><span class="op">,</span> (event<span class="op">:</span> EventKeyboard) <span class="kw">=&gt;</span> {</span>
<span id="cb8-46"><a href="#cb8-46" aria-hidden="true" tabindex="-1"></a>            <span class="cf">switch</span> (<span class="bu">event</span><span class="op">.</span><span class="at">keyCode</span>) {</span>
<span id="cb8-47"><a href="#cb8-47" aria-hidden="true" tabindex="-1"></a>                <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">KEY_W</span><span class="op">:</span></span>
<span id="cb8-48"><a href="#cb8-48" aria-hidden="true" tabindex="-1"></a>                <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">KEY_S</span><span class="op">:</span></span>
<span id="cb8-49"><a href="#cb8-49" aria-hidden="true" tabindex="-1"></a>                    <span class="kw">this</span><span class="op">.</span><span class="at">inputVector</span><span class="op">.</span><span class="at">z</span> <span class="op">=</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb8-50"><a href="#cb8-50" aria-hidden="true" tabindex="-1"></a>                    <span class="cf">break</span><span class="op">;</span></span>
<span id="cb8-51"><a href="#cb8-51" aria-hidden="true" tabindex="-1"></a>                <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">KEY_A</span><span class="op">:</span></span>
<span id="cb8-52"><a href="#cb8-52" aria-hidden="true" tabindex="-1"></a>                <span class="cf">case</span> KeyCode<span class="op">.</span><span class="at">KEY_D</span><span class="op">:</span></span>
<span id="cb8-53"><a href="#cb8-53" aria-hidden="true" tabindex="-1"></a>                    <span class="kw">this</span><span class="op">.</span><span class="at">inputVector</span><span class="op">.</span><span class="at">x</span> <span class="op">=</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb8-54"><a href="#cb8-54" aria-hidden="true" tabindex="-1"></a>                    <span class="cf">break</span><span class="op">;</span></span>
<span id="cb8-55"><a href="#cb8-55" aria-hidden="true" tabindex="-1"></a>            }</span>
<span id="cb8-56"><a href="#cb8-56" aria-hidden="true" tabindex="-1"></a>        }<span class="op">,</span> <span class="kw">this</span>)<span class="op">;</span></span>
<span id="cb8-57"><a href="#cb8-57" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb8-58"><a href="#cb8-58" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-59"><a href="#cb8-59" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">handleMovement</span>(dt<span class="op">:</span> <span class="dt">number</span>) {</span>
<span id="cb8-60"><a href="#cb8-60" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="kw">this</span><span class="op">.</span><span class="at">inputVector</span><span class="op">.</span><span class="fu">lengthSqr</span>() <span class="op">&gt;</span> <span class="dv">0</span>) {</span>
<span id="cb8-61"><a href="#cb8-61" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 标准化输入向量</span></span>
<span id="cb8-62"><a href="#cb8-62" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="at">inputVector</span><span class="op">.</span><span class="fu">normalize</span>()<span class="op">;</span></span>
<span id="cb8-63"><a href="#cb8-63" aria-hidden="true" tabindex="-1"></a>            </span>
<span id="cb8-64"><a href="#cb8-64" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 计算移动向量</span></span>
<span id="cb8-65"><a href="#cb8-65" aria-hidden="true" tabindex="-1"></a>            <span class="kw">const</span> forward <span class="op">=</span> <span class="kw">new</span> <span class="fu">Vec3</span>()<span class="op">;</span></span>
<span id="cb8-66"><a href="#cb8-66" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="at">node</span><span class="op">.</span><span class="fu">getForward</span>(forward)<span class="op">;</span></span>
<span id="cb8-67"><a href="#cb8-67" aria-hidden="true" tabindex="-1"></a>            <span class="kw">const</span> right <span class="op">=</span> <span class="kw">new</span> <span class="fu">Vec3</span>()<span class="op">;</span></span>
<span id="cb8-68"><a href="#cb8-68" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="at">node</span><span class="op">.</span><span class="fu">getRight</span>(right)<span class="op">;</span></span>
<span id="cb8-69"><a href="#cb8-69" aria-hidden="true" tabindex="-1"></a>            </span>
<span id="cb8-70"><a href="#cb8-70" aria-hidden="true" tabindex="-1"></a>            <span class="kw">const</span> moveVector <span class="op">=</span> <span class="kw">new</span> <span class="fu">Vec3</span>()<span class="op">;</span></span>
<span id="cb8-71"><a href="#cb8-71" aria-hidden="true" tabindex="-1"></a>            Vec3<span class="op">.</span><span class="fu">scaleAndAdd</span>(moveVector<span class="op">,</span> moveVector<span class="op">,</span> forward<span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">inputVector</span><span class="op">.</span><span class="at">z</span>)<span class="op">;</span></span>
<span id="cb8-72"><a href="#cb8-72" aria-hidden="true" tabindex="-1"></a>            Vec3<span class="op">.</span><span class="fu">scaleAndAdd</span>(moveVector<span class="op">,</span> moveVector<span class="op">,</span> right<span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">inputVector</span><span class="op">.</span><span class="at">x</span>)<span class="op">;</span></span>
<span id="cb8-73"><a href="#cb8-73" aria-hidden="true" tabindex="-1"></a>            </span>
<span id="cb8-74"><a href="#cb8-74" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 应用移动</span></span>
<span id="cb8-75"><a href="#cb8-75" aria-hidden="true" tabindex="-1"></a>            Vec3<span class="op">.</span><span class="fu">scaleAndAdd</span>(moveVector<span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">node</span><span class="op">.</span><span class="at">position</span><span class="op">,</span> moveVector<span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">moveSpeed</span> <span class="op">*</span> dt)<span class="op">;</span></span>
<span id="cb8-76"><a href="#cb8-76" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="at">node</span><span class="op">.</span><span class="fu">setPosition</span>(moveVector)<span class="op">;</span></span>
<span id="cb8-77"><a href="#cb8-77" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb8-78"><a href="#cb8-78" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb8-79"><a href="#cb8-79" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-80"><a href="#cb8-80" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">handleRotation</span>(dt<span class="op">:</span> <span class="dt">number</span>) {</span>
<span id="cb8-81"><a href="#cb8-81" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 鼠标控制旋转（简化实现）</span></span>
<span id="cb8-82"><a href="#cb8-82" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 实际项目中需要处理鼠标输入事件</span></span>
<span id="cb8-83"><a href="#cb8-83" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb8-84"><a href="#cb8-84" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<h2 id="脚本编程">脚本编程</h2>
<h3 id="组件生命周期">组件生命周期</h3>
<div class="sourceCode" id="cb9"><pre
class="sourceCode typescript"><code class="sourceCode typescript"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> { _decorator<span class="op">,</span> Component } <span class="im">from</span> <span class="st">&#39;cc&#39;</span><span class="op">;</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a><span class="kw">const</span> { ccclass } <span class="op">=</span> _decorator<span class="op">;</span></span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a>@<span class="fu">ccclass</span>(<span class="st">&#39;LifecycleExample&#39;</span>)</span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> LifecycleExample <span class="kw">extends</span> Component {</span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 组件被创建时调用</span></span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a>    <span class="fu">onLoad</span>() {</span>
<span id="cb9-8"><a href="#cb9-8" aria-hidden="true" tabindex="-1"></a>        <span class="bu">console</span><span class="op">.</span><span class="fu">log</span>(<span class="st">&#39;onLoad: 组件初始化&#39;</span>)<span class="op">;</span></span>
<span id="cb9-9"><a href="#cb9-9" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 初始化组件数据</span></span>
<span id="cb9-10"><a href="#cb9-10" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb9-11"><a href="#cb9-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-12"><a href="#cb9-12" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 组件第一次激活时调用</span></span>
<span id="cb9-13"><a href="#cb9-13" aria-hidden="true" tabindex="-1"></a>    <span class="fu">start</span>() {</span>
<span id="cb9-14"><a href="#cb9-14" aria-hidden="true" tabindex="-1"></a>        <span class="bu">console</span><span class="op">.</span><span class="fu">log</span>(<span class="st">&#39;start: 组件开始运行&#39;</span>)<span class="op">;</span></span>
<span id="cb9-15"><a href="#cb9-15" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 开始游戏逻辑</span></span>
<span id="cb9-16"><a href="#cb9-16" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb9-17"><a href="#cb9-17" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-18"><a href="#cb9-18" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 每帧调用</span></span>
<span id="cb9-19"><a href="#cb9-19" aria-hidden="true" tabindex="-1"></a>    <span class="fu">update</span>(deltaTime<span class="op">:</span> <span class="dt">number</span>) {</span>
<span id="cb9-20"><a href="#cb9-20" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 游戏逻辑更新</span></span>
<span id="cb9-21"><a href="#cb9-21" aria-hidden="true" tabindex="-1"></a>        <span class="co">// deltaTime: 距离上一帧的时间间隔</span></span>
<span id="cb9-22"><a href="#cb9-22" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb9-23"><a href="#cb9-23" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-24"><a href="#cb9-24" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 固定时间间隔调用（用于物理计算）</span></span>
<span id="cb9-25"><a href="#cb9-25" aria-hidden="true" tabindex="-1"></a>    <span class="fu">lateUpdate</span>(deltaTime<span class="op">:</span> <span class="dt">number</span>) {</span>
<span id="cb9-26"><a href="#cb9-26" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 在所有 update 之后调用</span></span>
<span id="cb9-27"><a href="#cb9-27" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 通常用于相机跟随等逻辑</span></span>
<span id="cb9-28"><a href="#cb9-28" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb9-29"><a href="#cb9-29" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-30"><a href="#cb9-30" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 组件被激活时调用</span></span>
<span id="cb9-31"><a href="#cb9-31" aria-hidden="true" tabindex="-1"></a>    <span class="fu">onEnable</span>() {</span>
<span id="cb9-32"><a href="#cb9-32" aria-hidden="true" tabindex="-1"></a>        <span class="bu">console</span><span class="op">.</span><span class="fu">log</span>(<span class="st">&#39;onEnable: 组件被激活&#39;</span>)<span class="op">;</span></span>
<span id="cb9-33"><a href="#cb9-33" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb9-34"><a href="#cb9-34" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-35"><a href="#cb9-35" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 组件被禁用时调用</span></span>
<span id="cb9-36"><a href="#cb9-36" aria-hidden="true" tabindex="-1"></a>    <span class="fu">onDisable</span>() {</span>
<span id="cb9-37"><a href="#cb9-37" aria-hidden="true" tabindex="-1"></a>        <span class="bu">console</span><span class="op">.</span><span class="fu">log</span>(<span class="st">&#39;onDisable: 组件被禁用&#39;</span>)<span class="op">;</span></span>
<span id="cb9-38"><a href="#cb9-38" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb9-39"><a href="#cb9-39" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-40"><a href="#cb9-40" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 组件被销毁时调用</span></span>
<span id="cb9-41"><a href="#cb9-41" aria-hidden="true" tabindex="-1"></a>    <span class="fu">onDestroy</span>() {</span>
<span id="cb9-42"><a href="#cb9-42" aria-hidden="true" tabindex="-1"></a>        <span class="bu">console</span><span class="op">.</span><span class="fu">log</span>(<span class="st">&#39;onDestroy: 组件被销毁&#39;</span>)<span class="op">;</span></span>
<span id="cb9-43"><a href="#cb9-43" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 清理资源，移除事件监听</span></span>
<span id="cb9-44"><a href="#cb9-44" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb9-45"><a href="#cb9-45" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<h3 id="事件系统">事件系统</h3>
<div class="sourceCode" id="cb10"><pre
class="sourceCode typescript"><code class="sourceCode typescript"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> { _decorator<span class="op">,</span> Component<span class="op">,</span> <span class="bu">EventTarget</span> } <span class="im">from</span> <span class="st">&#39;cc&#39;</span><span class="op">;</span></span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a><span class="kw">const</span> { ccclass } <span class="op">=</span> _decorator<span class="op">;</span></span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a><span class="co">// 自定义事件管理器</span></span>
<span id="cb10-5"><a href="#cb10-5" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> GameEventManager {</span>
<span id="cb10-6"><a href="#cb10-6" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="kw">static</span> instance<span class="op">:</span> GameEventManager<span class="op">;</span></span>
<span id="cb10-7"><a href="#cb10-7" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> eventTarget<span class="op">:</span> <span class="bu">EventTarget</span><span class="op">;</span></span>
<span id="cb10-8"><a href="#cb10-8" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-9"><a href="#cb10-9" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="kw">constructor</span>() {</span>
<span id="cb10-10"><a href="#cb10-10" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">eventTarget</span> <span class="op">=</span> <span class="kw">new</span> <span class="bu">EventTarget</span>()<span class="op">;</span></span>
<span id="cb10-11"><a href="#cb10-11" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-12"><a href="#cb10-12" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-13"><a href="#cb10-13" aria-hidden="true" tabindex="-1"></a>    <span class="kw">static</span> <span class="fu">getInstance</span>()<span class="op">:</span> GameEventManager {</span>
<span id="cb10-14"><a href="#cb10-14" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="op">!</span>GameEventManager<span class="op">.</span><span class="at">instance</span>) {</span>
<span id="cb10-15"><a href="#cb10-15" aria-hidden="true" tabindex="-1"></a>            GameEventManager<span class="op">.</span><span class="at">instance</span> <span class="op">=</span> <span class="kw">new</span> <span class="fu">GameEventManager</span>()<span class="op">;</span></span>
<span id="cb10-16"><a href="#cb10-16" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb10-17"><a href="#cb10-17" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> GameEventManager<span class="op">.</span><span class="at">instance</span><span class="op">;</span></span>
<span id="cb10-18"><a href="#cb10-18" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-19"><a href="#cb10-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-20"><a href="#cb10-20" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 发送事件</span></span>
<span id="cb10-21"><a href="#cb10-21" aria-hidden="true" tabindex="-1"></a>    <span class="fu">emit</span>(eventName<span class="op">:</span> <span class="dt">string</span><span class="op">,</span> <span class="op">...</span>args<span class="op">:</span> <span class="dt">any</span>[]) {</span>
<span id="cb10-22"><a href="#cb10-22" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">eventTarget</span><span class="op">.</span><span class="fu">emit</span>(eventName<span class="op">,</span> <span class="op">...</span>args)<span class="op">;</span></span>
<span id="cb10-23"><a href="#cb10-23" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-24"><a href="#cb10-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-25"><a href="#cb10-25" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 监听事件</span></span>
<span id="cb10-26"><a href="#cb10-26" aria-hidden="true" tabindex="-1"></a>    <span class="fu">on</span>(eventName<span class="op">:</span> <span class="dt">string</span><span class="op">,</span> callback<span class="op">:</span> <span class="bu">Function</span><span class="op">,</span> target<span class="op">?:</span> <span class="dt">any</span>) {</span>
<span id="cb10-27"><a href="#cb10-27" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">eventTarget</span><span class="op">.</span><span class="fu">on</span>(eventName<span class="op">,</span> callback<span class="op">,</span> target)<span class="op">;</span></span>
<span id="cb10-28"><a href="#cb10-28" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-29"><a href="#cb10-29" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-30"><a href="#cb10-30" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 移除事件监听</span></span>
<span id="cb10-31"><a href="#cb10-31" aria-hidden="true" tabindex="-1"></a>    <span class="fu">off</span>(eventName<span class="op">:</span> <span class="dt">string</span><span class="op">,</span> callback<span class="op">?:</span> <span class="bu">Function</span><span class="op">,</span> target<span class="op">?:</span> <span class="dt">any</span>) {</span>
<span id="cb10-32"><a href="#cb10-32" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">eventTarget</span><span class="op">.</span><span class="fu">off</span>(eventName<span class="op">,</span> callback<span class="op">,</span> target)<span class="op">;</span></span>
<span id="cb10-33"><a href="#cb10-33" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-34"><a href="#cb10-34" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-35"><a href="#cb10-35" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 监听一次事件</span></span>
<span id="cb10-36"><a href="#cb10-36" aria-hidden="true" tabindex="-1"></a>    <span class="fu">once</span>(eventName<span class="op">:</span> <span class="dt">string</span><span class="op">,</span> callback<span class="op">:</span> <span class="bu">Function</span><span class="op">,</span> target<span class="op">?:</span> <span class="dt">any</span>) {</span>
<span id="cb10-37"><a href="#cb10-37" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">eventTarget</span><span class="op">.</span><span class="fu">once</span>(eventName<span class="op">,</span> callback<span class="op">,</span> target)<span class="op">;</span></span>
<span id="cb10-38"><a href="#cb10-38" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-39"><a href="#cb10-39" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb10-40"><a href="#cb10-40" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-41"><a href="#cb10-41" aria-hidden="true" tabindex="-1"></a><span class="co">// 使用示例</span></span>
<span id="cb10-42"><a href="#cb10-42" aria-hidden="true" tabindex="-1"></a>@<span class="fu">ccclass</span>(<span class="st">&#39;EventExample&#39;</span>)</span>
<span id="cb10-43"><a href="#cb10-43" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> EventExample <span class="kw">extends</span> Component {</span>
<span id="cb10-44"><a href="#cb10-44" aria-hidden="true" tabindex="-1"></a>    <span class="fu">start</span>() {</span>
<span id="cb10-45"><a href="#cb10-45" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> eventManager <span class="op">=</span> GameEventManager<span class="op">.</span><span class="fu">getInstance</span>()<span class="op">;</span></span>
<span id="cb10-46"><a href="#cb10-46" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb10-47"><a href="#cb10-47" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 监听事件</span></span>
<span id="cb10-48"><a href="#cb10-48" aria-hidden="true" tabindex="-1"></a>        eventManager<span class="op">.</span><span class="fu">on</span>(<span class="st">&#39;player-score&#39;</span><span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">onPlayerScore</span><span class="op">,</span> <span class="kw">this</span>)<span class="op">;</span></span>
<span id="cb10-49"><a href="#cb10-49" aria-hidden="true" tabindex="-1"></a>        eventManager<span class="op">.</span><span class="fu">on</span>(<span class="st">&#39;game-over&#39;</span><span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">onGameOver</span><span class="op">,</span> <span class="kw">this</span>)<span class="op">;</span></span>
<span id="cb10-50"><a href="#cb10-50" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-51"><a href="#cb10-51" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-52"><a href="#cb10-52" aria-hidden="true" tabindex="-1"></a>    <span class="fu">onDestroy</span>() {</span>
<span id="cb10-53"><a href="#cb10-53" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> eventManager <span class="op">=</span> GameEventManager<span class="op">.</span><span class="fu">getInstance</span>()<span class="op">;</span></span>
<span id="cb10-54"><a href="#cb10-54" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb10-55"><a href="#cb10-55" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 移除事件监听</span></span>
<span id="cb10-56"><a href="#cb10-56" aria-hidden="true" tabindex="-1"></a>        eventManager<span class="op">.</span><span class="fu">off</span>(<span class="st">&#39;player-score&#39;</span><span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">onPlayerScore</span><span class="op">,</span> <span class="kw">this</span>)<span class="op">;</span></span>
<span id="cb10-57"><a href="#cb10-57" aria-hidden="true" tabindex="-1"></a>        eventManager<span class="op">.</span><span class="fu">off</span>(<span class="st">&#39;game-over&#39;</span><span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">onGameOver</span><span class="op">,</span> <span class="kw">this</span>)<span class="op">;</span></span>
<span id="cb10-58"><a href="#cb10-58" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-59"><a href="#cb10-59" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-60"><a href="#cb10-60" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">onPlayerScore</span>(score<span class="op">:</span> <span class="dt">number</span>) {</span>
<span id="cb10-61"><a href="#cb10-61" aria-hidden="true" tabindex="-1"></a>        <span class="bu">console</span><span class="op">.</span><span class="fu">log</span>(<span class="st">&#39;玩家得分:&#39;</span><span class="op">,</span> score)<span class="op">;</span></span>
<span id="cb10-62"><a href="#cb10-62" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-63"><a href="#cb10-63" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-64"><a href="#cb10-64" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">onGameOver</span>() {</span>
<span id="cb10-65"><a href="#cb10-65" aria-hidden="true" tabindex="-1"></a>        <span class="bu">console</span><span class="op">.</span><span class="fu">log</span>(<span class="st">&#39;游戏结束&#39;</span>)<span class="op">;</span></span>
<span id="cb10-66"><a href="#cb10-66" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-67"><a href="#cb10-67" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-68"><a href="#cb10-68" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 触发事件</span></span>
<span id="cb10-69"><a href="#cb10-69" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="fu">triggerScore</span>() {</span>
<span id="cb10-70"><a href="#cb10-70" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> eventManager <span class="op">=</span> GameEventManager<span class="op">.</span><span class="fu">getInstance</span>()<span class="op">;</span></span>
<span id="cb10-71"><a href="#cb10-71" aria-hidden="true" tabindex="-1"></a>        eventManager<span class="op">.</span><span class="fu">emit</span>(<span class="st">&#39;player-score&#39;</span><span class="op">,</span> <span class="dv">100</span>)<span class="op">;</span></span>
<span id="cb10-72"><a href="#cb10-72" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb10-73"><a href="#cb10-73" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<h2 id="移动端优化">移动端优化</h2>
<h3 id="性能优化">性能优化</h3>
<div class="sourceCode" id="cb11"><pre
class="sourceCode typescript"><code class="sourceCode typescript"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 对象池管理</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> ObjectPool<span class="op">&lt;</span>T<span class="op">&gt;</span> {</span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> pool<span class="op">:</span> T[] <span class="op">=</span> []<span class="op">;</span></span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> createFunc<span class="op">:</span> () <span class="kw">=&gt;</span> T<span class="op">;</span></span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> resetFunc<span class="op">?:</span> (obj<span class="op">:</span> T) <span class="kw">=&gt;</span> <span class="dt">void</span><span class="op">;</span></span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-7"><a href="#cb11-7" aria-hidden="true" tabindex="-1"></a>    <span class="kw">constructor</span>(createFunc<span class="op">:</span> () <span class="kw">=&gt;</span> T<span class="op">,</span> resetFunc<span class="op">?:</span> (obj<span class="op">:</span> T) <span class="kw">=&gt;</span> <span class="dt">void</span><span class="op">,</span> initialSize<span class="op">:</span> <span class="dt">number</span> <span class="op">=</span> <span class="dv">10</span>) {</span>
<span id="cb11-8"><a href="#cb11-8" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">createFunc</span> <span class="op">=</span> createFunc<span class="op">;</span></span>
<span id="cb11-9"><a href="#cb11-9" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">resetFunc</span> <span class="op">=</span> resetFunc<span class="op">;</span></span>
<span id="cb11-10"><a href="#cb11-10" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb11-11"><a href="#cb11-11" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 预创建对象</span></span>
<span id="cb11-12"><a href="#cb11-12" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> (<span class="kw">let</span> i <span class="op">=</span> <span class="dv">0</span><span class="op">;</span> i <span class="op">&lt;</span> initialSize<span class="op">;</span> i<span class="op">++</span>) {</span>
<span id="cb11-13"><a href="#cb11-13" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="at">pool</span><span class="op">.</span><span class="fu">push</span>(<span class="kw">this</span><span class="op">.</span><span class="fu">createFunc</span>())<span class="op">;</span></span>
<span id="cb11-14"><a href="#cb11-14" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb11-15"><a href="#cb11-15" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb11-16"><a href="#cb11-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-17"><a href="#cb11-17" aria-hidden="true" tabindex="-1"></a>    <span class="kw">get</span>()<span class="op">:</span> T {</span>
<span id="cb11-18"><a href="#cb11-18" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="kw">this</span><span class="op">.</span><span class="at">pool</span><span class="op">.</span><span class="at">length</span> <span class="op">&gt;</span> <span class="dv">0</span>) {</span>
<span id="cb11-19"><a href="#cb11-19" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> <span class="kw">this</span><span class="op">.</span><span class="at">pool</span><span class="op">.</span><span class="fu">pop</span>()<span class="op">!;</span></span>
<span id="cb11-20"><a href="#cb11-20" aria-hidden="true" tabindex="-1"></a>        } <span class="cf">else</span> {</span>
<span id="cb11-21"><a href="#cb11-21" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> <span class="kw">this</span><span class="op">.</span><span class="fu">createFunc</span>()<span class="op">;</span></span>
<span id="cb11-22"><a href="#cb11-22" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb11-23"><a href="#cb11-23" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb11-24"><a href="#cb11-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-25"><a href="#cb11-25" aria-hidden="true" tabindex="-1"></a>    <span class="fu">put</span>(obj<span class="op">:</span> T) {</span>
<span id="cb11-26"><a href="#cb11-26" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="kw">this</span><span class="op">.</span><span class="at">resetFunc</span>) {</span>
<span id="cb11-27"><a href="#cb11-27" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="fu">resetFunc</span>(obj)<span class="op">;</span></span>
<span id="cb11-28"><a href="#cb11-28" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb11-29"><a href="#cb11-29" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">pool</span><span class="op">.</span><span class="fu">push</span>(obj)<span class="op">;</span></span>
<span id="cb11-30"><a href="#cb11-30" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb11-31"><a href="#cb11-31" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-32"><a href="#cb11-32" aria-hidden="true" tabindex="-1"></a>    <span class="fu">clear</span>() {</span>
<span id="cb11-33"><a href="#cb11-33" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">pool</span><span class="op">.</span><span class="at">length</span> <span class="op">=</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb11-34"><a href="#cb11-34" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb11-35"><a href="#cb11-35" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb11-36"><a href="#cb11-36" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-37"><a href="#cb11-37" aria-hidden="true" tabindex="-1"></a><span class="co">// 使用示例</span></span>
<span id="cb11-38"><a href="#cb11-38" aria-hidden="true" tabindex="-1"></a>@<span class="fu">ccclass</span>(<span class="st">&#39;BulletManager&#39;</span>)</span>
<span id="cb11-39"><a href="#cb11-39" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> BulletManager <span class="kw">extends</span> Component {</span>
<span id="cb11-40"><a href="#cb11-40" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> bulletPool<span class="op">:</span> ObjectPool<span class="op">&lt;</span><span class="bu">Node</span><span class="op">&gt;;</span></span>
<span id="cb11-41"><a href="#cb11-41" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-42"><a href="#cb11-42" aria-hidden="true" tabindex="-1"></a>    <span class="fu">start</span>() {</span>
<span id="cb11-43"><a href="#cb11-43" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建子弹对象池</span></span>
<span id="cb11-44"><a href="#cb11-44" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">bulletPool</span> <span class="op">=</span> <span class="kw">new</span> <span class="fu">ObjectPool</span><span class="op">&lt;</span><span class="bu">Node</span><span class="op">&gt;</span>(</span>
<span id="cb11-45"><a href="#cb11-45" aria-hidden="true" tabindex="-1"></a>            () <span class="kw">=&gt;</span> {</span>
<span id="cb11-46"><a href="#cb11-46" aria-hidden="true" tabindex="-1"></a>                <span class="co">// 创建子弹节点</span></span>
<span id="cb11-47"><a href="#cb11-47" aria-hidden="true" tabindex="-1"></a>                <span class="kw">const</span> bullet <span class="op">=</span> <span class="kw">new</span> <span class="bu">Node</span>(<span class="st">&#39;Bullet&#39;</span>)<span class="op">;</span></span>
<span id="cb11-48"><a href="#cb11-48" aria-hidden="true" tabindex="-1"></a>                <span class="co">// 添加必要的组件</span></span>
<span id="cb11-49"><a href="#cb11-49" aria-hidden="true" tabindex="-1"></a>                <span class="cf">return</span> bullet<span class="op">;</span></span>
<span id="cb11-50"><a href="#cb11-50" aria-hidden="true" tabindex="-1"></a>            }<span class="op">,</span></span>
<span id="cb11-51"><a href="#cb11-51" aria-hidden="true" tabindex="-1"></a>            (bullet) <span class="kw">=&gt;</span> {</span>
<span id="cb11-52"><a href="#cb11-52" aria-hidden="true" tabindex="-1"></a>                <span class="co">// 重置子弹状态</span></span>
<span id="cb11-53"><a href="#cb11-53" aria-hidden="true" tabindex="-1"></a>                bullet<span class="op">.</span><span class="at">active</span> <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb11-54"><a href="#cb11-54" aria-hidden="true" tabindex="-1"></a>                bullet<span class="op">.</span><span class="fu">setPosition</span>(<span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="dv">0</span>)<span class="op">;</span></span>
<span id="cb11-55"><a href="#cb11-55" aria-hidden="true" tabindex="-1"></a>            }<span class="op">,</span></span>
<span id="cb11-56"><a href="#cb11-56" aria-hidden="true" tabindex="-1"></a>            <span class="dv">20</span> <span class="co">// 初始池大小</span></span>
<span id="cb11-57"><a href="#cb11-57" aria-hidden="true" tabindex="-1"></a>        )<span class="op">;</span></span>
<span id="cb11-58"><a href="#cb11-58" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb11-59"><a href="#cb11-59" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-60"><a href="#cb11-60" aria-hidden="true" tabindex="-1"></a>    <span class="fu">createBullet</span>(position<span class="op">:</span> Vec3)<span class="op">:</span> <span class="bu">Node</span> {</span>
<span id="cb11-61"><a href="#cb11-61" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> bullet <span class="op">=</span> <span class="kw">this</span><span class="op">.</span><span class="at">bulletPool</span><span class="op">.</span><span class="fu">get</span>()<span class="op">;</span></span>
<span id="cb11-62"><a href="#cb11-62" aria-hidden="true" tabindex="-1"></a>        bullet<span class="op">.</span><span class="fu">setPosition</span>(position)<span class="op">;</span></span>
<span id="cb11-63"><a href="#cb11-63" aria-hidden="true" tabindex="-1"></a>        bullet<span class="op">.</span><span class="at">active</span> <span class="op">=</span> <span class="kw">true</span><span class="op">;</span></span>
<span id="cb11-64"><a href="#cb11-64" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> bullet<span class="op">;</span></span>
<span id="cb11-65"><a href="#cb11-65" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb11-66"><a href="#cb11-66" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-67"><a href="#cb11-67" aria-hidden="true" tabindex="-1"></a>    <span class="fu">destroyBullet</span>(bullet<span class="op">:</span> <span class="bu">Node</span>) {</span>
<span id="cb11-68"><a href="#cb11-68" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">bulletPool</span><span class="op">.</span><span class="fu">put</span>(bullet)<span class="op">;</span></span>
<span id="cb11-69"><a href="#cb11-69" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb11-70"><a href="#cb11-70" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<h2 id="最佳实践">最佳实践</h2>
<h3 id="项目结构">1. 项目结构</h3>
<pre><code>assets/
├── scenes/              # 场景文件
├── scripts/             # 脚本文件
│   ├── components/      # 组件脚本
│   ├── managers/        # 管理器脚本
│   ├── utils/          # 工具脚本
│   └── data/           # 数据脚本
├── resources/          # 动态加载资源
├── textures/           # 纹理资源
├── audio/              # 音频资源
├── prefabs/            # 预制体
└── animations/         # 动画资源</code></pre>
<h3 id="代码规范">2. 代码规范</h3>
<div class="sourceCode" id="cb13"><pre
class="sourceCode typescript"><code class="sourceCode typescript"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 使用 TypeScript 严格模式</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a><span class="co">// 组件命名使用 PascalCase</span></span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a><span class="co">// 属性和方法使用 camelCase</span></span>
<span id="cb13-4"><a href="#cb13-4" aria-hidden="true" tabindex="-1"></a><span class="co">// 常量使用 UPPER_SNAKE_CASE</span></span>
<span id="cb13-5"><a href="#cb13-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-6"><a href="#cb13-6" aria-hidden="true" tabindex="-1"></a>@<span class="fu">ccclass</span>(<span class="st">&#39;PlayerController&#39;</span>)</span>
<span id="cb13-7"><a href="#cb13-7" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> PlayerController <span class="kw">extends</span> Component {</span>
<span id="cb13-8"><a href="#cb13-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 公共属性使用 @property 装饰器</span></span>
<span id="cb13-9"><a href="#cb13-9" aria-hidden="true" tabindex="-1"></a>    @property</span>
<span id="cb13-10"><a href="#cb13-10" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> moveSpeed<span class="op">:</span> <span class="dt">number</span> <span class="op">=</span> <span class="dv">100</span><span class="op">;</span></span>
<span id="cb13-11"><a href="#cb13-11" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-12"><a href="#cb13-12" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 私有属性使用 private</span></span>
<span id="cb13-13"><a href="#cb13-13" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> currentHealth<span class="op">:</span> <span class="dt">number</span> <span class="op">=</span> <span class="dv">100</span><span class="op">;</span></span>
<span id="cb13-14"><a href="#cb13-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-15"><a href="#cb13-15" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 常量</span></span>
<span id="cb13-16"><a href="#cb13-16" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="kw">static</span> <span class="kw">readonly</span> MAX_HEALTH<span class="op">:</span> <span class="dt">number</span> <span class="op">=</span> <span class="dv">100</span><span class="op">;</span></span>
<span id="cb13-17"><a href="#cb13-17" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-18"><a href="#cb13-18" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 方法命名清晰</span></span>
<span id="cb13-19"><a href="#cb13-19" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="fu">takeDamage</span>(damage<span class="op">:</span> <span class="dt">number</span>)<span class="op">:</span> <span class="dt">void</span> {</span>
<span id="cb13-20"><a href="#cb13-20" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">currentHealth</span> <span class="op">=</span> <span class="bu">Math</span><span class="op">.</span><span class="fu">max</span>(<span class="dv">0</span><span class="op">,</span> <span class="kw">this</span><span class="op">.</span><span class="at">currentHealth</span> <span class="op">-</span> damage)<span class="op">;</span></span>
<span id="cb13-21"><a href="#cb13-21" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb13-22"><a href="#cb13-22" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> (<span class="kw">this</span><span class="op">.</span><span class="at">currentHealth</span> <span class="op">&lt;=</span> <span class="dv">0</span>) {</span>
<span id="cb13-23"><a href="#cb13-23" aria-hidden="true" tabindex="-1"></a>            <span class="kw">this</span><span class="op">.</span><span class="fu">handleDeath</span>()<span class="op">;</span></span>
<span id="cb13-24"><a href="#cb13-24" aria-hidden="true" tabindex="-1"></a>        }</span>
<span id="cb13-25"><a href="#cb13-25" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb13-26"><a href="#cb13-26" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-27"><a href="#cb13-27" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="fu">handleDeath</span>()<span class="op">:</span> <span class="dt">void</span> {</span>
<span id="cb13-28"><a href="#cb13-28" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 处理死亡逻辑</span></span>
<span id="cb13-29"><a href="#cb13-29" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb13-30"><a href="#cb13-30" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<h3 id="内存管理">3. 内存管理</h3>
<div class="sourceCode" id="cb14"><pre
class="sourceCode typescript"><code class="sourceCode typescript"><span id="cb14-1"><a href="#cb14-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 及时释放资源</span></span>
<span id="cb14-2"><a href="#cb14-2" aria-hidden="true" tabindex="-1"></a><span class="im">export</span> <span class="kw">class</span> ResourceCleaner {</span>
<span id="cb14-3"><a href="#cb14-3" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> loadedTextures<span class="op">:</span> Texture2D[] <span class="op">=</span> []<span class="op">;</span></span>
<span id="cb14-4"><a href="#cb14-4" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb14-5"><a href="#cb14-5" aria-hidden="true" tabindex="-1"></a>    <span class="kw">async</span> <span class="fu">loadTexture</span>(path<span class="op">:</span> <span class="dt">string</span>)<span class="op">:</span> <span class="bu">Promise</span><span class="op">&lt;</span>Texture2D<span class="op">&gt;</span> {</span>
<span id="cb14-6"><a href="#cb14-6" aria-hidden="true" tabindex="-1"></a>        <span class="kw">const</span> texture <span class="op">=</span> <span class="cf">await</span> ResourceManager<span class="op">.</span><span class="fu">loadTexture</span>(path)<span class="op">;</span></span>
<span id="cb14-7"><a href="#cb14-7" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">loadedTextures</span><span class="op">.</span><span class="fu">push</span>(texture)<span class="op">;</span></span>
<span id="cb14-8"><a href="#cb14-8" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> texture<span class="op">;</span></span>
<span id="cb14-9"><a href="#cb14-9" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb14-10"><a href="#cb14-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb14-11"><a href="#cb14-11" aria-hidden="true" tabindex="-1"></a>    <span class="fu">cleanup</span>() {</span>
<span id="cb14-12"><a href="#cb14-12" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 释放所有加载的纹理</span></span>
<span id="cb14-13"><a href="#cb14-13" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">loadedTextures</span><span class="op">.</span><span class="fu">forEach</span>(texture <span class="kw">=&gt;</span> {</span>
<span id="cb14-14"><a href="#cb14-14" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> (texture <span class="op">&amp;&amp;</span> texture<span class="op">.</span><span class="at">isValid</span>) {</span>
<span id="cb14-15"><a href="#cb14-15" aria-hidden="true" tabindex="-1"></a>                texture<span class="op">.</span><span class="fu">destroy</span>()<span class="op">;</span></span>
<span id="cb14-16"><a href="#cb14-16" aria-hidden="true" tabindex="-1"></a>            }</span>
<span id="cb14-17"><a href="#cb14-17" aria-hidden="true" tabindex="-1"></a>        })<span class="op">;</span></span>
<span id="cb14-18"><a href="#cb14-18" aria-hidden="true" tabindex="-1"></a>        <span class="kw">this</span><span class="op">.</span><span class="at">loadedTextures</span><span class="op">.</span><span class="at">length</span> <span class="op">=</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb14-19"><a href="#cb14-19" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb14-20"><a href="#cb14-20" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<hr />
<p><em>Cocos Creator
以其轻量高效和强大的2D能力在移动游戏开发领域占据重要地位。更多详细信息请参考
<a href="https://docs.cocos.com/creator/manual/">Cocos Creator
官方文档</a></em></p>
</div><div lang="en" style="display: none;"><h1>[English translation pending]</h1><p>The content for this page has not been translated yet. Please check back later.</p></div>
                </main>
            </div>
        </section>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">Qt 3D 开发指南</h4>
                    <p data-i18n="footer-subtitle">专业的跨平台 3D 应用开发解决方案</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.</p>
            </div>
        </div>
    </footer>

    <script src="../../assets/js/prism.js"></script>
    <script src="../../assets/js/docs.js"></script>
</body>
</html> 