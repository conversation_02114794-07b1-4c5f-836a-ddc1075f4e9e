<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kanzi 引擎开发指南</title> <!-- The title will be replaced for each page -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/docs.css">
    <link rel="stylesheet" href="../../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html" class="logo-text" data-i18n="nav-logo-text">3D 引擎指南</a>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="../index.html#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="../index.html#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="../index.html#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 文档内容区域 -->
    <div class="doc-layout container">
        <!-- 左侧目录 -->
        <aside class="doc-sidebar card">
            <div class="toc-container">
                <!-- 目录内容将通过JavaScript动态生成 -->
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <section class="doc-section card">
            <div class="doc-container">
                <main class="doc-content">
                    <div lang="zh"><h1 id="kanzi-引擎开发指南">Kanzi 引擎开发指南</h1>
<h2 id="目录">目录</h2>
<ol type="1">
<li><a href="#概述">概述</a></li>
<li><a href="#技术特性">技术特性</a></li>
<li><a href="#开发环境搭建">开发环境搭建</a></li>
<li><a href="#核心概念">核心概念</a></li>
<li><a href="#hmi-开发">HMI 开发</a></li>
<li><a href="#2d3d-界面设计">2D/3D 界面设计</a></li>
<li><a href="#数据绑定与交互">数据绑定与交互</a></li>
<li><a href="#最佳实践">最佳实践</a></li>
</ol>
<h2 id="概述">概述</h2>
<p>Kanzi 是 Rightware 开发的专业
HMI（人机界面）开发平台，专门针对嵌入式系统和汽车行业设计。Kanzi
以其极低的资源占用、实时性能和符合汽车安全标准的特性，成为汽车仪表盘、工业控制和高端消费电子产品的首选
HMI 解决方案。</p>
<h3 id="主要优势">主要优势</h3>
<ul>
<li><strong>嵌入式优化</strong>：专为资源受限的嵌入式系统设计</li>
<li><strong>极低资源占用</strong>：最小化内存和 CPU 使用</li>
<li><strong>实时性能</strong>：保证实时响应和流畅动画</li>
<li><strong>汽车标准</strong>：符合 ISO 26262 等汽车安全标准</li>
<li><strong>专业工具链</strong>：完整的 HMI 设计和开发工具</li>
<li><strong>跨平台支持</strong>：支持多种嵌入式平台和操作系统</li>
</ul>
<h2 id="技术特性">技术特性</h2>
<h3 id="渲染引擎">渲染引擎</h3>
<ul>
<li><strong>GPU 加速渲染</strong>：充分利用硬件 GPU 加速</li>
<li><strong>矢量图形</strong>：高质量的 2D 矢量图形渲染</li>
<li><strong>3D 渲染</strong>：轻量级 3D 渲染能力</li>
<li><strong>多层合成</strong>：高效的图层合成系统</li>
<li><strong>抗锯齿</strong>：高质量的抗锯齿技术</li>
<li><strong>HDR 支持</strong>：高动态范围显示支持</li>
</ul>
<h3 id="hmi-框架">HMI 框架</h3>
<ul>
<li><strong>组件化架构</strong>：模块化的 UI 组件系统</li>
<li><strong>状态管理</strong>：强大的状态机和状态管理</li>
<li><strong>动画系统</strong>：流畅的 2D/3D 动画引擎</li>
<li><strong>触摸交互</strong>：多点触控和手势识别</li>
<li><strong>数据绑定</strong>：实时数据绑定和更新</li>
<li><strong>主题系统</strong>：灵活的主题和样式管理</li>
</ul>
<h3 id="平台支持">平台支持</h3>
<ul>
<li><strong>汽车平台</strong>：QNX、Linux、Android Automotive</li>
<li><strong>工业平台</strong>：VxWorks、Linux RT、Windows IoT</li>
<li><strong>移动平台</strong>：Android、iOS（有限支持）</li>
<li><strong>硬件平台</strong>：ARM、x86、PowerPC 等</li>
</ul>
<h2 id="开发环境搭建">开发环境搭建</h2>
<h3 id="系统要求">系统要求</h3>
<ul>
<li><strong>开发主机</strong>：Windows 10+, Ubuntu 18.04+, macOS
10.14+</li>
<li><strong>内存</strong>：最少 8GB RAM，推荐 16GB+</li>
<li><strong>存储</strong>：至少 10GB 可用空间</li>
<li><strong>显卡</strong>：支持 OpenGL ES 2.0+ 或 DirectX 11+</li>
</ul>
<h3 id="安装步骤">安装步骤</h3>
<h4 id="获取-kanzi-许可证">1. 获取 Kanzi 许可证</h4>
<div class="sourceCode" id="cb1"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 联系 Rightware 获取评估许可证</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="co"># https://www.rightware.com/kanzi/</span></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a><span class="co"># 商业许可证需要与销售团队联系</span></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a><span class="co"># 教育许可证可通过学术合作伙伴获取</span></span></code></pre></div>
<h4 id="安装-kanzi-studio">2. 安装 Kanzi Studio</h4>
<div class="sourceCode" id="cb2"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 下载 Kanzi Studio 安装包</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a><span class="co"># 运行安装程序</span></span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a><span class="co"># 输入许可证密钥</span></span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a><span class="co"># 选择安装组件：</span></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a><span class="co"># - Kanzi Studio (设计工具)</span></span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a><span class="co"># - Kanzi Engine (运行时)</span></span>
<span id="cb2-7"><a href="#cb2-7" aria-hidden="true" tabindex="-1"></a><span class="co"># - Platform SDKs (目标平台 SDK)</span></span></code></pre></div>
<h4 id="配置开发环境">3. 配置开发环境</h4>
<div class="sourceCode" id="cb3"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 设置环境变量</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a><span class="bu">export</span> <span class="va">KANZI_HOME</span><span class="op">=</span>/opt/kanzi</span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a><span class="bu">export</span> <span class="va">PATH</span><span class="op">=</span><span class="va">$KANZI_HOME</span>/bin:<span class="va">$PATH</span></span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a><span class="co"># 配置目标平台工具链</span></span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a><span class="co"># 例如：ARM 交叉编译工具链</span></span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a><span class="bu">export</span> <span class="va">CROSS_COMPILE</span><span class="op">=</span>arm-linux-gnueabihf-</span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a><span class="bu">export</span> <span class="va">CC</span><span class="op">=</span><span class="va">${CROSS_COMPILE}</span>gcc</span>
<span id="cb3-9"><a href="#cb3-9" aria-hidden="true" tabindex="-1"></a><span class="bu">export</span> <span class="va">CXX</span><span class="op">=</span><span class="va">${CROSS_COMPILE}</span>g++</span></code></pre></div>
<h2 id="核心概念">核心概念</h2>
<h3 id="node-层次结构">Node 层次结构</h3>
<div class="sourceCode" id="cb4"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Kanzi 中的基本节点类型</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a><span class="pp">#include </span><span class="im">&lt;kanzi/kanzi.hpp&gt;</span></span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> <span class="kw">namespace</span> kanzi<span class="op">;</span></span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a><span class="co">// 创建场景图</span></span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MyApplication <span class="op">:</span> <span class="kw">public</span> Application</span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> onConfigure<span class="op">(</span>ApplicationProperties<span class="op">&amp;</span> configuration<span class="op">)</span> <span class="kw">override</span></span>
<span id="cb4-11"><a href="#cb4-11" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-12"><a href="#cb4-12" aria-hidden="true" tabindex="-1"></a>        configuration<span class="op">.</span>binaryName <span class="op">=</span> <span class="st">&quot;MyKanziApp&quot;</span><span class="op">;</span></span>
<span id="cb4-13"><a href="#cb4-13" aria-hidden="true" tabindex="-1"></a>        configuration<span class="op">.</span>defaultWindowProperties<span class="op">.</span>width <span class="op">=</span> <span class="dv">1280</span><span class="op">;</span></span>
<span id="cb4-14"><a href="#cb4-14" aria-hidden="true" tabindex="-1"></a>        configuration<span class="op">.</span>defaultWindowProperties<span class="op">.</span>height <span class="op">=</span> <span class="dv">720</span><span class="op">;</span></span>
<span id="cb4-15"><a href="#cb4-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-16"><a href="#cb4-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-17"><a href="#cb4-17" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> onProjectLoaded<span class="op">()</span> <span class="kw">override</span></span>
<span id="cb4-18"><a href="#cb4-18" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-19"><a href="#cb4-19" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 获取根节点</span></span>
<span id="cb4-20"><a href="#cb4-20" aria-hidden="true" tabindex="-1"></a>        Node2DSharedPtr rootNode <span class="op">=</span> getScreen<span class="op">();</span></span>
<span id="cb4-21"><a href="#cb4-21" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb4-22"><a href="#cb4-22" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建子节点</span></span>
<span id="cb4-23"><a href="#cb4-23" aria-hidden="true" tabindex="-1"></a>        Node2DSharedPtr panel <span class="op">=</span> Node2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;MainPanel&quot;</span><span class="op">);</span></span>
<span id="cb4-24"><a href="#cb4-24" aria-hidden="true" tabindex="-1"></a>        rootNode<span class="op">-&gt;</span>addChild<span class="op">(</span>panel<span class="op">);</span></span>
<span id="cb4-25"><a href="#cb4-25" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb4-26"><a href="#cb4-26" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 设置节点属性</span></span>
<span id="cb4-27"><a href="#cb4-27" aria-hidden="true" tabindex="-1"></a>        panel<span class="op">-&gt;</span>setWidth<span class="op">(</span><span class="dv">400</span><span class="op">);</span></span>
<span id="cb4-28"><a href="#cb4-28" aria-hidden="true" tabindex="-1"></a>        panel<span class="op">-&gt;</span>setHeight<span class="op">(</span><span class="dv">300</span><span class="op">);</span></span>
<span id="cb4-29"><a href="#cb4-29" aria-hidden="true" tabindex="-1"></a>        panel<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">100</span><span class="op">,</span> <span class="dv">100</span><span class="op">));</span></span>
<span id="cb4-30"><a href="#cb4-30" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-31"><a href="#cb4-31" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb4-32"><a href="#cb4-32" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-33"><a href="#cb4-33" aria-hidden="true" tabindex="-1"></a><span class="co">// 应用程序入口点</span></span>
<span id="cb4-34"><a href="#cb4-34" aria-hidden="true" tabindex="-1"></a>Application<span class="op">*</span> createApplication<span class="op">()</span></span>
<span id="cb4-35"><a href="#cb4-35" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb4-36"><a href="#cb4-36" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> <span class="kw">new</span> MyApplication<span class="op">;</span></span>
<span id="cb4-37"><a href="#cb4-37" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="资源管理">资源管理</h3>
<div class="sourceCode" id="cb5"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 资源加载和管理</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> ResourceManager</span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> loadResources<span class="op">()</span></span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载纹理</span></span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a>        TextureSharedPtr texture <span class="op">=</span> Texture<span class="op">::</span>createFromFile<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;assets/button.png&quot;</span><span class="op">);</span></span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb5-10"><a href="#cb5-10" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载字体</span></span>
<span id="cb5-11"><a href="#cb5-11" aria-hidden="true" tabindex="-1"></a>        FontSharedPtr font <span class="op">=</span> Font<span class="op">::</span>createFromFile<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;assets/arial.ttf&quot;</span><span class="op">);</span></span>
<span id="cb5-12"><a href="#cb5-12" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb5-13"><a href="#cb5-13" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载 3D 模型</span></span>
<span id="cb5-14"><a href="#cb5-14" aria-hidden="true" tabindex="-1"></a>        MeshSharedPtr mesh <span class="op">=</span> Mesh<span class="op">::</span>createFromFile<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;assets/car.fbx&quot;</span><span class="op">);</span></span>
<span id="cb5-15"><a href="#cb5-15" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb5-16"><a href="#cb5-16" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 缓存资源</span></span>
<span id="cb5-17"><a href="#cb5-17" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_textureCache</span><span class="op">[</span><span class="st">&quot;button&quot;</span><span class="op">]</span> <span class="op">=</span> texture<span class="op">;</span></span>
<span id="cb5-18"><a href="#cb5-18" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_fontCache</span><span class="op">[</span><span class="st">&quot;default&quot;</span><span class="op">]</span> <span class="op">=</span> font<span class="op">;</span></span>
<span id="cb5-19"><a href="#cb5-19" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_meshCache</span><span class="op">[</span><span class="st">&quot;car&quot;</span><span class="op">]</span> <span class="op">=</span> mesh<span class="op">;</span></span>
<span id="cb5-20"><a href="#cb5-20" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-21"><a href="#cb5-21" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-22"><a href="#cb5-22" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb5-23"><a href="#cb5-23" aria-hidden="true" tabindex="-1"></a>    map<span class="op">&lt;</span>string<span class="op">,</span> TextureSharedPtr<span class="op">&gt;</span> <span class="va">m_textureCache</span><span class="op">;</span></span>
<span id="cb5-24"><a href="#cb5-24" aria-hidden="true" tabindex="-1"></a>    map<span class="op">&lt;</span>string<span class="op">,</span> FontSharedPtr<span class="op">&gt;</span> <span class="va">m_fontCache</span><span class="op">;</span></span>
<span id="cb5-25"><a href="#cb5-25" aria-hidden="true" tabindex="-1"></a>    map<span class="op">&lt;</span>string<span class="op">,</span> MeshSharedPtr<span class="op">&gt;</span> <span class="va">m_meshCache</span><span class="op">;</span></span>
<span id="cb5-26"><a href="#cb5-26" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="组件系统">组件系统</h3>
<div class="sourceCode" id="cb6"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 自定义组件</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> SpeedometerComponent <span class="op">:</span> <span class="kw">public</span> NodeComponent</span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a>    KZ_COMPONENT<span class="op">(</span>SpeedometerComponent<span class="op">)</span></span>
<span id="cb6-5"><a href="#cb6-5" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-6"><a href="#cb6-6" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb6-7"><a href="#cb6-7" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> PropertyType<span class="op">&lt;</span><span class="dt">float</span><span class="op">&gt;</span> SpeedProperty<span class="op">;</span></span>
<span id="cb6-8"><a href="#cb6-8" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> PropertyType<span class="op">&lt;</span><span class="dt">float</span><span class="op">&gt;</span> MaxSpeedProperty<span class="op">;</span></span>
<span id="cb6-9"><a href="#cb6-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-10"><a href="#cb6-10" aria-hidden="true" tabindex="-1"></a>    <span class="kw">explicit</span> SpeedometerComponent<span class="op">(</span>Domain<span class="op">*</span> domain<span class="op">,</span> string_view name<span class="op">)</span></span>
<span id="cb6-11"><a href="#cb6-11" aria-hidden="true" tabindex="-1"></a>        <span class="op">:</span> NodeComponent<span class="op">(</span>domain<span class="op">,</span> name<span class="op">)</span></span>
<span id="cb6-12"><a href="#cb6-12" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-13"><a href="#cb6-13" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-14"><a href="#cb6-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-15"><a href="#cb6-15" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> initialize<span class="op">()</span> <span class="kw">override</span></span>
<span id="cb6-16"><a href="#cb6-16" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-17"><a href="#cb6-17" aria-hidden="true" tabindex="-1"></a>        NodeComponent<span class="op">::</span>initialize<span class="op">();</span></span>
<span id="cb6-18"><a href="#cb6-18" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb6-19"><a href="#cb6-19" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 监听属性变化</span></span>
<span id="cb6-20"><a href="#cb6-20" aria-hidden="true" tabindex="-1"></a>        addPropertyNotificationHandler<span class="op">(</span>SpeedProperty<span class="op">,</span> </span>
<span id="cb6-21"><a href="#cb6-21" aria-hidden="true" tabindex="-1"></a>            bind<span class="op">(&amp;</span>SpeedometerComponent<span class="op">::</span>onSpeedChanged<span class="op">,</span> <span class="kw">this</span><span class="op">,</span> placeholders<span class="op">::</span>_1<span class="op">));</span></span>
<span id="cb6-22"><a href="#cb6-22" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-23"><a href="#cb6-23" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-24"><a href="#cb6-24" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb6-25"><a href="#cb6-25" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> onSpeedChanged<span class="op">(</span>PropertyObject<span class="op">&amp;</span> object<span class="op">)</span></span>
<span id="cb6-26"><a href="#cb6-26" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-27"><a href="#cb6-27" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> speed <span class="op">=</span> getProperty<span class="op">(</span>SpeedProperty<span class="op">);</span></span>
<span id="cb6-28"><a href="#cb6-28" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> maxSpeed <span class="op">=</span> getProperty<span class="op">(</span>MaxSpeedProperty<span class="op">);</span></span>
<span id="cb6-29"><a href="#cb6-29" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> angle <span class="op">=</span> <span class="op">(</span>speed <span class="op">/</span> maxSpeed<span class="op">)</span> <span class="op">*</span> <span class="fl">270.0</span><span class="bu">f</span> <span class="op">-</span> <span class="fl">135.0</span><span class="bu">f</span><span class="op">;</span> <span class="co">// -135° to +135°</span></span>
<span id="cb6-30"><a href="#cb6-30" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb6-31"><a href="#cb6-31" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 更新指针旋转</span></span>
<span id="cb6-32"><a href="#cb6-32" aria-hidden="true" tabindex="-1"></a>        Node2DSharedPtr needle <span class="op">=</span> findChild<span class="op">&lt;</span>Node2D<span class="op">&gt;(</span><span class="st">&quot;Needle&quot;</span><span class="op">);</span></span>
<span id="cb6-33"><a href="#cb6-33" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>needle<span class="op">)</span></span>
<span id="cb6-34"><a href="#cb6-34" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb6-35"><a href="#cb6-35" aria-hidden="true" tabindex="-1"></a>            needle<span class="op">-&gt;</span>setRotation<span class="op">(</span>angle<span class="op">);</span></span>
<span id="cb6-36"><a href="#cb6-36" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb6-37"><a href="#cb6-37" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-38"><a href="#cb6-38" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb6-39"><a href="#cb6-39" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-40"><a href="#cb6-40" aria-hidden="true" tabindex="-1"></a><span class="co">// 注册属性</span></span>
<span id="cb6-41"><a href="#cb6-41" aria-hidden="true" tabindex="-1"></a>PropertyType<span class="op">&lt;</span><span class="dt">float</span><span class="op">&gt;</span> SpeedometerComponent<span class="op">::</span>SpeedProperty<span class="op">(</span></span>
<span id="cb6-42"><a href="#cb6-42" aria-hidden="true" tabindex="-1"></a>    kzMakeFixedString<span class="op">(</span><span class="st">&quot;SpeedometerComponent.Speed&quot;</span><span class="op">),</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb6-43"><a href="#cb6-43" aria-hidden="true" tabindex="-1"></a>PropertyType<span class="op">&lt;</span><span class="dt">float</span><span class="op">&gt;</span> SpeedometerComponent<span class="op">::</span>MaxSpeedProperty<span class="op">(</span></span>
<span id="cb6-44"><a href="#cb6-44" aria-hidden="true" tabindex="-1"></a>    kzMakeFixedString<span class="op">(</span><span class="st">&quot;SpeedometerComponent.MaxSpeed&quot;</span><span class="op">),</span> <span class="fl">200.0</span><span class="bu">f</span><span class="op">);</span></span></code></pre></div>
<h2 id="hmi-开发">HMI 开发</h2>
<h3 id="仪表盘界面">仪表盘界面</h3>
<div class="sourceCode" id="cb7"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 汽车仪表盘实现</span></span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> CarDashboard <span class="op">:</span> <span class="kw">public</span> Node2D</span>
<span id="cb7-3"><a href="#cb7-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb7-4"><a href="#cb7-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb7-5"><a href="#cb7-5" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> Node2DSharedPtr create<span class="op">(</span>Domain<span class="op">*</span> domain<span class="op">,</span> string_view name<span class="op">)</span></span>
<span id="cb7-6"><a href="#cb7-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-7"><a href="#cb7-7" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> Node2DSharedPtr<span class="op">(</span><span class="kw">new</span> CarDashboard<span class="op">(</span>domain<span class="op">,</span> name<span class="op">));</span></span>
<span id="cb7-8"><a href="#cb7-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-9"><a href="#cb7-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-10"><a href="#cb7-10" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb7-11"><a href="#cb7-11" aria-hidden="true" tabindex="-1"></a>    <span class="kw">explicit</span> CarDashboard<span class="op">(</span>Domain<span class="op">*</span> domain<span class="op">,</span> string_view name<span class="op">)</span></span>
<span id="cb7-12"><a href="#cb7-12" aria-hidden="true" tabindex="-1"></a>        <span class="op">:</span> Node2D<span class="op">(</span>domain<span class="op">,</span> name<span class="op">)</span></span>
<span id="cb7-13"><a href="#cb7-13" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-14"><a href="#cb7-14" aria-hidden="true" tabindex="-1"></a>        initialize<span class="op">();</span></span>
<span id="cb7-15"><a href="#cb7-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-16"><a href="#cb7-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-17"><a href="#cb7-17" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> initialize<span class="op">()</span></span>
<span id="cb7-18"><a href="#cb7-18" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-19"><a href="#cb7-19" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建速度表</span></span>
<span id="cb7-20"><a href="#cb7-20" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_speedometer</span> <span class="op">=</span> createSpeedometer<span class="op">();</span></span>
<span id="cb7-21"><a href="#cb7-21" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span><span class="va">m_speedometer</span><span class="op">);</span></span>
<span id="cb7-22"><a href="#cb7-22" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-23"><a href="#cb7-23" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建转速表</span></span>
<span id="cb7-24"><a href="#cb7-24" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_tachometer</span> <span class="op">=</span> createTachometer<span class="op">();</span></span>
<span id="cb7-25"><a href="#cb7-25" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span><span class="va">m_tachometer</span><span class="op">);</span></span>
<span id="cb7-26"><a href="#cb7-26" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-27"><a href="#cb7-27" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建燃油表</span></span>
<span id="cb7-28"><a href="#cb7-28" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_fuelGauge</span> <span class="op">=</span> createFuelGauge<span class="op">();</span></span>
<span id="cb7-29"><a href="#cb7-29" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span><span class="va">m_fuelGauge</span><span class="op">);</span></span>
<span id="cb7-30"><a href="#cb7-30" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-31"><a href="#cb7-31" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建警告灯</span></span>
<span id="cb7-32"><a href="#cb7-32" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_warningLights</span> <span class="op">=</span> createWarningLights<span class="op">();</span></span>
<span id="cb7-33"><a href="#cb7-33" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span><span class="va">m_warningLights</span><span class="op">);</span></span>
<span id="cb7-34"><a href="#cb7-34" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-35"><a href="#cb7-35" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建信息显示区</span></span>
<span id="cb7-36"><a href="#cb7-36" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_infoDisplay</span> <span class="op">=</span> createInfoDisplay<span class="op">();</span></span>
<span id="cb7-37"><a href="#cb7-37" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span><span class="va">m_infoDisplay</span><span class="op">);</span></span>
<span id="cb7-38"><a href="#cb7-38" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-39"><a href="#cb7-39" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-40"><a href="#cb7-40" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb7-41"><a href="#cb7-41" aria-hidden="true" tabindex="-1"></a>    Node2DSharedPtr createSpeedometer<span class="op">()</span></span>
<span id="cb7-42"><a href="#cb7-42" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-43"><a href="#cb7-43" aria-hidden="true" tabindex="-1"></a>        Node2DSharedPtr speedometer <span class="op">=</span> Node2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;Speedometer&quot;</span><span class="op">);</span></span>
<span id="cb7-44"><a href="#cb7-44" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-45"><a href="#cb7-45" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 背景圆盘</span></span>
<span id="cb7-46"><a href="#cb7-46" aria-hidden="true" tabindex="-1"></a>        Node2DSharedPtr background <span class="op">=</span> Image2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;SpeedBackground&quot;</span><span class="op">);</span></span>
<span id="cb7-47"><a href="#cb7-47" aria-hidden="true" tabindex="-1"></a>        background<span class="op">-&gt;</span>setTexture<span class="op">(</span>getResourceManager<span class="op">()-&gt;</span>acquireResource<span class="op">&lt;</span>Texture<span class="op">&gt;(</span><span class="st">&quot;speedometer_bg&quot;</span><span class="op">));</span></span>
<span id="cb7-48"><a href="#cb7-48" aria-hidden="true" tabindex="-1"></a>        speedometer<span class="op">-&gt;</span>addChild<span class="op">(</span>background<span class="op">);</span></span>
<span id="cb7-49"><a href="#cb7-49" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-50"><a href="#cb7-50" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 刻度</span></span>
<span id="cb7-51"><a href="#cb7-51" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> <span class="op">(</span><span class="dt">int</span> i <span class="op">=</span> <span class="dv">0</span><span class="op">;</span> i <span class="op">&lt;=</span> <span class="dv">200</span><span class="op">;</span> i <span class="op">+=</span> <span class="dv">20</span><span class="op">)</span></span>
<span id="cb7-52"><a href="#cb7-52" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb7-53"><a href="#cb7-53" aria-hidden="true" tabindex="-1"></a>            Node2DSharedPtr tick <span class="op">=</span> createSpeedTick<span class="op">(</span>i<span class="op">);</span></span>
<span id="cb7-54"><a href="#cb7-54" aria-hidden="true" tabindex="-1"></a>            speedometer<span class="op">-&gt;</span>addChild<span class="op">(</span>tick<span class="op">);</span></span>
<span id="cb7-55"><a href="#cb7-55" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb7-56"><a href="#cb7-56" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-57"><a href="#cb7-57" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 指针</span></span>
<span id="cb7-58"><a href="#cb7-58" aria-hidden="true" tabindex="-1"></a>        Node2DSharedPtr needle <span class="op">=</span> Image2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;SpeedNeedle&quot;</span><span class="op">);</span></span>
<span id="cb7-59"><a href="#cb7-59" aria-hidden="true" tabindex="-1"></a>        needle<span class="op">-&gt;</span>setTexture<span class="op">(</span>getResourceManager<span class="op">()-&gt;</span>acquireResource<span class="op">&lt;</span>Texture<span class="op">&gt;(</span><span class="st">&quot;needle&quot;</span><span class="op">));</span></span>
<span id="cb7-60"><a href="#cb7-60" aria-hidden="true" tabindex="-1"></a>        needle<span class="op">-&gt;</span>setOrigin<span class="op">(</span>Vector2<span class="op">(</span><span class="fl">0.5</span><span class="bu">f</span><span class="op">,</span> <span class="fl">0.9</span><span class="bu">f</span><span class="op">));</span> <span class="co">// 设置旋转中心</span></span>
<span id="cb7-61"><a href="#cb7-61" aria-hidden="true" tabindex="-1"></a>        speedometer<span class="op">-&gt;</span>addChild<span class="op">(</span>needle<span class="op">);</span></span>
<span id="cb7-62"><a href="#cb7-62" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-63"><a href="#cb7-63" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 数字显示</span></span>
<span id="cb7-64"><a href="#cb7-64" aria-hidden="true" tabindex="-1"></a>        Text2DSharedPtr speedText <span class="op">=</span> Text2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;SpeedText&quot;</span><span class="op">);</span></span>
<span id="cb7-65"><a href="#cb7-65" aria-hidden="true" tabindex="-1"></a>        speedText<span class="op">-&gt;</span>setFont<span class="op">(</span>getResourceManager<span class="op">()-&gt;</span>acquireResource<span class="op">&lt;</span>Font<span class="op">&gt;(</span><span class="st">&quot;digital_font&quot;</span><span class="op">));</span></span>
<span id="cb7-66"><a href="#cb7-66" aria-hidden="true" tabindex="-1"></a>        speedText<span class="op">-&gt;</span>setText<span class="op">(</span><span class="st">&quot;0&quot;</span><span class="op">);</span></span>
<span id="cb7-67"><a href="#cb7-67" aria-hidden="true" tabindex="-1"></a>        speedometer<span class="op">-&gt;</span>addChild<span class="op">(</span>speedText<span class="op">);</span></span>
<span id="cb7-68"><a href="#cb7-68" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-69"><a href="#cb7-69" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> speedometer<span class="op">;</span></span>
<span id="cb7-70"><a href="#cb7-70" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-71"><a href="#cb7-71" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-72"><a href="#cb7-72" aria-hidden="true" tabindex="-1"></a>    Node2DSharedPtr createSpeedTick<span class="op">(</span><span class="dt">int</span> speed<span class="op">)</span></span>
<span id="cb7-73"><a href="#cb7-73" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-74"><a href="#cb7-74" aria-hidden="true" tabindex="-1"></a>        Node2DSharedPtr tick <span class="op">=</span> Node2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;Tick_&quot;</span> <span class="op">+</span> to_string<span class="op">(</span>speed<span class="op">));</span></span>
<span id="cb7-75"><a href="#cb7-75" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-76"><a href="#cb7-76" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 计算刻度位置</span></span>
<span id="cb7-77"><a href="#cb7-77" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> angle <span class="op">=</span> <span class="op">(</span>speed <span class="op">/</span> <span class="fl">200.0</span><span class="bu">f</span><span class="op">)</span> <span class="op">*</span> <span class="fl">270.0</span><span class="bu">f</span> <span class="op">-</span> <span class="fl">135.0</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb7-78"><a href="#cb7-78" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> radius <span class="op">=</span> <span class="fl">150.0</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb7-79"><a href="#cb7-79" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> x <span class="op">=</span> cos<span class="op">(</span>degreesToRadians<span class="op">(</span>angle<span class="op">))</span> <span class="op">*</span> radius<span class="op">;</span></span>
<span id="cb7-80"><a href="#cb7-80" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> y <span class="op">=</span> sin<span class="op">(</span>degreesToRadians<span class="op">(</span>angle<span class="op">))</span> <span class="op">*</span> radius<span class="op">;</span></span>
<span id="cb7-81"><a href="#cb7-81" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-82"><a href="#cb7-82" aria-hidden="true" tabindex="-1"></a>        tick<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span>x<span class="op">,</span> y<span class="op">));</span></span>
<span id="cb7-83"><a href="#cb7-83" aria-hidden="true" tabindex="-1"></a>        tick<span class="op">-&gt;</span>setRotation<span class="op">(</span>angle <span class="op">+</span> <span class="fl">90.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb7-84"><a href="#cb7-84" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-85"><a href="#cb7-85" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 主刻度和次刻度</span></span>
<span id="cb7-86"><a href="#cb7-86" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>speed <span class="op">%</span> <span class="dv">40</span> <span class="op">==</span> <span class="dv">0</span><span class="op">)</span></span>
<span id="cb7-87"><a href="#cb7-87" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb7-88"><a href="#cb7-88" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 主刻度 - 较长较粗</span></span>
<span id="cb7-89"><a href="#cb7-89" aria-hidden="true" tabindex="-1"></a>            Rectangle2DSharedPtr line <span class="op">=</span> Rectangle2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;MajorTick&quot;</span><span class="op">);</span></span>
<span id="cb7-90"><a href="#cb7-90" aria-hidden="true" tabindex="-1"></a>            line<span class="op">-&gt;</span>setWidth<span class="op">(</span><span class="dv">3</span><span class="op">);</span></span>
<span id="cb7-91"><a href="#cb7-91" aria-hidden="true" tabindex="-1"></a>            line<span class="op">-&gt;</span>setHeight<span class="op">(</span><span class="dv">20</span><span class="op">);</span></span>
<span id="cb7-92"><a href="#cb7-92" aria-hidden="true" tabindex="-1"></a>            line<span class="op">-&gt;</span>setBrush<span class="op">(</span>ColorBrush<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> Color<span class="op">::</span>createRGB<span class="op">(</span><span class="fl">1.0</span><span class="bu">f</span><span class="op">,</span> <span class="fl">1.0</span><span class="bu">f</span><span class="op">,</span> <span class="fl">1.0</span><span class="bu">f</span><span class="op">)));</span></span>
<span id="cb7-93"><a href="#cb7-93" aria-hidden="true" tabindex="-1"></a>            tick<span class="op">-&gt;</span>addChild<span class="op">(</span>line<span class="op">);</span></span>
<span id="cb7-94"><a href="#cb7-94" aria-hidden="true" tabindex="-1"></a>            </span>
<span id="cb7-95"><a href="#cb7-95" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 数字标签</span></span>
<span id="cb7-96"><a href="#cb7-96" aria-hidden="true" tabindex="-1"></a>            Text2DSharedPtr label <span class="op">=</span> Text2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;TickLabel&quot;</span><span class="op">);</span></span>
<span id="cb7-97"><a href="#cb7-97" aria-hidden="true" tabindex="-1"></a>            label<span class="op">-&gt;</span>setText<span class="op">(</span>to_string<span class="op">(</span>speed<span class="op">));</span></span>
<span id="cb7-98"><a href="#cb7-98" aria-hidden="true" tabindex="-1"></a>            label<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">0</span><span class="op">,</span> <span class="op">-</span><span class="dv">30</span><span class="op">));</span></span>
<span id="cb7-99"><a href="#cb7-99" aria-hidden="true" tabindex="-1"></a>            tick<span class="op">-&gt;</span>addChild<span class="op">(</span>label<span class="op">);</span></span>
<span id="cb7-100"><a href="#cb7-100" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb7-101"><a href="#cb7-101" aria-hidden="true" tabindex="-1"></a>        <span class="cf">else</span></span>
<span id="cb7-102"><a href="#cb7-102" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb7-103"><a href="#cb7-103" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 次刻度 - 较短较细</span></span>
<span id="cb7-104"><a href="#cb7-104" aria-hidden="true" tabindex="-1"></a>            Rectangle2DSharedPtr line <span class="op">=</span> Rectangle2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;MinorTick&quot;</span><span class="op">);</span></span>
<span id="cb7-105"><a href="#cb7-105" aria-hidden="true" tabindex="-1"></a>            line<span class="op">-&gt;</span>setWidth<span class="op">(</span><span class="dv">1</span><span class="op">);</span></span>
<span id="cb7-106"><a href="#cb7-106" aria-hidden="true" tabindex="-1"></a>            line<span class="op">-&gt;</span>setHeight<span class="op">(</span><span class="dv">10</span><span class="op">);</span></span>
<span id="cb7-107"><a href="#cb7-107" aria-hidden="true" tabindex="-1"></a>            line<span class="op">-&gt;</span>setBrush<span class="op">(</span>ColorBrush<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> Color<span class="op">::</span>createRGB<span class="op">(</span><span class="fl">0.8</span><span class="bu">f</span><span class="op">,</span> <span class="fl">0.8</span><span class="bu">f</span><span class="op">,</span> <span class="fl">0.8</span><span class="bu">f</span><span class="op">)));</span></span>
<span id="cb7-108"><a href="#cb7-108" aria-hidden="true" tabindex="-1"></a>            tick<span class="op">-&gt;</span>addChild<span class="op">(</span>line<span class="op">);</span></span>
<span id="cb7-109"><a href="#cb7-109" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb7-110"><a href="#cb7-110" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-111"><a href="#cb7-111" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> tick<span class="op">;</span></span>
<span id="cb7-112"><a href="#cb7-112" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-113"><a href="#cb7-113" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-114"><a href="#cb7-114" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> updateSpeed<span class="op">(</span><span class="dt">float</span> speed<span class="op">)</span></span>
<span id="cb7-115"><a href="#cb7-115" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-116"><a href="#cb7-116" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 更新速度表指针</span></span>
<span id="cb7-117"><a href="#cb7-117" aria-hidden="true" tabindex="-1"></a>        Node2DSharedPtr needle <span class="op">=</span> <span class="va">m_speedometer</span><span class="op">-&gt;</span>findChild<span class="op">&lt;</span>Node2D<span class="op">&gt;(</span><span class="st">&quot;SpeedNeedle&quot;</span><span class="op">);</span></span>
<span id="cb7-118"><a href="#cb7-118" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>needle<span class="op">)</span></span>
<span id="cb7-119"><a href="#cb7-119" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb7-120"><a href="#cb7-120" aria-hidden="true" tabindex="-1"></a>            <span class="dt">float</span> angle <span class="op">=</span> <span class="op">(</span>speed <span class="op">/</span> <span class="fl">200.0</span><span class="bu">f</span><span class="op">)</span> <span class="op">*</span> <span class="fl">270.0</span><span class="bu">f</span> <span class="op">-</span> <span class="fl">135.0</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb7-121"><a href="#cb7-121" aria-hidden="true" tabindex="-1"></a>            </span>
<span id="cb7-122"><a href="#cb7-122" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 平滑动画</span></span>
<span id="cb7-123"><a href="#cb7-123" aria-hidden="true" tabindex="-1"></a>            PropertyAnimationSharedPtr animation <span class="op">=</span> PropertyAnimation<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;SpeedAnimation&quot;</span><span class="op">);</span></span>
<span id="cb7-124"><a href="#cb7-124" aria-hidden="true" tabindex="-1"></a>            animation<span class="op">-&gt;</span>setTargetObject<span class="op">(</span>needle<span class="op">);</span></span>
<span id="cb7-125"><a href="#cb7-125" aria-hidden="true" tabindex="-1"></a>            animation<span class="op">-&gt;</span>setTargetProperty<span class="op">(</span>Node2D<span class="op">::</span>RotationProperty<span class="op">);</span></span>
<span id="cb7-126"><a href="#cb7-126" aria-hidden="true" tabindex="-1"></a>            animation<span class="op">-&gt;</span>setDuration<span class="op">(</span>chrono<span class="op">::</span>milliseconds<span class="op">(</span><span class="dv">500</span><span class="op">));</span></span>
<span id="cb7-127"><a href="#cb7-127" aria-hidden="true" tabindex="-1"></a>            animation<span class="op">-&gt;</span>setStartValue<span class="op">(</span>needle<span class="op">-&gt;</span>getRotation<span class="op">());</span></span>
<span id="cb7-128"><a href="#cb7-128" aria-hidden="true" tabindex="-1"></a>            animation<span class="op">-&gt;</span>setTargetValue<span class="op">(</span>angle<span class="op">);</span></span>
<span id="cb7-129"><a href="#cb7-129" aria-hidden="true" tabindex="-1"></a>            animation<span class="op">-&gt;</span>setEasingFunction<span class="op">(</span>EasingFunction<span class="op">::</span>createEaseInOut<span class="op">());</span></span>
<span id="cb7-130"><a href="#cb7-130" aria-hidden="true" tabindex="-1"></a>            animation<span class="op">-&gt;</span>start<span class="op">();</span></span>
<span id="cb7-131"><a href="#cb7-131" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb7-132"><a href="#cb7-132" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-133"><a href="#cb7-133" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 更新数字显示</span></span>
<span id="cb7-134"><a href="#cb7-134" aria-hidden="true" tabindex="-1"></a>        Text2DSharedPtr speedText <span class="op">=</span> <span class="va">m_speedometer</span><span class="op">-&gt;</span>findChild<span class="op">&lt;</span>Text2D<span class="op">&gt;(</span><span class="st">&quot;SpeedText&quot;</span><span class="op">);</span></span>
<span id="cb7-135"><a href="#cb7-135" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>speedText<span class="op">)</span></span>
<span id="cb7-136"><a href="#cb7-136" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb7-137"><a href="#cb7-137" aria-hidden="true" tabindex="-1"></a>            speedText<span class="op">-&gt;</span>setText<span class="op">(</span>to_string<span class="op">(</span><span class="kw">static_cast</span><span class="op">&lt;</span><span class="dt">int</span><span class="op">&gt;(</span>speed<span class="op">)));</span></span>
<span id="cb7-138"><a href="#cb7-138" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb7-139"><a href="#cb7-139" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-140"><a href="#cb7-140" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-141"><a href="#cb7-141" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb7-142"><a href="#cb7-142" aria-hidden="true" tabindex="-1"></a>    Node2DSharedPtr <span class="va">m_speedometer</span><span class="op">;</span></span>
<span id="cb7-143"><a href="#cb7-143" aria-hidden="true" tabindex="-1"></a>    Node2DSharedPtr <span class="va">m_tachometer</span><span class="op">;</span></span>
<span id="cb7-144"><a href="#cb7-144" aria-hidden="true" tabindex="-1"></a>    Node2DSharedPtr <span class="va">m_fuelGauge</span><span class="op">;</span></span>
<span id="cb7-145"><a href="#cb7-145" aria-hidden="true" tabindex="-1"></a>    Node2DSharedPtr <span class="va">m_warningLights</span><span class="op">;</span></span>
<span id="cb7-146"><a href="#cb7-146" aria-hidden="true" tabindex="-1"></a>    Node2DSharedPtr <span class="va">m_infoDisplay</span><span class="op">;</span></span>
<span id="cb7-147"><a href="#cb7-147" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="d3d-界面设计">2D/3D 界面设计</h2>
<h3 id="混合-2d3d-界面">混合 2D/3D 界面</h3>
<div class="sourceCode" id="cb8"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 3D 场景中的 2D UI 叠加</span></span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> Mixed2D3DInterface <span class="op">:</span> <span class="kw">public</span> Node3D</span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb8-4"><a href="#cb8-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb8-5"><a href="#cb8-5" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> Node3DSharedPtr create<span class="op">(</span>Domain<span class="op">*</span> domain<span class="op">,</span> string_view name<span class="op">)</span></span>
<span id="cb8-6"><a href="#cb8-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-7"><a href="#cb8-7" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> Node3DSharedPtr<span class="op">(</span><span class="kw">new</span> Mixed2D3DInterface<span class="op">(</span>domain<span class="op">,</span> name<span class="op">));</span></span>
<span id="cb8-8"><a href="#cb8-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-9"><a href="#cb8-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-10"><a href="#cb8-10" aria-hidden="true" tabindex="-1"></a><span class="kw">protected</span><span class="op">:</span></span>
<span id="cb8-11"><a href="#cb8-11" aria-hidden="true" tabindex="-1"></a>    <span class="kw">explicit</span> Mixed2D3DInterface<span class="op">(</span>Domain<span class="op">*</span> domain<span class="op">,</span> string_view name<span class="op">)</span></span>
<span id="cb8-12"><a href="#cb8-12" aria-hidden="true" tabindex="-1"></a>        <span class="op">:</span> Node3D<span class="op">(</span>domain<span class="op">,</span> name<span class="op">)</span></span>
<span id="cb8-13"><a href="#cb8-13" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-14"><a href="#cb8-14" aria-hidden="true" tabindex="-1"></a>        initialize<span class="op">();</span></span>
<span id="cb8-15"><a href="#cb8-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-16"><a href="#cb8-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-17"><a href="#cb8-17" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> initialize<span class="op">()</span></span>
<span id="cb8-18"><a href="#cb8-18" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-19"><a href="#cb8-19" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建 3D 场景</span></span>
<span id="cb8-20"><a href="#cb8-20" aria-hidden="true" tabindex="-1"></a>        create3DScene<span class="op">();</span></span>
<span id="cb8-21"><a href="#cb8-21" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb8-22"><a href="#cb8-22" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建 2D UI 叠加层</span></span>
<span id="cb8-23"><a href="#cb8-23" aria-hidden="true" tabindex="-1"></a>        create2DOverlay<span class="op">();</span></span>
<span id="cb8-24"><a href="#cb8-24" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb8-25"><a href="#cb8-25" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 设置相机</span></span>
<span id="cb8-26"><a href="#cb8-26" aria-hidden="true" tabindex="-1"></a>        setupCamera<span class="op">();</span></span>
<span id="cb8-27"><a href="#cb8-27" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-28"><a href="#cb8-28" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-29"><a href="#cb8-29" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb8-30"><a href="#cb8-30" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> create3DScene<span class="op">()</span></span>
<span id="cb8-31"><a href="#cb8-31" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-32"><a href="#cb8-32" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载 3D 汽车模型</span></span>
<span id="cb8-33"><a href="#cb8-33" aria-hidden="true" tabindex="-1"></a>        Node3DSharedPtr carModel <span class="op">=</span> Model3D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;CarModel&quot;</span><span class="op">);</span></span>
<span id="cb8-34"><a href="#cb8-34" aria-hidden="true" tabindex="-1"></a>        carModel<span class="op">-&gt;</span>setMesh<span class="op">(</span>getResourceManager<span class="op">()-&gt;</span>acquireResource<span class="op">&lt;</span>Mesh<span class="op">&gt;(</span><span class="st">&quot;car_model.fbx&quot;</span><span class="op">));</span></span>
<span id="cb8-35"><a href="#cb8-35" aria-hidden="true" tabindex="-1"></a>        carModel<span class="op">-&gt;</span>setMaterial<span class="op">(</span>getResourceManager<span class="op">()-&gt;</span>acquireResource<span class="op">&lt;</span>Material<span class="op">&gt;(</span><span class="st">&quot;car_material&quot;</span><span class="op">));</span></span>
<span id="cb8-36"><a href="#cb8-36" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span>carModel<span class="op">);</span></span>
<span id="cb8-37"><a href="#cb8-37" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb8-38"><a href="#cb8-38" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 环境光照</span></span>
<span id="cb8-39"><a href="#cb8-39" aria-hidden="true" tabindex="-1"></a>        LightSharedPtr ambientLight <span class="op">=</span> AmbientLight<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;AmbientLight&quot;</span><span class="op">);</span></span>
<span id="cb8-40"><a href="#cb8-40" aria-hidden="true" tabindex="-1"></a>        ambientLight<span class="op">-&gt;</span>setColor<span class="op">(</span>Color<span class="op">::</span>createRGB<span class="op">(</span><span class="fl">0.3</span><span class="bu">f</span><span class="op">,</span> <span class="fl">0.3</span><span class="bu">f</span><span class="op">,</span> <span class="fl">0.3</span><span class="bu">f</span><span class="op">));</span></span>
<span id="cb8-41"><a href="#cb8-41" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span>ambientLight<span class="op">);</span></span>
<span id="cb8-42"><a href="#cb8-42" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb8-43"><a href="#cb8-43" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 方向光</span></span>
<span id="cb8-44"><a href="#cb8-44" aria-hidden="true" tabindex="-1"></a>        LightSharedPtr directionalLight <span class="op">=</span> DirectionalLight<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;DirectionalLight&quot;</span><span class="op">);</span></span>
<span id="cb8-45"><a href="#cb8-45" aria-hidden="true" tabindex="-1"></a>        directionalLight<span class="op">-&gt;</span>setDirection<span class="op">(</span>Vector3<span class="op">(-</span><span class="dv">1</span><span class="op">,</span> <span class="op">-</span><span class="dv">1</span><span class="op">,</span> <span class="op">-</span><span class="dv">1</span><span class="op">));</span></span>
<span id="cb8-46"><a href="#cb8-46" aria-hidden="true" tabindex="-1"></a>        directionalLight<span class="op">-&gt;</span>setColor<span class="op">(</span>Color<span class="op">::</span>createRGB<span class="op">(</span><span class="fl">0.8</span><span class="bu">f</span><span class="op">,</span> <span class="fl">0.8</span><span class="bu">f</span><span class="op">,</span> <span class="fl">0.8</span><span class="bu">f</span><span class="op">));</span></span>
<span id="cb8-47"><a href="#cb8-47" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span>directionalLight<span class="op">);</span></span>
<span id="cb8-48"><a href="#cb8-48" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-49"><a href="#cb8-49" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-50"><a href="#cb8-50" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> create2DOverlay<span class="op">()</span></span>
<span id="cb8-51"><a href="#cb8-51" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-52"><a href="#cb8-52" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建 2D 叠加层</span></span>
<span id="cb8-53"><a href="#cb8-53" aria-hidden="true" tabindex="-1"></a>        Node2DSharedPtr overlay <span class="op">=</span> Node2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;UIOverlay&quot;</span><span class="op">);</span></span>
<span id="cb8-54"><a href="#cb8-54" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb8-55"><a href="#cb8-55" aria-hidden="true" tabindex="-1"></a>        <span class="co">// HUD 元素</span></span>
<span id="cb8-56"><a href="#cb8-56" aria-hidden="true" tabindex="-1"></a>        createSpeedDisplay<span class="op">(</span>overlay<span class="op">);</span></span>
<span id="cb8-57"><a href="#cb8-57" aria-hidden="true" tabindex="-1"></a>        createNavigationInfo<span class="op">(</span>overlay<span class="op">);</span></span>
<span id="cb8-58"><a href="#cb8-58" aria-hidden="true" tabindex="-1"></a>        createControlButtons<span class="op">(</span>overlay<span class="op">);</span></span>
<span id="cb8-59"><a href="#cb8-59" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb8-60"><a href="#cb8-60" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 将 2D 叠加层添加到 3D 场景</span></span>
<span id="cb8-61"><a href="#cb8-61" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span>overlay<span class="op">);</span></span>
<span id="cb8-62"><a href="#cb8-62" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-63"><a href="#cb8-63" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-64"><a href="#cb8-64" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> createSpeedDisplay<span class="op">(</span>Node2DSharedPtr parent<span class="op">)</span></span>
<span id="cb8-65"><a href="#cb8-65" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-66"><a href="#cb8-66" aria-hidden="true" tabindex="-1"></a>        Node2DSharedPtr speedPanel <span class="op">=</span> Rectangle2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;SpeedPanel&quot;</span><span class="op">);</span></span>
<span id="cb8-67"><a href="#cb8-67" aria-hidden="true" tabindex="-1"></a>        speedPanel<span class="op">-&gt;</span>setWidth<span class="op">(</span><span class="dv">200</span><span class="op">);</span></span>
<span id="cb8-68"><a href="#cb8-68" aria-hidden="true" tabindex="-1"></a>        speedPanel<span class="op">-&gt;</span>setHeight<span class="op">(</span><span class="dv">100</span><span class="op">);</span></span>
<span id="cb8-69"><a href="#cb8-69" aria-hidden="true" tabindex="-1"></a>        speedPanel<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">50</span><span class="op">,</span> <span class="dv">50</span><span class="op">));</span></span>
<span id="cb8-70"><a href="#cb8-70" aria-hidden="true" tabindex="-1"></a>        speedPanel<span class="op">-&gt;</span>setBrush<span class="op">(</span>ColorBrush<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> Color<span class="op">::</span>createRGBA<span class="op">(</span><span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="fl">0.7</span><span class="bu">f</span><span class="op">)));</span></span>
<span id="cb8-71"><a href="#cb8-71" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb8-72"><a href="#cb8-72" aria-hidden="true" tabindex="-1"></a>        Text2DSharedPtr speedLabel <span class="op">=</span> Text2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;SpeedLabel&quot;</span><span class="op">);</span></span>
<span id="cb8-73"><a href="#cb8-73" aria-hidden="true" tabindex="-1"></a>        speedLabel<span class="op">-&gt;</span>setText<span class="op">(</span><span class="st">&quot;Speed&quot;</span><span class="op">);</span></span>
<span id="cb8-74"><a href="#cb8-74" aria-hidden="true" tabindex="-1"></a>        speedLabel<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">10</span><span class="op">,</span> <span class="dv">10</span><span class="op">));</span></span>
<span id="cb8-75"><a href="#cb8-75" aria-hidden="true" tabindex="-1"></a>        speedPanel<span class="op">-&gt;</span>addChild<span class="op">(</span>speedLabel<span class="op">);</span></span>
<span id="cb8-76"><a href="#cb8-76" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb8-77"><a href="#cb8-77" aria-hidden="true" tabindex="-1"></a>        Text2DSharedPtr speedValue <span class="op">=</span> Text2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;SpeedValue&quot;</span><span class="op">);</span></span>
<span id="cb8-78"><a href="#cb8-78" aria-hidden="true" tabindex="-1"></a>        speedValue<span class="op">-&gt;</span>setText<span class="op">(</span><span class="st">&quot;0 km/h&quot;</span><span class="op">);</span></span>
<span id="cb8-79"><a href="#cb8-79" aria-hidden="true" tabindex="-1"></a>        speedValue<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector2<span class="op">(</span><span class="dv">10</span><span class="op">,</span> <span class="dv">40</span><span class="op">));</span></span>
<span id="cb8-80"><a href="#cb8-80" aria-hidden="true" tabindex="-1"></a>        speedValue<span class="op">-&gt;</span>setFontSize<span class="op">(</span><span class="dv">24</span><span class="op">);</span></span>
<span id="cb8-81"><a href="#cb8-81" aria-hidden="true" tabindex="-1"></a>        speedPanel<span class="op">-&gt;</span>addChild<span class="op">(</span>speedValue<span class="op">);</span></span>
<span id="cb8-82"><a href="#cb8-82" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb8-83"><a href="#cb8-83" aria-hidden="true" tabindex="-1"></a>        parent<span class="op">-&gt;</span>addChild<span class="op">(</span>speedPanel<span class="op">);</span></span>
<span id="cb8-84"><a href="#cb8-84" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-85"><a href="#cb8-85" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-86"><a href="#cb8-86" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> setupCamera<span class="op">()</span></span>
<span id="cb8-87"><a href="#cb8-87" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-88"><a href="#cb8-88" aria-hidden="true" tabindex="-1"></a>        CameraSharedPtr camera <span class="op">=</span> Camera<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;MainCamera&quot;</span><span class="op">);</span></span>
<span id="cb8-89"><a href="#cb8-89" aria-hidden="true" tabindex="-1"></a>        camera<span class="op">-&gt;</span>setTranslation<span class="op">(</span>Vector3<span class="op">(</span><span class="dv">0</span><span class="op">,</span> <span class="dv">2</span><span class="op">,</span> <span class="dv">5</span><span class="op">));</span></span>
<span id="cb8-90"><a href="#cb8-90" aria-hidden="true" tabindex="-1"></a>        camera<span class="op">-&gt;</span>setRotation<span class="op">(</span>Vector3<span class="op">(-</span><span class="dv">15</span><span class="op">,</span> <span class="dv">0</span><span class="op">,</span> <span class="dv">0</span><span class="op">));</span></span>
<span id="cb8-91"><a href="#cb8-91" aria-hidden="true" tabindex="-1"></a>        camera<span class="op">-&gt;</span>setFieldOfView<span class="op">(</span><span class="fl">45.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb8-92"><a href="#cb8-92" aria-hidden="true" tabindex="-1"></a>        camera<span class="op">-&gt;</span>setNearPlane<span class="op">(</span><span class="fl">0.1</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb8-93"><a href="#cb8-93" aria-hidden="true" tabindex="-1"></a>        camera<span class="op">-&gt;</span>setFarPlane<span class="op">(</span><span class="fl">1000.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb8-94"><a href="#cb8-94" aria-hidden="true" tabindex="-1"></a>        addChild<span class="op">(</span>camera<span class="op">);</span></span>
<span id="cb8-95"><a href="#cb8-95" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-96"><a href="#cb8-96" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="数据绑定与交互">数据绑定与交互</h2>
<h3 id="数据绑定系统">数据绑定系统</h3>
<div class="sourceCode" id="cb9"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 数据源定义</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> VehicleDataSource <span class="op">:</span> <span class="kw">public</span> DataObject</span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a>    KZ_METACLASS_BEGIN<span class="op">(</span>VehicleDataSource<span class="op">,</span> DataObject<span class="op">,</span> <span class="st">&quot;VehicleDataSource&quot;</span><span class="op">)</span></span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a>        KZ_METACLASS_PROPERTY_TYPE<span class="op">(</span>SpeedProperty<span class="op">)</span></span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a>        KZ_METACLASS_PROPERTY_TYPE<span class="op">(</span>RPMProperty<span class="op">)</span></span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a>        KZ_METACLASS_PROPERTY_TYPE<span class="op">(</span>FuelLevelProperty<span class="op">)</span></span>
<span id="cb9-8"><a href="#cb9-8" aria-hidden="true" tabindex="-1"></a>    KZ_METACLASS_END<span class="op">()</span></span>
<span id="cb9-9"><a href="#cb9-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-10"><a href="#cb9-10" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb9-11"><a href="#cb9-11" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> PropertyType<span class="op">&lt;</span><span class="dt">float</span><span class="op">&gt;</span> SpeedProperty<span class="op">;</span></span>
<span id="cb9-12"><a href="#cb9-12" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> PropertyType<span class="op">&lt;</span><span class="dt">float</span><span class="op">&gt;</span> RPMProperty<span class="op">;</span></span>
<span id="cb9-13"><a href="#cb9-13" aria-hidden="true" tabindex="-1"></a>    <span class="at">static</span> PropertyType<span class="op">&lt;</span><span class="dt">float</span><span class="op">&gt;</span> FuelLevelProperty<span class="op">;</span></span>
<span id="cb9-14"><a href="#cb9-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-15"><a href="#cb9-15" aria-hidden="true" tabindex="-1"></a>    <span class="kw">explicit</span> VehicleDataSource<span class="op">(</span>Domain<span class="op">*</span> domain<span class="op">,</span> string_view name<span class="op">)</span></span>
<span id="cb9-16"><a href="#cb9-16" aria-hidden="true" tabindex="-1"></a>        <span class="op">:</span> DataObject<span class="op">(</span>domain<span class="op">,</span> name<span class="op">)</span></span>
<span id="cb9-17"><a href="#cb9-17" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-18"><a href="#cb9-18" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 模拟数据更新</span></span>
<span id="cb9-19"><a href="#cb9-19" aria-hidden="true" tabindex="-1"></a>        startDataSimulation<span class="op">();</span></span>
<span id="cb9-20"><a href="#cb9-20" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-21"><a href="#cb9-21" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-22"><a href="#cb9-22" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> updateSpeed<span class="op">(</span><span class="dt">float</span> speed<span class="op">)</span></span>
<span id="cb9-23"><a href="#cb9-23" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-24"><a href="#cb9-24" aria-hidden="true" tabindex="-1"></a>        setProperty<span class="op">(</span>SpeedProperty<span class="op">,</span> speed<span class="op">);</span></span>
<span id="cb9-25"><a href="#cb9-25" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-26"><a href="#cb9-26" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-27"><a href="#cb9-27" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> updateRPM<span class="op">(</span><span class="dt">float</span> rpm<span class="op">)</span></span>
<span id="cb9-28"><a href="#cb9-28" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-29"><a href="#cb9-29" aria-hidden="true" tabindex="-1"></a>        setProperty<span class="op">(</span>RPMProperty<span class="op">,</span> rpm<span class="op">);</span></span>
<span id="cb9-30"><a href="#cb9-30" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-31"><a href="#cb9-31" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-32"><a href="#cb9-32" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> updateFuelLevel<span class="op">(</span><span class="dt">float</span> level<span class="op">)</span></span>
<span id="cb9-33"><a href="#cb9-33" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-34"><a href="#cb9-34" aria-hidden="true" tabindex="-1"></a>        setProperty<span class="op">(</span>FuelLevelProperty<span class="op">,</span> level<span class="op">);</span></span>
<span id="cb9-35"><a href="#cb9-35" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-36"><a href="#cb9-36" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-37"><a href="#cb9-37" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb9-38"><a href="#cb9-38" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> startDataSimulation<span class="op">()</span></span>
<span id="cb9-39"><a href="#cb9-39" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-40"><a href="#cb9-40" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建定时器模拟数据变化</span></span>
<span id="cb9-41"><a href="#cb9-41" aria-hidden="true" tabindex="-1"></a>        TimerSharedPtr timer <span class="op">=</span> Timer<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">());</span></span>
<span id="cb9-42"><a href="#cb9-42" aria-hidden="true" tabindex="-1"></a>        timer<span class="op">-&gt;</span>setInterval<span class="op">(</span>chrono<span class="op">::</span>milliseconds<span class="op">(</span><span class="dv">100</span><span class="op">));</span></span>
<span id="cb9-43"><a href="#cb9-43" aria-hidden="true" tabindex="-1"></a>        timer<span class="op">-&gt;</span>setTimeout<span class="op">([</span><span class="kw">this</span><span class="op">]()</span> <span class="op">{</span></span>
<span id="cb9-44"><a href="#cb9-44" aria-hidden="true" tabindex="-1"></a>            simulateDataUpdate<span class="op">();</span></span>
<span id="cb9-45"><a href="#cb9-45" aria-hidden="true" tabindex="-1"></a>        <span class="op">});</span></span>
<span id="cb9-46"><a href="#cb9-46" aria-hidden="true" tabindex="-1"></a>        timer<span class="op">-&gt;</span>start<span class="op">();</span></span>
<span id="cb9-47"><a href="#cb9-47" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-48"><a href="#cb9-48" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-49"><a href="#cb9-49" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> simulateDataUpdate<span class="op">()</span></span>
<span id="cb9-50"><a href="#cb9-50" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-51"><a href="#cb9-51" aria-hidden="true" tabindex="-1"></a>        <span class="at">static</span> <span class="dt">float</span> time <span class="op">=</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb9-52"><a href="#cb9-52" aria-hidden="true" tabindex="-1"></a>        time <span class="op">+=</span> <span class="fl">0.1</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb9-53"><a href="#cb9-53" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb9-54"><a href="#cb9-54" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 模拟速度变化</span></span>
<span id="cb9-55"><a href="#cb9-55" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> speed <span class="op">=</span> <span class="fl">60.0</span><span class="bu">f</span> <span class="op">+</span> <span class="fl">30.0</span><span class="bu">f</span> <span class="op">*</span> sin<span class="op">(</span>time <span class="op">*</span> <span class="fl">0.5</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb9-56"><a href="#cb9-56" aria-hidden="true" tabindex="-1"></a>        updateSpeed<span class="op">(</span>speed<span class="op">);</span></span>
<span id="cb9-57"><a href="#cb9-57" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb9-58"><a href="#cb9-58" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 模拟转速变化</span></span>
<span id="cb9-59"><a href="#cb9-59" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> rpm <span class="op">=</span> <span class="fl">2000.0</span><span class="bu">f</span> <span class="op">+</span> <span class="fl">1000.0</span><span class="bu">f</span> <span class="op">*</span> sin<span class="op">(</span>time <span class="op">*</span> <span class="fl">0.7</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb9-60"><a href="#cb9-60" aria-hidden="true" tabindex="-1"></a>        updateRPM<span class="op">(</span>rpm<span class="op">);</span></span>
<span id="cb9-61"><a href="#cb9-61" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb9-62"><a href="#cb9-62" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 模拟燃油消耗</span></span>
<span id="cb9-63"><a href="#cb9-63" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> fuel <span class="op">=</span> <span class="fl">100.0</span><span class="bu">f</span> <span class="op">-</span> time <span class="op">*</span> <span class="fl">0.1</span><span class="bu">f</span><span class="op">;</span></span>
<span id="cb9-64"><a href="#cb9-64" aria-hidden="true" tabindex="-1"></a>        updateFuelLevel<span class="op">(</span>max<span class="op">(</span><span class="fl">0.0</span><span class="bu">f</span><span class="op">,</span> fuel<span class="op">));</span></span>
<span id="cb9-65"><a href="#cb9-65" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-66"><a href="#cb9-66" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb9-67"><a href="#cb9-67" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-68"><a href="#cb9-68" aria-hidden="true" tabindex="-1"></a><span class="co">// 属性定义</span></span>
<span id="cb9-69"><a href="#cb9-69" aria-hidden="true" tabindex="-1"></a>PropertyType<span class="op">&lt;</span><span class="dt">float</span><span class="op">&gt;</span> VehicleDataSource<span class="op">::</span>SpeedProperty<span class="op">(</span></span>
<span id="cb9-70"><a href="#cb9-70" aria-hidden="true" tabindex="-1"></a>    kzMakeFixedString<span class="op">(</span><span class="st">&quot;VehicleDataSource.Speed&quot;</span><span class="op">),</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb9-71"><a href="#cb9-71" aria-hidden="true" tabindex="-1"></a>PropertyType<span class="op">&lt;</span><span class="dt">float</span><span class="op">&gt;</span> VehicleDataSource<span class="op">::</span>RPMProperty<span class="op">(</span></span>
<span id="cb9-72"><a href="#cb9-72" aria-hidden="true" tabindex="-1"></a>    kzMakeFixedString<span class="op">(</span><span class="st">&quot;VehicleDataSource.RPM&quot;</span><span class="op">),</span> <span class="fl">0.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb9-73"><a href="#cb9-73" aria-hidden="true" tabindex="-1"></a>PropertyType<span class="op">&lt;</span><span class="dt">float</span><span class="op">&gt;</span> VehicleDataSource<span class="op">::</span>FuelLevelProperty<span class="op">(</span></span>
<span id="cb9-74"><a href="#cb9-74" aria-hidden="true" tabindex="-1"></a>    kzMakeFixedString<span class="op">(</span><span class="st">&quot;VehicleDataSource.FuelLevel&quot;</span><span class="op">),</span> <span class="fl">100.0</span><span class="bu">f</span><span class="op">);</span></span></code></pre></div>
<h3 id="触摸交互">触摸交互</h3>
<div class="sourceCode" id="cb10"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 触摸处理</span></span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> TouchInteractionHandler <span class="op">:</span> <span class="kw">public</span> InputManipulator</span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb10-5"><a href="#cb10-5" aria-hidden="true" tabindex="-1"></a>    <span class="kw">explicit</span> TouchInteractionHandler<span class="op">(</span>Domain<span class="op">*</span> domain<span class="op">)</span></span>
<span id="cb10-6"><a href="#cb10-6" aria-hidden="true" tabindex="-1"></a>        <span class="op">:</span> InputManipulator<span class="op">(</span>domain<span class="op">)</span></span>
<span id="cb10-7"><a href="#cb10-7" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-8"><a href="#cb10-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-9"><a href="#cb10-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-10"><a href="#cb10-10" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> attachTo<span class="op">(</span>Node<span class="op">&amp;</span> node<span class="op">)</span> <span class="kw">override</span></span>
<span id="cb10-11"><a href="#cb10-11" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-12"><a href="#cb10-12" aria-hidden="true" tabindex="-1"></a>        InputManipulator<span class="op">::</span>attachTo<span class="op">(</span>node<span class="op">);</span></span>
<span id="cb10-13"><a href="#cb10-13" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb10-14"><a href="#cb10-14" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 注册触摸事件</span></span>
<span id="cb10-15"><a href="#cb10-15" aria-hidden="true" tabindex="-1"></a>        node<span class="op">.</span>addMessageHandler<span class="op">(</span>InputTouchBeginMessage<span class="op">::</span>getStaticMessageType<span class="op">(),</span></span>
<span id="cb10-16"><a href="#cb10-16" aria-hidden="true" tabindex="-1"></a>            bind<span class="op">(&amp;</span>TouchInteractionHandler<span class="op">::</span>onTouchBegin<span class="op">,</span> <span class="kw">this</span><span class="op">,</span> placeholders<span class="op">::</span>_1<span class="op">));</span></span>
<span id="cb10-17"><a href="#cb10-17" aria-hidden="true" tabindex="-1"></a>        node<span class="op">.</span>addMessageHandler<span class="op">(</span>InputTouchMoveMessage<span class="op">::</span>getStaticMessageType<span class="op">(),</span></span>
<span id="cb10-18"><a href="#cb10-18" aria-hidden="true" tabindex="-1"></a>            bind<span class="op">(&amp;</span>TouchInteractionHandler<span class="op">::</span>onTouchMove<span class="op">,</span> <span class="kw">this</span><span class="op">,</span> placeholders<span class="op">::</span>_1<span class="op">));</span></span>
<span id="cb10-19"><a href="#cb10-19" aria-hidden="true" tabindex="-1"></a>        node<span class="op">.</span>addMessageHandler<span class="op">(</span>InputTouchEndMessage<span class="op">::</span>getStaticMessageType<span class="op">(),</span></span>
<span id="cb10-20"><a href="#cb10-20" aria-hidden="true" tabindex="-1"></a>            bind<span class="op">(&amp;</span>TouchInteractionHandler<span class="op">::</span>onTouchEnd<span class="op">,</span> <span class="kw">this</span><span class="op">,</span> placeholders<span class="op">::</span>_1<span class="op">));</span></span>
<span id="cb10-21"><a href="#cb10-21" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-22"><a href="#cb10-22" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-23"><a href="#cb10-23" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb10-24"><a href="#cb10-24" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> onTouchBegin<span class="op">(</span>InputTouchBeginMessageArguments<span class="op">&amp;</span> messageArguments<span class="op">)</span></span>
<span id="cb10-25"><a href="#cb10-25" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-26"><a href="#cb10-26" aria-hidden="true" tabindex="-1"></a>        Vector2 touchPosition <span class="op">=</span> messageArguments<span class="op">.</span>getTouchPoint<span class="op">().</span>position<span class="op">;</span></span>
<span id="cb10-27"><a href="#cb10-27" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_lastTouchPosition</span> <span class="op">=</span> touchPosition<span class="op">;</span></span>
<span id="cb10-28"><a href="#cb10-28" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_isTouching</span> <span class="op">=</span> <span class="kw">true</span><span class="op">;</span></span>
<span id="cb10-29"><a href="#cb10-29" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb10-30"><a href="#cb10-30" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 处理触摸开始</span></span>
<span id="cb10-31"><a href="#cb10-31" aria-hidden="true" tabindex="-1"></a>        handleTouchStart<span class="op">(</span>touchPosition<span class="op">);</span></span>
<span id="cb10-32"><a href="#cb10-32" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-33"><a href="#cb10-33" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-34"><a href="#cb10-34" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> onTouchMove<span class="op">(</span>InputTouchMoveMessageArguments<span class="op">&amp;</span> messageArguments<span class="op">)</span></span>
<span id="cb10-35"><a href="#cb10-35" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-36"><a href="#cb10-36" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(!</span><span class="va">m_isTouching</span><span class="op">)</span> <span class="cf">return</span><span class="op">;</span></span>
<span id="cb10-37"><a href="#cb10-37" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb10-38"><a href="#cb10-38" aria-hidden="true" tabindex="-1"></a>        Vector2 touchPosition <span class="op">=</span> messageArguments<span class="op">.</span>getTouchPoint<span class="op">().</span>position<span class="op">;</span></span>
<span id="cb10-39"><a href="#cb10-39" aria-hidden="true" tabindex="-1"></a>        Vector2 delta <span class="op">=</span> touchPosition <span class="op">-</span> <span class="va">m_lastTouchPosition</span><span class="op">;</span></span>
<span id="cb10-40"><a href="#cb10-40" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb10-41"><a href="#cb10-41" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 处理拖拽</span></span>
<span id="cb10-42"><a href="#cb10-42" aria-hidden="true" tabindex="-1"></a>        handleTouchDrag<span class="op">(</span>delta<span class="op">);</span></span>
<span id="cb10-43"><a href="#cb10-43" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb10-44"><a href="#cb10-44" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_lastTouchPosition</span> <span class="op">=</span> touchPosition<span class="op">;</span></span>
<span id="cb10-45"><a href="#cb10-45" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-46"><a href="#cb10-46" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-47"><a href="#cb10-47" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> onTouchEnd<span class="op">(</span>InputTouchEndMessageArguments<span class="op">&amp;</span> messageArguments<span class="op">)</span></span>
<span id="cb10-48"><a href="#cb10-48" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-49"><a href="#cb10-49" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_isTouching</span> <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb10-50"><a href="#cb10-50" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb10-51"><a href="#cb10-51" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 处理触摸结束</span></span>
<span id="cb10-52"><a href="#cb10-52" aria-hidden="true" tabindex="-1"></a>        handleTouchEnd<span class="op">();</span></span>
<span id="cb10-53"><a href="#cb10-53" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-54"><a href="#cb10-54" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-55"><a href="#cb10-55" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> handleTouchStart<span class="op">(</span><span class="at">const</span> Vector2<span class="op">&amp;</span> position<span class="op">)</span></span>
<span id="cb10-56"><a href="#cb10-56" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-57"><a href="#cb10-57" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 实现触摸开始逻辑</span></span>
<span id="cb10-58"><a href="#cb10-58" aria-hidden="true" tabindex="-1"></a>        Node<span class="op">*</span> attachedNode <span class="op">=</span> getAttachedNode<span class="op">();</span></span>
<span id="cb10-59"><a href="#cb10-59" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>attachedNode<span class="op">)</span></span>
<span id="cb10-60"><a href="#cb10-60" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb10-61"><a href="#cb10-61" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 例如：高亮显示</span></span>
<span id="cb10-62"><a href="#cb10-62" aria-hidden="true" tabindex="-1"></a>            attachedNode<span class="op">-&gt;</span>setProperty<span class="op">(</span>Node<span class="op">::</span>OpacityProperty<span class="op">,</span> <span class="fl">0.8</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb10-63"><a href="#cb10-63" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb10-64"><a href="#cb10-64" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-65"><a href="#cb10-65" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-66"><a href="#cb10-66" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> handleTouchDrag<span class="op">(</span><span class="at">const</span> Vector2<span class="op">&amp;</span> delta<span class="op">)</span></span>
<span id="cb10-67"><a href="#cb10-67" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-68"><a href="#cb10-68" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 实现拖拽逻辑</span></span>
<span id="cb10-69"><a href="#cb10-69" aria-hidden="true" tabindex="-1"></a>        Node<span class="op">*</span> attachedNode <span class="op">=</span> getAttachedNode<span class="op">();</span></span>
<span id="cb10-70"><a href="#cb10-70" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>attachedNode<span class="op">)</span></span>
<span id="cb10-71"><a href="#cb10-71" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb10-72"><a href="#cb10-72" aria-hidden="true" tabindex="-1"></a>            Vector2 currentPos <span class="op">=</span> attachedNode<span class="op">-&gt;</span>getTranslation<span class="op">();</span></span>
<span id="cb10-73"><a href="#cb10-73" aria-hidden="true" tabindex="-1"></a>            attachedNode<span class="op">-&gt;</span>setTranslation<span class="op">(</span>currentPos <span class="op">+</span> delta<span class="op">);</span></span>
<span id="cb10-74"><a href="#cb10-74" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb10-75"><a href="#cb10-75" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-76"><a href="#cb10-76" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-77"><a href="#cb10-77" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> handleTouchEnd<span class="op">()</span></span>
<span id="cb10-78"><a href="#cb10-78" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-79"><a href="#cb10-79" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 实现触摸结束逻辑</span></span>
<span id="cb10-80"><a href="#cb10-80" aria-hidden="true" tabindex="-1"></a>        Node<span class="op">*</span> attachedNode <span class="op">=</span> getAttachedNode<span class="op">();</span></span>
<span id="cb10-81"><a href="#cb10-81" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span>attachedNode<span class="op">)</span></span>
<span id="cb10-82"><a href="#cb10-82" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb10-83"><a href="#cb10-83" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 恢复正常状态</span></span>
<span id="cb10-84"><a href="#cb10-84" aria-hidden="true" tabindex="-1"></a>            attachedNode<span class="op">-&gt;</span>setProperty<span class="op">(</span>Node<span class="op">::</span>OpacityProperty<span class="op">,</span> <span class="fl">1.0</span><span class="bu">f</span><span class="op">);</span></span>
<span id="cb10-85"><a href="#cb10-85" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb10-86"><a href="#cb10-86" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-87"><a href="#cb10-87" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb10-88"><a href="#cb10-88" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb10-89"><a href="#cb10-89" aria-hidden="true" tabindex="-1"></a>    Vector2 <span class="va">m_lastTouchPosition</span><span class="op">;</span></span>
<span id="cb10-90"><a href="#cb10-90" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> <span class="va">m_isTouching</span> <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb10-91"><a href="#cb10-91" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h2 id="最佳实践">最佳实践</h2>
<h3 id="性能优化">1. 性能优化</h3>
<div class="sourceCode" id="cb11"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 内存池管理</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> MemoryPoolManager</span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a>    <span class="kw">template</span><span class="op">&lt;</span><span class="kw">typename</span> T<span class="op">&gt;</span></span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a>    T<span class="op">*</span> allocate<span class="op">()</span></span>
<span id="cb11-7"><a href="#cb11-7" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-8"><a href="#cb11-8" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">static_cast</span><span class="op">&lt;</span>T<span class="op">*&gt;(</span><span class="va">m_pool</span><span class="op">.</span>allocate<span class="op">(</span><span class="kw">sizeof</span><span class="op">(</span>T<span class="op">)));</span></span>
<span id="cb11-9"><a href="#cb11-9" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-10"><a href="#cb11-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-11"><a href="#cb11-11" aria-hidden="true" tabindex="-1"></a>    <span class="kw">template</span><span class="op">&lt;</span><span class="kw">typename</span> T<span class="op">&gt;</span></span>
<span id="cb11-12"><a href="#cb11-12" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> deallocate<span class="op">(</span>T<span class="op">*</span> ptr<span class="op">)</span></span>
<span id="cb11-13"><a href="#cb11-13" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-14"><a href="#cb11-14" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_pool</span><span class="op">.</span>deallocate<span class="op">(</span>ptr<span class="op">,</span> <span class="kw">sizeof</span><span class="op">(</span>T<span class="op">));</span></span>
<span id="cb11-15"><a href="#cb11-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-16"><a href="#cb11-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-17"><a href="#cb11-17" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb11-18"><a href="#cb11-18" aria-hidden="true" tabindex="-1"></a>    FixedSizePool <span class="va">m_pool</span><span class="op">;</span></span>
<span id="cb11-19"><a href="#cb11-19" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span>
<span id="cb11-20"><a href="#cb11-20" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-21"><a href="#cb11-21" aria-hidden="true" tabindex="-1"></a><span class="co">// 对象复用</span></span>
<span id="cb11-22"><a href="#cb11-22" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> UIElementPool</span>
<span id="cb11-23"><a href="#cb11-23" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb11-24"><a href="#cb11-24" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb11-25"><a href="#cb11-25" aria-hidden="true" tabindex="-1"></a>    Node2DSharedPtr getButton<span class="op">()</span></span>
<span id="cb11-26"><a href="#cb11-26" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-27"><a href="#cb11-27" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(!</span><span class="va">m_buttonPool</span><span class="op">.</span>empty<span class="op">())</span></span>
<span id="cb11-28"><a href="#cb11-28" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb11-29"><a href="#cb11-29" aria-hidden="true" tabindex="-1"></a>            Node2DSharedPtr button <span class="op">=</span> <span class="va">m_buttonPool</span><span class="op">.</span>back<span class="op">();</span></span>
<span id="cb11-30"><a href="#cb11-30" aria-hidden="true" tabindex="-1"></a>            <span class="va">m_buttonPool</span><span class="op">.</span>pop_back<span class="op">();</span></span>
<span id="cb11-31"><a href="#cb11-31" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> button<span class="op">;</span></span>
<span id="cb11-32"><a href="#cb11-32" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb11-33"><a href="#cb11-33" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb11-34"><a href="#cb11-34" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> createNewButton<span class="op">();</span></span>
<span id="cb11-35"><a href="#cb11-35" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-36"><a href="#cb11-36" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-37"><a href="#cb11-37" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> returnButton<span class="op">(</span>Node2DSharedPtr button<span class="op">)</span></span>
<span id="cb11-38"><a href="#cb11-38" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-39"><a href="#cb11-39" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 重置状态</span></span>
<span id="cb11-40"><a href="#cb11-40" aria-hidden="true" tabindex="-1"></a>        button<span class="op">-&gt;</span>setVisible<span class="op">(</span><span class="kw">false</span><span class="op">);</span></span>
<span id="cb11-41"><a href="#cb11-41" aria-hidden="true" tabindex="-1"></a>        button<span class="op">-&gt;</span>removeFromParent<span class="op">();</span></span>
<span id="cb11-42"><a href="#cb11-42" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb11-43"><a href="#cb11-43" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_buttonPool</span><span class="op">.</span>push_back<span class="op">(</span>button<span class="op">);</span></span>
<span id="cb11-44"><a href="#cb11-44" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-45"><a href="#cb11-45" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-46"><a href="#cb11-46" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb11-47"><a href="#cb11-47" aria-hidden="true" tabindex="-1"></a>    vector<span class="op">&lt;</span>Node2DSharedPtr<span class="op">&gt;</span> <span class="va">m_buttonPool</span><span class="op">;</span></span>
<span id="cb11-48"><a href="#cb11-48" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-49"><a href="#cb11-49" aria-hidden="true" tabindex="-1"></a>    Node2DSharedPtr createNewButton<span class="op">()</span></span>
<span id="cb11-50"><a href="#cb11-50" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-51"><a href="#cb11-51" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建新按钮的实现</span></span>
<span id="cb11-52"><a href="#cb11-52" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> Button2D<span class="op">::</span>create<span class="op">(</span>getDomain<span class="op">(),</span> <span class="st">&quot;PooledButton&quot;</span><span class="op">);</span></span>
<span id="cb11-53"><a href="#cb11-53" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-54"><a href="#cb11-54" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="资源管理-1">2. 资源管理</h3>
<div class="sourceCode" id="cb12"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 异步资源加载</span></span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> AsyncResourceLoader</span>
<span id="cb12-3"><a href="#cb12-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb12-4"><a href="#cb12-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb12-5"><a href="#cb12-5" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> loadResourceAsync<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> path<span class="op">,</span> function<span class="op">&lt;</span><span class="dt">void</span><span class="op">(</span>ResourceSharedPtr<span class="op">)&gt;</span> callback<span class="op">)</span></span>
<span id="cb12-6"><a href="#cb12-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-7"><a href="#cb12-7" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 在后台线程加载资源</span></span>
<span id="cb12-8"><a href="#cb12-8" aria-hidden="true" tabindex="-1"></a>        thread loadThread<span class="op">([</span><span class="kw">this</span><span class="op">,</span> path<span class="op">,</span> callback<span class="op">]()</span> <span class="op">{</span></span>
<span id="cb12-9"><a href="#cb12-9" aria-hidden="true" tabindex="-1"></a>            ResourceSharedPtr resource <span class="op">=</span> loadResourceFromFile<span class="op">(</span>path<span class="op">);</span></span>
<span id="cb12-10"><a href="#cb12-10" aria-hidden="true" tabindex="-1"></a>            </span>
<span id="cb12-11"><a href="#cb12-11" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 在主线程中调用回调</span></span>
<span id="cb12-12"><a href="#cb12-12" aria-hidden="true" tabindex="-1"></a>            <span class="va">m_mainThreadQueue</span><span class="op">.</span>push<span class="op">([</span>callback<span class="op">,</span> resource<span class="op">]()</span> <span class="op">{</span></span>
<span id="cb12-13"><a href="#cb12-13" aria-hidden="true" tabindex="-1"></a>                callback<span class="op">(</span>resource<span class="op">);</span></span>
<span id="cb12-14"><a href="#cb12-14" aria-hidden="true" tabindex="-1"></a>            <span class="op">});</span></span>
<span id="cb12-15"><a href="#cb12-15" aria-hidden="true" tabindex="-1"></a>        <span class="op">});</span></span>
<span id="cb12-16"><a href="#cb12-16" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb12-17"><a href="#cb12-17" aria-hidden="true" tabindex="-1"></a>        loadThread<span class="op">.</span>detach<span class="op">();</span></span>
<span id="cb12-18"><a href="#cb12-18" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-19"><a href="#cb12-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-20"><a href="#cb12-20" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> processMainThreadQueue<span class="op">()</span></span>
<span id="cb12-21"><a href="#cb12-21" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-22"><a href="#cb12-22" aria-hidden="true" tabindex="-1"></a>        <span class="cf">while</span> <span class="op">(!</span><span class="va">m_mainThreadQueue</span><span class="op">.</span>empty<span class="op">())</span></span>
<span id="cb12-23"><a href="#cb12-23" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb12-24"><a href="#cb12-24" aria-hidden="true" tabindex="-1"></a>            <span class="kw">auto</span> task <span class="op">=</span> <span class="va">m_mainThreadQueue</span><span class="op">.</span>front<span class="op">();</span></span>
<span id="cb12-25"><a href="#cb12-25" aria-hidden="true" tabindex="-1"></a>            <span class="va">m_mainThreadQueue</span><span class="op">.</span>pop<span class="op">();</span></span>
<span id="cb12-26"><a href="#cb12-26" aria-hidden="true" tabindex="-1"></a>            task<span class="op">();</span></span>
<span id="cb12-27"><a href="#cb12-27" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb12-28"><a href="#cb12-28" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-29"><a href="#cb12-29" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-30"><a href="#cb12-30" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb12-31"><a href="#cb12-31" aria-hidden="true" tabindex="-1"></a>    queue<span class="op">&lt;</span>function<span class="op">&lt;</span><span class="dt">void</span><span class="op">()&gt;&gt;</span> <span class="va">m_mainThreadQueue</span><span class="op">;</span></span>
<span id="cb12-32"><a href="#cb12-32" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-33"><a href="#cb12-33" aria-hidden="true" tabindex="-1"></a>    ResourceSharedPtr loadResourceFromFile<span class="op">(</span><span class="at">const</span> string<span class="op">&amp;</span> path<span class="op">)</span></span>
<span id="cb12-34"><a href="#cb12-34" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-35"><a href="#cb12-35" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 实际的文件加载逻辑</span></span>
<span id="cb12-36"><a href="#cb12-36" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="kw">nullptr</span><span class="op">;</span></span>
<span id="cb12-37"><a href="#cb12-37" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-38"><a href="#cb12-38" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<h3 id="状态管理">3. 状态管理</h3>
<div class="sourceCode" id="cb13"><pre
class="sourceCode cpp"><code class="sourceCode cpp"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 状态机实现</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a><span class="kw">class</span> UIStateMachine</span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb13-4"><a href="#cb13-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span><span class="op">:</span></span>
<span id="cb13-5"><a href="#cb13-5" aria-hidden="true" tabindex="-1"></a>    <span class="kw">enum</span> <span class="kw">class</span> State</span>
<span id="cb13-6"><a href="#cb13-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-7"><a href="#cb13-7" aria-hidden="true" tabindex="-1"></a>        Idle<span class="op">,</span></span>
<span id="cb13-8"><a href="#cb13-8" aria-hidden="true" tabindex="-1"></a>        Navigation<span class="op">,</span></span>
<span id="cb13-9"><a href="#cb13-9" aria-hidden="true" tabindex="-1"></a>        MediaPlayer<span class="op">,</span></span>
<span id="cb13-10"><a href="#cb13-10" aria-hidden="true" tabindex="-1"></a>        Settings<span class="op">,</span></span>
<span id="cb13-11"><a href="#cb13-11" aria-hidden="true" tabindex="-1"></a>        Emergency</span>
<span id="cb13-12"><a href="#cb13-12" aria-hidden="true" tabindex="-1"></a>    <span class="op">};</span></span>
<span id="cb13-13"><a href="#cb13-13" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-14"><a href="#cb13-14" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> setState<span class="op">(</span>State newState<span class="op">)</span></span>
<span id="cb13-15"><a href="#cb13-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-16"><a href="#cb13-16" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">(</span><span class="va">m_currentState</span> <span class="op">==</span> newState<span class="op">)</span> <span class="cf">return</span><span class="op">;</span></span>
<span id="cb13-17"><a href="#cb13-17" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb13-18"><a href="#cb13-18" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 退出当前状态</span></span>
<span id="cb13-19"><a href="#cb13-19" aria-hidden="true" tabindex="-1"></a>        exitState<span class="op">(</span><span class="va">m_currentState</span><span class="op">);</span></span>
<span id="cb13-20"><a href="#cb13-20" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb13-21"><a href="#cb13-21" aria-hidden="true" tabindex="-1"></a>        State previousState <span class="op">=</span> <span class="va">m_currentState</span><span class="op">;</span></span>
<span id="cb13-22"><a href="#cb13-22" aria-hidden="true" tabindex="-1"></a>        <span class="va">m_currentState</span> <span class="op">=</span> newState<span class="op">;</span></span>
<span id="cb13-23"><a href="#cb13-23" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb13-24"><a href="#cb13-24" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 进入新状态</span></span>
<span id="cb13-25"><a href="#cb13-25" aria-hidden="true" tabindex="-1"></a>        enterState<span class="op">(</span><span class="va">m_currentState</span><span class="op">,</span> previousState<span class="op">);</span></span>
<span id="cb13-26"><a href="#cb13-26" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-27"><a href="#cb13-27" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-28"><a href="#cb13-28" aria-hidden="true" tabindex="-1"></a><span class="kw">private</span><span class="op">:</span></span>
<span id="cb13-29"><a href="#cb13-29" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> enterState<span class="op">(</span>State state<span class="op">,</span> State previousState<span class="op">)</span></span>
<span id="cb13-30"><a href="#cb13-30" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-31"><a href="#cb13-31" aria-hidden="true" tabindex="-1"></a>        <span class="cf">switch</span> <span class="op">(</span>state<span class="op">)</span></span>
<span id="cb13-32"><a href="#cb13-32" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb13-33"><a href="#cb13-33" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> State<span class="op">::</span>Navigation<span class="op">:</span></span>
<span id="cb13-34"><a href="#cb13-34" aria-hidden="true" tabindex="-1"></a>                showNavigationUI<span class="op">();</span></span>
<span id="cb13-35"><a href="#cb13-35" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb13-36"><a href="#cb13-36" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> State<span class="op">::</span>MediaPlayer<span class="op">:</span></span>
<span id="cb13-37"><a href="#cb13-37" aria-hidden="true" tabindex="-1"></a>                showMediaPlayerUI<span class="op">();</span></span>
<span id="cb13-38"><a href="#cb13-38" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb13-39"><a href="#cb13-39" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> State<span class="op">::</span>Settings<span class="op">:</span></span>
<span id="cb13-40"><a href="#cb13-40" aria-hidden="true" tabindex="-1"></a>                showSettingsUI<span class="op">();</span></span>
<span id="cb13-41"><a href="#cb13-41" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb13-42"><a href="#cb13-42" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> State<span class="op">::</span>Emergency<span class="op">:</span></span>
<span id="cb13-43"><a href="#cb13-43" aria-hidden="true" tabindex="-1"></a>                showEmergencyUI<span class="op">();</span></span>
<span id="cb13-44"><a href="#cb13-44" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb13-45"><a href="#cb13-45" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb13-46"><a href="#cb13-46" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-47"><a href="#cb13-47" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-48"><a href="#cb13-48" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> exitState<span class="op">(</span>State state<span class="op">)</span></span>
<span id="cb13-49"><a href="#cb13-49" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-50"><a href="#cb13-50" aria-hidden="true" tabindex="-1"></a>        <span class="cf">switch</span> <span class="op">(</span>state<span class="op">)</span></span>
<span id="cb13-51"><a href="#cb13-51" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb13-52"><a href="#cb13-52" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> State<span class="op">::</span>Navigation<span class="op">:</span></span>
<span id="cb13-53"><a href="#cb13-53" aria-hidden="true" tabindex="-1"></a>                hideNavigationUI<span class="op">();</span></span>
<span id="cb13-54"><a href="#cb13-54" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb13-55"><a href="#cb13-55" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> State<span class="op">::</span>MediaPlayer<span class="op">:</span></span>
<span id="cb13-56"><a href="#cb13-56" aria-hidden="true" tabindex="-1"></a>                hideMediaPlayerUI<span class="op">();</span></span>
<span id="cb13-57"><a href="#cb13-57" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb13-58"><a href="#cb13-58" aria-hidden="true" tabindex="-1"></a>            <span class="cf">case</span> State<span class="op">::</span>Settings<span class="op">:</span></span>
<span id="cb13-59"><a href="#cb13-59" aria-hidden="true" tabindex="-1"></a>                hideSettingsUI<span class="op">();</span></span>
<span id="cb13-60"><a href="#cb13-60" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span><span class="op">;</span></span>
<span id="cb13-61"><a href="#cb13-61" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb13-62"><a href="#cb13-62" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-63"><a href="#cb13-63" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-64"><a href="#cb13-64" aria-hidden="true" tabindex="-1"></a>    State <span class="va">m_currentState</span> <span class="op">=</span> State<span class="op">::</span>Idle<span class="op">;</span></span>
<span id="cb13-65"><a href="#cb13-65" aria-hidden="true" tabindex="-1"></a><span class="op">};</span></span></code></pre></div>
<hr />
<p><em>Kanzi 专为嵌入式 HMI
开发而设计，在汽车、工业和消费电子领域具有独特优势。更多详细信息请参考
<a href="https://www.rightware.com/kanzi/documentation/">Kanzi
官方文档</a></em></p>
</div><div lang="en" style="display: none;"><h1>[English translation pending]</h1><p>The content for this page has not been translated yet. Please check back later.</p></div>
                </main>
            </div>
        </section>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">Qt 3D 开发指南</h4>
                    <p data-i18n="footer-subtitle">专业的跨平台 3D 应用开发解决方案</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.</p>
            </div>
        </div>
    </footer>

    <script src="../../assets/js/prism.js"></script>
    <script src="../../assets/js/docs.js"></script>
</body>
</html> 