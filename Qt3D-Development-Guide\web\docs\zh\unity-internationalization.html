<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unity 国际化与本地化指南</title> <!-- The title will be replaced for each page -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/docs.css">
    <link rel="stylesheet" href="../../assets/css/prism.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html" class="logo-text" data-i18n="nav-logo-text">3D 引擎指南</a>
            </div>
            <div class="nav-menu">
                <a href="../index.html#engines" class="nav-link" data-i18n="nav-engines">引擎对比</a>
                <a href="../index.html#features" class="nav-link" data-i18n="nav-features">特性分析</a>
                <a href="../index.html#i18n" class="nav-link" data-i18n="nav-guides">国际化</a>
                <a href="../index.html#docs" class="nav-link" data-i18n="nav-resources">文档</a>
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="zh" data-i18n="lang-zh">中文</option>
                        <option value="en" data-i18n="lang-en">English</option>
                    </select>
                </div>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 文档内容区域 -->
    <div class="doc-layout container">
        <!-- 左侧目录 -->
        <aside class="doc-sidebar card">
            <div class="toc-container">
                <!-- 目录内容将通过JavaScript动态生成 -->
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <section class="doc-section card">
            <div class="doc-container">
                <main class="doc-content">
                    <div lang="zh"><h1 id="unity-国际化与本地化指南">Unity 国际化与本地化指南</h1>
<h2 id="目录">目录</h2>
<ol type="1">
<li><a href="#概述">概述</a></li>
<li><a href="#unity-本地化包">Unity 本地化包</a></li>
<li><a href="#文本本地化">文本本地化</a></li>
<li><a href="#资源本地化">资源本地化</a></li>
<li><a href="#音频本地化">音频本地化</a></li>
<li><a href="#脚本国际化">脚本国际化</a></li>
<li><a href="#最佳实践">最佳实践</a></li>
<li><a href="#工具与工作流">工具与工作流</a></li>
</ol>
<h2 id="概述">概述</h2>
<p>Unity 提供了强大的本地化系统，通过 Localization Package
支持多语言游戏和应用的开发。Unity
的国际化解决方案涵盖了文本、音频、纹理等各种资源的本地化，并提供了完整的工具链来管理多语言内容。</p>
<h3 id="主要特性">主要特性</h3>
<ul>
<li><strong>统一本地化系统</strong>：集成的本地化包管理所有类型的资源</li>
<li><strong>实时语言切换</strong>：运行时动态切换语言</li>
<li><strong>智能回退</strong>：自动回退到默认语言</li>
<li><strong>CSV 导入导出</strong>：支持与翻译团队协作</li>
<li><strong>伪本地化</strong>：测试和调试本地化功能</li>
<li><strong>平台特定本地化</strong>：针对不同平台的特殊处理</li>
</ul>
<h2 id="unity-本地化包">Unity 本地化包</h2>
<h3 id="安装本地化包">安装本地化包</h3>
<div class="sourceCode" id="cb1"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 通过 Package Manager 安装</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="co">// Window &gt; Package Manager</span></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a><span class="co">// 搜索 &quot;Localization&quot;</span></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a><span class="co">// 安装 &quot;Localization&quot; 包</span></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a><span class="co">// 或通过 manifest.json 添加</span></span>
<span id="cb1-7"><a href="#cb1-7" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb1-8"><a href="#cb1-8" aria-hidden="true" tabindex="-1"></a>  <span class="st">&quot;dependencies&quot;</span><span class="op">:</span> <span class="op">{</span></span>
<span id="cb1-9"><a href="#cb1-9" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;com.unity.localization&quot;</span><span class="op">:</span> <span class="st">&quot;1.4.4&quot;</span></span>
<span id="cb1-10"><a href="#cb1-10" aria-hidden="true" tabindex="-1"></a>  <span class="op">}</span></span>
<span id="cb1-11"><a href="#cb1-11" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="基础设置">基础设置</h3>
<div class="sourceCode" id="cb2"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">;</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">;</span></span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">;</span></span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> LocalizationManager <span class="op">:</span> MonoBehaviour</span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb2-7"><a href="#cb2-7" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">Start</span><span class="op">()</span></span>
<span id="cb2-8"><a href="#cb2-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb2-9"><a href="#cb2-9" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 初始化本地化系统</span></span>
<span id="cb2-10"><a href="#cb2-10" aria-hidden="true" tabindex="-1"></a>        LocalizationSettings<span class="op">.</span><span class="fu">InitializationOperation</span><span class="op">.</span><span class="fu">Completed</span> <span class="op">+=</span> OnLocalizationInitialized<span class="op">;</span></span>
<span id="cb2-11"><a href="#cb2-11" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-12"><a href="#cb2-12" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb2-13"><a href="#cb2-13" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">OnLocalizationInitialized</span><span class="op">(</span>UnityEngine<span class="op">.</span><span class="fu">ResourceManagement</span><span class="op">.</span><span class="fu">AsyncOperations</span><span class="op">.</span><span class="fu">AsyncOperationHandle</span> obj<span class="op">)</span></span>
<span id="cb2-14"><a href="#cb2-14" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb2-15"><a href="#cb2-15" aria-hidden="true" tabindex="-1"></a>        Debug<span class="op">.</span><span class="fu">Log</span><span class="op">(</span><span class="st">&quot;Localization system initialized&quot;</span><span class="op">);</span></span>
<span id="cb2-16"><a href="#cb2-16" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb2-17"><a href="#cb2-17" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 设置默认语言</span></span>
<span id="cb2-18"><a href="#cb2-18" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> availableLocales <span class="op">=</span> LocalizationSettings<span class="op">.</span><span class="fu">AvailableLocales</span><span class="op">.</span><span class="fu">Locales</span><span class="op">;</span></span>
<span id="cb2-19"><a href="#cb2-19" aria-hidden="true" tabindex="-1"></a>        <span class="kw">if</span> <span class="op">(</span>availableLocales<span class="op">.</span><span class="fu">Count</span> <span class="op">&gt;</span> <span class="dv">0</span><span class="op">)</span></span>
<span id="cb2-20"><a href="#cb2-20" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb2-21"><a href="#cb2-21" aria-hidden="true" tabindex="-1"></a>            LocalizationSettings<span class="op">.</span><span class="fu">SelectedLocale</span> <span class="op">=</span> availableLocales<span class="op">[</span><span class="dv">0</span><span class="op">];</span></span>
<span id="cb2-22"><a href="#cb2-22" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb2-23"><a href="#cb2-23" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-24"><a href="#cb2-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb2-25"><a href="#cb2-25" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">void</span> <span class="fu">ChangeLanguage</span><span class="op">(</span><span class="dt">string</span> localeCode<span class="op">)</span></span>
<span id="cb2-26"><a href="#cb2-26" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb2-27"><a href="#cb2-27" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> locale <span class="op">=</span> LocalizationSettings<span class="op">.</span><span class="fu">AvailableLocales</span><span class="op">.</span><span class="fu">GetLocale</span><span class="op">(</span>localeCode<span class="op">);</span></span>
<span id="cb2-28"><a href="#cb2-28" aria-hidden="true" tabindex="-1"></a>        <span class="kw">if</span> <span class="op">(</span>locale <span class="op">!=</span> <span class="kw">null</span><span class="op">)</span></span>
<span id="cb2-29"><a href="#cb2-29" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb2-30"><a href="#cb2-30" aria-hidden="true" tabindex="-1"></a>            LocalizationSettings<span class="op">.</span><span class="fu">SelectedLocale</span> <span class="op">=</span> locale<span class="op">;</span></span>
<span id="cb2-31"><a href="#cb2-31" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb2-32"><a href="#cb2-32" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-33"><a href="#cb2-33" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="语言环境配置">语言环境配置</h3>
<div class="sourceCode" id="cb3"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 创建语言环境资源</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a><span class="op">[</span><span class="fu">CreateAssetMenu</span><span class="op">(</span>fileName <span class="op">=</span> <span class="st">&quot;LocaleSettings&quot;</span><span class="op">,</span> menuName <span class="op">=</span> <span class="st">&quot;Localization/Locale Settings&quot;</span><span class="op">)]</span></span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> LocaleSettings <span class="op">:</span> ScriptableObject</span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>System<span class="op">.</span><span class="fu">Serializable</span><span class="op">]</span></span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="kw">class</span> LocaleInfo</span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a>        <span class="kw">public</span> <span class="dt">string</span> localeCode<span class="op">;</span></span>
<span id="cb3-9"><a href="#cb3-9" aria-hidden="true" tabindex="-1"></a>        <span class="kw">public</span> <span class="dt">string</span> displayName<span class="op">;</span></span>
<span id="cb3-10"><a href="#cb3-10" aria-hidden="true" tabindex="-1"></a>        <span class="kw">public</span> Sprite flag<span class="op">;</span></span>
<span id="cb3-11"><a href="#cb3-11" aria-hidden="true" tabindex="-1"></a>        <span class="kw">public</span> <span class="dt">bool</span> isRightToLeft<span class="op">;</span></span>
<span id="cb3-12"><a href="#cb3-12" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-13"><a href="#cb3-13" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb3-14"><a href="#cb3-14" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> LocaleInfo<span class="op">[]</span> supportedLocales <span class="op">=</span> <span class="kw">new</span> LocaleInfo<span class="op">[]</span></span>
<span id="cb3-15"><a href="#cb3-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-16"><a href="#cb3-16" aria-hidden="true" tabindex="-1"></a>        <span class="kw">new</span> LocaleInfo <span class="op">{</span> localeCode <span class="op">=</span> <span class="st">&quot;en&quot;</span><span class="op">,</span> displayName <span class="op">=</span> <span class="st">&quot;English&quot;</span><span class="op">,</span> isRightToLeft <span class="op">=</span> <span class="kw">false</span> <span class="op">},</span></span>
<span id="cb3-17"><a href="#cb3-17" aria-hidden="true" tabindex="-1"></a>        <span class="kw">new</span> LocaleInfo <span class="op">{</span> localeCode <span class="op">=</span> <span class="st">&quot;zh-CN&quot;</span><span class="op">,</span> displayName <span class="op">=</span> <span class="st">&quot;简体中文&quot;</span><span class="op">,</span> isRightToLeft <span class="op">=</span> <span class="kw">false</span> <span class="op">},</span></span>
<span id="cb3-18"><a href="#cb3-18" aria-hidden="true" tabindex="-1"></a>        <span class="kw">new</span> LocaleInfo <span class="op">{</span> localeCode <span class="op">=</span> <span class="st">&quot;ja&quot;</span><span class="op">,</span> displayName <span class="op">=</span> <span class="st">&quot;日本語&quot;</span><span class="op">,</span> isRightToLeft <span class="op">=</span> <span class="kw">false</span> <span class="op">},</span></span>
<span id="cb3-19"><a href="#cb3-19" aria-hidden="true" tabindex="-1"></a>        <span class="kw">new</span> LocaleInfo <span class="op">{</span> localeCode <span class="op">=</span> <span class="st">&quot;ar&quot;</span><span class="op">,</span> displayName <span class="op">=</span> <span class="st">&quot;العربية&quot;</span><span class="op">,</span> isRightToLeft <span class="op">=</span> <span class="kw">true</span> <span class="op">}</span></span>
<span id="cb3-20"><a href="#cb3-20" aria-hidden="true" tabindex="-1"></a>    <span class="op">};</span></span>
<span id="cb3-21"><a href="#cb3-21" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb3-22"><a href="#cb3-22" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> LocaleInfo <span class="fu">GetLocaleInfo</span><span class="op">(</span><span class="dt">string</span> localeCode<span class="op">)</span></span>
<span id="cb3-23"><a href="#cb3-23" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb3-24"><a href="#cb3-24" aria-hidden="true" tabindex="-1"></a>        <span class="kw">return</span> System<span class="op">.</span><span class="fu">Array</span><span class="op">.</span><span class="fu">Find</span><span class="op">(</span>supportedLocales<span class="op">,</span> locale <span class="op">=&gt;</span> locale<span class="op">.</span><span class="fu">localeCode</span> <span class="op">==</span> localeCode<span class="op">);</span></span>
<span id="cb3-25"><a href="#cb3-25" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-26"><a href="#cb3-26" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="文本本地化">文本本地化</h2>
<h3 id="本地化字符串表">本地化字符串表</h3>
<div class="sourceCode" id="cb4"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">;</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">;</span></span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Components</span><span class="op">;</span></span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> LocalizedTextExample <span class="op">:</span> MonoBehaviour</span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> LocalizedString welcomeMessage <span class="op">=</span> <span class="kw">new</span> <span class="fu">LocalizedString</span><span class="op">(</span><span class="st">&quot;UI&quot;</span><span class="op">,</span> <span class="st">&quot;welcome_message&quot;</span><span class="op">);</span></span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> LocalizedString playerName <span class="op">=</span> <span class="kw">new</span> <span class="fu">LocalizedString</span><span class="op">(</span><span class="st">&quot;Game&quot;</span><span class="op">,</span> <span class="st">&quot;player_name&quot;</span><span class="op">);</span></span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">Start</span><span class="op">()</span></span>
<span id="cb4-11"><a href="#cb4-11" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-12"><a href="#cb4-12" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 获取本地化文本</span></span>
<span id="cb4-13"><a href="#cb4-13" aria-hidden="true" tabindex="-1"></a>        <span class="dt">string</span> localizedWelcome <span class="op">=</span> welcomeMessage<span class="op">.</span><span class="fu">GetLocalizedString</span><span class="op">();</span></span>
<span id="cb4-14"><a href="#cb4-14" aria-hidden="true" tabindex="-1"></a>        Debug<span class="op">.</span><span class="fu">Log</span><span class="op">(</span>localizedWelcome<span class="op">);</span></span>
<span id="cb4-15"><a href="#cb4-15" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb4-16"><a href="#cb4-16" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 带参数的本地化文本</span></span>
<span id="cb4-17"><a href="#cb4-17" aria-hidden="true" tabindex="-1"></a>        <span class="dt">string</span> localizedPlayerName <span class="op">=</span> playerName<span class="op">.</span><span class="fu">GetLocalizedString</span><span class="op">(</span><span class="st">&quot;John&quot;</span><span class="op">);</span></span>
<span id="cb4-18"><a href="#cb4-18" aria-hidden="true" tabindex="-1"></a>        Debug<span class="op">.</span><span class="fu">Log</span><span class="op">(</span>localizedPlayerName<span class="op">);</span></span>
<span id="cb4-19"><a href="#cb4-19" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-20"><a href="#cb4-20" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-21"><a href="#cb4-21" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 动态创建本地化字符串</span></span>
<span id="cb4-22"><a href="#cb4-22" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">void</span> <span class="fu">ShowDynamicMessage</span><span class="op">(</span><span class="dt">string</span> tableReference<span class="op">,</span> <span class="dt">string</span> entryReference<span class="op">)</span></span>
<span id="cb4-23"><a href="#cb4-23" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-24"><a href="#cb4-24" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> localizedString <span class="op">=</span> <span class="kw">new</span> <span class="fu">LocalizedString</span><span class="op">(</span>tableReference<span class="op">,</span> entryReference<span class="op">);</span></span>
<span id="cb4-25"><a href="#cb4-25" aria-hidden="true" tabindex="-1"></a>        <span class="dt">string</span> message <span class="op">=</span> localizedString<span class="op">.</span><span class="fu">GetLocalizedString</span><span class="op">();</span></span>
<span id="cb4-26"><a href="#cb4-26" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb4-27"><a href="#cb4-27" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 显示消息</span></span>
<span id="cb4-28"><a href="#cb4-28" aria-hidden="true" tabindex="-1"></a>        <span class="fu">ShowMessage</span><span class="op">(</span>message<span class="op">);</span></span>
<span id="cb4-29"><a href="#cb4-29" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-30"><a href="#cb4-30" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-31"><a href="#cb4-31" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="dt">void</span> <span class="fu">ShowMessage</span><span class="op">(</span><span class="dt">string</span> message<span class="op">)</span></span>
<span id="cb4-32"><a href="#cb4-32" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb4-33"><a href="#cb4-33" aria-hidden="true" tabindex="-1"></a>        <span class="co">// UI 显示逻辑</span></span>
<span id="cb4-34"><a href="#cb4-34" aria-hidden="true" tabindex="-1"></a>        Debug<span class="op">.</span><span class="fu">Log</span><span class="op">(</span>$<span class="st">&quot;Showing message: {message}&quot;</span><span class="op">);</span></span>
<span id="cb4-35"><a href="#cb4-35" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-36"><a href="#cb4-36" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="ui-文本组件">UI 文本组件</h3>
<div class="sourceCode" id="cb5"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">;</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">UI</span><span class="op">;</span></span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Components</span><span class="op">;</span></span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> LocalizedUIText <span class="op">:</span> MonoBehaviour</span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> Text uiText<span class="op">;</span></span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> LocalizeStringEvent localizeStringEvent<span class="op">;</span></span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-10"><a href="#cb5-10" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">Start</span><span class="op">()</span></span>
<span id="cb5-11"><a href="#cb5-11" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb5-12"><a href="#cb5-12" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 设置本地化事件</span></span>
<span id="cb5-13"><a href="#cb5-13" aria-hidden="true" tabindex="-1"></a>        <span class="kw">if</span> <span class="op">(</span>localizeStringEvent <span class="op">==</span> <span class="kw">null</span><span class="op">)</span></span>
<span id="cb5-14"><a href="#cb5-14" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb5-15"><a href="#cb5-15" aria-hidden="true" tabindex="-1"></a>            localizeStringEvent <span class="op">=</span> GetComponent<span class="op">&lt;</span>LocalizeStringEvent<span class="op">&gt;();</span></span>
<span id="cb5-16"><a href="#cb5-16" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb5-17"><a href="#cb5-17" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb5-18"><a href="#cb5-18" aria-hidden="true" tabindex="-1"></a>        <span class="kw">if</span> <span class="op">(</span>localizeStringEvent <span class="op">!=</span> <span class="kw">null</span><span class="op">)</span></span>
<span id="cb5-19"><a href="#cb5-19" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb5-20"><a href="#cb5-20" aria-hidden="true" tabindex="-1"></a>            localizeStringEvent<span class="op">.</span><span class="fu">OnUpdateString</span><span class="op">.</span><span class="fu">AddListener</span><span class="op">(</span>UpdateText<span class="op">);</span></span>
<span id="cb5-21"><a href="#cb5-21" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb5-22"><a href="#cb5-22" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-23"><a href="#cb5-23" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-24"><a href="#cb5-24" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">UpdateText</span><span class="op">(</span><span class="dt">string</span> localizedText<span class="op">)</span></span>
<span id="cb5-25"><a href="#cb5-25" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb5-26"><a href="#cb5-26" aria-hidden="true" tabindex="-1"></a>        <span class="kw">if</span> <span class="op">(</span>uiText <span class="op">!=</span> <span class="kw">null</span><span class="op">)</span></span>
<span id="cb5-27"><a href="#cb5-27" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb5-28"><a href="#cb5-28" aria-hidden="true" tabindex="-1"></a>            uiText<span class="op">.</span><span class="fu">text</span> <span class="op">=</span> localizedText<span class="op">;</span></span>
<span id="cb5-29"><a href="#cb5-29" aria-hidden="true" tabindex="-1"></a>            </span>
<span id="cb5-30"><a href="#cb5-30" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 处理从右到左的文本</span></span>
<span id="cb5-31"><a href="#cb5-31" aria-hidden="true" tabindex="-1"></a>            <span class="fu">HandleRTLText</span><span class="op">();</span></span>
<span id="cb5-32"><a href="#cb5-32" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb5-33"><a href="#cb5-33" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-34"><a href="#cb5-34" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-35"><a href="#cb5-35" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">HandleRTLText</span><span class="op">()</span></span>
<span id="cb5-36"><a href="#cb5-36" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb5-37"><a href="#cb5-37" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> currentLocale <span class="op">=</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">SelectedLocale</span><span class="op">;</span></span>
<span id="cb5-38"><a href="#cb5-38" aria-hidden="true" tabindex="-1"></a>        <span class="kw">if</span> <span class="op">(</span>currentLocale <span class="op">!=</span> <span class="kw">null</span><span class="op">)</span></span>
<span id="cb5-39"><a href="#cb5-39" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb5-40"><a href="#cb5-40" aria-hidden="true" tabindex="-1"></a>            <span class="co">// 检查是否为从右到左的语言</span></span>
<span id="cb5-41"><a href="#cb5-41" aria-hidden="true" tabindex="-1"></a>            <span class="dt">bool</span> isRTL <span class="op">=</span> <span class="fu">IsRightToLeftLanguage</span><span class="op">(</span>currentLocale<span class="op">.</span><span class="fu">Identifier</span><span class="op">.</span><span class="fu">Code</span><span class="op">);</span></span>
<span id="cb5-42"><a href="#cb5-42" aria-hidden="true" tabindex="-1"></a>            </span>
<span id="cb5-43"><a href="#cb5-43" aria-hidden="true" tabindex="-1"></a>            <span class="kw">if</span> <span class="op">(</span>isRTL<span class="op">)</span></span>
<span id="cb5-44"><a href="#cb5-44" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb5-45"><a href="#cb5-45" aria-hidden="true" tabindex="-1"></a>                <span class="co">// 调整文本对齐</span></span>
<span id="cb5-46"><a href="#cb5-46" aria-hidden="true" tabindex="-1"></a>                uiText<span class="op">.</span><span class="fu">alignment</span> <span class="op">=</span> TextAnchor<span class="op">.</span><span class="fu">MiddleRight</span><span class="op">;</span></span>
<span id="cb5-47"><a href="#cb5-47" aria-hidden="true" tabindex="-1"></a>                </span>
<span id="cb5-48"><a href="#cb5-48" aria-hidden="true" tabindex="-1"></a>                <span class="co">// 可能需要反转文本或使用 RTL 插件</span></span>
<span id="cb5-49"><a href="#cb5-49" aria-hidden="true" tabindex="-1"></a>                <span class="fu">ProcessRTLText</span><span class="op">();</span></span>
<span id="cb5-50"><a href="#cb5-50" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb5-51"><a href="#cb5-51" aria-hidden="true" tabindex="-1"></a>            <span class="kw">else</span></span>
<span id="cb5-52"><a href="#cb5-52" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb5-53"><a href="#cb5-53" aria-hidden="true" tabindex="-1"></a>                uiText<span class="op">.</span><span class="fu">alignment</span> <span class="op">=</span> TextAnchor<span class="op">.</span><span class="fu">MiddleLeft</span><span class="op">;</span></span>
<span id="cb5-54"><a href="#cb5-54" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb5-55"><a href="#cb5-55" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb5-56"><a href="#cb5-56" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-57"><a href="#cb5-57" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-58"><a href="#cb5-58" aria-hidden="true" tabindex="-1"></a>    <span class="dt">bool</span> <span class="fu">IsRightToLeftLanguage</span><span class="op">(</span><span class="dt">string</span> localeCode<span class="op">)</span></span>
<span id="cb5-59"><a href="#cb5-59" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb5-60"><a href="#cb5-60" aria-hidden="true" tabindex="-1"></a>        <span class="dt">string</span><span class="op">[]</span> rtlLanguages <span class="op">=</span> <span class="op">{</span> <span class="st">&quot;ar&quot;</span><span class="op">,</span> <span class="st">&quot;he&quot;</span><span class="op">,</span> <span class="st">&quot;fa&quot;</span><span class="op">,</span> <span class="st">&quot;ur&quot;</span> <span class="op">};</span></span>
<span id="cb5-61"><a href="#cb5-61" aria-hidden="true" tabindex="-1"></a>        <span class="kw">return</span> System<span class="op">.</span><span class="fu">Array</span><span class="op">.</span><span class="fu">Exists</span><span class="op">(</span>rtlLanguages<span class="op">,</span> lang <span class="op">=&gt;</span> localeCode<span class="op">.</span><span class="fu">StartsWith</span><span class="op">(</span>lang<span class="op">));</span></span>
<span id="cb5-62"><a href="#cb5-62" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-63"><a href="#cb5-63" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-64"><a href="#cb5-64" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">ProcessRTLText</span><span class="op">()</span></span>
<span id="cb5-65"><a href="#cb5-65" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb5-66"><a href="#cb5-66" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 使用 RTL 文本处理插件或自定义逻辑</span></span>
<span id="cb5-67"><a href="#cb5-67" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 例如：Arabic Support for Unity 插件</span></span>
<span id="cb5-68"><a href="#cb5-68" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-69"><a href="#cb5-69" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="复数形式处理">复数形式处理</h3>
<div class="sourceCode" id="cb6"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">;</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">;</span></span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">SmartFormat</span><span class="op">.</span><span class="fu">PersistentVariables</span><span class="op">;</span></span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-5"><a href="#cb6-5" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> PluralFormsExample <span class="op">:</span> MonoBehaviour</span>
<span id="cb6-6"><a href="#cb6-6" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb6-7"><a href="#cb6-7" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> LocalizedString itemCountMessage<span class="op">;</span></span>
<span id="cb6-8"><a href="#cb6-8" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-9"><a href="#cb6-9" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">void</span> <span class="fu">ShowItemCount</span><span class="op">(</span><span class="dt">int</span> count<span class="op">)</span></span>
<span id="cb6-10"><a href="#cb6-10" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb6-11"><a href="#cb6-11" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 设置变量</span></span>
<span id="cb6-12"><a href="#cb6-12" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> variable <span class="op">=</span> <span class="kw">new</span> IntVariable <span class="op">{</span> Value <span class="op">=</span> count <span class="op">};</span></span>
<span id="cb6-13"><a href="#cb6-13" aria-hidden="true" tabindex="-1"></a>        itemCountMessage<span class="op">.</span><span class="fu">Arguments</span> <span class="op">=</span> <span class="kw">new</span> <span class="dt">object</span><span class="op">[]</span> <span class="op">{</span> variable <span class="op">};</span></span>
<span id="cb6-14"><a href="#cb6-14" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb6-15"><a href="#cb6-15" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 获取本地化文本（支持复数形式）</span></span>
<span id="cb6-16"><a href="#cb6-16" aria-hidden="true" tabindex="-1"></a>        <span class="dt">string</span> message <span class="op">=</span> itemCountMessage<span class="op">.</span><span class="fu">GetLocalizedString</span><span class="op">();</span></span>
<span id="cb6-17"><a href="#cb6-17" aria-hidden="true" tabindex="-1"></a>        Debug<span class="op">.</span><span class="fu">Log</span><span class="op">(</span>message<span class="op">);</span></span>
<span id="cb6-18"><a href="#cb6-18" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb6-19"><a href="#cb6-19" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb6-20"><a href="#cb6-20" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 在字符串表中的条目示例：</span></span>
<span id="cb6-21"><a href="#cb6-21" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 英文: &quot;You have {0} {0:item|items}&quot;</span></span>
<span id="cb6-22"><a href="#cb6-22" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 中文: &quot;你有 {0} 个物品&quot;</span></span>
<span id="cb6-23"><a href="#cb6-23" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 俄文: &quot;У вас есть {0} {0:предмет|предмета|предметов}&quot;</span></span>
<span id="cb6-24"><a href="#cb6-24" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="资源本地化">资源本地化</h2>
<h3 id="纹理本地化">纹理本地化</h3>
<div class="sourceCode" id="cb7"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">;</span></span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">;</span></span>
<span id="cb7-3"><a href="#cb7-3" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">UI</span><span class="op">;</span></span>
<span id="cb7-4"><a href="#cb7-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-5"><a href="#cb7-5" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> LocalizedImageExample <span class="op">:</span> MonoBehaviour</span>
<span id="cb7-6"><a href="#cb7-6" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb7-7"><a href="#cb7-7" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> Image targetImage<span class="op">;</span></span>
<span id="cb7-8"><a href="#cb7-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> LocalizedAsset<span class="op">&lt;</span>Sprite<span class="op">&gt;</span> localizedSprite<span class="op">;</span></span>
<span id="cb7-9"><a href="#cb7-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-10"><a href="#cb7-10" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">Start</span><span class="op">()</span></span>
<span id="cb7-11"><a href="#cb7-11" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-12"><a href="#cb7-12" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载本地化精灵</span></span>
<span id="cb7-13"><a href="#cb7-13" aria-hidden="true" tabindex="-1"></a>        <span class="fu">LoadLocalizedSprite</span><span class="op">();</span></span>
<span id="cb7-14"><a href="#cb7-14" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-15"><a href="#cb7-15" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 监听语言变化</span></span>
<span id="cb7-16"><a href="#cb7-16" aria-hidden="true" tabindex="-1"></a>        UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">SelectedLocaleChanged</span> <span class="op">+=</span> OnLocaleChanged<span class="op">;</span></span>
<span id="cb7-17"><a href="#cb7-17" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-18"><a href="#cb7-18" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-19"><a href="#cb7-19" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">LoadLocalizedSprite</span><span class="op">()</span></span>
<span id="cb7-20"><a href="#cb7-20" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-21"><a href="#cb7-21" aria-hidden="true" tabindex="-1"></a>        <span class="kw">if</span> <span class="op">(</span>localizedSprite<span class="op">.</span><span class="fu">IsEmpty</span><span class="op">)</span> <span class="kw">return</span><span class="op">;</span></span>
<span id="cb7-22"><a href="#cb7-22" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb7-23"><a href="#cb7-23" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> loadOperation <span class="op">=</span> localizedSprite<span class="op">.</span><span class="fu">LoadAssetAsync</span><span class="op">();</span></span>
<span id="cb7-24"><a href="#cb7-24" aria-hidden="true" tabindex="-1"></a>        loadOperation<span class="op">.</span><span class="fu">Completed</span> <span class="op">+=</span> <span class="op">(</span>operation<span class="op">)</span> <span class="op">=&gt;</span></span>
<span id="cb7-25"><a href="#cb7-25" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb7-26"><a href="#cb7-26" aria-hidden="true" tabindex="-1"></a>            <span class="kw">if</span> <span class="op">(</span>operation<span class="op">.</span><span class="fu">Status</span> <span class="op">==</span> UnityEngine<span class="op">.</span><span class="fu">ResourceManagement</span><span class="op">.</span><span class="fu">AsyncOperations</span><span class="op">.</span><span class="fu">AsyncOperationStatus</span><span class="op">.</span><span class="fu">Succeeded</span><span class="op">)</span></span>
<span id="cb7-27"><a href="#cb7-27" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb7-28"><a href="#cb7-28" aria-hidden="true" tabindex="-1"></a>                targetImage<span class="op">.</span><span class="fu">sprite</span> <span class="op">=</span> operation<span class="op">.</span><span class="fu">Result</span><span class="op">;</span></span>
<span id="cb7-29"><a href="#cb7-29" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb7-30"><a href="#cb7-30" aria-hidden="true" tabindex="-1"></a>        <span class="op">};</span></span>
<span id="cb7-31"><a href="#cb7-31" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-32"><a href="#cb7-32" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-33"><a href="#cb7-33" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">OnLocaleChanged</span><span class="op">(</span>UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Locale</span> locale<span class="op">)</span></span>
<span id="cb7-34"><a href="#cb7-34" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-35"><a href="#cb7-35" aria-hidden="true" tabindex="-1"></a>        <span class="fu">LoadLocalizedSprite</span><span class="op">();</span></span>
<span id="cb7-36"><a href="#cb7-36" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-37"><a href="#cb7-37" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb7-38"><a href="#cb7-38" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">OnDestroy</span><span class="op">()</span></span>
<span id="cb7-39"><a href="#cb7-39" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb7-40"><a href="#cb7-40" aria-hidden="true" tabindex="-1"></a>        UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">SelectedLocaleChanged</span> <span class="op">-=</span> OnLocaleChanged<span class="op">;</span></span>
<span id="cb7-41"><a href="#cb7-41" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb7-42"><a href="#cb7-42" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="预制体本地化">预制体本地化</h3>
<div class="sourceCode" id="cb8"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">;</span></span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">;</span></span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-4"><a href="#cb8-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> LocalizedPrefabManager <span class="op">:</span> MonoBehaviour</span>
<span id="cb8-5"><a href="#cb8-5" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb8-6"><a href="#cb8-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> LocalizedAsset<span class="op">&lt;</span>GameObject<span class="op">&gt;</span> localizedUIPrefab<span class="op">;</span></span>
<span id="cb8-7"><a href="#cb8-7" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> Transform parentTransform<span class="op">;</span></span>
<span id="cb8-8"><a href="#cb8-8" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-9"><a href="#cb8-9" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> GameObject currentInstance<span class="op">;</span></span>
<span id="cb8-10"><a href="#cb8-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-11"><a href="#cb8-11" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">Start</span><span class="op">()</span></span>
<span id="cb8-12"><a href="#cb8-12" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-13"><a href="#cb8-13" aria-hidden="true" tabindex="-1"></a>        <span class="fu">LoadLocalizedPrefab</span><span class="op">();</span></span>
<span id="cb8-14"><a href="#cb8-14" aria-hidden="true" tabindex="-1"></a>        UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">SelectedLocaleChanged</span> <span class="op">+=</span> OnLocaleChanged<span class="op">;</span></span>
<span id="cb8-15"><a href="#cb8-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-16"><a href="#cb8-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-17"><a href="#cb8-17" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">LoadLocalizedPrefab</span><span class="op">()</span></span>
<span id="cb8-18"><a href="#cb8-18" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-19"><a href="#cb8-19" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 销毁当前实例</span></span>
<span id="cb8-20"><a href="#cb8-20" aria-hidden="true" tabindex="-1"></a>        <span class="kw">if</span> <span class="op">(</span>currentInstance <span class="op">!=</span> <span class="kw">null</span><span class="op">)</span></span>
<span id="cb8-21"><a href="#cb8-21" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb8-22"><a href="#cb8-22" aria-hidden="true" tabindex="-1"></a>            <span class="fu">DestroyImmediate</span><span class="op">(</span>currentInstance<span class="op">);</span></span>
<span id="cb8-23"><a href="#cb8-23" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb8-24"><a href="#cb8-24" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb8-25"><a href="#cb8-25" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载本地化预制体</span></span>
<span id="cb8-26"><a href="#cb8-26" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> loadOperation <span class="op">=</span> localizedUIPrefab<span class="op">.</span><span class="fu">LoadAssetAsync</span><span class="op">();</span></span>
<span id="cb8-27"><a href="#cb8-27" aria-hidden="true" tabindex="-1"></a>        loadOperation<span class="op">.</span><span class="fu">Completed</span> <span class="op">+=</span> <span class="op">(</span>operation<span class="op">)</span> <span class="op">=&gt;</span></span>
<span id="cb8-28"><a href="#cb8-28" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb8-29"><a href="#cb8-29" aria-hidden="true" tabindex="-1"></a>            <span class="kw">if</span> <span class="op">(</span>operation<span class="op">.</span><span class="fu">Status</span> <span class="op">==</span> UnityEngine<span class="op">.</span><span class="fu">ResourceManagement</span><span class="op">.</span><span class="fu">AsyncOperations</span><span class="op">.</span><span class="fu">AsyncOperationStatus</span><span class="op">.</span><span class="fu">Succeeded</span><span class="op">)</span></span>
<span id="cb8-30"><a href="#cb8-30" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb8-31"><a href="#cb8-31" aria-hidden="true" tabindex="-1"></a>                currentInstance <span class="op">=</span> <span class="fu">Instantiate</span><span class="op">(</span>operation<span class="op">.</span><span class="fu">Result</span><span class="op">,</span> parentTransform<span class="op">);</span></span>
<span id="cb8-32"><a href="#cb8-32" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb8-33"><a href="#cb8-33" aria-hidden="true" tabindex="-1"></a>        <span class="op">};</span></span>
<span id="cb8-34"><a href="#cb8-34" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-35"><a href="#cb8-35" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb8-36"><a href="#cb8-36" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">OnLocaleChanged</span><span class="op">(</span>UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Locale</span> locale<span class="op">)</span></span>
<span id="cb8-37"><a href="#cb8-37" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb8-38"><a href="#cb8-38" aria-hidden="true" tabindex="-1"></a>        <span class="fu">LoadLocalizedPrefab</span><span class="op">();</span></span>
<span id="cb8-39"><a href="#cb8-39" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-40"><a href="#cb8-40" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="音频本地化">音频本地化</h2>
<h3 id="本地化音频剪辑">本地化音频剪辑</h3>
<div class="sourceCode" id="cb9"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">;</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">;</span></span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> LocalizedAudioManager <span class="op">:</span> MonoBehaviour</span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> AudioSource audioSource<span class="op">;</span></span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> LocalizedAsset<span class="op">&lt;</span>AudioClip<span class="op">&gt;</span> localizedBackgroundMusic<span class="op">;</span></span>
<span id="cb9-8"><a href="#cb9-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> LocalizedAsset<span class="op">&lt;</span>AudioClip<span class="op">&gt;</span> localizedVoiceover<span class="op">;</span></span>
<span id="cb9-9"><a href="#cb9-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-10"><a href="#cb9-10" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">Start</span><span class="op">()</span></span>
<span id="cb9-11"><a href="#cb9-11" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-12"><a href="#cb9-12" aria-hidden="true" tabindex="-1"></a>        <span class="fu">LoadLocalizedAudio</span><span class="op">();</span></span>
<span id="cb9-13"><a href="#cb9-13" aria-hidden="true" tabindex="-1"></a>        UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">SelectedLocaleChanged</span> <span class="op">+=</span> OnLocaleChanged<span class="op">;</span></span>
<span id="cb9-14"><a href="#cb9-14" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-15"><a href="#cb9-15" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-16"><a href="#cb9-16" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">LoadLocalizedAudio</span><span class="op">()</span></span>
<span id="cb9-17"><a href="#cb9-17" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-18"><a href="#cb9-18" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载背景音乐</span></span>
<span id="cb9-19"><a href="#cb9-19" aria-hidden="true" tabindex="-1"></a>        <span class="fu">LoadBackgroundMusic</span><span class="op">();</span></span>
<span id="cb9-20"><a href="#cb9-20" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb9-21"><a href="#cb9-21" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 加载语音</span></span>
<span id="cb9-22"><a href="#cb9-22" aria-hidden="true" tabindex="-1"></a>        <span class="fu">LoadVoiceover</span><span class="op">();</span></span>
<span id="cb9-23"><a href="#cb9-23" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-24"><a href="#cb9-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-25"><a href="#cb9-25" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">LoadBackgroundMusic</span><span class="op">()</span></span>
<span id="cb9-26"><a href="#cb9-26" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-27"><a href="#cb9-27" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> loadOperation <span class="op">=</span> localizedBackgroundMusic<span class="op">.</span><span class="fu">LoadAssetAsync</span><span class="op">();</span></span>
<span id="cb9-28"><a href="#cb9-28" aria-hidden="true" tabindex="-1"></a>        loadOperation<span class="op">.</span><span class="fu">Completed</span> <span class="op">+=</span> <span class="op">(</span>operation<span class="op">)</span> <span class="op">=&gt;</span></span>
<span id="cb9-29"><a href="#cb9-29" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb9-30"><a href="#cb9-30" aria-hidden="true" tabindex="-1"></a>            <span class="kw">if</span> <span class="op">(</span>operation<span class="op">.</span><span class="fu">Status</span> <span class="op">==</span> UnityEngine<span class="op">.</span><span class="fu">ResourceManagement</span><span class="op">.</span><span class="fu">AsyncOperations</span><span class="op">.</span><span class="fu">AsyncOperationStatus</span><span class="op">.</span><span class="fu">Succeeded</span><span class="op">)</span></span>
<span id="cb9-31"><a href="#cb9-31" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb9-32"><a href="#cb9-32" aria-hidden="true" tabindex="-1"></a>                audioSource<span class="op">.</span><span class="fu">clip</span> <span class="op">=</span> operation<span class="op">.</span><span class="fu">Result</span><span class="op">;</span></span>
<span id="cb9-33"><a href="#cb9-33" aria-hidden="true" tabindex="-1"></a>                audioSource<span class="op">.</span><span class="fu">Play</span><span class="op">();</span></span>
<span id="cb9-34"><a href="#cb9-34" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb9-35"><a href="#cb9-35" aria-hidden="true" tabindex="-1"></a>        <span class="op">};</span></span>
<span id="cb9-36"><a href="#cb9-36" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-37"><a href="#cb9-37" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-38"><a href="#cb9-38" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">LoadVoiceover</span><span class="op">()</span></span>
<span id="cb9-39"><a href="#cb9-39" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-40"><a href="#cb9-40" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> loadOperation <span class="op">=</span> localizedVoiceover<span class="op">.</span><span class="fu">LoadAssetAsync</span><span class="op">();</span></span>
<span id="cb9-41"><a href="#cb9-41" aria-hidden="true" tabindex="-1"></a>        loadOperation<span class="op">.</span><span class="fu">Completed</span> <span class="op">+=</span> <span class="op">(</span>operation<span class="op">)</span> <span class="op">=&gt;</span></span>
<span id="cb9-42"><a href="#cb9-42" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb9-43"><a href="#cb9-43" aria-hidden="true" tabindex="-1"></a>            <span class="kw">if</span> <span class="op">(</span>operation<span class="op">.</span><span class="fu">Status</span> <span class="op">==</span> UnityEngine<span class="op">.</span><span class="fu">ResourceManagement</span><span class="op">.</span><span class="fu">AsyncOperations</span><span class="op">.</span><span class="fu">AsyncOperationStatus</span><span class="op">.</span><span class="fu">Succeeded</span><span class="op">)</span></span>
<span id="cb9-44"><a href="#cb9-44" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb9-45"><a href="#cb9-45" aria-hidden="true" tabindex="-1"></a>                <span class="co">// 播放语音</span></span>
<span id="cb9-46"><a href="#cb9-46" aria-hidden="true" tabindex="-1"></a>                <span class="fu">PlayVoiceover</span><span class="op">(</span>operation<span class="op">.</span><span class="fu">Result</span><span class="op">);</span></span>
<span id="cb9-47"><a href="#cb9-47" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb9-48"><a href="#cb9-48" aria-hidden="true" tabindex="-1"></a>        <span class="op">};</span></span>
<span id="cb9-49"><a href="#cb9-49" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-50"><a href="#cb9-50" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-51"><a href="#cb9-51" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">PlayVoiceover</span><span class="op">(</span>AudioClip clip<span class="op">)</span></span>
<span id="cb9-52"><a href="#cb9-52" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-53"><a href="#cb9-53" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建临时音频源播放语音</span></span>
<span id="cb9-54"><a href="#cb9-54" aria-hidden="true" tabindex="-1"></a>        GameObject tempAudioObject <span class="op">=</span> <span class="kw">new</span> <span class="fu">GameObject</span><span class="op">(</span><span class="st">&quot;Voiceover&quot;</span><span class="op">);</span></span>
<span id="cb9-55"><a href="#cb9-55" aria-hidden="true" tabindex="-1"></a>        AudioSource voiceSource <span class="op">=</span> tempAudioObject<span class="op">.</span><span class="fu">AddComponent</span><span class="op">&lt;</span>AudioSource<span class="op">&gt;();</span></span>
<span id="cb9-56"><a href="#cb9-56" aria-hidden="true" tabindex="-1"></a>        voiceSource<span class="op">.</span><span class="fu">clip</span> <span class="op">=</span> clip<span class="op">;</span></span>
<span id="cb9-57"><a href="#cb9-57" aria-hidden="true" tabindex="-1"></a>        voiceSource<span class="op">.</span><span class="fu">Play</span><span class="op">();</span></span>
<span id="cb9-58"><a href="#cb9-58" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb9-59"><a href="#cb9-59" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 播放完成后销毁</span></span>
<span id="cb9-60"><a href="#cb9-60" aria-hidden="true" tabindex="-1"></a>        <span class="fu">Destroy</span><span class="op">(</span>tempAudioObject<span class="op">,</span> clip<span class="op">.</span><span class="fu">length</span><span class="op">);</span></span>
<span id="cb9-61"><a href="#cb9-61" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-62"><a href="#cb9-62" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-63"><a href="#cb9-63" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">OnLocaleChanged</span><span class="op">(</span>UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Locale</span> locale<span class="op">)</span></span>
<span id="cb9-64"><a href="#cb9-64" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb9-65"><a href="#cb9-65" aria-hidden="true" tabindex="-1"></a>        <span class="fu">LoadLocalizedAudio</span><span class="op">();</span></span>
<span id="cb9-66"><a href="#cb9-66" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-67"><a href="#cb9-67" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="脚本国际化">脚本国际化</h2>
<h3 id="数字和日期格式化">数字和日期格式化</h3>
<div class="sourceCode" id="cb10"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> System<span class="op">;</span></span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> System<span class="op">.</span><span class="fu">Globalization</span><span class="op">;</span></span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">;</span></span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-5"><a href="#cb10-5" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> NumberFormatting <span class="op">:</span> MonoBehaviour</span>
<span id="cb10-6"><a href="#cb10-6" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb10-7"><a href="#cb10-7" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">void</span> <span class="fu">FormatNumbers</span><span class="op">()</span></span>
<span id="cb10-8"><a href="#cb10-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb10-9"><a href="#cb10-9" aria-hidden="true" tabindex="-1"></a>        <span class="dt">float</span> price <span class="op">=</span> <span class="fl">1234.56f</span><span class="op">;</span></span>
<span id="cb10-10"><a href="#cb10-10" aria-hidden="true" tabindex="-1"></a>        DateTime date <span class="op">=</span> DateTime<span class="op">.</span><span class="fu">Now</span><span class="op">;</span></span>
<span id="cb10-11"><a href="#cb10-11" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb10-12"><a href="#cb10-12" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 获取当前语言环境</span></span>
<span id="cb10-13"><a href="#cb10-13" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> currentLocale <span class="op">=</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">SelectedLocale</span><span class="op">;</span></span>
<span id="cb10-14"><a href="#cb10-14" aria-hidden="true" tabindex="-1"></a>        CultureInfo culture <span class="op">=</span> <span class="kw">new</span> <span class="fu">CultureInfo</span><span class="op">(</span>currentLocale<span class="op">.</span><span class="fu">Identifier</span><span class="op">.</span><span class="fu">Code</span><span class="op">);</span></span>
<span id="cb10-15"><a href="#cb10-15" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb10-16"><a href="#cb10-16" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 格式化货币</span></span>
<span id="cb10-17"><a href="#cb10-17" aria-hidden="true" tabindex="-1"></a>        <span class="dt">string</span> formattedPrice <span class="op">=</span> price<span class="op">.</span><span class="fu">ToString</span><span class="op">(</span><span class="st">&quot;C&quot;</span><span class="op">,</span> culture<span class="op">);</span></span>
<span id="cb10-18"><a href="#cb10-18" aria-hidden="true" tabindex="-1"></a>        Debug<span class="op">.</span><span class="fu">Log</span><span class="op">(</span>$<span class="st">&quot;Price: {formattedPrice}&quot;</span><span class="op">);</span></span>
<span id="cb10-19"><a href="#cb10-19" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb10-20"><a href="#cb10-20" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 格式化日期</span></span>
<span id="cb10-21"><a href="#cb10-21" aria-hidden="true" tabindex="-1"></a>        <span class="dt">string</span> formattedDate <span class="op">=</span> date<span class="op">.</span><span class="fu">ToString</span><span class="op">(</span><span class="st">&quot;d&quot;</span><span class="op">,</span> culture<span class="op">);</span></span>
<span id="cb10-22"><a href="#cb10-22" aria-hidden="true" tabindex="-1"></a>        Debug<span class="op">.</span><span class="fu">Log</span><span class="op">(</span>$<span class="st">&quot;Date: {formattedDate}&quot;</span><span class="op">);</span></span>
<span id="cb10-23"><a href="#cb10-23" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb10-24"><a href="#cb10-24" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 格式化数字</span></span>
<span id="cb10-25"><a href="#cb10-25" aria-hidden="true" tabindex="-1"></a>        <span class="dt">int</span> largeNumber <span class="op">=</span> <span class="dv">1234567</span><span class="op">;</span></span>
<span id="cb10-26"><a href="#cb10-26" aria-hidden="true" tabindex="-1"></a>        <span class="dt">string</span> formattedNumber <span class="op">=</span> largeNumber<span class="op">.</span><span class="fu">ToString</span><span class="op">(</span><span class="st">&quot;N0&quot;</span><span class="op">,</span> culture<span class="op">);</span></span>
<span id="cb10-27"><a href="#cb10-27" aria-hidden="true" tabindex="-1"></a>        Debug<span class="op">.</span><span class="fu">Log</span><span class="op">(</span>$<span class="st">&quot;Number: {formattedNumber}&quot;</span><span class="op">);</span></span>
<span id="cb10-28"><a href="#cb10-28" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-29"><a href="#cb10-29" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="字符串比较和排序">字符串比较和排序</h3>
<div class="sourceCode" id="cb11"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> System<span class="op">;</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> System<span class="op">.</span><span class="fu">Collections</span><span class="op">.</span><span class="fu">Generic</span><span class="op">;</span></span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> System<span class="op">.</span><span class="fu">Globalization</span><span class="op">;</span></span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">;</span></span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> StringComparison <span class="op">:</span> MonoBehaviour</span>
<span id="cb11-7"><a href="#cb11-7" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb11-8"><a href="#cb11-8" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">void</span> <span class="fu">SortLocalizedStrings</span><span class="op">(</span>List<span class="op">&lt;</span><span class="dt">string</span><span class="op">&gt;</span> strings<span class="op">)</span></span>
<span id="cb11-9"><a href="#cb11-9" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-10"><a href="#cb11-10" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> currentLocale <span class="op">=</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">SelectedLocale</span><span class="op">;</span></span>
<span id="cb11-11"><a href="#cb11-11" aria-hidden="true" tabindex="-1"></a>        CultureInfo culture <span class="op">=</span> <span class="kw">new</span> <span class="fu">CultureInfo</span><span class="op">(</span>currentLocale<span class="op">.</span><span class="fu">Identifier</span><span class="op">.</span><span class="fu">Code</span><span class="op">);</span></span>
<span id="cb11-12"><a href="#cb11-12" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb11-13"><a href="#cb11-13" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 使用当前文化进行排序</span></span>
<span id="cb11-14"><a href="#cb11-14" aria-hidden="true" tabindex="-1"></a>        strings<span class="op">.</span><span class="fu">Sort</span><span class="op">((</span>x<span class="op">,</span> y<span class="op">)</span> <span class="op">=&gt;</span> <span class="dt">string</span><span class="op">.</span><span class="fu">Compare</span><span class="op">(</span>x<span class="op">,</span> y<span class="op">,</span> culture<span class="op">,</span> CompareOptions<span class="op">.</span><span class="fu">None</span><span class="op">));</span></span>
<span id="cb11-15"><a href="#cb11-15" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb11-16"><a href="#cb11-16" aria-hidden="true" tabindex="-1"></a>        <span class="kw">foreach</span> <span class="op">(</span><span class="dt">string</span> str <span class="kw">in</span> strings<span class="op">)</span></span>
<span id="cb11-17"><a href="#cb11-17" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb11-18"><a href="#cb11-18" aria-hidden="true" tabindex="-1"></a>            Debug<span class="op">.</span><span class="fu">Log</span><span class="op">(</span>str<span class="op">);</span></span>
<span id="cb11-19"><a href="#cb11-19" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb11-20"><a href="#cb11-20" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-21"><a href="#cb11-21" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb11-22"><a href="#cb11-22" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">bool</span> <span class="fu">CompareStringsIgnoreCase</span><span class="op">(</span><span class="dt">string</span> str1<span class="op">,</span> <span class="dt">string</span> str2<span class="op">)</span></span>
<span id="cb11-23"><a href="#cb11-23" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb11-24"><a href="#cb11-24" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> currentLocale <span class="op">=</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">SelectedLocale</span><span class="op">;</span></span>
<span id="cb11-25"><a href="#cb11-25" aria-hidden="true" tabindex="-1"></a>        CultureInfo culture <span class="op">=</span> <span class="kw">new</span> <span class="fu">CultureInfo</span><span class="op">(</span>currentLocale<span class="op">.</span><span class="fu">Identifier</span><span class="op">.</span><span class="fu">Code</span><span class="op">);</span></span>
<span id="cb11-26"><a href="#cb11-26" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb11-27"><a href="#cb11-27" aria-hidden="true" tabindex="-1"></a>        <span class="kw">return</span> <span class="dt">string</span><span class="op">.</span><span class="fu">Compare</span><span class="op">(</span>str1<span class="op">,</span> str2<span class="op">,</span> culture<span class="op">,</span> CompareOptions<span class="op">.</span><span class="fu">IgnoreCase</span><span class="op">)</span> <span class="op">==</span> <span class="dv">0</span><span class="op">;</span></span>
<span id="cb11-28"><a href="#cb11-28" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-29"><a href="#cb11-29" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="最佳实践">最佳实践</h2>
<h3 id="字符串键管理">1. 字符串键管理</h3>
<div class="sourceCode" id="cb12"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 使用常量管理字符串键</span></span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">static</span> <span class="kw">class</span> LocalizationKeys</span>
<span id="cb12-3"><a href="#cb12-3" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb12-4"><a href="#cb12-4" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">const</span> <span class="dt">string</span> TABLE_UI <span class="op">=</span> <span class="st">&quot;UI&quot;</span><span class="op">;</span></span>
<span id="cb12-5"><a href="#cb12-5" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">const</span> <span class="dt">string</span> TABLE_GAME <span class="op">=</span> <span class="st">&quot;Game&quot;</span><span class="op">;</span></span>
<span id="cb12-6"><a href="#cb12-6" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">const</span> <span class="dt">string</span> TABLE_TUTORIAL <span class="op">=</span> <span class="st">&quot;Tutorial&quot;</span><span class="op">;</span></span>
<span id="cb12-7"><a href="#cb12-7" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-8"><a href="#cb12-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// UI 相关</span></span>
<span id="cb12-9"><a href="#cb12-9" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">const</span> <span class="dt">string</span> UI_WELCOME <span class="op">=</span> <span class="st">&quot;welcome_message&quot;</span><span class="op">;</span></span>
<span id="cb12-10"><a href="#cb12-10" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">const</span> <span class="dt">string</span> UI_START_GAME <span class="op">=</span> <span class="st">&quot;start_game&quot;</span><span class="op">;</span></span>
<span id="cb12-11"><a href="#cb12-11" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">const</span> <span class="dt">string</span> UI_SETTINGS <span class="op">=</span> <span class="st">&quot;settings&quot;</span><span class="op">;</span></span>
<span id="cb12-12"><a href="#cb12-12" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">const</span> <span class="dt">string</span> UI_EXIT <span class="op">=</span> <span class="st">&quot;exit&quot;</span><span class="op">;</span></span>
<span id="cb12-13"><a href="#cb12-13" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-14"><a href="#cb12-14" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 游戏相关</span></span>
<span id="cb12-15"><a href="#cb12-15" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">const</span> <span class="dt">string</span> GAME_SCORE <span class="op">=</span> <span class="st">&quot;score&quot;</span><span class="op">;</span></span>
<span id="cb12-16"><a href="#cb12-16" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">const</span> <span class="dt">string</span> GAME_LEVEL <span class="op">=</span> <span class="st">&quot;level&quot;</span><span class="op">;</span></span>
<span id="cb12-17"><a href="#cb12-17" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">const</span> <span class="dt">string</span> GAME_HEALTH <span class="op">=</span> <span class="st">&quot;health&quot;</span><span class="op">;</span></span>
<span id="cb12-18"><a href="#cb12-18" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-19"><a href="#cb12-19" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 创建本地化字符串的辅助方法</span></span>
<span id="cb12-20"><a href="#cb12-20" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="kw">static</span> LocalizedString <span class="fu">CreateUIString</span><span class="op">(</span><span class="dt">string</span> key<span class="op">)</span></span>
<span id="cb12-21"><a href="#cb12-21" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-22"><a href="#cb12-22" aria-hidden="true" tabindex="-1"></a>        <span class="kw">return</span> <span class="kw">new</span> <span class="fu">LocalizedString</span><span class="op">(</span>TABLE_UI<span class="op">,</span> key<span class="op">);</span></span>
<span id="cb12-23"><a href="#cb12-23" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-24"><a href="#cb12-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-25"><a href="#cb12-25" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="kw">static</span> LocalizedString <span class="fu">CreateGameString</span><span class="op">(</span><span class="dt">string</span> key<span class="op">)</span></span>
<span id="cb12-26"><a href="#cb12-26" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-27"><a href="#cb12-27" aria-hidden="true" tabindex="-1"></a>        <span class="kw">return</span> <span class="kw">new</span> <span class="fu">LocalizedString</span><span class="op">(</span>TABLE_GAME<span class="op">,</span> key<span class="op">);</span></span>
<span id="cb12-28"><a href="#cb12-28" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-29"><a href="#cb12-29" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb12-30"><a href="#cb12-30" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-31"><a href="#cb12-31" aria-hidden="true" tabindex="-1"></a><span class="co">// 使用示例</span></span>
<span id="cb12-32"><a href="#cb12-32" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> UIManager <span class="op">:</span> MonoBehaviour</span>
<span id="cb12-33"><a href="#cb12-33" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb12-34"><a href="#cb12-34" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> LocalizedString welcomeMessage<span class="op">;</span></span>
<span id="cb12-35"><a href="#cb12-35" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb12-36"><a href="#cb12-36" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">Start</span><span class="op">()</span></span>
<span id="cb12-37"><a href="#cb12-37" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb12-38"><a href="#cb12-38" aria-hidden="true" tabindex="-1"></a>        welcomeMessage <span class="op">=</span> LocalizationKeys<span class="op">.</span><span class="fu">CreateUIString</span><span class="op">(</span>LocalizationKeys<span class="op">.</span><span class="fu">UI_WELCOME</span><span class="op">);</span></span>
<span id="cb12-39"><a href="#cb12-39" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-40"><a href="#cb12-40" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h3 id="伪本地化测试">2. 伪本地化测试</h3>
<div class="sourceCode" id="cb13"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">;</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">;</span></span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-4"><a href="#cb13-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> PseudoLocalizationTester <span class="op">:</span> MonoBehaviour</span>
<span id="cb13-5"><a href="#cb13-5" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb13-6"><a href="#cb13-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>SerializeField<span class="op">]</span> <span class="kw">private</span> <span class="dt">bool</span> enablePseudoLocalization <span class="op">=</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb13-7"><a href="#cb13-7" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-8"><a href="#cb13-8" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">Start</span><span class="op">()</span></span>
<span id="cb13-9"><a href="#cb13-9" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-10"><a href="#cb13-10" aria-hidden="true" tabindex="-1"></a>        <span class="kw">if</span> <span class="op">(</span>enablePseudoLocalization<span class="op">)</span></span>
<span id="cb13-11"><a href="#cb13-11" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb13-12"><a href="#cb13-12" aria-hidden="true" tabindex="-1"></a>            <span class="fu">EnablePseudoLocalization</span><span class="op">();</span></span>
<span id="cb13-13"><a href="#cb13-13" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb13-14"><a href="#cb13-14" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-15"><a href="#cb13-15" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-16"><a href="#cb13-16" aria-hidden="true" tabindex="-1"></a>    <span class="dt">void</span> <span class="fu">EnablePseudoLocalization</span><span class="op">()</span></span>
<span id="cb13-17"><a href="#cb13-17" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-18"><a href="#cb13-18" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 创建伪本地化处理器</span></span>
<span id="cb13-19"><a href="#cb13-19" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> pseudoLocale <span class="op">=</span> ScriptableObject<span class="op">.</span><span class="fu">CreateInstance</span><span class="op">&lt;</span>UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Locale</span><span class="op">&gt;();</span></span>
<span id="cb13-20"><a href="#cb13-20" aria-hidden="true" tabindex="-1"></a>        pseudoLocale<span class="op">.</span><span class="fu">Identifier</span> <span class="op">=</span> <span class="kw">new</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">LocaleIdentifier</span><span class="op">(</span><span class="st">&quot;pseudo&quot;</span><span class="op">);</span></span>
<span id="cb13-21"><a href="#cb13-21" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb13-22"><a href="#cb13-22" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 添加到可用语言列表</span></span>
<span id="cb13-23"><a href="#cb13-23" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> availableLocales <span class="op">=</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">AvailableLocales</span><span class="op">;</span></span>
<span id="cb13-24"><a href="#cb13-24" aria-hidden="true" tabindex="-1"></a>        availableLocales<span class="op">.</span><span class="fu">AddLocale</span><span class="op">(</span>pseudoLocale<span class="op">);</span></span>
<span id="cb13-25"><a href="#cb13-25" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb13-26"><a href="#cb13-26" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 设置为当前语言</span></span>
<span id="cb13-27"><a href="#cb13-27" aria-hidden="true" tabindex="-1"></a>        UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">SelectedLocale</span> <span class="op">=</span> pseudoLocale<span class="op">;</span></span>
<span id="cb13-28"><a href="#cb13-28" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-29"><a href="#cb13-29" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb13-30"><a href="#cb13-30" aria-hidden="true" tabindex="-1"></a>    <span class="co">// 伪本地化文本处理</span></span>
<span id="cb13-31"><a href="#cb13-31" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="dt">string</span> <span class="fu">PseudoLocalizeText</span><span class="op">(</span><span class="dt">string</span> originalText<span class="op">)</span></span>
<span id="cb13-32"><a href="#cb13-32" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb13-33"><a href="#cb13-33" aria-hidden="true" tabindex="-1"></a>        <span class="kw">if</span> <span class="op">(!</span>enablePseudoLocalization<span class="op">)</span> <span class="kw">return</span> originalText<span class="op">;</span></span>
<span id="cb13-34"><a href="#cb13-34" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb13-35"><a href="#cb13-35" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 添加前缀和后缀</span></span>
<span id="cb13-36"><a href="#cb13-36" aria-hidden="true" tabindex="-1"></a>        <span class="dt">string</span> pseudoText <span class="op">=</span> <span class="st">&quot;[&quot;</span> <span class="op">+</span> originalText <span class="op">+</span> <span class="st">&quot;]&quot;</span><span class="op">;</span></span>
<span id="cb13-37"><a href="#cb13-37" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb13-38"><a href="#cb13-38" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 增加长度（模拟翻译后的长度变化）</span></span>
<span id="cb13-39"><a href="#cb13-39" aria-hidden="true" tabindex="-1"></a>        pseudoText <span class="op">+=</span> <span class="kw">new</span> <span class="dt">string</span><span class="op">(</span><span class="ch">&#39;x&#39;</span><span class="op">,</span> originalText<span class="op">.</span><span class="fu">Length</span> <span class="op">/</span> <span class="dv">3</span><span class="op">);</span></span>
<span id="cb13-40"><a href="#cb13-40" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb13-41"><a href="#cb13-41" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 替换某些字符（模拟特殊字符）</span></span>
<span id="cb13-42"><a href="#cb13-42" aria-hidden="true" tabindex="-1"></a>        pseudoText <span class="op">=</span> pseudoText<span class="op">.</span><span class="fu">Replace</span><span class="op">(</span><span class="ch">&#39;a&#39;</span><span class="op">,</span> &#39;á&#39;<span class="op">).</span><span class="fu">Replace</span><span class="op">(</span><span class="ch">&#39;e&#39;</span><span class="op">,</span> &#39;é&#39;<span class="op">).</span><span class="fu">Replace</span><span class="op">(</span><span class="ch">&#39;o&#39;</span><span class="op">,</span> &#39;ó&#39;<span class="op">);</span></span>
<span id="cb13-43"><a href="#cb13-43" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb13-44"><a href="#cb13-44" aria-hidden="true" tabindex="-1"></a>        <span class="kw">return</span> pseudoText<span class="op">;</span></span>
<span id="cb13-45"><a href="#cb13-45" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-46"><a href="#cb13-46" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<h2 id="工具与工作流">工具与工作流</h2>
<h3 id="csv-导入导出">CSV 导入导出</h3>
<div class="sourceCode" id="cb14"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb14-1"><a href="#cb14-1" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">;</span></span>
<span id="cb14-2"><a href="#cb14-2" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEditor<span class="op">;</span></span>
<span id="cb14-3"><a href="#cb14-3" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">;</span></span>
<span id="cb14-4"><a href="#cb14-4" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> System<span class="op">.</span><span class="fu">IO</span><span class="op">;</span></span>
<span id="cb14-5"><a href="#cb14-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb14-6"><a href="#cb14-6" aria-hidden="true" tabindex="-1"></a><span class="kw">#if</span> UNITY_EDITOR</span>
<span id="cb14-7"><a href="#cb14-7" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> LocalizationCSVExporter <span class="op">:</span> EditorWindow</span>
<span id="cb14-8"><a href="#cb14-8" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb14-9"><a href="#cb14-9" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span><span class="fu">MenuItem</span><span class="op">(</span><span class="st">&quot;Tools/Localization/Export to CSV&quot;</span><span class="op">)]</span></span>
<span id="cb14-10"><a href="#cb14-10" aria-hidden="true" tabindex="-1"></a>    <span class="kw">static</span> <span class="dt">void</span> <span class="fu">ExportToCSV</span><span class="op">()</span></span>
<span id="cb14-11"><a href="#cb14-11" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb14-12"><a href="#cb14-12" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> stringTables <span class="op">=</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">StringDatabase</span><span class="op">.</span><span class="fu">GetAllTables</span><span class="op">();</span></span>
<span id="cb14-13"><a href="#cb14-13" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb14-14"><a href="#cb14-14" aria-hidden="true" tabindex="-1"></a>        <span class="kw">foreach</span> <span class="op">(</span><span class="dt">var</span> table <span class="kw">in</span> stringTables<span class="op">)</span></span>
<span id="cb14-15"><a href="#cb14-15" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb14-16"><a href="#cb14-16" aria-hidden="true" tabindex="-1"></a>            <span class="dt">string</span> csvPath <span class="op">=</span> Path<span class="op">.</span><span class="fu">Combine</span><span class="op">(</span>Application<span class="op">.</span><span class="fu">dataPath</span><span class="op">,</span> $<span class="st">&quot;Localization_{table.TableCollectionName}.csv&quot;</span><span class="op">);</span></span>
<span id="cb14-17"><a href="#cb14-17" aria-hidden="true" tabindex="-1"></a>            </span>
<span id="cb14-18"><a href="#cb14-18" aria-hidden="true" tabindex="-1"></a>            <span class="kw">using</span> <span class="op">(</span>StreamWriter writer <span class="op">=</span> <span class="kw">new</span> <span class="fu">StreamWriter</span><span class="op">(</span>csvPath<span class="op">))</span></span>
<span id="cb14-19"><a href="#cb14-19" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb14-20"><a href="#cb14-20" aria-hidden="true" tabindex="-1"></a>                <span class="co">// 写入标题行</span></span>
<span id="cb14-21"><a href="#cb14-21" aria-hidden="true" tabindex="-1"></a>                writer<span class="op">.</span><span class="fu">WriteLine</span><span class="op">(</span><span class="st">&quot;Key,English,Chinese,Japanese,Arabic&quot;</span><span class="op">);</span></span>
<span id="cb14-22"><a href="#cb14-22" aria-hidden="true" tabindex="-1"></a>                </span>
<span id="cb14-23"><a href="#cb14-23" aria-hidden="true" tabindex="-1"></a>                <span class="co">// 写入数据行</span></span>
<span id="cb14-24"><a href="#cb14-24" aria-hidden="true" tabindex="-1"></a>                <span class="kw">foreach</span> <span class="op">(</span><span class="dt">var</span> entry <span class="kw">in</span> table<span class="op">.</span><span class="fu">Values</span><span class="op">)</span></span>
<span id="cb14-25"><a href="#cb14-25" aria-hidden="true" tabindex="-1"></a>                <span class="op">{</span></span>
<span id="cb14-26"><a href="#cb14-26" aria-hidden="true" tabindex="-1"></a>                    <span class="dt">string</span> key <span class="op">=</span> entry<span class="op">.</span><span class="fu">Key</span><span class="op">;</span></span>
<span id="cb14-27"><a href="#cb14-27" aria-hidden="true" tabindex="-1"></a>                    <span class="dt">string</span> english <span class="op">=</span> <span class="fu">GetLocalizedValue</span><span class="op">(</span>table<span class="op">.</span><span class="fu">TableCollectionName</span><span class="op">,</span> key<span class="op">,</span> <span class="st">&quot;en&quot;</span><span class="op">);</span></span>
<span id="cb14-28"><a href="#cb14-28" aria-hidden="true" tabindex="-1"></a>                    <span class="dt">string</span> chinese <span class="op">=</span> <span class="fu">GetLocalizedValue</span><span class="op">(</span>table<span class="op">.</span><span class="fu">TableCollectionName</span><span class="op">,</span> key<span class="op">,</span> <span class="st">&quot;zh-CN&quot;</span><span class="op">);</span></span>
<span id="cb14-29"><a href="#cb14-29" aria-hidden="true" tabindex="-1"></a>                    <span class="dt">string</span> japanese <span class="op">=</span> <span class="fu">GetLocalizedValue</span><span class="op">(</span>table<span class="op">.</span><span class="fu">TableCollectionName</span><span class="op">,</span> key<span class="op">,</span> <span class="st">&quot;ja&quot;</span><span class="op">);</span></span>
<span id="cb14-30"><a href="#cb14-30" aria-hidden="true" tabindex="-1"></a>                    <span class="dt">string</span> arabic <span class="op">=</span> <span class="fu">GetLocalizedValue</span><span class="op">(</span>table<span class="op">.</span><span class="fu">TableCollectionName</span><span class="op">,</span> key<span class="op">,</span> <span class="st">&quot;ar&quot;</span><span class="op">);</span></span>
<span id="cb14-31"><a href="#cb14-31" aria-hidden="true" tabindex="-1"></a>                    </span>
<span id="cb14-32"><a href="#cb14-32" aria-hidden="true" tabindex="-1"></a>                    writer<span class="op">.</span><span class="fu">WriteLine</span><span class="op">(</span>$<span class="st">&quot;{key},{english},{chinese},{japanese},{arabic}&quot;</span><span class="op">);</span></span>
<span id="cb14-33"><a href="#cb14-33" aria-hidden="true" tabindex="-1"></a>                <span class="op">}</span></span>
<span id="cb14-34"><a href="#cb14-34" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb14-35"><a href="#cb14-35" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb14-36"><a href="#cb14-36" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb14-37"><a href="#cb14-37" aria-hidden="true" tabindex="-1"></a>        Debug<span class="op">.</span><span class="fu">Log</span><span class="op">(</span><span class="st">&quot;CSV export completed&quot;</span><span class="op">);</span></span>
<span id="cb14-38"><a href="#cb14-38" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb14-39"><a href="#cb14-39" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb14-40"><a href="#cb14-40" aria-hidden="true" tabindex="-1"></a>    <span class="kw">static</span> <span class="dt">string</span> <span class="fu">GetLocalizedValue</span><span class="op">(</span><span class="dt">string</span> tableName<span class="op">,</span> <span class="dt">string</span> key<span class="op">,</span> <span class="dt">string</span> localeCode<span class="op">)</span></span>
<span id="cb14-41"><a href="#cb14-41" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb14-42"><a href="#cb14-42" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 获取指定语言的本地化值</span></span>
<span id="cb14-43"><a href="#cb14-43" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> locale <span class="op">=</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">AvailableLocales</span><span class="op">.</span><span class="fu">GetLocale</span><span class="op">(</span>localeCode<span class="op">);</span></span>
<span id="cb14-44"><a href="#cb14-44" aria-hidden="true" tabindex="-1"></a>        <span class="kw">if</span> <span class="op">(</span>locale <span class="op">==</span> <span class="kw">null</span><span class="op">)</span> <span class="kw">return</span> <span class="st">&quot;&quot;</span><span class="op">;</span></span>
<span id="cb14-45"><a href="#cb14-45" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb14-46"><a href="#cb14-46" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> table <span class="op">=</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">StringDatabase</span><span class="op">.</span><span class="fu">GetTable</span><span class="op">(</span>tableName<span class="op">,</span> locale<span class="op">);</span></span>
<span id="cb14-47"><a href="#cb14-47" aria-hidden="true" tabindex="-1"></a>        <span class="kw">if</span> <span class="op">(</span>table <span class="op">==</span> <span class="kw">null</span><span class="op">)</span> <span class="kw">return</span> <span class="st">&quot;&quot;</span><span class="op">;</span></span>
<span id="cb14-48"><a href="#cb14-48" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb14-49"><a href="#cb14-49" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> entry <span class="op">=</span> table<span class="op">.</span><span class="fu">GetEntry</span><span class="op">(</span>key<span class="op">);</span></span>
<span id="cb14-50"><a href="#cb14-50" aria-hidden="true" tabindex="-1"></a>        <span class="kw">return</span> entry<span class="op">?.</span><span class="fu">Value</span> <span class="op">??</span> <span class="st">&quot;&quot;</span><span class="op">;</span></span>
<span id="cb14-51"><a href="#cb14-51" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb14-52"><a href="#cb14-52" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb14-53"><a href="#cb14-53" aria-hidden="true" tabindex="-1"></a><span class="kw">#endif</span></span></code></pre></div>
<h3 id="自动化本地化验证">自动化本地化验证</h3>
<div class="sourceCode" id="cb15"><pre
class="sourceCode csharp"><code class="sourceCode cs"><span id="cb15-1"><a href="#cb15-1" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> UnityEngine<span class="op">;</span></span>
<span id="cb15-2"><a href="#cb15-2" aria-hidden="true" tabindex="-1"></a><span class="kw">using</span> System<span class="op">.</span><span class="fu">Collections</span><span class="op">.</span><span class="fu">Generic</span><span class="op">;</span></span>
<span id="cb15-3"><a href="#cb15-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-4"><a href="#cb15-4" aria-hidden="true" tabindex="-1"></a><span class="kw">public</span> <span class="kw">class</span> LocalizationValidator <span class="op">:</span> MonoBehaviour</span>
<span id="cb15-5"><a href="#cb15-5" aria-hidden="true" tabindex="-1"></a><span class="op">{</span></span>
<span id="cb15-6"><a href="#cb15-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">[</span>System<span class="op">.</span><span class="fu">Serializable</span><span class="op">]</span></span>
<span id="cb15-7"><a href="#cb15-7" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> <span class="kw">class</span> ValidationResult</span>
<span id="cb15-8"><a href="#cb15-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb15-9"><a href="#cb15-9" aria-hidden="true" tabindex="-1"></a>        <span class="kw">public</span> <span class="dt">string</span> tableName<span class="op">;</span></span>
<span id="cb15-10"><a href="#cb15-10" aria-hidden="true" tabindex="-1"></a>        <span class="kw">public</span> <span class="dt">string</span> key<span class="op">;</span></span>
<span id="cb15-11"><a href="#cb15-11" aria-hidden="true" tabindex="-1"></a>        <span class="kw">public</span> <span class="dt">string</span> localeCode<span class="op">;</span></span>
<span id="cb15-12"><a href="#cb15-12" aria-hidden="true" tabindex="-1"></a>        <span class="kw">public</span> <span class="dt">string</span> issue<span class="op">;</span></span>
<span id="cb15-13"><a href="#cb15-13" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb15-14"><a href="#cb15-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-15"><a href="#cb15-15" aria-hidden="true" tabindex="-1"></a>    <span class="kw">public</span> List<span class="op">&lt;</span>ValidationResult<span class="op">&gt;</span> <span class="fu">ValidateLocalization</span><span class="op">()</span></span>
<span id="cb15-16"><a href="#cb15-16" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb15-17"><a href="#cb15-17" aria-hidden="true" tabindex="-1"></a>        List<span class="op">&lt;</span>ValidationResult<span class="op">&gt;</span> results <span class="op">=</span> <span class="kw">new</span> List<span class="op">&lt;</span>ValidationResult<span class="op">&gt;();</span></span>
<span id="cb15-18"><a href="#cb15-18" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb15-19"><a href="#cb15-19" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> stringTables <span class="op">=</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">StringDatabase</span><span class="op">.</span><span class="fu">GetAllTables</span><span class="op">();</span></span>
<span id="cb15-20"><a href="#cb15-20" aria-hidden="true" tabindex="-1"></a>        <span class="dt">var</span> availableLocales <span class="op">=</span> UnityEngine<span class="op">.</span><span class="fu">Localization</span><span class="op">.</span><span class="fu">Settings</span><span class="op">.</span><span class="fu">LocalizationSettings</span><span class="op">.</span><span class="fu">AvailableLocales</span><span class="op">.</span><span class="fu">Locales</span><span class="op">;</span></span>
<span id="cb15-21"><a href="#cb15-21" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb15-22"><a href="#cb15-22" aria-hidden="true" tabindex="-1"></a>        <span class="kw">foreach</span> <span class="op">(</span><span class="dt">var</span> table <span class="kw">in</span> stringTables<span class="op">)</span></span>
<span id="cb15-23"><a href="#cb15-23" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb15-24"><a href="#cb15-24" aria-hidden="true" tabindex="-1"></a>            <span class="kw">foreach</span> <span class="op">(</span><span class="dt">var</span> entry <span class="kw">in</span> table<span class="op">.</span><span class="fu">Values</span><span class="op">)</span></span>
<span id="cb15-25"><a href="#cb15-25" aria-hidden="true" tabindex="-1"></a>            <span class="op">{</span></span>
<span id="cb15-26"><a href="#cb15-26" aria-hidden="true" tabindex="-1"></a>                <span class="dt">string</span> key <span class="op">=</span> entry<span class="op">.</span><span class="fu">Key</span><span class="op">;</span></span>
<span id="cb15-27"><a href="#cb15-27" aria-hidden="true" tabindex="-1"></a>                </span>
<span id="cb15-28"><a href="#cb15-28" aria-hidden="true" tabindex="-1"></a>                <span class="kw">foreach</span> <span class="op">(</span><span class="dt">var</span> locale <span class="kw">in</span> availableLocales<span class="op">)</span></span>
<span id="cb15-29"><a href="#cb15-29" aria-hidden="true" tabindex="-1"></a>                <span class="op">{</span></span>
<span id="cb15-30"><a href="#cb15-30" aria-hidden="true" tabindex="-1"></a>                    <span class="dt">string</span> value <span class="op">=</span> <span class="fu">GetLocalizedValue</span><span class="op">(</span>table<span class="op">.</span><span class="fu">TableCollectionName</span><span class="op">,</span> key<span class="op">,</span> locale<span class="op">.</span><span class="fu">Identifier</span><span class="op">.</span><span class="fu">Code</span><span class="op">);</span></span>
<span id="cb15-31"><a href="#cb15-31" aria-hidden="true" tabindex="-1"></a>                    </span>
<span id="cb15-32"><a href="#cb15-32" aria-hidden="true" tabindex="-1"></a>                    <span class="co">// 检查缺失的翻译</span></span>
<span id="cb15-33"><a href="#cb15-33" aria-hidden="true" tabindex="-1"></a>                    <span class="kw">if</span> <span class="op">(</span><span class="dt">string</span><span class="op">.</span><span class="fu">IsNullOrEmpty</span><span class="op">(</span>value<span class="op">))</span></span>
<span id="cb15-34"><a href="#cb15-34" aria-hidden="true" tabindex="-1"></a>                    <span class="op">{</span></span>
<span id="cb15-35"><a href="#cb15-35" aria-hidden="true" tabindex="-1"></a>                        results<span class="op">.</span><span class="fu">Add</span><span class="op">(</span><span class="kw">new</span> ValidationResult</span>
<span id="cb15-36"><a href="#cb15-36" aria-hidden="true" tabindex="-1"></a>                        <span class="op">{</span></span>
<span id="cb15-37"><a href="#cb15-37" aria-hidden="true" tabindex="-1"></a>                            tableName <span class="op">=</span> table<span class="op">.</span><span class="fu">TableCollectionName</span><span class="op">,</span></span>
<span id="cb15-38"><a href="#cb15-38" aria-hidden="true" tabindex="-1"></a>                            key <span class="op">=</span> key<span class="op">,</span></span>
<span id="cb15-39"><a href="#cb15-39" aria-hidden="true" tabindex="-1"></a>                            localeCode <span class="op">=</span> locale<span class="op">.</span><span class="fu">Identifier</span><span class="op">.</span><span class="fu">Code</span><span class="op">,</span></span>
<span id="cb15-40"><a href="#cb15-40" aria-hidden="true" tabindex="-1"></a>                            issue <span class="op">=</span> <span class="st">&quot;Missing translation&quot;</span></span>
<span id="cb15-41"><a href="#cb15-41" aria-hidden="true" tabindex="-1"></a>                        <span class="op">});</span></span>
<span id="cb15-42"><a href="#cb15-42" aria-hidden="true" tabindex="-1"></a>                    <span class="op">}</span></span>
<span id="cb15-43"><a href="#cb15-43" aria-hidden="true" tabindex="-1"></a>                    </span>
<span id="cb15-44"><a href="#cb15-44" aria-hidden="true" tabindex="-1"></a>                    <span class="co">// 检查过长的文本</span></span>
<span id="cb15-45"><a href="#cb15-45" aria-hidden="true" tabindex="-1"></a>                    <span class="kw">if</span> <span class="op">(</span>value<span class="op">.</span><span class="fu">Length</span> <span class="op">&gt;</span> <span class="dv">100</span><span class="op">)</span></span>
<span id="cb15-46"><a href="#cb15-46" aria-hidden="true" tabindex="-1"></a>                    <span class="op">{</span></span>
<span id="cb15-47"><a href="#cb15-47" aria-hidden="true" tabindex="-1"></a>                        results<span class="op">.</span><span class="fu">Add</span><span class="op">(</span><span class="kw">new</span> ValidationResult</span>
<span id="cb15-48"><a href="#cb15-48" aria-hidden="true" tabindex="-1"></a>                        <span class="op">{</span></span>
<span id="cb15-49"><a href="#cb15-49" aria-hidden="true" tabindex="-1"></a>                            tableName <span class="op">=</span> table<span class="op">.</span><span class="fu">TableCollectionName</span><span class="op">,</span></span>
<span id="cb15-50"><a href="#cb15-50" aria-hidden="true" tabindex="-1"></a>                            key <span class="op">=</span> key<span class="op">,</span></span>
<span id="cb15-51"><a href="#cb15-51" aria-hidden="true" tabindex="-1"></a>                            localeCode <span class="op">=</span> locale<span class="op">.</span><span class="fu">Identifier</span><span class="op">.</span><span class="fu">Code</span><span class="op">,</span></span>
<span id="cb15-52"><a href="#cb15-52" aria-hidden="true" tabindex="-1"></a>                            issue <span class="op">=</span> <span class="st">&quot;Text too long&quot;</span></span>
<span id="cb15-53"><a href="#cb15-53" aria-hidden="true" tabindex="-1"></a>                        <span class="op">});</span></span>
<span id="cb15-54"><a href="#cb15-54" aria-hidden="true" tabindex="-1"></a>                    <span class="op">}</span></span>
<span id="cb15-55"><a href="#cb15-55" aria-hidden="true" tabindex="-1"></a>                    </span>
<span id="cb15-56"><a href="#cb15-56" aria-hidden="true" tabindex="-1"></a>                    <span class="co">// 检查特殊字符</span></span>
<span id="cb15-57"><a href="#cb15-57" aria-hidden="true" tabindex="-1"></a>                    <span class="kw">if</span> <span class="op">(</span><span class="fu">ContainsInvalidCharacters</span><span class="op">(</span>value<span class="op">))</span></span>
<span id="cb15-58"><a href="#cb15-58" aria-hidden="true" tabindex="-1"></a>                    <span class="op">{</span></span>
<span id="cb15-59"><a href="#cb15-59" aria-hidden="true" tabindex="-1"></a>                        results<span class="op">.</span><span class="fu">Add</span><span class="op">(</span><span class="kw">new</span> ValidationResult</span>
<span id="cb15-60"><a href="#cb15-60" aria-hidden="true" tabindex="-1"></a>                        <span class="op">{</span></span>
<span id="cb15-61"><a href="#cb15-61" aria-hidden="true" tabindex="-1"></a>                            tableName <span class="op">=</span> table<span class="op">.</span><span class="fu">TableCollectionName</span><span class="op">,</span></span>
<span id="cb15-62"><a href="#cb15-62" aria-hidden="true" tabindex="-1"></a>                            key <span class="op">=</span> key<span class="op">,</span></span>
<span id="cb15-63"><a href="#cb15-63" aria-hidden="true" tabindex="-1"></a>                            localeCode <span class="op">=</span> locale<span class="op">.</span><span class="fu">Identifier</span><span class="op">.</span><span class="fu">Code</span><span class="op">,</span></span>
<span id="cb15-64"><a href="#cb15-64" aria-hidden="true" tabindex="-1"></a>                            issue <span class="op">=</span> <span class="st">&quot;Contains invalid characters&quot;</span></span>
<span id="cb15-65"><a href="#cb15-65" aria-hidden="true" tabindex="-1"></a>                        <span class="op">});</span></span>
<span id="cb15-66"><a href="#cb15-66" aria-hidden="true" tabindex="-1"></a>                    <span class="op">}</span></span>
<span id="cb15-67"><a href="#cb15-67" aria-hidden="true" tabindex="-1"></a>                <span class="op">}</span></span>
<span id="cb15-68"><a href="#cb15-68" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb15-69"><a href="#cb15-69" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb15-70"><a href="#cb15-70" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb15-71"><a href="#cb15-71" aria-hidden="true" tabindex="-1"></a>        <span class="kw">return</span> results<span class="op">;</span></span>
<span id="cb15-72"><a href="#cb15-72" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb15-73"><a href="#cb15-73" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-74"><a href="#cb15-74" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="dt">string</span> <span class="fu">GetLocalizedValue</span><span class="op">(</span><span class="dt">string</span> tableName<span class="op">,</span> <span class="dt">string</span> key<span class="op">,</span> <span class="dt">string</span> localeCode<span class="op">)</span></span>
<span id="cb15-75"><a href="#cb15-75" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb15-76"><a href="#cb15-76" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 实现获取本地化值的逻辑</span></span>
<span id="cb15-77"><a href="#cb15-77" aria-hidden="true" tabindex="-1"></a>        <span class="kw">return</span> <span class="st">&quot;&quot;</span><span class="op">;</span></span>
<span id="cb15-78"><a href="#cb15-78" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb15-79"><a href="#cb15-79" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-80"><a href="#cb15-80" aria-hidden="true" tabindex="-1"></a>    <span class="kw">private</span> <span class="dt">bool</span> <span class="fu">ContainsInvalidCharacters</span><span class="op">(</span><span class="dt">string</span> text<span class="op">)</span></span>
<span id="cb15-81"><a href="#cb15-81" aria-hidden="true" tabindex="-1"></a>    <span class="op">{</span></span>
<span id="cb15-82"><a href="#cb15-82" aria-hidden="true" tabindex="-1"></a>        <span class="co">// 检查是否包含无效字符</span></span>
<span id="cb15-83"><a href="#cb15-83" aria-hidden="true" tabindex="-1"></a>        <span class="kw">return</span> <span class="kw">false</span><span class="op">;</span></span>
<span id="cb15-84"><a href="#cb15-84" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb15-85"><a href="#cb15-85" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code></pre></div>
<hr />
<p><em>Unity
的本地化系统提供了完整的多语言支持，适合各种规模的项目。更多详细信息请参考
<a
href="https://docs.unity3d.com/Packages/com.unity.localization@latest/">Unity
本地化包文档</a></em></p>
</div><div lang="en" style="display: none;"><h1>[English translation pending]</h1><p>The content for this page has not been translated yet. Please check back later.</p></div>
                </main>
            </div>
        </section>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-title">Qt 3D 开发指南</h4>
                    <p data-i18n="footer-subtitle">专业的跨平台 3D 应用开发解决方案</p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-docs">文档</h4>
                    <ul>
                        <li><a href="qt3d-development-guide.html" data-i18n="footer-link-guide">开发指南</a></li>
                        <li><a href="engine-comparison.html" data-i18n="footer-link-comparison">引擎对比</a></li>
                        <li><a href="internationalization.html" data-i18n="footer-link-i18n">国际化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="footer-resources">资源</h4>
                    <ul>
                        <li><a href="https://doc.qt.io/qt-6/qt3d-index.html" target="_blank" data-i18n="footer-link-official-docs">官方文档</a></li>
                        <li><a href="https://github.com/qt" target="_blank" data-i18n="footer-link-github">GitHub</a></li>
                        <li><a href="https://forum.qt.io/" target="_blank" data-i18n="footer-link-forum">社区论坛</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-i18n="footer-copyright">&copy; 2024 Qt 3D 开发指南. 基于 Qt 框架构建.</p>
            </div>
        </div>
    </footer>

    <script src="../../assets/js/prism.js"></script>
    <script src="../../assets/js/docs.js"></script>
</body>
</html> 