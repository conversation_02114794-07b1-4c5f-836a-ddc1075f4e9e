/* PrismJS 代码高亮样式 */
code[class*="language-"],
pre[class*="language-"] {
    color: #f8f8f2;
    background: none;
    text-shadow: 0 1px rgba(0, 0, 0, 0.3);
    font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', <PERSON><PERSON><PERSON>, 'Courier New', monospace;
    font-size: 14px;
    text-align: left;
    white-space: pre;
    word-spacing: normal;
    word-break: normal;
    word-wrap: normal;
    line-height: 1.5;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
    -webkit-hyphens: none;
    -moz-hyphens: none;
    -ms-hyphens: none;
    hyphens: none;
}

/* Code blocks */
pre[class*="language-"] {
    padding: 1em;
    margin: .5em 0;
    overflow: auto;
    border-radius: 8px;
    background: #282a36;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
    background: #282a36;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
    padding: .2em .4em;
    border-radius: 4px;
    white-space: normal;
    background: #44475a;
    color: #f8f8f2;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: #6272a4;
    font-style: italic;
}

.token.punctuation {
    color: #f8f8f2;
}

.namespace {
    opacity: .7;
}

.token.property,
.token.tag,
.token.constant,
.token.symbol,
.token.deleted {
    color: #ff79c6;
}

.token.boolean,
.token.number {
    color: #bd93f9;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
    color: #50fa7b;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
    color: #f8f8f2;
}

.token.atrule,
.token.attr-value,
.token.function,
.token.class-name {
    color: #f1fa8c;
}

.token.keyword {
    color: #8be9fd;
    font-weight: bold;
}

.token.regex,
.token.important {
    color: #ffb86c;
}

.token.important,
.token.bold {
    font-weight: bold;
}

.token.italic {
    font-style: italic;
}

.token.entity {
    cursor: help;
}

/* 行号 */
pre[class*="language-"].line-numbers {
    position: relative;
    padding-left: 3.8em;
    counter-reset: linenumber;
}

pre[class*="language-"].line-numbers > code {
    position: relative;
    white-space: inherit;
}

.line-numbers .line-numbers-rows {
    position: absolute;
    pointer-events: none;
    top: 0;
    font-size: 100%;
    left: -3.8em;
    width: 3em;
    letter-spacing: -1px;
    border-right: 1px solid #44475a;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.line-numbers-rows > span {
    pointer-events: none;
    display: block;
    counter-increment: linenumber;
}

.line-numbers-rows > span:before {
    content: counter(linenumber);
    color: #6272a4;
    display: block;
    padding-right: 0.8em;
    text-align: right;
}

/* 代码块标题 */
.code-toolbar {
    position: relative;
}

.code-toolbar > .toolbar {
    position: absolute;
    top: 0.3em;
    right: 0.2em;
    transition: opacity 0.3s ease-in-out;
    opacity: 0;
}

.code-toolbar:hover > .toolbar {
    opacity: 1;
}

.code-toolbar > .toolbar .toolbar-item {
    display: inline-block;
}

.code-toolbar > .toolbar a {
    cursor: pointer;
}

.code-toolbar > .toolbar button {
    background: none;
    border: 0;
    color: inherit;
    font: inherit;
    line-height: normal;
    overflow: visible;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.code-toolbar > .toolbar a,
.code-toolbar > .toolbar button,
.code-toolbar > .toolbar span {
    color: #bbb;
    font-size: .8em;
    padding: 0 .5em;
    background: #44475a;
    background: rgba(68, 71, 90, 0.8);
    box-shadow: 0 2px 0 0 rgba(0,0,0,0.2);
    border-radius: .5em;
}

.code-toolbar > .toolbar a:hover,
.code-toolbar > .toolbar a:focus,
.code-toolbar > .toolbar button:hover,
.code-toolbar > .toolbar button:focus,
.code-toolbar > .toolbar span:hover,
.code-toolbar > .toolbar span:focus {
    color: inherit;
    text-decoration: none;
}

/* 特定语言样式 */
.language-cpp .token.keyword {
    color: #ff79c6;
}

.language-cpp .token.class-name {
    color: #8be9fd;
}

.language-cpp .token.function {
    color: #50fa7b;
}

.language-qml .token.property {
    color: #f1fa8c;
}

.language-qml .token.string {
    color: #50fa7b;
}

.language-bash .token.function {
    color: #8be9fd;
}

.language-bash .token.parameter {
    color: #f8f8f2;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
    code[class*="language-"],
    pre[class*="language-"] {
        font-size: 12px;
    }
    
    pre[class*="language-"].line-numbers {
        padding-left: 2.8em;
    }
    
    .line-numbers .line-numbers-rows {
        left: -2.8em;
        width: 2em;
    }
}

/* 自定义滚动条 */
pre[class*="language-"]::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

pre[class*="language-"]::-webkit-scrollbar-track {
    background: #44475a;
    border-radius: 4px;
}

pre[class*="language-"]::-webkit-scrollbar-thumb {
    background: #6272a4;
    border-radius: 4px;
}

pre[class*="language-"]::-webkit-scrollbar-thumb:hover {
    background: #8be9fd;
}

/* 代码块动画 */
pre[class*="language-"] {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 高亮行 */
.highlight-lines .highlight-line {
    background: rgba(255, 255, 255, 0.1);
    display: block;
    margin: 0 -1em;
    padding: 0 1em;
}

/* 差异显示 */
.token.inserted {
    background: rgba(80, 250, 123, 0.2);
}

.token.deleted {
    background: rgba(255, 85, 85, 0.2);
}

/* 代码块复制按钮样式 */
.copy-to-clipboard-button {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #44475a;
    color: #f8f8f2;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.code-toolbar:hover .copy-to-clipboard-button {
    opacity: 1;
}

.copy-to-clipboard-button:hover {
    background: #6272a4;
}

.copy-to-clipboard-button:active {
    background: #8be9fd;
    color: #282a36;
}

/* 代码块语言标签 */
.code-language {
    position: absolute;
    top: 0;
    right: 0;
    background: #ff79c6;
    color: #282a36;
    padding: 2px 8px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    border-bottom-left-radius: 4px;
}

/* 代码块标题 */
.code-title {
    background: #44475a;
    color: #f8f8f2;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    margin-bottom: 0;
}

.code-title + pre[class*="language-"] {
    margin-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
